/* AI Question Generation Styles */
.ai-question-generation {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

/* Dashboard Styles */
.ai-question-dashboard .ant-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.ai-question-dashboard .ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.action-card {
  height: 100%;
}

.action-card .ant-card-body {
  height: calc(100% - 120px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.action-card ul {
  margin: 16px 0;
  padding-left: 20px;
}

.action-card li {
  margin-bottom: 8px;
  color: #666;
}

/* Form Styles */
.question-generation-form .ant-card {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.form-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  border: none;
  background: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: #f0f0f0;
  color: #1890ff;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
}

.title-icon {
  font-size: 20px;
  color: #1890ff;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

/* Question Preview Styles */
.question-preview .ant-card {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.preview-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.selection-info {
  color: #666;
  font-weight: 500;
}

.preview-summary {
  background-color: #fafafa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.summary-item {
  text-align: center;
}

.summary-item strong {
  display: block;
  margin-bottom: 4px;
  color: #333;
}

/* Question Card Styles */
.question-card {
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.question-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.question-card.selected {
  border-color: #52c41a;
  background-color: #f6ffed;
}

.question-card.rejected {
  border-color: #ff4d4f;
  background-color: #fff2f0;
  opacity: 0.7;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.question-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.question-number {
  font-weight: 600;
  color: #333;
}

.question-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.question-content h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  line-height: 1.5;
}

.options {
  margin-top: 16px;
}

.option {
  padding: 8px 12px;
  margin: 4px 0;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background-color: #fafafa;
  transition: all 0.2s ease;
}

.option.correct {
  background-color: #f6ffed;
  border-color: #52c41a;
  color: #389e0d;
}

.correct-answer {
  margin-top: 12px;
  padding: 8px 12px;
  background-color: #f6ffed;
  border: 1px solid #52c41a;
  border-radius: 4px;
  color: #389e0d;
  font-weight: 500;
}

.fill-blank-answer {
  margin-top: 16px;
  padding: 8px 12px;
  background-color: #f6ffed;
  border-radius: 4px;
  color: #389e0d;
}

.image-description {
  margin-top: 16px;
  padding: 8px 12px;
  background-color: #fff7e6;
  border-radius: 4px;
  color: #d46b08;
  display: flex;
  align-items: center;
  gap: 8px;
}

.syllabus-topics {
  margin-top: 16px;
}

.syllabus-topics strong {
  display: block;
  margin-bottom: 8px;
  color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-question-generation {
    padding: 12px;
  }

  .page-header {
    padding: 16px;
    margin-bottom: 24px;
  }

  .page-header h2 {
    font-size: 24px;
  }

  .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .preview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .preview-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    width: 100%;
  }

  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .question-meta {
    flex-wrap: wrap;
  }

  .question-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .form-actions {
    flex-direction: column;
    gap: 8px;
  }

  .form-actions button {
    width: 100%;
  }
}

/* Loading and Progress Styles */
.ant-progress-line {
  margin-top: 8px;
}

.ant-alert {
  border-radius: 8px;
}

/* Statistics Cards */
.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  color: #666;
  font-weight: 500;
}

.ant-statistic-content {
  color: #333;
  font-weight: 600;
}

.ant-statistic-content-prefix {
  margin-right: 8px;
  color: #1890ff;
}

/* Table Styles */
.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* Button Styles */
.ant-btn-primary {
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* Tag Styles */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* Modal Styles */
.ant-modal {
  border-radius: 12px;
}

.ant-modal-header {
  border-radius: 12px 12px 0 0;
}

.ant-modal-footer {
  border-radius: 0 0 12px 12px;
}
