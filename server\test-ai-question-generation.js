const AIQuestionGenerationService = require('./services/aiQuestionGenerationService');
const QuestionValidationService = require('./services/questionValidationService');

// Test the AI Question Generation Service
async function testAIQuestionGeneration() {
  console.log('🤖 Testing AI Question Generation Service...\n');

  const aiService = new AIQuestionGenerationService();
  const validationService = new QuestionValidationService();

  // Test parameters
  const testParams = {
    questionTypes: ['multiple_choice', 'fill_blank'],
    subjects: ['Mathematics'],
    level: 'primary',
    class: '3',
    difficultyLevels: ['easy', 'medium'],
    syllabusTopics: ['Numbers', 'Addition'],
    totalQuestions: 3,
    questionDistribution: {
      multiple_choice: 2,
      fill_blank: 1,
      picture_based: 0,
    },
  };

  try {
    console.log('📋 Test Parameters:');
    console.log(JSON.stringify(testParams, null, 2));
    console.log('\n⏳ Generating questions...\n');

    // Test multiple choice generation
    console.log('1️⃣ Testing Multiple Choice Generation...');
    const mcQuestions = await aiService.generateMultipleChoiceQuestions(testParams, 1);
    
    if (mcQuestions.length > 0) {
      console.log('✅ Multiple choice question generated successfully');
      console.log('Question:', mcQuestions[0].name);
      console.log('Options:', mcQuestions[0].options);
      console.log('Correct:', mcQuestions[0].correctOption);
      
      // Validate the question
      const validation = validationService.validateQuestion(
        mcQuestions[0],
        testParams.level,
        testParams.class,
        testParams.subjects[0]
      );
      console.log('Validation Score:', validation.score);
      console.log('Quality Level:', validation.qualityLevel);
      if (validation.errors.length > 0) {
        console.log('Errors:', validation.errors);
      }
      if (validation.warnings.length > 0) {
        console.log('Warnings:', validation.warnings);
      }
    } else {
      console.log('❌ Failed to generate multiple choice question');
    }

    console.log('\n2️⃣ Testing Fill in the Blank Generation...');
    const fillBlankQuestions = await aiService.generateFillBlankQuestions(testParams, 1);
    
    if (fillBlankQuestions.length > 0) {
      console.log('✅ Fill in the blank question generated successfully');
      console.log('Question:', fillBlankQuestions[0].name);
      console.log('Answer:', fillBlankQuestions[0].correctAnswer);
      
      // Validate the question
      const validation = validationService.validateQuestion(
        fillBlankQuestions[0],
        testParams.level,
        testParams.class,
        testParams.subjects[0]
      );
      console.log('Validation Score:', validation.score);
      console.log('Quality Level:', validation.qualityLevel);
    } else {
      console.log('❌ Failed to generate fill in the blank question');
    }

    console.log('\n3️⃣ Testing Validation Service...');
    
    // Test with a sample question
    const sampleQuestion = {
      name: "What is 2 + 2?",
      answerType: "Options",
      options: {
        A: "3",
        B: "4",
        C: "5",
        D: "6"
      },
      correctOption: "B",
      isAIGenerated: true,
      questionType: "multiple_choice",
      difficultyLevel: "easy",
      syllabusTopics: ["Addition"]
    };

    const validation = validationService.validateQuestion(
      sampleQuestion,
      'primary',
      '1',
      'Mathematics'
    );

    console.log('✅ Validation test completed');
    console.log('Sample Question Score:', validation.score);
    console.log('Quality Level:', validation.qualityLevel);
    console.log('Is Valid:', validation.isValid);

    console.log('\n4️⃣ Testing Syllabus Data...');
    const syllabusData = aiService.getSyllabusData('primary', '1', 'Mathematics');
    console.log('✅ Syllabus data retrieved');
    console.log('Available topics:', Object.keys(syllabusData));

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('- Multiple Choice Generation: ✅');
    console.log('- Fill in the Blank Generation: ✅');
    console.log('- Question Validation: ✅');
    console.log('- Syllabus Integration: ✅');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Test validation service independently
function testValidationService() {
  console.log('\n🔍 Testing Validation Service Independently...\n');
  
  const validationService = new QuestionValidationService();

  // Test cases
  const testCases = [
    {
      name: "Valid Multiple Choice",
      question: {
        name: "What is the capital of Tanzania?",
        answerType: "Options",
        options: { A: "Dar es Salaam", B: "Dodoma", C: "Arusha", D: "Mwanza" },
        correctOption: "B",
        questionType: "multiple_choice",
        difficultyLevel: "easy"
      }
    },
    {
      name: "Invalid Multiple Choice (Missing Option)",
      question: {
        name: "What is 5 + 5?",
        answerType: "Options",
        options: { A: "10", B: "15" }, // Missing C and D
        correctOption: "A",
        questionType: "multiple_choice"
      }
    },
    {
      name: "Valid Fill in the Blank",
      question: {
        name: "The largest city in Tanzania is ____.",
        answerType: "Fill in the Blank",
        correctAnswer: "Dar es Salaam",
        questionType: "fill_blank"
      }
    },
    {
      name: "Invalid Fill in the Blank (No Blanks)",
      question: {
        name: "What is the largest city in Tanzania?",
        answerType: "Fill in the Blank",
        correctAnswer: "Dar es Salaam",
        questionType: "fill_blank"
      }
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. Testing: ${testCase.name}`);
    const result = validationService.validateQuestion(
      testCase.question,
      'primary',
      '5',
      'Geography'
    );
    
    console.log(`   Score: ${result.score}`);
    console.log(`   Valid: ${result.isValid}`);
    console.log(`   Quality: ${result.qualityLevel}`);
    if (result.errors.length > 0) {
      console.log(`   Errors: ${result.errors.join(', ')}`);
    }
    if (result.warnings.length > 0) {
      console.log(`   Warnings: ${result.warnings.join(', ')}`);
    }
    console.log('');
  });
}

// Run tests
async function runAllTests() {
  console.log('🚀 Starting AI Question Generation Tests\n');
  console.log('=' * 50);
  
  // Check if OpenAI API key is configured
  if (!process.env.OPENAI_API_KEY) {
    console.log('⚠️  Warning: OPENAI_API_KEY not found in environment variables');
    console.log('   Some tests may fail. Please configure your API key.\n');
  }

  try {
    await testAIQuestionGeneration();
    testValidationService();
    
    console.log('\n' + '=' * 50);
    console.log('🎉 All tests completed! The AI Question Generation feature is ready to use.');
    console.log('\nNext steps:');
    console.log('1. Start your server: npm run dev');
    console.log('2. Navigate to Admin Panel → AI Questions');
    console.log('3. Try generating your first set of questions!');
    
  } catch (error) {
    console.error('\n❌ Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testAIQuestionGeneration,
  testValidationService,
  runAllTests
};
