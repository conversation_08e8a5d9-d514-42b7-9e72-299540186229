// Tanzania Institute of Education (TIE) Official Syllabus Structure
// Based on official syllabuses from https://www.tie.go.tz/
// Covers Primary (Standards I-VI), Ordinary Secondary (Forms I-IV), and Advanced Secondary (Forms V-VI)

// PRIMARY EDUCATION SYLLABUS (Standards I-VI)
// Based on TIE Primary Education Curriculum 2016
const primarySyllabus = {
  "Mathematics": {
    "I": {
      topics: [
        {
          topicName: "Numbers and Numeration",
          subtopics: [
            { name: "Counting 1-10", difficulty: "easy", keyTerms: ["numbers", "counting", "sequence"] },
            { name: "Number Recognition", difficulty: "easy", keyTerms: ["digits", "symbols", "identification"] },
            { name: "Number Writing", difficulty: "medium", keyTerms: ["writing", "formation", "practice"] }
          ]
        },
        {
          topicName: "Basic Operations",
          subtopics: [
            { name: "Addition within 10", difficulty: "medium", keyTerms: ["addition", "plus", "sum"] },
            { name: "Subtraction within 10", difficulty: "medium", keyTerms: ["subtraction", "minus", "difference"] }
          ]
        },
        {
          topicName: "Geometry",
          subtopics: [
            { name: "Basic Shapes", difficulty: "easy", keyTerms: ["circle", "square", "triangle", "rectangle"] },
            { name: "Shape Recognition", difficulty: "easy", keyTerms: ["identify", "name", "describe"] }
          ]
        }
      ]
    },
    "II": {
      topics: [
        {
          topicName: "Numbers and Numeration",
          subtopics: [
            { name: "Counting 1-100", difficulty: "easy", keyTerms: ["hundreds", "tens", "ones"] },
            { name: "Place Value", difficulty: "medium", keyTerms: ["place value", "position", "digit value"] },
            { name: "Number Patterns", difficulty: "medium", keyTerms: ["patterns", "sequences", "skip counting"] }
          ]
        },
        {
          topicName: "Operations",
          subtopics: [
            { name: "Addition within 100", difficulty: "medium", keyTerms: ["addition", "carrying", "regrouping"] },
            { name: "Subtraction within 100", difficulty: "medium", keyTerms: ["subtraction", "borrowing", "difference"] },
            { name: "Introduction to Multiplication", difficulty: "medium", keyTerms: ["groups", "repeated addition", "times"] }
          ]
        },
        {
          topicName: "Measurement",
          subtopics: [
            { name: "Length", difficulty: "easy", keyTerms: ["long", "short", "meter", "centimeter"] },
            { name: "Time", difficulty: "medium", keyTerms: ["clock", "hours", "minutes", "time"] }
          ]
        }
      ]
    },
    "III": {
      topics: [
        {
          topicName: "Numbers and Numeration",
          subtopics: [
            { name: "Numbers up to 1000", difficulty: "medium", keyTerms: ["thousands", "hundreds", "tens", "ones"] },
            { name: "Comparing Numbers", difficulty: "medium", keyTerms: ["greater than", "less than", "equal"] }
          ]
        },
        {
          topicName: "Fractions",
          subtopics: [
            { name: "Introduction to Fractions", difficulty: "medium", keyTerms: ["half", "quarter", "whole", "part"] },
            { name: "Simple Fractions", difficulty: "medium", keyTerms: ["numerator", "denominator", "fraction"] }
          ]
        }
      ]
    },
    "4": {
      topics: [
        {
          topicName: "Numbers and Operations",
          subtopics: [
            { name: "Multiplication Tables", difficulty: "medium", keyTerms: ["multiplication", "times", "product"] },
            { name: "Division", difficulty: "hard", keyTerms: ["division", "quotient", "remainder"] }
          ]
        },
        {
          topicName: "Measurement",
          subtopics: [
            { name: "Length and Distance", difficulty: "medium", keyTerms: ["meter", "centimeter", "kilometer"] },
            { name: "Time", difficulty: "medium", keyTerms: ["hours", "minutes", "seconds", "clock"] }
          ]
        }
      ]
    },
    "5": {
      topics: [
        {
          topicName: "Decimals",
          subtopics: [
            { name: "Introduction to Decimals", difficulty: "medium", keyTerms: ["decimal point", "tenths", "hundredths"] },
            { name: "Decimal Operations", difficulty: "hard", keyTerms: ["adding decimals", "subtracting decimals"] }
          ]
        },
        {
          topicName: "Geometry",
          subtopics: [
            { name: "Angles", difficulty: "medium", keyTerms: ["right angle", "acute", "obtuse"] },
            { name: "Perimeter and Area", difficulty: "hard", keyTerms: ["perimeter", "area", "square units"] }
          ]
        }
      ]
    },
    "6": {
      topics: [
        {
          topicName: "Percentages",
          subtopics: [
            { name: "Introduction to Percentages", difficulty: "medium", keyTerms: ["percent", "percentage", "out of 100"] },
            { name: "Percentage Calculations", difficulty: "hard", keyTerms: ["finding percentage", "percentage of amount"] }
          ]
        },
        {
          topicName: "Data Handling",
          subtopics: [
            { name: "Charts and Graphs", difficulty: "medium", keyTerms: ["bar chart", "pie chart", "line graph"] },
            { name: "Data Collection", difficulty: "medium", keyTerms: ["survey", "tally", "frequency"] }
          ]
        }
      ]
    },
    "7": {
      topics: [
        {
          topicName: "Algebra Introduction",
          subtopics: [
            { name: "Simple Equations", difficulty: "hard", keyTerms: ["variable", "equation", "solve"] },
            { name: "Number Patterns", difficulty: "medium", keyTerms: ["sequence", "pattern", "rule"] }
          ]
        },
        {
          topicName: "Advanced Geometry",
          subtopics: [
            { name: "Volume and Surface Area", difficulty: "hard", keyTerms: ["volume", "surface area", "cubic units"] },
            { name: "Coordinate Geometry", difficulty: "hard", keyTerms: ["coordinates", "x-axis", "y-axis"] }
          ]
        }
      ]
    }
  },
  "Science and Technology": {
    "1": {
      topics: [
        {
          topicName: "Living Things",
          subtopics: [
            { name: "Animals Around Us", difficulty: "easy", keyTerms: ["animals", "pets", "wild animals"] },
            { name: "Plants Around Us", difficulty: "easy", keyTerms: ["plants", "trees", "flowers"] }
          ]
        },
        {
          topicName: "Our Environment",
          subtopics: [
            { name: "Clean and Dirty", difficulty: "easy", keyTerms: ["clean", "dirty", "hygiene"] },
            { name: "Day and Night", difficulty: "easy", keyTerms: ["sun", "moon", "day", "night"] }
          ]
        }
      ]
    },
    "2": {
      topics: [
        {
          topicName: "Human Body",
          subtopics: [
            { name: "Body Parts", difficulty: "easy", keyTerms: ["head", "hands", "legs", "body"] },
            { name: "Senses", difficulty: "medium", keyTerms: ["sight", "hearing", "touch", "taste", "smell"] }
          ]
        }
      ]
    },
    "3": {
      topics: [
        {
          topicName: "Matter",
          subtopics: [
            { name: "Solids, Liquids, and Gases", difficulty: "medium", keyTerms: ["solid", "liquid", "gas", "matter"] },
            { name: "Properties of Materials", difficulty: "medium", keyTerms: ["hard", "soft", "rough", "smooth"] }
          ]
        }
      ]
    },
    "4": {
      topics: [
        {
          topicName: "Energy",
          subtopics: [
            { name: "Sources of Energy", difficulty: "medium", keyTerms: ["sun", "wind", "water", "energy"] },
            { name: "Heat and Light", difficulty: "medium", keyTerms: ["heat", "light", "temperature"] }
          ]
        }
      ]
    },
    "5": {
      topics: [
        {
          topicName: "Forces and Motion",
          subtopics: [
            { name: "Push and Pull", difficulty: "medium", keyTerms: ["force", "push", "pull", "motion"] },
            { name: "Simple Machines", difficulty: "hard", keyTerms: ["lever", "pulley", "wheel", "machine"] }
          ]
        }
      ]
    },
    "6": {
      topics: [
        {
          topicName: "Earth and Space",
          subtopics: [
            { name: "Solar System", difficulty: "medium", keyTerms: ["sun", "planets", "moon", "stars"] },
            { name: "Weather and Climate", difficulty: "medium", keyTerms: ["weather", "rain", "sunshine", "wind"] }
          ]
        }
      ]
    },
    "7": {
      topics: [
        {
          topicName: "Technology",
          subtopics: [
            { name: "Communication Technology", difficulty: "medium", keyTerms: ["telephone", "radio", "internet"] },
            { name: "Transportation", difficulty: "medium", keyTerms: ["cars", "planes", "ships", "transport"] }
          ]
        }
      ]
    }
  },
  "English": {
    "1": {
      topics: [
        {
          topicName: "Vocabulary",
          subtopics: [
            { name: "Common Words", difficulty: "easy", keyTerms: ["words", "vocabulary", "meaning"] },
            { name: "Simple Sentences", difficulty: "medium", keyTerms: ["sentences", "grammar", "structure"] }
          ]
        },
        {
          topicName: "Reading",
          subtopics: [
            { name: "Letter Recognition", difficulty: "easy", keyTerms: ["letters", "alphabet", "reading"] },
            { name: "Simple Words", difficulty: "medium", keyTerms: ["words", "reading", "pronunciation"] }
          ]
        }
      ]
    },
    "2": {
      topics: [
        {
          topicName: "Grammar",
          subtopics: [
            { name: "Nouns", difficulty: "medium", keyTerms: ["nouns", "naming words", "person", "place", "thing"] },
            { name: "Verbs", difficulty: "medium", keyTerms: ["verbs", "action words", "doing words"] }
          ]
        }
      ]
    },
    "3": {
      topics: [
        {
          topicName: "Writing",
          subtopics: [
            { name: "Sentence Construction", difficulty: "medium", keyTerms: ["sentences", "capital letters", "full stops"] },
            { name: "Paragraph Writing", difficulty: "hard", keyTerms: ["paragraphs", "ideas", "organization"] }
          ]
        }
      ]
    },
    "4": {
      topics: [
        {
          topicName: "Literature",
          subtopics: [
            { name: "Stories and Poems", difficulty: "medium", keyTerms: ["stories", "poems", "characters"] },
            { name: "Reading Comprehension", difficulty: "medium", keyTerms: ["understanding", "meaning", "questions"] }
          ]
        }
      ]
    },
    "5": {
      topics: [
        {
          topicName: "Advanced Grammar",
          subtopics: [
            { name: "Adjectives and Adverbs", difficulty: "medium", keyTerms: ["adjectives", "adverbs", "describing words"] },
            { name: "Tenses", difficulty: "hard", keyTerms: ["past", "present", "future", "tense"] }
          ]
        }
      ]
    },
    "6": {
      topics: [
        {
          topicName: "Communication Skills",
          subtopics: [
            { name: "Speaking and Listening", difficulty: "medium", keyTerms: ["speaking", "listening", "communication"] },
            { name: "Presentations", difficulty: "hard", keyTerms: ["presenting", "audience", "confidence"] }
          ]
        }
      ]
    },
    "7": {
      topics: [
        {
          topicName: "Creative Writing",
          subtopics: [
            { name: "Essay Writing", difficulty: "hard", keyTerms: ["essays", "arguments", "structure"] },
            { name: "Creative Stories", difficulty: "hard", keyTerms: ["creativity", "imagination", "plot"] }
          ]
        }
      ]
    }
  },
  "Geography": {
    "1": {
      topics: [
        {
          topicName: "Our Surroundings",
          subtopics: [
            { name: "Home and School", difficulty: "easy", keyTerms: ["home", "school", "neighborhood"] },
            { name: "Local Area", difficulty: "easy", keyTerms: ["village", "town", "community"] }
          ]
        }
      ]
    },
    "2": {
      topics: [
        {
          topicName: "Maps and Directions",
          subtopics: [
            { name: "Simple Maps", difficulty: "medium", keyTerms: ["map", "directions", "symbols"] },
            { name: "North, South, East, West", difficulty: "medium", keyTerms: ["compass", "directions", "navigation"] }
          ]
        }
      ]
    },
    "3": {
      topics: [
        {
          topicName: "Physical Features",
          subtopics: [
            { name: "Mountains and Hills", difficulty: "medium", keyTerms: ["mountains", "hills", "valleys"] },
            { name: "Rivers and Lakes", difficulty: "medium", keyTerms: ["rivers", "lakes", "water bodies"] }
          ]
        }
      ]
    },
    "4": {
      topics: [
        {
          topicName: "Tanzania Geography",
          subtopics: [
            { name: "Regions of Tanzania", difficulty: "medium", keyTerms: ["regions", "Tanzania", "boundaries"] },
            { name: "Major Cities", difficulty: "medium", keyTerms: ["Dar es Salaam", "Dodoma", "Arusha", "Mwanza"] }
          ]
        }
      ]
    },
    "5": {
      topics: [
        {
          topicName: "Climate and Weather",
          subtopics: [
            { name: "Seasons in Tanzania", difficulty: "medium", keyTerms: ["dry season", "wet season", "climate"] },
            { name: "Weather Patterns", difficulty: "medium", keyTerms: ["rainfall", "temperature", "humidity"] }
          ]
        }
      ]
    },
    "6": {
      topics: [
        {
          topicName: "Economic Activities",
          subtopics: [
            { name: "Agriculture", difficulty: "medium", keyTerms: ["farming", "crops", "livestock"] },
            { name: "Mining and Industry", difficulty: "hard", keyTerms: ["mining", "gold", "diamonds", "industry"] }
          ]
        }
      ]
    },
    "7": {
      topics: [
        {
          topicName: "East Africa",
          subtopics: [
            { name: "East African Countries", difficulty: "medium", keyTerms: ["Kenya", "Uganda", "Rwanda", "Burundi"] },
            { name: "East African Community", difficulty: "hard", keyTerms: ["EAC", "cooperation", "trade"] }
          ]
        }
      ]
    }
  }
};

const secondarySyllabus = {
  "Mathematics": {
    "1": {
      topics: [
        {
          topicName: "Numbers",
          subtopics: [
            { name: "Integers", difficulty: "medium", keyTerms: ["positive", "negative", "zero"] },
            { name: "Rational Numbers", difficulty: "hard", keyTerms: ["fractions", "decimals", "ratios"] }
          ]
        },
        {
          topicName: "Algebra",
          subtopics: [
            { name: "Basic Algebraic Expressions", difficulty: "medium", keyTerms: ["variables", "coefficients", "terms"] },
            { name: "Simple Equations", difficulty: "hard", keyTerms: ["equations", "solving", "unknown"] }
          ]
        }
      ]
    }
  },
  "Physics": {
    "1": {
      topics: [
        {
          topicName: "Measurement",
          subtopics: [
            { name: "Length and Time", difficulty: "easy", keyTerms: ["meter", "second", "measurement"] },
            { name: "Mass and Weight", difficulty: "medium", keyTerms: ["kilogram", "newton", "gravity"] }
          ]
        }
      ]
    }
  },
  "Chemistry": {
    "1": {
      topics: [
        {
          topicName: "Introduction to Chemistry",
          subtopics: [
            { name: "Matter and its Properties", difficulty: "medium", keyTerms: ["matter", "solid", "liquid", "gas"] },
            { name: "Elements and Compounds", difficulty: "hard", keyTerms: ["elements", "compounds", "mixtures"] }
          ]
        }
      ]
    }
  },
  "Biology": {
    "1": {
      topics: [
        {
          topicName: "Classification of Living Things",
          subtopics: [
            { name: "Characteristics of Living Things", difficulty: "easy", keyTerms: ["living", "non-living", "characteristics"] },
            { name: "Classification Systems", difficulty: "medium", keyTerms: ["kingdom", "phylum", "class"] }
          ]
        }
      ]
    }
  }
};

const advanceSyllabus = {
  "Advanced Mathematics": {
    "5": {
      topics: [
        {
          topicName: "Functions",
          subtopics: [
            { name: "Polynomial Functions", difficulty: "hard", keyTerms: ["polynomial", "degree", "coefficients"] },
            { name: "Exponential Functions", difficulty: "hard", keyTerms: ["exponential", "logarithm", "base"] }
          ]
        }
      ]
    }
  },
  "Physics": {
    "5": {
      topics: [
        {
          topicName: "Mechanics",
          subtopics: [
            { name: "Kinematics", difficulty: "hard", keyTerms: ["velocity", "acceleration", "displacement"] },
            { name: "Dynamics", difficulty: "hard", keyTerms: ["force", "momentum", "energy"] }
          ]
        }
      ]
    }
  }
};

// Question generation guidelines for Tanzania curriculum
const questionGenerationGuidelines = {
  primary: {
    languageLevel: "Simple, clear language appropriate for young learners",
    contextualReferences: "Use familiar Tanzanian contexts (local animals, foods, places)",
    culturalSensitivity: "Include diverse Tanzanian cultural references",
    assessmentFocus: "Basic understanding and application of concepts"
  },
  secondary: {
    languageLevel: "Intermediate academic language",
    contextualReferences: "Real-world applications relevant to Tanzanian society",
    culturalSensitivity: "Include examples from Tanzanian geography, economy, and society",
    assessmentFocus: "Analysis, synthesis, and problem-solving skills"
  },
  advance: {
    languageLevel: "Advanced academic and technical language",
    contextualReferences: "Complex real-world scenarios and applications",
    culturalSensitivity: "Global perspectives with Tanzanian context",
    assessmentFocus: "Critical thinking, evaluation, and advanced problem-solving"
  }
};

// Subject-specific question types mapping
const subjectQuestionTypes = {
  "Mathematics": ["multiple_choice", "fill_blank", "picture_based"],
  "Science and Technology": ["multiple_choice", "fill_blank", "picture_based"],
  "Physics": ["multiple_choice", "fill_blank", "picture_based"],
  "Chemistry": ["multiple_choice", "fill_blank", "picture_based"],
  "Biology": ["multiple_choice", "fill_blank", "picture_based"],
  "Geography": ["multiple_choice", "fill_blank", "picture_based"],
  "English": ["multiple_choice", "fill_blank"],
  "Kiswahili": ["multiple_choice", "fill_blank"],
  "History": ["multiple_choice", "fill_blank"],
  "Civics": ["multiple_choice", "fill_blank"]
};

module.exports = {
  primarySyllabus,
  secondarySyllabus,
  advanceSyllabus,
  questionGenerationGuidelines,
  subjectQuestionTypes,
};
