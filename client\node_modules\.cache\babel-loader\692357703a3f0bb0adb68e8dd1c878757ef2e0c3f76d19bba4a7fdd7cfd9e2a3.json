{"ast": null, "code": "const {\n  default: axiosInstance\n} = require(\".\");\n\n// add report\nexport const addReport = async payload => {\n  try {\n    const response = await axiosInstance.post(\"/api/reports/add-report\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// get all reports\nexport const getAllReports = async filters => {\n  try {\n    const response = await axiosInstance.post(\"/api/reports/get-all-reports\", filters);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// get all reports by user\nexport const getAllReportsByUser = async payload => {\n  try {\n    const response = await axiosInstance.post(\"/api/reports/get-all-reports-by-user\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// get all reports for ranking (legacy)\nexport const getAllReportsForRanking = async filters => {\n  try {\n    const response = await axiosInstance.get(\"/api/reports/get-all-reports-for-ranking\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Enhanced XP-based ranking endpoints\nexport const getEnhancedLeaderboard = async (level = 'all', limit = 1000) => {\n  try {\n    const response = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?level=${level}&limit=${limit}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const getXPLeaderboard = async (options = {}) => {\n  try {\n    const params = new URLSearchParams();\n    if (options.limit) params.append('limit', options.limit);\n    if (options.classFilter) params.append('classFilter', options.classFilter);\n    if (options.levelFilter) params.append('levelFilter', options.levelFilter);\n    if (options.seasonFilter) params.append('seasonFilter', options.seasonFilter);\n    if (options.includeInactive) params.append('includeInactive', options.includeInactive);\n    const response = await axiosInstance.get(`/api/quiz/xp-leaderboard?${params.toString()}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const getUserRanking = async (userId, context = 5) => {\n  try {\n    const response = await axiosInstance.get(`/api/quiz/user-ranking/${userId}?context=${context}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const getClassRankings = async (className, limit = 50) => {\n  try {\n    const response = await axiosInstance.get(`/api/quiz/class-rankings/${className}?limit=${limit}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// XP Dashboard endpoints\nexport const getXPDashboard = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/xp-dashboard/dashboard\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const getClassLeaderboard = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/xp-dashboard/class-leaderboard\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};", "map": {"version": 3, "names": ["default", "axiosInstance", "require", "addReport", "payload", "response", "post", "data", "error", "getAllReports", "filters", "getAllReportsByUser", "getAllReportsForRanking", "get", "getEnhancedLeaderboard", "level", "limit", "getXPLeaderboard", "options", "params", "URLSearchParams", "append", "classFilter", "levelFilter", "seasonFilter", "includeInactive", "toString", "getUserRanking", "userId", "context", "getClassRankings", "className", "getXPDashboard", "getClassLeaderboard"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/apicalls/reports.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\n// add report\r\nexport const addReport = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/add-report\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports\r\nexport const getAllReports = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports\", filters);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports by user\r\nexport const getAllReportsByUser = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports-by-user\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports for ranking (legacy)\r\nexport const getAllReportsForRanking = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/reports/get-all-reports-for-ranking\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// Enhanced XP-based ranking endpoints\r\nexport const getEnhancedLeaderboard = async (level = 'all', limit = 1000) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?level=${level}&limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getXPLeaderboard = async (options = {}) => {\r\n    try {\r\n        const params = new URLSearchParams();\r\n        if (options.limit) params.append('limit', options.limit);\r\n        if (options.classFilter) params.append('classFilter', options.classFilter);\r\n        if (options.levelFilter) params.append('levelFilter', options.levelFilter);\r\n        if (options.seasonFilter) params.append('seasonFilter', options.seasonFilter);\r\n        if (options.includeInactive) params.append('includeInactive', options.includeInactive);\r\n\r\n        const response = await axiosInstance.get(`/api/quiz/xp-leaderboard?${params.toString()}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getUserRanking = async (userId, context = 5) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/user-ranking/${userId}?context=${context}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassRankings = async (className, limit = 50) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/class-rankings/${className}?limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// XP Dashboard endpoints\r\nexport const getXPDashboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/dashboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassLeaderboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/class-leaderboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n"], "mappings": "AAAA,MAAM;EAAEA,OAAO,EAAEC;AAAc,CAAC,GAAGC,OAAO,CAAC,GAAG,CAAC;;AAE/C;AACA,OAAO,MAAMC,SAAS,GAAG,MAAOC,OAAO,IAAK;EACxC,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,yBAAyB,EAAEF,OAAO,CAAC;IAC7E,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;;AAED;AACA,OAAO,MAAME,aAAa,GAAG,MAAOC,OAAO,IAAK;EAC5C,IAAI;IACA,MAAML,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,8BAA8B,EAAEI,OAAO,CAAC;IAClF,OAAOL,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;;AAED;AACA,OAAO,MAAMI,mBAAmB,GAAG,MAAOP,OAAO,IAAK;EAClD,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,sCAAsC,EAAEF,OAAO,CAAC;IAC1F,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;;AAED;AACA,OAAO,MAAMK,uBAAuB,GAAG,MAAOF,OAAO,IAAK;EACtD,IAAI;IACA,MAAML,QAAQ,GAAG,MAAMJ,aAAa,CAACY,GAAG,CAAC,0CAA0C,CAAC;IACpF,OAAOR,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;;AAED;AACA,OAAO,MAAMO,sBAAsB,GAAG,MAAAA,CAAOC,KAAK,GAAG,KAAK,EAAEC,KAAK,GAAG,IAAI,KAAK;EACzE,IAAI;IACA,MAAMX,QAAQ,GAAG,MAAMJ,aAAa,CAACY,GAAG,CAAE,wCAAuCE,KAAM,UAASC,KAAM,EAAC,CAAC;IACxG,OAAOX,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;AAED,OAAO,MAAMU,gBAAgB,GAAG,MAAAA,CAAOC,OAAO,GAAG,CAAC,CAAC,KAAK;EACpD,IAAI;IACA,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIF,OAAO,CAACF,KAAK,EAAEG,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEH,OAAO,CAACF,KAAK,CAAC;IACxD,IAAIE,OAAO,CAACI,WAAW,EAAEH,MAAM,CAACE,MAAM,CAAC,aAAa,EAAEH,OAAO,CAACI,WAAW,CAAC;IAC1E,IAAIJ,OAAO,CAACK,WAAW,EAAEJ,MAAM,CAACE,MAAM,CAAC,aAAa,EAAEH,OAAO,CAACK,WAAW,CAAC;IAC1E,IAAIL,OAAO,CAACM,YAAY,EAAEL,MAAM,CAACE,MAAM,CAAC,cAAc,EAAEH,OAAO,CAACM,YAAY,CAAC;IAC7E,IAAIN,OAAO,CAACO,eAAe,EAAEN,MAAM,CAACE,MAAM,CAAC,iBAAiB,EAAEH,OAAO,CAACO,eAAe,CAAC;IAEtF,MAAMpB,QAAQ,GAAG,MAAMJ,aAAa,CAACY,GAAG,CAAE,4BAA2BM,MAAM,CAACO,QAAQ,CAAC,CAAE,EAAC,CAAC;IACzF,OAAOrB,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;AAED,OAAO,MAAMoB,cAAc,GAAG,MAAAA,CAAOC,MAAM,EAAEC,OAAO,GAAG,CAAC,KAAK;EACzD,IAAI;IACA,MAAMxB,QAAQ,GAAG,MAAMJ,aAAa,CAACY,GAAG,CAAE,0BAAyBe,MAAO,YAAWC,OAAQ,EAAC,CAAC;IAC/F,OAAOxB,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;AAED,OAAO,MAAMuB,gBAAgB,GAAG,MAAAA,CAAOC,SAAS,EAAEf,KAAK,GAAG,EAAE,KAAK;EAC7D,IAAI;IACA,MAAMX,QAAQ,GAAG,MAAMJ,aAAa,CAACY,GAAG,CAAE,4BAA2BkB,SAAU,UAASf,KAAM,EAAC,CAAC;IAChG,OAAOX,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;;AAED;AACA,OAAO,MAAMyB,cAAc,GAAG,MAAAA,CAAA,KAAY;EACtC,IAAI;IACA,MAAM3B,QAAQ,GAAG,MAAMJ,aAAa,CAACY,GAAG,CAAC,6BAA6B,CAAC;IACvE,OAAOR,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;AAED,OAAO,MAAM0B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;EAC3C,IAAI;IACA,MAAM5B,QAAQ,GAAG,MAAMJ,aAAa,CAACY,GAAG,CAAC,qCAAqC,CAAC;IAC/E,OAAOR,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}