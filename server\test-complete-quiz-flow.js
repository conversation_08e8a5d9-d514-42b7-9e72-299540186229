const mongoose = require('mongoose');
const AIQuestionGenerationService = require('./services/aiQuestionGenerationService');
const { AIQuestionGeneration } = require('./models/aiQuestionGenerationModel');
const Question = require('./models/questionModel');
const Exam = require('./models/examModel');
const User = require('./models/userModel');
const Report = require('./models/reportModel');
require('dotenv').config();

async function testCompleteQuizFlow() {
  try {
    console.log('🎯 Testing Complete Quiz Flow with AI-Generated Questions...\n');
    
    // Connect to database
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    // 1. Generate AI questions
    console.log('1️⃣ Generating AI questions...');
    const service = new AIQuestionGenerationService();
    const testUserId = new mongoose.Types.ObjectId();
    
    const testParams = {
      questionTypes: ['multiple_choice', 'fill_blank'],
      subjects: ['Mathematics'],
      level: 'primary',
      class: '3',
      difficultyLevels: ['easy', 'medium'],
      syllabusTopics: ['Numbers', 'Addition'],
      totalQuestions: 3,
      questionDistribution: {
        multiple_choice: 2,
        fill_blank: 1,
        picture_based: 0
      }
    };
    
    const result = await service.generateQuestions(testParams, testUserId, null);
    
    if (!result.success) {
      console.log('❌ Question generation failed:', result.error);
      return;
    }
    
    console.log(`✅ Generated ${result.questions.length} questions`);
    
    // 2. Create exam and approve questions
    console.log('\n2️⃣ Creating exam and approving questions...');
    
    const testExam = new Exam({
      name: 'AI Quiz Test Exam',
      duration: 30,
      category: 'Mathematics',
      level: 'primary',
      class: '3',
      totalMarks: 3,
      passingMarks: 2,
      questions: []
    });
    
    const savedExam = await testExam.save();
    console.log(`✅ Created exam: ${savedExam.name}`);
    
    // Get the generation record
    const generation = await AIQuestionGeneration.findById(result.generationId);
    generation.examId = savedExam._id;
    await generation.save();
    
    // Approve all questions
    const approvedQuestions = [];
    for (let i = 0; i < generation.generatedQuestions.length; i++) {
      const generatedQuestion = generation.generatedQuestions[i];
      
      const newQuestion = new Question({
        ...generatedQuestion.generatedContent,
        exam: savedExam._id,
      });
      
      const savedQuestion = await newQuestion.save();
      approvedQuestions.push(savedQuestion);
      
      savedExam.questions.push(savedQuestion._id);
      
      generation.generatedQuestions[i].approved = true;
      generation.generatedQuestions[i].questionId = savedQuestion._id;
    }
    
    await savedExam.save();
    await generation.save();
    
    console.log(`✅ Approved and added ${approvedQuestions.length} questions to exam`);
    
    // 3. Create test user
    console.log('\n3️⃣ Creating test user...');
    const testUser = new User({
      name: 'Test Student',
      email: '<EMAIL>',
      password: 'hashedpassword',
      isAdmin: false,
      level: 'primary',
      class: '3'
    });
    
    const savedUser = await testUser.save();
    console.log(`✅ Created test user: ${savedUser.name}`);
    
    // 4. Simulate quiz taking and marking
    console.log('\n4️⃣ Simulating quiz taking and marking...');
    
    // Fetch exam with questions
    const examWithQuestions = await Exam.findById(savedExam._id).populate('questions');
    console.log(`📝 Exam has ${examWithQuestions.questions.length} questions`);
    
    // Simulate user answers
    const userAnswers = {};
    const correctAnswers = [];
    const wrongAnswers = [];
    
    examWithQuestions.questions.forEach((question, index) => {
      console.log(`\nQuestion ${index + 1}: ${question.name}`);
      console.log(`Type: ${question.answerType}`);
      console.log(`AI Generated: ${question.isAIGenerated}`);
      
      if (question.answerType === 'Options') {
        console.log(`Options:`, question.options);
        console.log(`Correct Option: ${question.correctOption}`);
        
        // Simulate user selecting correct answer for first question, wrong for others
        const userAnswer = index === 0 ? question.correctOption : 
                          Object.keys(question.options).find(key => key !== question.correctOption);
        
        userAnswers[index] = userAnswer;
        console.log(`User Selected: ${userAnswer}`);
        
        // Apply marking logic
        const isCorrect = question.correctOption === userAnswer;
        console.log(`Result: ${isCorrect ? 'CORRECT ✅' : 'WRONG ❌'}`);
        
        if (isCorrect) {
          correctAnswers.push({ ...question.toObject(), userAnswer });
        } else {
          wrongAnswers.push({ ...question.toObject(), userAnswer });
        }
        
      } else if (question.answerType === 'Fill in the Blank') {
        console.log(`Correct Answer: ${question.correctAnswer}`);
        
        // Simulate user answer (correct for first, wrong for others)
        const userAnswer = index === 0 ? question.correctAnswer : 'wrong answer';
        userAnswers[index] = userAnswer;
        console.log(`User Answer: ${userAnswer}`);
        
        // For fill-in-blank, we'd normally use AI to check, but for testing we'll do simple comparison
        const isCorrect = question.correctAnswer.toLowerCase() === userAnswer.toLowerCase();
        console.log(`Result: ${isCorrect ? 'CORRECT ✅' : 'WRONG ❌'}`);
        
        if (isCorrect) {
          correctAnswers.push({ ...question.toObject(), userAnswer });
        } else {
          wrongAnswers.push({ ...question.toObject(), userAnswer });
        }
      }
    });
    
    // 5. Calculate final result
    console.log('\n5️⃣ Calculating final result...');
    const totalQuestions = examWithQuestions.questions.length;
    const correctCount = correctAnswers.length;
    const wrongCount = wrongAnswers.length;
    const verdict = correctCount >= examWithQuestions.passingMarks ? 'Pass' : 'Fail';
    
    console.log(`Total Questions: ${totalQuestions}`);
    console.log(`Correct Answers: ${correctCount}`);
    console.log(`Wrong Answers: ${wrongCount}`);
    console.log(`Passing Marks: ${examWithQuestions.passingMarks}`);
    console.log(`Final Verdict: ${verdict} ${verdict === 'Pass' ? '🎉' : '😞'}`);
    
    // 6. Save report
    console.log('\n6️⃣ Saving quiz report...');
    const quizResult = {
      correctAnswers,
      wrongAnswers,
      verdict
    };
    
    const report = new Report({
      exam: savedExam._id,
      user: savedUser._id,
      result: quizResult
    });
    
    const savedReport = await report.save();
    console.log(`✅ Report saved: ${savedReport._id}`);
    
    // 7. Verify AI questions worked correctly
    console.log('\n7️⃣ Verification Summary...');
    const aiQuestions = examWithQuestions.questions.filter(q => q.isAIGenerated);
    console.log(`✅ AI-generated questions in exam: ${aiQuestions.length}/${totalQuestions}`);
    console.log(`✅ Quiz marking logic worked correctly`);
    console.log(`✅ Report generation successful`);
    console.log(`✅ Complete flow test: PASSED 🎯`);
    
  } catch (error) {
    console.error('❌ Test error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run the complete flow test
testCompleteQuizFlow();
