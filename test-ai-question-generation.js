const AIQuestionGenerationService = require('./services/aiQuestionGenerationService');
const mongoose = require('mongoose');
require('dotenv').config();

async function testFixedQuestionGeneration() {
  try {
    console.log('🔍 Testing Fixed AI Question Generation...');
    
    // Connect to database
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    const service = new AIQuestionGenerationService();
    
    // Test the parseJSONResponse method
    console.log('1️⃣ Testing JSON response parsing...');
    
    const testResponses = [
      '{"question": "Test question", "options": {"A": "Option A"}}',
      '```json\n{"question": "Test question", "options": {"A": "Option A"}}\n```',
      '```\n{"question": "Test question", "options": {"A": "Option A"}}\n```'
    ];
    
    for (let i = 0; i < testResponses.length; i++) {
      try {
        const parsed = service.parseJSONResponse(testResponses[i]);
        console.log(`✅ Test ${i + 1} passed:`, parsed.question);
      } catch (error) {
        console.log(`❌ Test ${i + 1} failed:`, error.message);
      }
    }
    
    // Test actual question generation
    console.log('2️⃣ Testing actual question generation...');
    
    const generationParams = {
      questionTypes: ['multiple_choice'],
      subjects: ['Mathematics'],
      level: 'primary',
      class: '3',
      difficultyLevels: ['medium'],
      syllabusTopics: ['Basic arithmetic', 'Addition'],
      totalQuestions: 1,
      questionDistribution: {
        multiple_choice: 1,
        fill_blank: 0,
        picture_based: 0
      }
    };
    
    const result = await service.generateQuestions(
      generationParams,
      'test-user-id',
      null // No exam ID
    );
    
    if (result.success) {
      console.log('✅ Question generation successful!');
      console.log('Generated questions:', result.questions.length);
      console.log('Generation time:', result.generationTime, 'ms');
      if (result.questions.length > 0) {
        console.log('Sample question:', result.questions[0].name);
        console.log('Options:', result.questions[0].options);
        console.log('Correct answer:', result.questions[0].correctOption);
      }
    } else {
      console.log('❌ Question generation failed:', result.error);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await mongoose.connection.close();
  }
}

testFixedQuestionGeneration();
