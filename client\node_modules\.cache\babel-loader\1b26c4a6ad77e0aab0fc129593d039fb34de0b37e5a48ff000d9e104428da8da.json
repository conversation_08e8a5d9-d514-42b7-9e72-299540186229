{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbTarget, TbBrain, TbSearch, TbFilter, TbRefresh, TbMedal, TbBolt, TbRocket, TbDiamond, TbHeart, TbEye, TbTrendingUp, TbAward, TbShield } from 'react-icons/tb';\nimport { getAllReportsForRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const user = userState.user || null;\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const headerRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Performance tiers with amazing visual themes\n  const performanceTiers = {\n    legendary: {\n      min: 10000,\n      color: 'from-purple-600 via-pink-600 to-red-600',\n      glow: 'shadow-purple-500/50',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery'\n    },\n    diamond: {\n      min: 7500,\n      color: 'from-cyan-400 via-blue-500 to-indigo-600',\n      glow: 'shadow-cyan-500/50',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance'\n    },\n    platinum: {\n      min: 5000,\n      color: 'from-gray-300 via-gray-400 to-gray-600',\n      glow: 'shadow-gray-500/50',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding'\n    },\n    gold: {\n      min: 2500,\n      color: 'from-yellow-400 via-yellow-500 to-yellow-600',\n      glow: 'shadow-yellow-500/50',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent'\n    },\n    silver: {\n      min: 1000,\n      color: 'from-gray-400 via-gray-500 to-gray-600',\n      glow: 'shadow-gray-500/50',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress'\n    },\n    bronze: {\n      min: 0,\n      color: 'from-orange-400 via-orange-500 to-orange-600',\n      glow: 'shadow-orange-500/50',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = xp => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return {\n        tier,\n        ...config\n      };\n    }\n    return {\n      tier: 'bronze',\n      ...performanceTiers.bronze\n    };\n  };\n\n  // Fetch ranking data with multiple fallbacks\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching amazing ranking data...');\n      let response;\n\n      // Try ranking reports first\n      try {\n        response = await getAllReportsForRanking();\n        console.log('✨ Ranking reports response:', response);\n      } catch (error) {\n        console.log('⚡ Trying all users...');\n        response = await getAllUsers();\n      }\n      if (!response || !response.success) {\n        console.log('🔄 Falling back to all users...');\n        response = await getAllUsers();\n      }\n      if (response && response.success && response.data) {\n        const transformedData = response.data.map((item, index) => {\n          // Handle both user data and report data structures\n          const userData = item.user || item;\n          const reportData = item.reports || [];\n\n          // Calculate stats from reports if available\n          const totalQuizzes = reportData.length || userData.totalQuizzesTaken || 0;\n          const totalScore = reportData.reduce((sum, report) => sum + (report.score || 0), 0);\n          const averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : 0;\n          const totalXP = userData.totalXP || userData.xp || totalScore || Math.floor(averageScore * totalQuizzes / 10) || 0;\n          return {\n            _id: userData._id || userData.userId || userData.id,\n            name: userData.name || userData.userName || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || userData.className || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || userData.avatar || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: userData.currentStreak || userData.streak || 0,\n            bestStreak: userData.bestStreak || userData.maxStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || userData.normalizedSubscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(totalXP)\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank\n        const userRank = user ? transformedData.findIndex(item => item._id === user._id) : -1;\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'champions');\n      } else {\n        // Fallback demo data to showcase the amazing design\n        console.log('🎭 Loading demo data to showcase the amazing design...');\n        const demoData = [{\n          _id: 'demo1',\n          name: 'Alex Champion',\n          email: '<EMAIL>',\n          class: '7',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 15000,\n          totalQuizzesTaken: 45,\n          averageScore: 92,\n          currentStreak: 12,\n          bestStreak: 18,\n          subscriptionStatus: 'premium',\n          rank: 1,\n          tier: getUserTier(15000)\n        }, {\n          _id: 'demo2',\n          name: 'Sarah Excellence',\n          email: '<EMAIL>',\n          class: '6',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 12500,\n          totalQuizzesTaken: 38,\n          averageScore: 88,\n          currentStreak: 8,\n          bestStreak: 15,\n          subscriptionStatus: 'premium',\n          rank: 2,\n          tier: getUserTier(12500)\n        }, {\n          _id: 'demo3',\n          name: 'Mike Achiever',\n          email: '<EMAIL>',\n          class: '7',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 9800,\n          totalQuizzesTaken: 32,\n          averageScore: 85,\n          currentStreak: 5,\n          bestStreak: 12,\n          subscriptionStatus: 'free',\n          rank: 3,\n          tier: getUserTier(9800)\n        }, {\n          _id: 'demo4',\n          name: 'Emma Rising',\n          email: '<EMAIL>',\n          class: '5',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 7200,\n          totalQuizzesTaken: 28,\n          averageScore: 82,\n          currentStreak: 3,\n          bestStreak: 9,\n          subscriptionStatus: 'free',\n          rank: 4,\n          tier: getUserTier(7200)\n        }, {\n          _id: 'demo5',\n          name: 'David Progress',\n          email: '<EMAIL>',\n          class: '6',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 5500,\n          totalQuizzesTaken: 22,\n          averageScore: 78,\n          currentStreak: 2,\n          bestStreak: 7,\n          subscriptionStatus: 'free',\n          rank: 5,\n          tier: getUserTier(5500)\n        }];\n        setRankingData(demoData);\n        setCurrentUserRank(null); // No current user in demo data\n        message.success('Welcome to the Hall of Champions! 🏆');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n    return () => clearInterval(animationTimer);\n  }, []);\n\n  // Filter and search functionality\n  const filteredData = rankingData.filter(rankingUser => {\n    const matchesSearch = rankingUser.name.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterType === 'all' || filterType === 'premium' && rankingUser.subscriptionStatus === 'premium' || filterType === 'free' && rankingUser.subscriptionStatus === 'free' || filterType === 'class' && user && rankingUser.class === user.class;\n    return matchesSearch && matchesFilter;\n  });\n\n  // Get top performers for special display\n  const topPerformers = filteredData.slice(0, 3);\n  const otherPerformers = filteredData.slice(3);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n      children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n        animate: {\n          y: [0, -100, 0],\n          x: [0, Math.random() * 100 - 50, 0],\n          opacity: [0.2, 0.8, 0.2]\n        },\n        transition: {\n          duration: 3 + Math.random() * 2,\n          repeat: Infinity,\n          delay: Math.random() * 2\n        },\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        ref: headerRef,\n        initial: {\n          opacity: 0,\n          y: -50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 1,\n          ease: \"easeOut\"\n        },\n        className: \"relative overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-black/20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10 px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-7xl mx-auto text-center\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  scale: [1, 1.02, 1],\n                  rotateY: [0, 5, 0]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                className: \"mb-8\",\n                children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-5xl sm:text-6xl lg:text-8xl font-black text-white mb-4 tracking-tight\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    animate: {\n                      backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity,\n                      ease: \"linear\"\n                    },\n                    className: \"bg-gradient-to-r from-yellow-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent bg-300% animate-gradient-x\",\n                    style: {\n                      backgroundSize: '300% 300%',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent'\n                    },\n                    children: \"HALL OF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    animate: {\n                      textShadow: ['0 0 20px rgba(255,255,255,0.5)', '0 0 40px rgba(255,255,255,0.8)', '0 0 20px rgba(255,255,255,0.5)']\n                    },\n                    transition: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    className: \"text-white\",\n                    children: \"CHAMPIONS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.5,\n                  duration: 0.8\n                },\n                className: \"text-xl sm:text-2xl lg:text-3xl text-white/90 font-medium mb-8 max-w-4xl mx-auto leading-relaxed\",\n                children: \"Where legends are born and greatness is celebrated\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  delay: 1,\n                  duration: 0.8\n                },\n                className: \"bg-white/10 backdrop-blur-lg rounded-2xl p-6 max-w-2xl mx-auto mb-8 border border-white/20\",\n                children: /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 10\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"text-lg sm:text-xl text-white font-medium\",\n                  children: motivationalQuote\n                }, motivationalQuote, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 30\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 1.2,\n                  duration: 0.8\n                },\n                className: \"grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto\",\n                children: [{\n                  icon: TbTrophy,\n                  label: 'Total Champions',\n                  value: rankingData.length,\n                  color: 'text-yellow-400'\n                }, {\n                  icon: TbFlame,\n                  label: 'Active Streaks',\n                  value: rankingData.filter(u => u.currentStreak > 0).length,\n                  color: 'text-orange-400'\n                }, {\n                  icon: TbBrain,\n                  label: 'Quizzes Taken',\n                  value: rankingData.reduce((sum, u) => sum + u.totalQuizzesTaken, 0),\n                  color: 'text-blue-400'\n                }, {\n                  icon: TbBolt,\n                  label: 'Total XP',\n                  value: rankingData.reduce((sum, u) => sum + u.totalXP, 0).toLocaleString(),\n                  color: 'text-purple-400'\n                }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  whileHover: {\n                    scale: 1.05,\n                    y: -5\n                  },\n                  className: \"bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/20 text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(stat.icon, {\n                    className: `w-8 h-8 ${stat.color} mx-auto mb-2`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl sm:text-3xl font-bold text-white mb-1\",\n                    children: stat.value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-white/70\",\n                    children: stat.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 23\n                  }, this)]\n                }, stat.label, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 1.5,\n          duration: 0.8\n        },\n        className: \"px-4 sm:px-6 lg:px-8 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col lg:flex-row gap-6 items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative flex-1 max-w-md\",\n                children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n                  className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search champions...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  className: \"w-full pl-12 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-3\",\n                children: [{\n                  key: 'all',\n                  label: 'All Champions',\n                  icon: TbTrophy\n                }, {\n                  key: 'premium',\n                  label: 'Premium',\n                  icon: TbCrown\n                }, {\n                  key: 'free',\n                  label: 'Free',\n                  icon: TbStar\n                }, {\n                  key: 'class',\n                  label: 'My Class',\n                  icon: TbTarget\n                }].map(filter => /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setFilterType(filter.key),\n                  className: `flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${filterType === filter.key ? 'bg-purple-600 text-white shadow-lg shadow-purple-500/25' : 'bg-white/10 text-white/80 hover:bg-white/20'}`,\n                  children: [/*#__PURE__*/_jsxDEV(filter.icon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"hidden sm:inline\",\n                    children: filter.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 23\n                  }, this)]\n                }, filter.key, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05,\n                  rotate: 180\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: fetchRankingData,\n                disabled: loading,\n                className: \"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50\",\n                children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                  className: `w-5 h-5 ${loading ? 'animate-spin' : ''}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Refresh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"flex flex-col items-center justify-center py-20\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 11\n      }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3,\n          duration: 0.8\n        },\n        className: \"px-4 sm:px-6 lg:px-8 pb-20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              delay: 0.5,\n              duration: 0.8\n            },\n            className: \"mb-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl sm:text-4xl font-bold text-white text-center mb-8\",\n              children: \"\\uD83C\\uDFC6 Champions Podium \\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\",\n              children: topPerformers.map((champion, index) => {\n                const position = index + 1;\n                const isCurrentUser = user && champion._id === user._id;\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.7 + index * 0.2,\n                    duration: 0.8\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative ${position === 1 ? 'md:order-2 md:scale-110' : position === 2 ? 'md:order-1' : 'md:order-3'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-2xl ${champion.tier.glow} shadow-2xl`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-slate-900/90 backdrop-blur-lg rounded-2xl p-6 text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center text-white font-black text-xl shadow-lg`,\n                        children: position\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 594,\n                        columnNumber: 31\n                      }, this), position === 1 && /*#__PURE__*/_jsxDEV(motion.div, {\n                        animate: {\n                          rotate: [0, 10, -10, 0]\n                        },\n                        transition: {\n                          duration: 2,\n                          repeat: Infinity\n                        },\n                        className: \"absolute -top-8 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                          className: \"w-8 h-8 text-yellow-400\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 605,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-4 ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-20 h-20 rounded-full overflow-hidden mx-auto bg-gradient-to-br from-purple-500 to-pink-500 p-1\",\n                          children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: champion.profilePicture,\n                            alt: champion.name,\n                            className: \"w-full h-full object-cover rounded-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 613,\n                            columnNumber: 37\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-2xl\",\n                            children: champion.name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 619,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 611,\n                          columnNumber: 33\n                        }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-2 -right-2 bg-yellow-400 text-black rounded-full p-1\",\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-4 h-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 626,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 625,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 610,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-xl font-bold text-white mb-2\",\n                        children: champion.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 632,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r ${champion.tier.color} rounded-full text-white text-sm font-medium mb-3`,\n                        children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 634,\n                          columnNumber: 33\n                        }, this), champion.tier.title]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 633,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-white/70\",\n                            children: \"XP:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 641,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-white font-bold\",\n                            children: champion.totalXP.toLocaleString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 642,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 640,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-white/70\",\n                            children: \"Quizzes:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 645,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-white font-bold\",\n                            children: champion.totalQuizzesTaken\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 646,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 644,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-white/70\",\n                            children: \"Streak:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 649,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-white font-bold flex items-center gap-1\",\n                            children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                              className: \"w-4 h-4 text-orange-400\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 651,\n                              columnNumber: 37\n                            }, this), champion.currentStreak]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 650,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 648,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 639,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 591,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 27\n                  }, this)\n                }, champion._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 17\n          }, this), otherPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 1,\n              duration: 0.8\n            },\n            className: \"mt-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl sm:text-3xl font-bold text-white text-center mb-8\",\n              children: \"\\u26A1 Rising Champions \\u26A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: otherPerformers.map((champion, index) => {\n                const actualRank = index + 4; // Since top 3 are shown separately\n                const isCurrentUser = user && champion._id === user._id;\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 1.2 + index * 0.1,\n                    duration: 0.6\n                  },\n                  whileHover: {\n                    scale: 1.02,\n                    x: 10\n                  },\n                  className: `relative ${isCurrentUser ? 'ring-2 ring-yellow-400' : ''}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-slate-900/90 backdrop-blur-lg rounded-xl p-4 flex items-center gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `flex-shrink-0 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg`,\n                        children: actualRank\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 695,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-14 h-14 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 to-pink-500 p-1\",\n                          children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: champion.profilePicture,\n                            alt: champion.name,\n                            className: \"w-full h-full object-cover rounded-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 703,\n                            columnNumber: 37\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-lg\",\n                            children: champion.name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 709,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 701,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 700,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-2 mb-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-lg font-bold text-white truncate\",\n                            children: champion.name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 719,\n                            columnNumber: 35\n                          }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"bg-yellow-400 text-black px-2 py-1 rounded-full text-xs font-bold\",\n                            children: \"YOU\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 721,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 718,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `inline-flex items-center gap-1 px-2 py-1 bg-gradient-to-r ${champion.tier.color} rounded-full text-white text-xs font-medium`,\n                          children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                            className: \"w-3 h-3\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 727,\n                            columnNumber: 35\n                          }, this), champion.tier.title]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 726,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 717,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0 text-right\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-white font-bold text-lg mb-1\",\n                          children: [champion.totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 734,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-4 text-sm text-white/70\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"flex items-center gap-1\",\n                            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                              className: \"w-4 h-4\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 739,\n                              columnNumber: 37\n                            }, this), champion.totalQuizzesTaken]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 738,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"flex items-center gap-1\",\n                            children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                              className: \"w-4 h-4 text-orange-400\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 743,\n                              columnNumber: 37\n                            }, this), champion.currentStreak]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 742,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 737,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 733,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 692,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 691,\n                    columnNumber: 27\n                  }, this)\n                }, champion._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 17\n          }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              delay: 1.5,\n              duration: 0.8\n            },\n            className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold text-white mb-2\",\n                children: \"Your Current Position\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-6xl font-black text-yellow-400 mb-2\",\n                children: [\"#\", currentUserRank]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-white/80 text-lg\",\n                children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 2,\n              duration: 0.8\n            },\n            className: \"mt-16 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  scale: [1, 1.05, 1]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity\n                },\n                children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                  className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 787,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-3xl font-bold text-white mb-4\",\n                children: \"Ready to Rise Higher?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl text-white/80 mb-6 max-w-2xl mx-auto\",\n                children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                onClick: () => window.location.href = '/user/quiz',\n                children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 782,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 15\n          }, this), filteredData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            className: \"text-center py-20\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-white mb-4\",\n              children: \"No Champions Found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-white/70 text-lg\",\n              children: \"Try adjusting your search or filter criteria to find more champions!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 312,\n    columnNumber: 5\n  }, this);\n};\n_s(AmazingRankingPage, \"w6ncjJ4K0HgVPDyL7WwM8iBVJKM=\", false, function () {\n  return [useSelector];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbTarget", "TbBrain", "TbSearch", "Tb<PERSON><PERSON>er", "TbRefresh", "TbMedal", "TbBolt", "TbRocket", "TbDiamond", "TbHeart", "TbEye", "TbTrendingUp", "TbAward", "TbShield", "getAllReportsForRanking", "getAllUsers", "jsxDEV", "_jsxDEV", "AmazingRankingPage", "_s", "userState", "state", "users", "user", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "searchTerm", "setSearchTerm", "filterType", "setFilterType", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "headerRef", "motivationalQuotes", "performanceTiers", "legendary", "min", "color", "glow", "icon", "title", "description", "diamond", "platinum", "gold", "silver", "bronze", "getUserTier", "xp", "tier", "config", "Object", "entries", "fetchRankingData", "console", "log", "response", "error", "success", "data", "transformedData", "map", "item", "index", "userData", "reportData", "reports", "totalQuizzes", "length", "totalQuizzesTaken", "totalScore", "reduce", "sum", "report", "score", "averageScore", "Math", "round", "totalXP", "floor", "_id", "userId", "id", "name", "userName", "email", "class", "className", "level", "profilePicture", "avatar", "currentStreak", "streak", "bestStreak", "maxStreak", "subscriptionStatus", "normalizedSubscriptionStatus", "rank", "sort", "a", "b", "for<PERSON>ach", "userRank", "findIndex", "demoData", "randomQuote", "random", "animationTimer", "setInterval", "prev", "clearInterval", "filteredData", "filter", "rankingUser", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "topPerformers", "slice", "otherPerformers", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "_", "i", "div", "animate", "y", "x", "opacity", "transition", "duration", "repeat", "Infinity", "delay", "style", "left", "top", "ref", "initial", "ease", "scale", "rotateY", "span", "backgroundPosition", "backgroundSize", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "p", "label", "value", "u", "toLocaleString", "stat", "whileHover", "type", "placeholder", "onChange", "e", "target", "key", "button", "whileTap", "onClick", "rotate", "disabled", "champion", "position", "isCurrentUser", "src", "alt", "char<PERSON>t", "toUpperCase", "actualRank", "window", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbTarget,\n  TbBrain,\n  TbSearch,\n  TbFilter,\n  TbRefresh,\n  TbMedal,\n  TbBolt,\n  TbRocket,\n  TbDiamond,\n  TbHeart,\n  TbEye,\n  TbTrendingUp,\n  TbAward,\n  TbShield\n} from 'react-icons/tb';\nimport { getAllReportsForRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const user = userState.user || null;\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const headerRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Performance tiers with amazing visual themes\n  const performanceTiers = {\n    legendary: { \n      min: 10000, \n      color: 'from-purple-600 via-pink-600 to-red-600',\n      glow: 'shadow-purple-500/50',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery'\n    },\n    diamond: { \n      min: 7500, \n      color: 'from-cyan-400 via-blue-500 to-indigo-600',\n      glow: 'shadow-cyan-500/50',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance'\n    },\n    platinum: { \n      min: 5000, \n      color: 'from-gray-300 via-gray-400 to-gray-600',\n      glow: 'shadow-gray-500/50',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding'\n    },\n    gold: { \n      min: 2500, \n      color: 'from-yellow-400 via-yellow-500 to-yellow-600',\n      glow: 'shadow-yellow-500/50',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent'\n    },\n    silver: { \n      min: 1000, \n      color: 'from-gray-400 via-gray-500 to-gray-600',\n      glow: 'shadow-gray-500/50',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress'\n    },\n    bronze: { \n      min: 0, \n      color: 'from-orange-400 via-orange-500 to-orange-600',\n      glow: 'shadow-orange-500/50',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = (xp) => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return { tier, ...config };\n    }\n    return { tier: 'bronze', ...performanceTiers.bronze };\n  };\n\n  // Fetch ranking data with multiple fallbacks\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching amazing ranking data...');\n\n      let response;\n\n      // Try ranking reports first\n      try {\n        response = await getAllReportsForRanking();\n        console.log('✨ Ranking reports response:', response);\n      } catch (error) {\n        console.log('⚡ Trying all users...');\n        response = await getAllUsers();\n      }\n\n      if (!response || !response.success) {\n        console.log('🔄 Falling back to all users...');\n        response = await getAllUsers();\n      }\n\n      if (response && response.success && response.data) {\n        const transformedData = response.data.map((item, index) => {\n          // Handle both user data and report data structures\n          const userData = item.user || item;\n          const reportData = item.reports || [];\n\n          // Calculate stats from reports if available\n          const totalQuizzes = reportData.length || userData.totalQuizzesTaken || 0;\n          const totalScore = reportData.reduce((sum, report) => sum + (report.score || 0), 0);\n          const averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : 0;\n          const totalXP = userData.totalXP || userData.xp || totalScore || Math.floor(averageScore * totalQuizzes / 10) || 0;\n\n          return {\n            _id: userData._id || userData.userId || userData.id,\n            name: userData.name || userData.userName || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || userData.className || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || userData.avatar || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: userData.currentStreak || userData.streak || 0,\n            bestStreak: userData.bestStreak || userData.maxStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || userData.normalizedSubscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(totalXP)\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank\n        const userRank = user ? transformedData.findIndex(item => item._id === user._id) : -1;\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'champions');\n      } else {\n        // Fallback demo data to showcase the amazing design\n        console.log('🎭 Loading demo data to showcase the amazing design...');\n        const demoData = [\n          {\n            _id: 'demo1',\n            name: 'Alex Champion',\n            email: '<EMAIL>',\n            class: '7',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 15000,\n            totalQuizzesTaken: 45,\n            averageScore: 92,\n            currentStreak: 12,\n            bestStreak: 18,\n            subscriptionStatus: 'premium',\n            rank: 1,\n            tier: getUserTier(15000)\n          },\n          {\n            _id: 'demo2',\n            name: 'Sarah Excellence',\n            email: '<EMAIL>',\n            class: '6',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 12500,\n            totalQuizzesTaken: 38,\n            averageScore: 88,\n            currentStreak: 8,\n            bestStreak: 15,\n            subscriptionStatus: 'premium',\n            rank: 2,\n            tier: getUserTier(12500)\n          },\n          {\n            _id: 'demo3',\n            name: 'Mike Achiever',\n            email: '<EMAIL>',\n            class: '7',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 9800,\n            totalQuizzesTaken: 32,\n            averageScore: 85,\n            currentStreak: 5,\n            bestStreak: 12,\n            subscriptionStatus: 'free',\n            rank: 3,\n            tier: getUserTier(9800)\n          },\n          {\n            _id: 'demo4',\n            name: 'Emma Rising',\n            email: '<EMAIL>',\n            class: '5',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 7200,\n            totalQuizzesTaken: 28,\n            averageScore: 82,\n            currentStreak: 3,\n            bestStreak: 9,\n            subscriptionStatus: 'free',\n            rank: 4,\n            tier: getUserTier(7200)\n          },\n          {\n            _id: 'demo5',\n            name: 'David Progress',\n            email: '<EMAIL>',\n            class: '6',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 5500,\n            totalQuizzesTaken: 22,\n            averageScore: 78,\n            currentStreak: 2,\n            bestStreak: 7,\n            subscriptionStatus: 'free',\n            rank: 5,\n            tier: getUserTier(5500)\n          }\n        ];\n\n        setRankingData(demoData);\n        setCurrentUserRank(null); // No current user in demo data\n        message.success('Welcome to the Hall of Champions! 🏆');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n    \n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    return () => clearInterval(animationTimer);\n  }, []);\n\n  // Filter and search functionality\n  const filteredData = rankingData.filter(rankingUser => {\n    const matchesSearch = rankingUser.name.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterType === 'all' ||\n                         (filterType === 'premium' && rankingUser.subscriptionStatus === 'premium') ||\n                         (filterType === 'free' && rankingUser.subscriptionStatus === 'free') ||\n                         (filterType === 'class' && user && rankingUser.class === user.class);\n    return matchesSearch && matchesFilter;\n  });\n\n  // Get top performers for special display\n  const topPerformers = filteredData.slice(0, 3);\n  const otherPerformers = filteredData.slice(3);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* SPECTACULAR HEADER */}\n        <motion.div\n          ref={headerRef}\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden\"\n        >\n          {/* Header Background with Gradient */}\n          <div className=\"bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 relative\">\n            <div className=\"absolute inset-0 bg-black/20\"></div>\n            \n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n                \n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-8\"\n                >\n                  <h1 className=\"text-5xl sm:text-6xl lg:text-8xl font-black text-white mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 3,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent bg-300% animate-gradient-x\"\n                      style={{\n                        backgroundSize: '300% 300%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 20px rgba(255,255,255,0.5)',\n                          '0 0 40px rgba(255,255,255,0.8)',\n                          '0 0 20px rgba(255,255,255,0.5)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      className=\"text-white\"\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-xl sm:text-2xl lg:text-3xl text-white/90 font-medium mb-8 max-w-4xl mx-auto leading-relaxed\"\n                >\n                  Where legends are born and greatness is celebrated\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-6 max-w-2xl mx-auto mb-8 border border-white/20\"\n                >\n                  <motion.p\n                    key={motivationalQuote}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"text-lg sm:text-xl text-white font-medium\"\n                  >\n                    {motivationalQuote}\n                  </motion.p>\n                </motion.div>\n\n                {/* Stats Overview */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.2, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    { icon: TbTrophy, label: 'Total Champions', value: rankingData.length, color: 'text-yellow-400' },\n                    { icon: TbFlame, label: 'Active Streaks', value: rankingData.filter(u => u.currentStreak > 0).length, color: 'text-orange-400' },\n                    { icon: TbBrain, label: 'Quizzes Taken', value: rankingData.reduce((sum, u) => sum + u.totalQuizzesTaken, 0), color: 'text-blue-400' },\n                    { icon: TbBolt, label: 'Total XP', value: rankingData.reduce((sum, u) => sum + u.totalXP, 0).toLocaleString(), color: 'text-purple-400' }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={stat.label}\n                      whileHover={{ scale: 1.05, y: -5 }}\n                      className=\"bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/20 text-center\"\n                    >\n                      <stat.icon className={`w-8 h-8 ${stat.color} mx-auto mb-2`} />\n                      <div className=\"text-2xl sm:text-3xl font-bold text-white mb-1\">{stat.value}</div>\n                      <div className=\"text-sm text-white/70\">{stat.label}</div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* INTERACTIVE CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1.5, duration: 0.8 }}\n          className=\"px-4 sm:px-6 lg:px-8 py-8\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10\">\n              <div className=\"flex flex-col lg:flex-row gap-6 items-center justify-between\">\n\n                {/* Search Bar */}\n                <div className=\"relative flex-1 max-w-md\">\n                  <TbSearch className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search champions...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"w-full pl-12 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300\"\n                  />\n                </div>\n\n                {/* Filter Controls */}\n                <div className=\"flex flex-wrap gap-3\">\n                  {[\n                    { key: 'all', label: 'All Champions', icon: TbTrophy },\n                    { key: 'premium', label: 'Premium', icon: TbCrown },\n                    { key: 'free', label: 'Free', icon: TbStar },\n                    { key: 'class', label: 'My Class', icon: TbTarget }\n                  ].map((filter) => (\n                    <motion.button\n                      key={filter.key}\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => setFilterType(filter.key)}\n                      className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${\n                        filterType === filter.key\n                          ? 'bg-purple-600 text-white shadow-lg shadow-purple-500/25'\n                          : 'bg-white/10 text-white/80 hover:bg-white/20'\n                      }`}\n                    >\n                      <filter.icon className=\"w-4 h-4\" />\n                      <span className=\"hidden sm:inline\">{filter.label}</span>\n                    </motion.button>\n                  ))}\n                </div>\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50\"\n                >\n                  <TbRefresh className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 lg:px-8 pb-20\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-3xl sm:text-4xl font-bold text-white text-center mb-8\">\n                    🏆 Champions Podium 🏆\n                  </h2>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\">\n                    {topPerformers.map((champion, index) => {\n                      const position = index + 1;\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          initial={{ opacity: 0, y: 50 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: 0.7 + index * 0.2, duration: 0.8 }}\n                          whileHover={{ scale: 1.05, y: -10 }}\n                          className={`relative ${\n                            position === 1 ? 'md:order-2 md:scale-110' :\n                            position === 2 ? 'md:order-1' : 'md:order-3'\n                          }`}\n                        >\n                          {/* Podium Card */}\n                          <div className={`relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-2xl ${champion.tier.glow} shadow-2xl`}>\n                            <div className=\"bg-slate-900/90 backdrop-blur-lg rounded-2xl p-6 text-center\">\n\n                              {/* Position Badge */}\n                              <div className={`absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center text-white font-black text-xl shadow-lg`}>\n                                {position}\n                              </div>\n\n                              {/* Crown for #1 */}\n                              {position === 1 && (\n                                <motion.div\n                                  animate={{ rotate: [0, 10, -10, 0] }}\n                                  transition={{ duration: 2, repeat: Infinity }}\n                                  className=\"absolute -top-8 left-1/2 transform -translate-x-1/2\"\n                                >\n                                  <TbCrown className=\"w-8 h-8 text-yellow-400\" />\n                                </motion.div>\n                              )}\n\n                              {/* Profile Picture */}\n                              <div className={`relative mx-auto mb-4 ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''}`}>\n                                <div className=\"w-20 h-20 rounded-full overflow-hidden mx-auto bg-gradient-to-br from-purple-500 to-pink-500 p-1\">\n                                  {champion.profilePicture ? (\n                                    <img\n                                      src={champion.profilePicture}\n                                      alt={champion.name}\n                                      className=\"w-full h-full object-cover rounded-full\"\n                                    />\n                                  ) : (\n                                    <div className=\"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-2xl\">\n                                      {champion.name.charAt(0).toUpperCase()}\n                                    </div>\n                                  )}\n                                </div>\n                                {isCurrentUser && (\n                                  <div className=\"absolute -bottom-2 -right-2 bg-yellow-400 text-black rounded-full p-1\">\n                                    <TbStar className=\"w-4 h-4\" />\n                                  </div>\n                                )}\n                              </div>\n\n                              {/* Champion Info */}\n                              <h3 className=\"text-xl font-bold text-white mb-2\">{champion.name}</h3>\n                              <div className={`inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r ${champion.tier.color} rounded-full text-white text-sm font-medium mb-3`}>\n                                <champion.tier.icon className=\"w-4 h-4\" />\n                                {champion.tier.title}\n                              </div>\n\n                              {/* Stats */}\n                              <div className=\"space-y-2\">\n                                <div className=\"flex justify-between text-sm\">\n                                  <span className=\"text-white/70\">XP:</span>\n                                  <span className=\"text-white font-bold\">{champion.totalXP.toLocaleString()}</span>\n                                </div>\n                                <div className=\"flex justify-between text-sm\">\n                                  <span className=\"text-white/70\">Quizzes:</span>\n                                  <span className=\"text-white font-bold\">{champion.totalQuizzesTaken}</span>\n                                </div>\n                                <div className=\"flex justify-between text-sm\">\n                                  <span className=\"text-white/70\">Streak:</span>\n                                  <span className=\"text-white font-bold flex items-center gap-1\">\n                                    <TbFlame className=\"w-4 h-4 text-orange-400\" />\n                                    {champion.currentStreak}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* OTHER CHAMPIONS LIST */}\n              {otherPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"mt-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl font-bold text-white text-center mb-8\">\n                    ⚡ Rising Champions ⚡\n                  </h2>\n\n                  <div className=\"space-y-4\">\n                    {otherPerformers.map((champion, index) => {\n                      const actualRank = index + 4; // Since top 3 are shown separately\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          initial={{ opacity: 0, x: -50 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                          whileHover={{ scale: 1.02, x: 10 }}\n                          className={`relative ${isCurrentUser ? 'ring-2 ring-yellow-400' : ''}`}\n                        >\n                          <div className={`bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow}`}>\n                            <div className=\"bg-slate-900/90 backdrop-blur-lg rounded-xl p-4 flex items-center gap-4\">\n\n                              {/* Rank */}\n                              <div className={`flex-shrink-0 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg`}>\n                                {actualRank}\n                              </div>\n\n                              {/* Profile Picture */}\n                              <div className=\"flex-shrink-0\">\n                                <div className=\"w-14 h-14 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 to-pink-500 p-1\">\n                                  {champion.profilePicture ? (\n                                    <img\n                                      src={champion.profilePicture}\n                                      alt={champion.name}\n                                      className=\"w-full h-full object-cover rounded-full\"\n                                    />\n                                  ) : (\n                                    <div className=\"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-lg\">\n                                      {champion.name.charAt(0).toUpperCase()}\n                                    </div>\n                                  )}\n                                </div>\n                              </div>\n\n                              {/* Champion Info */}\n                              <div className=\"flex-1 min-w-0\">\n                                <div className=\"flex items-center gap-2 mb-1\">\n                                  <h3 className=\"text-lg font-bold text-white truncate\">{champion.name}</h3>\n                                  {isCurrentUser && (\n                                    <div className=\"bg-yellow-400 text-black px-2 py-1 rounded-full text-xs font-bold\">\n                                      YOU\n                                    </div>\n                                  )}\n                                </div>\n                                <div className={`inline-flex items-center gap-1 px-2 py-1 bg-gradient-to-r ${champion.tier.color} rounded-full text-white text-xs font-medium`}>\n                                  <champion.tier.icon className=\"w-3 h-3\" />\n                                  {champion.tier.title}\n                                </div>\n                              </div>\n\n                              {/* Stats */}\n                              <div className=\"flex-shrink-0 text-right\">\n                                <div className=\"text-white font-bold text-lg mb-1\">\n                                  {champion.totalXP.toLocaleString()} XP\n                                </div>\n                                <div className=\"flex items-center gap-4 text-sm text-white/70\">\n                                  <span className=\"flex items-center gap-1\">\n                                    <TbBrain className=\"w-4 h-4\" />\n                                    {champion.totalQuizzesTaken}\n                                  </span>\n                                  <span className=\"flex items-center gap-1\">\n                                    <TbFlame className=\"w-4 h-4 text-orange-400\" />\n                                    {champion.currentStreak}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold text-white mb-2\">Your Current Position</h3>\n                    <div className=\"text-6xl font-black text-yellow-400 mb-2\">#{currentUserRank}</div>\n                    <p className=\"text-white/80 text-lg\">\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold text-white mb-4\">Ready to Rise Higher?</h3>\n                  <p className=\"text-xl text-white/80 mb-6 max-w-2xl mx-auto\">\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {filteredData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbSearch className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold text-white mb-4\">No Champions Found</h3>\n                  <p className=\"text-white/70 text-lg\">\n                    Try adjusting your search or filter criteria to find more champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG1B,WAAW,CAAE2B,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,IAAI,GAAGH,SAAS,CAACG,IAAI,IAAI,IAAI;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACmD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAMqD,SAAS,GAAGnD,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMoD,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,gBAAgB,GAAG;IACvBC,SAAS,EAAE;MACTC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yCAAyC;MAChDC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEpD,OAAO;MACbqD,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE;IACf,CAAC;IACDC,OAAO,EAAE;MACPN,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,0CAA0C;MACjDC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEzC,SAAS;MACf0C,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE;IACf,CAAC;IACDE,QAAQ,EAAE;MACRP,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,wCAAwC;MAC/CC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEpC,QAAQ;MACdqC,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE;IACf,CAAC;IACDG,IAAI,EAAE;MACJR,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,8CAA8C;MACrDC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAErD,QAAQ;MACdsD,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC;IACDI,MAAM,EAAE;MACNT,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,wCAAwC;MAC/CC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAE5C,OAAO;MACb6C,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE;IACf,CAAC;IACDK,MAAM,EAAE;MACNV,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,8CAA8C;MACrDC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEnD,MAAM;MACZoD,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE;IACf;EACF,CAAC;;EAED;EACA,MAAMM,WAAW,GAAIC,EAAE,IAAK;IAC1B,KAAK,MAAM,CAACC,IAAI,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAClB,gBAAgB,CAAC,EAAE;MAC7D,IAAIc,EAAE,IAAIE,MAAM,CAACd,GAAG,EAAE,OAAO;QAAEa,IAAI;QAAE,GAAGC;MAAO,CAAC;IAClD;IACA,OAAO;MAAED,IAAI,EAAE,QAAQ;MAAE,GAAGf,gBAAgB,CAACY;IAAO,CAAC;EACvD,CAAC;;EAED;EACA,MAAMO,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFpC,UAAU,CAAC,IAAI,CAAC;MAChBqC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAElD,IAAIC,QAAQ;;MAEZ;MACA,IAAI;QACFA,QAAQ,GAAG,MAAMpD,uBAAuB,CAAC,CAAC;QAC1CkD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEC,QAAQ,CAAC;MACtD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdH,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACpCC,QAAQ,GAAG,MAAMnD,WAAW,CAAC,CAAC;MAChC;MAEA,IAAI,CAACmD,QAAQ,IAAI,CAACA,QAAQ,CAACE,OAAO,EAAE;QAClCJ,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9CC,QAAQ,GAAG,MAAMnD,WAAW,CAAC,CAAC;MAChC;MAEA,IAAImD,QAAQ,IAAIA,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACjD,MAAMC,eAAe,GAAGJ,QAAQ,CAACG,IAAI,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;UACzD;UACA,MAAMC,QAAQ,GAAGF,IAAI,CAACjD,IAAI,IAAIiD,IAAI;UAClC,MAAMG,UAAU,GAAGH,IAAI,CAACI,OAAO,IAAI,EAAE;;UAErC;UACA,MAAMC,YAAY,GAAGF,UAAU,CAACG,MAAM,IAAIJ,QAAQ,CAACK,iBAAiB,IAAI,CAAC;UACzE,MAAMC,UAAU,GAAGL,UAAU,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UACnF,MAAMC,YAAY,GAAGR,YAAY,GAAG,CAAC,GAAGS,IAAI,CAACC,KAAK,CAACP,UAAU,GAAGH,YAAY,CAAC,GAAG,CAAC;UACjF,MAAMW,OAAO,GAAGd,QAAQ,CAACc,OAAO,IAAId,QAAQ,CAAChB,EAAE,IAAIsB,UAAU,IAAIM,IAAI,CAACG,KAAK,CAACJ,YAAY,GAAGR,YAAY,GAAG,EAAE,CAAC,IAAI,CAAC;UAElH,OAAO;YACLa,GAAG,EAAEhB,QAAQ,CAACgB,GAAG,IAAIhB,QAAQ,CAACiB,MAAM,IAAIjB,QAAQ,CAACkB,EAAE;YACnDC,IAAI,EAAEnB,QAAQ,CAACmB,IAAI,IAAInB,QAAQ,CAACoB,QAAQ,IAAI,oBAAoB;YAChEC,KAAK,EAAErB,QAAQ,CAACqB,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEtB,QAAQ,CAACsB,KAAK,IAAItB,QAAQ,CAACuB,SAAS,IAAI,EAAE;YACjDC,KAAK,EAAExB,QAAQ,CAACwB,KAAK,IAAI,EAAE;YAC3BC,cAAc,EAAEzB,QAAQ,CAACyB,cAAc,IAAIzB,QAAQ,CAAC0B,MAAM,IAAI,EAAE;YAChEZ,OAAO,EAAEA,OAAO;YAChBT,iBAAiB,EAAEF,YAAY;YAC/BQ,YAAY,EAAEA,YAAY;YAC1BgB,aAAa,EAAE3B,QAAQ,CAAC2B,aAAa,IAAI3B,QAAQ,CAAC4B,MAAM,IAAI,CAAC;YAC7DC,UAAU,EAAE7B,QAAQ,CAAC6B,UAAU,IAAI7B,QAAQ,CAAC8B,SAAS,IAAI,CAAC;YAC1DC,kBAAkB,EAAE/B,QAAQ,CAAC+B,kBAAkB,IAAI/B,QAAQ,CAACgC,4BAA4B,IAAI,MAAM;YAClGC,IAAI,EAAElC,KAAK,GAAG,CAAC;YACfd,IAAI,EAAEF,WAAW,CAAC+B,OAAO;UAC3B,CAAC;QACH,CAAC,CAAC;;QAEF;QACAlB,eAAe,CAACsC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACtB,OAAO,GAAGqB,CAAC,CAACrB,OAAO,CAAC;;QAErD;QACAlB,eAAe,CAACyC,OAAO,CAAC,CAACxF,IAAI,EAAEkD,KAAK,KAAK;UACvClD,IAAI,CAACoF,IAAI,GAAGlC,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEFhD,cAAc,CAAC6C,eAAe,CAAC;;QAE/B;QACA,MAAM0C,QAAQ,GAAGzF,IAAI,GAAG+C,eAAe,CAAC2C,SAAS,CAACzC,IAAI,IAAIA,IAAI,CAACkB,GAAG,KAAKnE,IAAI,CAACmE,GAAG,CAAC,GAAG,CAAC,CAAC;QACrF7D,kBAAkB,CAACmF,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;QAEvDhD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEK,eAAe,CAACQ,MAAM,EAAE,WAAW,CAAC;MACrF,CAAC,MAAM;QACL;QACAd,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrE,MAAMiD,QAAQ,GAAG,CACf;UACExB,GAAG,EAAE,OAAO;UACZG,IAAI,EAAE,eAAe;UACrBE,KAAK,EAAE,kBAAkB;UACzBC,KAAK,EAAE,GAAG;UACVE,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBX,OAAO,EAAE,KAAK;UACdT,iBAAiB,EAAE,EAAE;UACrBM,YAAY,EAAE,EAAE;UAChBgB,aAAa,EAAE,EAAE;UACjBE,UAAU,EAAE,EAAE;UACdE,kBAAkB,EAAE,SAAS;UAC7BE,IAAI,EAAE,CAAC;UACPhD,IAAI,EAAEF,WAAW,CAAC,KAAK;QACzB,CAAC,EACD;UACEiC,GAAG,EAAE,OAAO;UACZG,IAAI,EAAE,kBAAkB;UACxBE,KAAK,EAAE,mBAAmB;UAC1BC,KAAK,EAAE,GAAG;UACVE,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBX,OAAO,EAAE,KAAK;UACdT,iBAAiB,EAAE,EAAE;UACrBM,YAAY,EAAE,EAAE;UAChBgB,aAAa,EAAE,CAAC;UAChBE,UAAU,EAAE,EAAE;UACdE,kBAAkB,EAAE,SAAS;UAC7BE,IAAI,EAAE,CAAC;UACPhD,IAAI,EAAEF,WAAW,CAAC,KAAK;QACzB,CAAC,EACD;UACEiC,GAAG,EAAE,OAAO;UACZG,IAAI,EAAE,eAAe;UACrBE,KAAK,EAAE,kBAAkB;UACzBC,KAAK,EAAE,GAAG;UACVE,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBX,OAAO,EAAE,IAAI;UACbT,iBAAiB,EAAE,EAAE;UACrBM,YAAY,EAAE,EAAE;UAChBgB,aAAa,EAAE,CAAC;UAChBE,UAAU,EAAE,EAAE;UACdE,kBAAkB,EAAE,MAAM;UAC1BE,IAAI,EAAE,CAAC;UACPhD,IAAI,EAAEF,WAAW,CAAC,IAAI;QACxB,CAAC,EACD;UACEiC,GAAG,EAAE,OAAO;UACZG,IAAI,EAAE,aAAa;UACnBE,KAAK,EAAE,kBAAkB;UACzBC,KAAK,EAAE,GAAG;UACVE,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBX,OAAO,EAAE,IAAI;UACbT,iBAAiB,EAAE,EAAE;UACrBM,YAAY,EAAE,EAAE;UAChBgB,aAAa,EAAE,CAAC;UAChBE,UAAU,EAAE,CAAC;UACbE,kBAAkB,EAAE,MAAM;UAC1BE,IAAI,EAAE,CAAC;UACPhD,IAAI,EAAEF,WAAW,CAAC,IAAI;QACxB,CAAC,EACD;UACEiC,GAAG,EAAE,OAAO;UACZG,IAAI,EAAE,gBAAgB;UACtBE,KAAK,EAAE,mBAAmB;UAC1BC,KAAK,EAAE,GAAG;UACVE,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBX,OAAO,EAAE,IAAI;UACbT,iBAAiB,EAAE,EAAE;UACrBM,YAAY,EAAE,EAAE;UAChBgB,aAAa,EAAE,CAAC;UAChBE,UAAU,EAAE,CAAC;UACbE,kBAAkB,EAAE,MAAM;UAC1BE,IAAI,EAAE,CAAC;UACPhD,IAAI,EAAEF,WAAW,CAAC,IAAI;QACxB,CAAC,CACF;QAEDhC,cAAc,CAACyF,QAAQ,CAAC;QACxBrF,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1BlC,OAAO,CAACyE,OAAO,CAAC,sCAAsC,CAAC;MACzD;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDxE,OAAO,CAACwE,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACArC,SAAS,CAAC,MAAM;IACdyE,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMoD,WAAW,GAAGxE,kBAAkB,CAAC2C,IAAI,CAACG,KAAK,CAACH,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAGzE,kBAAkB,CAACmC,MAAM,CAAC,CAAC;IAC7FrC,oBAAoB,CAAC0E,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvC/E,iBAAiB,CAACgF,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,cAAc,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,YAAY,GAAGjG,WAAW,CAACkG,MAAM,CAACC,WAAW,IAAI;IACrD,MAAMC,aAAa,GAAGD,WAAW,CAAC9B,IAAI,CAACgC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChG,UAAU,CAAC+F,WAAW,CAAC,CAAC,CAAC;IACvF,MAAME,aAAa,GAAG/F,UAAU,KAAK,KAAK,IACpBA,UAAU,KAAK,SAAS,IAAI2F,WAAW,CAAClB,kBAAkB,KAAK,SAAU,IACzEzE,UAAU,KAAK,MAAM,IAAI2F,WAAW,CAAClB,kBAAkB,KAAK,MAAO,IACnEzE,UAAU,KAAK,OAAO,IAAIT,IAAI,IAAIoG,WAAW,CAAC3B,KAAK,KAAKzE,IAAI,CAACyE,KAAM;IACzF,OAAO4B,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAGP,YAAY,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC9C,MAAMC,eAAe,GAAGT,YAAY,CAACQ,KAAK,CAAC,CAAC,CAAC;EAE7C,oBACEhH,OAAA;IAAKgF,SAAS,EAAC,oGAAoG;IAAAkC,QAAA,gBAEjHlH,OAAA;MAAKgF,SAAS,EAAC,kCAAkC;MAAAkC,QAAA,gBAC/ClH,OAAA;QAAKgF,SAAS,EAAC;MAA2H;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjJtH,OAAA;QAAKgF,SAAS,EAAC;MAAkJ;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACxKtH,OAAA;QAAKgF,SAAS,EAAC;MAA2I;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9J,CAAC,eAGNtH,OAAA;MAAKgF,SAAS,EAAC,sDAAsD;MAAAkC,QAAA,EAClE,CAAC,GAAGK,KAAK,CAAC,EAAE,CAAC,CAAC,CAACjE,GAAG,CAAC,CAACkE,CAAC,EAAEC,CAAC,kBACvBzH,OAAA,CAACzB,MAAM,CAACmJ,GAAG;QAET1C,SAAS,EAAC,mDAAmD;QAC7D2C,OAAO,EAAE;UACPC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;UACfC,CAAC,EAAE,CAAC,CAAC,EAAExD,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;UACnC2B,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;QACzB,CAAE;QACFC,UAAU,EAAE;UACVC,QAAQ,EAAE,CAAC,GAAG3D,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/B8B,MAAM,EAAEC,QAAQ;UAChBC,KAAK,EAAE9D,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG;QACzB,CAAE;QACFiC,KAAK,EAAE;UACLC,IAAI,EAAG,GAAEhE,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;UAC/BmC,GAAG,EAAG,GAAEjE,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG,GAAI;QAC9B;MAAE,GAfGsB,CAAC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENtH,OAAA;MAAKgF,SAAS,EAAC,eAAe;MAAAkC,QAAA,gBAE5BlH,OAAA,CAACzB,MAAM,CAACmJ,GAAG;QACTa,GAAG,EAAE9G,SAAU;QACf+G,OAAO,EAAE;UAAEV,OAAO,EAAE,CAAC;UAAEF,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCD,OAAO,EAAE;UAAEG,OAAO,EAAE,CAAC;UAAEF,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,QAAQ,EAAE,CAAC;UAAES,IAAI,EAAE;QAAU,CAAE;QAC7CzD,SAAS,EAAC,0BAA0B;QAAAkC,QAAA,eAGpClH,OAAA;UAAKgF,SAAS,EAAC,sEAAsE;UAAAkC,QAAA,gBACnFlH,OAAA;YAAKgF,SAAS,EAAC;UAA8B;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGpDtH,OAAA;YAAKgF,SAAS,EAAC,4DAA4D;YAAAkC,QAAA,eACzElH,OAAA;cAAKgF,SAAS,EAAC,+BAA+B;cAAAkC,QAAA,gBAG5ClH,OAAA,CAACzB,MAAM,CAACmJ,GAAG;gBACTC,OAAO,EAAE;kBACPe,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;kBACnBC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnB,CAAE;gBACFZ,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXC,MAAM,EAAEC,QAAQ;kBAChBO,IAAI,EAAE;gBACR,CAAE;gBACFzD,SAAS,EAAC,MAAM;gBAAAkC,QAAA,eAEhBlH,OAAA;kBAAIgF,SAAS,EAAC,4EAA4E;kBAAAkC,QAAA,gBACxFlH,OAAA,CAACzB,MAAM,CAACqK,IAAI;oBACVjB,OAAO,EAAE;sBACPkB,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;oBACrD,CAAE;oBACFd,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,MAAM,EAAEC,QAAQ;sBAChBO,IAAI,EAAE;oBACR,CAAE;oBACFzD,SAAS,EAAC,oHAAoH;oBAC9HoD,KAAK,EAAE;sBACLU,cAAc,EAAE,WAAW;sBAC3BC,oBAAoB,EAAE,MAAM;sBAC5BC,mBAAmB,EAAE;oBACvB,CAAE;oBAAA9B,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eACdtH,OAAA;oBAAAmH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNtH,OAAA,CAACzB,MAAM,CAACqK,IAAI;oBACVjB,OAAO,EAAE;sBACPsB,UAAU,EAAE,CACV,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC;oBAEpC,CAAE;oBACFlB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,MAAM,EAAEC,QAAQ;sBAChBO,IAAI,EAAE;oBACR,CAAE;oBACFzD,SAAS,EAAC,YAAY;oBAAAkC,QAAA,EACvB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAGbtH,OAAA,CAACzB,MAAM,CAAC2K,CAAC;gBACPV,OAAO,EAAE;kBAAEV,OAAO,EAAE,CAAC;kBAAEF,CAAC,EAAE;gBAAG,CAAE;gBAC/BD,OAAO,EAAE;kBAAEG,OAAO,EAAE,CAAC;kBAAEF,CAAC,EAAE;gBAAE,CAAE;gBAC9BG,UAAU,EAAE;kBAAEI,KAAK,EAAE,GAAG;kBAAEH,QAAQ,EAAE;gBAAI,CAAE;gBAC1ChD,SAAS,EAAC,kGAAkG;gBAAAkC,QAAA,EAC7G;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAGXtH,OAAA,CAACzB,MAAM,CAACmJ,GAAG;gBACTc,OAAO,EAAE;kBAAEV,OAAO,EAAE,CAAC;kBAAEY,KAAK,EAAE;gBAAI,CAAE;gBACpCf,OAAO,EAAE;kBAAEG,OAAO,EAAE,CAAC;kBAAEY,KAAK,EAAE;gBAAE,CAAE;gBAClCX,UAAU,EAAE;kBAAEI,KAAK,EAAE,CAAC;kBAAEH,QAAQ,EAAE;gBAAI,CAAE;gBACxChD,SAAS,EAAC,4FAA4F;gBAAAkC,QAAA,eAEtGlH,OAAA,CAACzB,MAAM,CAAC2K,CAAC;kBAEPV,OAAO,EAAE;oBAAEV,OAAO,EAAE,CAAC;oBAAEF,CAAC,EAAE;kBAAG,CAAE;kBAC/BD,OAAO,EAAE;oBAAEG,OAAO,EAAE,CAAC;oBAAEF,CAAC,EAAE;kBAAE,CAAE;kBAC9B5C,SAAS,EAAC,2CAA2C;kBAAAkC,QAAA,EAEpD3F;gBAAiB,GALbA,iBAAiB;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGbtH,OAAA,CAACzB,MAAM,CAACmJ,GAAG;gBACTc,OAAO,EAAE;kBAAEV,OAAO,EAAE,CAAC;kBAAEF,CAAC,EAAE;gBAAG,CAAE;gBAC/BD,OAAO,EAAE;kBAAEG,OAAO,EAAE,CAAC;kBAAEF,CAAC,EAAE;gBAAE,CAAE;gBAC9BG,UAAU,EAAE;kBAAEI,KAAK,EAAE,GAAG;kBAAEH,QAAQ,EAAE;gBAAI,CAAE;gBAC1ChD,SAAS,EAAC,kEAAkE;gBAAAkC,QAAA,EAE3E,CACC;kBAAElF,IAAI,EAAErD,QAAQ;kBAAEwK,KAAK,EAAE,iBAAiB;kBAAEC,KAAK,EAAE7I,WAAW,CAACsD,MAAM;kBAAE/B,KAAK,EAAE;gBAAkB,CAAC,EACjG;kBAAEE,IAAI,EAAElD,OAAO;kBAAEqK,KAAK,EAAE,gBAAgB;kBAAEC,KAAK,EAAE7I,WAAW,CAACkG,MAAM,CAAC4C,CAAC,IAAIA,CAAC,CAACjE,aAAa,GAAG,CAAC,CAAC,CAACvB,MAAM;kBAAE/B,KAAK,EAAE;gBAAkB,CAAC,EAChI;kBAAEE,IAAI,EAAEhD,OAAO;kBAAEmK,KAAK,EAAE,eAAe;kBAAEC,KAAK,EAAE7I,WAAW,CAACyD,MAAM,CAAC,CAACC,GAAG,EAAEoF,CAAC,KAAKpF,GAAG,GAAGoF,CAAC,CAACvF,iBAAiB,EAAE,CAAC,CAAC;kBAAEhC,KAAK,EAAE;gBAAgB,CAAC,EACtI;kBAAEE,IAAI,EAAE3C,MAAM;kBAAE8J,KAAK,EAAE,UAAU;kBAAEC,KAAK,EAAE7I,WAAW,CAACyD,MAAM,CAAC,CAACC,GAAG,EAAEoF,CAAC,KAAKpF,GAAG,GAAGoF,CAAC,CAAC9E,OAAO,EAAE,CAAC,CAAC,CAAC+E,cAAc,CAAC,CAAC;kBAAExH,KAAK,EAAE;gBAAkB,CAAC,CAC1I,CAACwB,GAAG,CAAC,CAACiG,IAAI,EAAE/F,KAAK,kBAChBxD,OAAA,CAACzB,MAAM,CAACmJ,GAAG;kBAET8B,UAAU,EAAE;oBAAEd,KAAK,EAAE,IAAI;oBAAEd,CAAC,EAAE,CAAC;kBAAE,CAAE;kBACnC5C,SAAS,EAAC,gFAAgF;kBAAAkC,QAAA,gBAE1FlH,OAAA,CAACuJ,IAAI,CAACvH,IAAI;oBAACgD,SAAS,EAAG,WAAUuE,IAAI,CAACzH,KAAM;kBAAe;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9DtH,OAAA;oBAAKgF,SAAS,EAAC,gDAAgD;oBAAAkC,QAAA,EAAEqC,IAAI,CAACH;kBAAK;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClFtH,OAAA;oBAAKgF,SAAS,EAAC,uBAAuB;oBAAAkC,QAAA,EAAEqC,IAAI,CAACJ;kBAAK;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GANpDiC,IAAI,CAACJ,KAAK;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOL,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbtH,OAAA,CAACzB,MAAM,CAACmJ,GAAG;QACTc,OAAO,EAAE;UAAEV,OAAO,EAAE,CAAC;UAAEF,CAAC,EAAE;QAAG,CAAE;QAC/BD,OAAO,EAAE;UAAEG,OAAO,EAAE,CAAC;UAAEF,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEI,KAAK,EAAE,GAAG;UAAEH,QAAQ,EAAE;QAAI,CAAE;QAC1ChD,SAAS,EAAC,2BAA2B;QAAAkC,QAAA,eAErClH,OAAA;UAAKgF,SAAS,EAAC,mBAAmB;UAAAkC,QAAA,eAChClH,OAAA;YAAKgF,SAAS,EAAC,oEAAoE;YAAAkC,QAAA,eACjFlH,OAAA;cAAKgF,SAAS,EAAC,8DAA8D;cAAAkC,QAAA,gBAG3ElH,OAAA;gBAAKgF,SAAS,EAAC,0BAA0B;gBAAAkC,QAAA,gBACvClH,OAAA,CAACf,QAAQ;kBAAC+F,SAAS,EAAC;gBAA0E;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjGtH,OAAA;kBACEyJ,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,qBAAqB;kBACjCN,KAAK,EAAEvI,UAAW;kBAClB8I,QAAQ,EAAGC,CAAC,IAAK9I,aAAa,CAAC8I,CAAC,CAACC,MAAM,CAACT,KAAK,CAAE;kBAC/CpE,SAAS,EAAC;gBAAiN;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5N,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNtH,OAAA;gBAAKgF,SAAS,EAAC,sBAAsB;gBAAAkC,QAAA,EAClC,CACC;kBAAE4C,GAAG,EAAE,KAAK;kBAAEX,KAAK,EAAE,eAAe;kBAAEnH,IAAI,EAAErD;gBAAS,CAAC,EACtD;kBAAEmL,GAAG,EAAE,SAAS;kBAAEX,KAAK,EAAE,SAAS;kBAAEnH,IAAI,EAAEpD;gBAAQ,CAAC,EACnD;kBAAEkL,GAAG,EAAE,MAAM;kBAAEX,KAAK,EAAE,MAAM;kBAAEnH,IAAI,EAAEnD;gBAAO,CAAC,EAC5C;kBAAEiL,GAAG,EAAE,OAAO;kBAAEX,KAAK,EAAE,UAAU;kBAAEnH,IAAI,EAAEjD;gBAAS,CAAC,CACpD,CAACuE,GAAG,CAAEmD,MAAM,iBACXzG,OAAA,CAACzB,MAAM,CAACwL,MAAM;kBAEZP,UAAU,EAAE;oBAAEd,KAAK,EAAE;kBAAK,CAAE;kBAC5BsB,QAAQ,EAAE;oBAAEtB,KAAK,EAAE;kBAAK,CAAE;kBAC1BuB,OAAO,EAAEA,CAAA,KAAMjJ,aAAa,CAACyF,MAAM,CAACqD,GAAG,CAAE;kBACzC9E,SAAS,EAAG,wFACVjE,UAAU,KAAK0F,MAAM,CAACqD,GAAG,GACrB,yDAAyD,GACzD,6CACL,EAAE;kBAAA5C,QAAA,gBAEHlH,OAAA,CAACyG,MAAM,CAACzE,IAAI;oBAACgD,SAAS,EAAC;kBAAS;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnCtH,OAAA;oBAAMgF,SAAS,EAAC,kBAAkB;oBAAAkC,QAAA,EAAET,MAAM,CAAC0C;kBAAK;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAXnDb,MAAM,CAACqD,GAAG;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYF,CAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNtH,OAAA,CAACzB,MAAM,CAACwL,MAAM;gBACZP,UAAU,EAAE;kBAAEd,KAAK,EAAE,IAAI;kBAAEwB,MAAM,EAAE;gBAAI,CAAE;gBACzCF,QAAQ,EAAE;kBAAEtB,KAAK,EAAE;gBAAK,CAAE;gBAC1BuB,OAAO,EAAEnH,gBAAiB;gBAC1BqH,QAAQ,EAAE1J,OAAQ;gBAClBuE,SAAS,EAAC,4LAA4L;gBAAAkC,QAAA,gBAEtMlH,OAAA,CAACb,SAAS;kBAAC6F,SAAS,EAAG,WAAUvE,OAAO,GAAG,cAAc,GAAG,EAAG;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpEtH,OAAA;kBAAAkH,QAAA,EAAM;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGZ7G,OAAO,iBACNT,OAAA,CAACzB,MAAM,CAACmJ,GAAG;QACTc,OAAO,EAAE;UAAEV,OAAO,EAAE;QAAE,CAAE;QACxBH,OAAO,EAAE;UAAEG,OAAO,EAAE;QAAE,CAAE;QACxB9C,SAAS,EAAC,iDAAiD;QAAAkC,QAAA,gBAE3DlH,OAAA,CAACzB,MAAM,CAACmJ,GAAG;UACTC,OAAO,EAAE;YAAEuC,MAAM,EAAE;UAAI,CAAE;UACzBnC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEO,IAAI,EAAE;UAAS,CAAE;UAC9DzD,SAAS,EAAC;QAA6E;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,eACFtH,OAAA;UAAGgF,SAAS,EAAC,mCAAmC;UAAAkC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CACb,EAGA,CAAC7G,OAAO,iBACPT,OAAA,CAACzB,MAAM,CAACmJ,GAAG;QACTc,OAAO,EAAE;UAAEV,OAAO,EAAE,CAAC;UAAEF,CAAC,EAAE;QAAG,CAAE;QAC/BD,OAAO,EAAE;UAAEG,OAAO,EAAE,CAAC;UAAEF,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEI,KAAK,EAAE,GAAG;UAAEH,QAAQ,EAAE;QAAI,CAAE;QAC1ChD,SAAS,EAAC,4BAA4B;QAAAkC,QAAA,eAEtClH,OAAA;UAAKgF,SAAS,EAAC,mBAAmB;UAAAkC,QAAA,GAG/BH,aAAa,CAAClD,MAAM,GAAG,CAAC,iBACvB7D,OAAA,CAACzB,MAAM,CAACmJ,GAAG;YACTc,OAAO,EAAE;cAAEV,OAAO,EAAE,CAAC;cAAEY,KAAK,EAAE;YAAI,CAAE;YACpCf,OAAO,EAAE;cAAEG,OAAO,EAAE,CAAC;cAAEY,KAAK,EAAE;YAAE,CAAE;YAClCX,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE;YAAI,CAAE;YAC1ChD,SAAS,EAAC,OAAO;YAAAkC,QAAA,gBAEjBlH,OAAA;cAAIgF,SAAS,EAAC,4DAA4D;cAAAkC,QAAA,EAAC;YAE3E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELtH,OAAA;cAAKgF,SAAS,EAAC,yDAAyD;cAAAkC,QAAA,EACrEH,aAAa,CAACzD,GAAG,CAAC,CAAC8G,QAAQ,EAAE5G,KAAK,KAAK;gBACtC,MAAM6G,QAAQ,GAAG7G,KAAK,GAAG,CAAC;gBAC1B,MAAM8G,aAAa,GAAGhK,IAAI,IAAI8J,QAAQ,CAAC3F,GAAG,KAAKnE,IAAI,CAACmE,GAAG;gBAEvD,oBACEzE,OAAA,CAACzB,MAAM,CAACmJ,GAAG;kBAETc,OAAO,EAAE;oBAAEV,OAAO,EAAE,CAAC;oBAAEF,CAAC,EAAE;kBAAG,CAAE;kBAC/BD,OAAO,EAAE;oBAAEG,OAAO,EAAE,CAAC;oBAAEF,CAAC,EAAE;kBAAE,CAAE;kBAC9BG,UAAU,EAAE;oBAAEI,KAAK,EAAE,GAAG,GAAG3E,KAAK,GAAG,GAAG;oBAAEwE,QAAQ,EAAE;kBAAI,CAAE;kBACxDwB,UAAU,EAAE;oBAAEd,KAAK,EAAE,IAAI;oBAAEd,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpC5C,SAAS,EAAG,YACVqF,QAAQ,KAAK,CAAC,GAAG,yBAAyB,GAC1CA,QAAQ,KAAK,CAAC,GAAG,YAAY,GAAG,YACjC,EAAE;kBAAAnD,QAAA,eAGHlH,OAAA;oBAAKgF,SAAS,EAAG,8BAA6BoF,QAAQ,CAAC1H,IAAI,CAACZ,KAAM,oBAAmBsI,QAAQ,CAAC1H,IAAI,CAACX,IAAK,aAAa;oBAAAmF,QAAA,eACnHlH,OAAA;sBAAKgF,SAAS,EAAC,8DAA8D;sBAAAkC,QAAA,gBAG3ElH,OAAA;wBAAKgF,SAAS,EAAG,mFAAkFoF,QAAQ,CAAC1H,IAAI,CAACZ,KAAM,wFAAwF;wBAAAoF,QAAA,EAC5MmD;sBAAQ;wBAAAlD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,EAGL+C,QAAQ,KAAK,CAAC,iBACbrK,OAAA,CAACzB,MAAM,CAACmJ,GAAG;wBACTC,OAAO,EAAE;0BAAEuC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;wBAAE,CAAE;wBACrCnC,UAAU,EAAE;0BAAEC,QAAQ,EAAE,CAAC;0BAAEC,MAAM,EAAEC;wBAAS,CAAE;wBAC9ClD,SAAS,EAAC,qDAAqD;wBAAAkC,QAAA,eAE/DlH,OAAA,CAACpB,OAAO;0BAACoG,SAAS,EAAC;wBAAyB;0BAAAmC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CACb,eAGDtH,OAAA;wBAAKgF,SAAS,EAAG,yBAAwBsF,aAAa,GAAG,wBAAwB,GAAG,EAAG,EAAE;wBAAApD,QAAA,gBACvFlH,OAAA;0BAAKgF,SAAS,EAAC,kGAAkG;0BAAAkC,QAAA,EAC9GkD,QAAQ,CAAClF,cAAc,gBACtBlF,OAAA;4BACEuK,GAAG,EAAEH,QAAQ,CAAClF,cAAe;4BAC7BsF,GAAG,EAAEJ,QAAQ,CAACxF,IAAK;4BACnBI,SAAS,EAAC;0BAAyC;4BAAAmC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFtH,OAAA;4BAAKgF,SAAS,EAAC,yIAAyI;4BAAAkC,QAAA,EACrJkD,QAAQ,CAACxF,IAAI,CAAC6F,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAvD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnC;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,EACLgD,aAAa,iBACZtK,OAAA;0BAAKgF,SAAS,EAAC,uEAAuE;0BAAAkC,QAAA,eACpFlH,OAAA,CAACnB,MAAM;4BAACmG,SAAS,EAAC;0BAAS;4BAAAmC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGNtH,OAAA;wBAAIgF,SAAS,EAAC,mCAAmC;wBAAAkC,QAAA,EAAEkD,QAAQ,CAACxF;sBAAI;wBAAAuC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACtEtH,OAAA;wBAAKgF,SAAS,EAAG,6DAA4DoF,QAAQ,CAAC1H,IAAI,CAACZ,KAAM,mDAAmD;wBAAAoF,QAAA,gBAClJlH,OAAA,CAACoK,QAAQ,CAAC1H,IAAI,CAACV,IAAI;0BAACgD,SAAS,EAAC;wBAAS;0BAAAmC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACzC8C,QAAQ,CAAC1H,IAAI,CAACT,KAAK;sBAAA;wBAAAkF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC,eAGNtH,OAAA;wBAAKgF,SAAS,EAAC,WAAW;wBAAAkC,QAAA,gBACxBlH,OAAA;0BAAKgF,SAAS,EAAC,8BAA8B;0BAAAkC,QAAA,gBAC3ClH,OAAA;4BAAMgF,SAAS,EAAC,eAAe;4BAAAkC,QAAA,EAAC;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC1CtH,OAAA;4BAAMgF,SAAS,EAAC,sBAAsB;4BAAAkC,QAAA,EAAEkD,QAAQ,CAAC7F,OAAO,CAAC+E,cAAc,CAAC;0BAAC;4BAAAnC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9E,CAAC,eACNtH,OAAA;0BAAKgF,SAAS,EAAC,8BAA8B;0BAAAkC,QAAA,gBAC3ClH,OAAA;4BAAMgF,SAAS,EAAC,eAAe;4BAAAkC,QAAA,EAAC;0BAAQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC/CtH,OAAA;4BAAMgF,SAAS,EAAC,sBAAsB;4BAAAkC,QAAA,EAAEkD,QAAQ,CAACtG;0BAAiB;4BAAAqD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvE,CAAC,eACNtH,OAAA;0BAAKgF,SAAS,EAAC,8BAA8B;0BAAAkC,QAAA,gBAC3ClH,OAAA;4BAAMgF,SAAS,EAAC,eAAe;4BAAAkC,QAAA,EAAC;0BAAO;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC9CtH,OAAA;4BAAMgF,SAAS,EAAC,8CAA8C;4BAAAkC,QAAA,gBAC5DlH,OAAA,CAAClB,OAAO;8BAACkG,SAAS,EAAC;4BAAyB;8BAAAmC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC9C8C,QAAQ,CAAChF,aAAa;0BAAA;4BAAA+B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GA9ED8C,QAAQ,CAAC3F,GAAG;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA+EP,CAAC;cAEjB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,EAGAL,eAAe,CAACpD,MAAM,GAAG,CAAC,iBACzB7D,OAAA,CAACzB,MAAM,CAACmJ,GAAG;YACTc,OAAO,EAAE;cAAEV,OAAO,EAAE,CAAC;cAAEF,CAAC,EAAE;YAAG,CAAE;YAC/BD,OAAO,EAAE;cAAEG,OAAO,EAAE,CAAC;cAAEF,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEI,KAAK,EAAE,CAAC;cAAEH,QAAQ,EAAE;YAAI,CAAE;YACxChD,SAAS,EAAC,OAAO;YAAAkC,QAAA,gBAEjBlH,OAAA;cAAIgF,SAAS,EAAC,4DAA4D;cAAAkC,QAAA,EAAC;YAE3E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELtH,OAAA;cAAKgF,SAAS,EAAC,WAAW;cAAAkC,QAAA,EACvBD,eAAe,CAAC3D,GAAG,CAAC,CAAC8G,QAAQ,EAAE5G,KAAK,KAAK;gBACxC,MAAMmH,UAAU,GAAGnH,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC9B,MAAM8G,aAAa,GAAGhK,IAAI,IAAI8J,QAAQ,CAAC3F,GAAG,KAAKnE,IAAI,CAACmE,GAAG;gBAEvD,oBACEzE,OAAA,CAACzB,MAAM,CAACmJ,GAAG;kBAETc,OAAO,EAAE;oBAAEV,OAAO,EAAE,CAAC;oBAAED,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCF,OAAO,EAAE;oBAAEG,OAAO,EAAE,CAAC;oBAAED,CAAC,EAAE;kBAAE,CAAE;kBAC9BE,UAAU,EAAE;oBAAEI,KAAK,EAAE,GAAG,GAAG3E,KAAK,GAAG,GAAG;oBAAEwE,QAAQ,EAAE;kBAAI,CAAE;kBACxDwB,UAAU,EAAE;oBAAEd,KAAK,EAAE,IAAI;oBAAEb,CAAC,EAAE;kBAAG,CAAE;kBACnC7C,SAAS,EAAG,YAAWsF,aAAa,GAAG,wBAAwB,GAAG,EAAG,EAAE;kBAAApD,QAAA,eAEvElH,OAAA;oBAAKgF,SAAS,EAAG,oBAAmBoF,QAAQ,CAAC1H,IAAI,CAACZ,KAAM,mBAAkBsI,QAAQ,CAAC1H,IAAI,CAACX,IAAK,EAAE;oBAAAmF,QAAA,eAC7FlH,OAAA;sBAAKgF,SAAS,EAAC,yEAAyE;sBAAAkC,QAAA,gBAGtFlH,OAAA;wBAAKgF,SAAS,EAAG,6CAA4CoF,QAAQ,CAAC1H,IAAI,CAACZ,KAAM,uFAAuF;wBAAAoF,QAAA,EACrKyD;sBAAU;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR,CAAC,eAGNtH,OAAA;wBAAKgF,SAAS,EAAC,eAAe;wBAAAkC,QAAA,eAC5BlH,OAAA;0BAAKgF,SAAS,EAAC,0FAA0F;0BAAAkC,QAAA,EACtGkD,QAAQ,CAAClF,cAAc,gBACtBlF,OAAA;4BACEuK,GAAG,EAAEH,QAAQ,CAAClF,cAAe;4BAC7BsF,GAAG,EAAEJ,QAAQ,CAACxF,IAAK;4BACnBI,SAAS,EAAC;0BAAyC;4BAAAmC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFtH,OAAA;4BAAKgF,SAAS,EAAC,wIAAwI;4BAAAkC,QAAA,EACpJkD,QAAQ,CAACxF,IAAI,CAAC6F,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAvD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnC;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNtH,OAAA;wBAAKgF,SAAS,EAAC,gBAAgB;wBAAAkC,QAAA,gBAC7BlH,OAAA;0BAAKgF,SAAS,EAAC,8BAA8B;0BAAAkC,QAAA,gBAC3ClH,OAAA;4BAAIgF,SAAS,EAAC,uCAAuC;4BAAAkC,QAAA,EAAEkD,QAAQ,CAACxF;0BAAI;4BAAAuC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,EACzEgD,aAAa,iBACZtK,OAAA;4BAAKgF,SAAS,EAAC,mEAAmE;4BAAAkC,QAAA,EAAC;0BAEnF;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACNtH,OAAA;0BAAKgF,SAAS,EAAG,6DAA4DoF,QAAQ,CAAC1H,IAAI,CAACZ,KAAM,8CAA8C;0BAAAoF,QAAA,gBAC7IlH,OAAA,CAACoK,QAAQ,CAAC1H,IAAI,CAACV,IAAI;4BAACgD,SAAS,EAAC;0BAAS;4BAAAmC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACzC8C,QAAQ,CAAC1H,IAAI,CAACT,KAAK;wBAAA;0BAAAkF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNtH,OAAA;wBAAKgF,SAAS,EAAC,0BAA0B;wBAAAkC,QAAA,gBACvClH,OAAA;0BAAKgF,SAAS,EAAC,mCAAmC;0BAAAkC,QAAA,GAC/CkD,QAAQ,CAAC7F,OAAO,CAAC+E,cAAc,CAAC,CAAC,EAAC,KACrC;wBAAA;0BAAAnC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACNtH,OAAA;0BAAKgF,SAAS,EAAC,+CAA+C;0BAAAkC,QAAA,gBAC5DlH,OAAA;4BAAMgF,SAAS,EAAC,yBAAyB;4BAAAkC,QAAA,gBACvClH,OAAA,CAAChB,OAAO;8BAACgG,SAAS,EAAC;4BAAS;8BAAAmC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC9B8C,QAAQ,CAACtG,iBAAiB;0BAAA;4BAAAqD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB,CAAC,eACPtH,OAAA;4BAAMgF,SAAS,EAAC,yBAAyB;4BAAAkC,QAAA,gBACvClH,OAAA,CAAClB,OAAO;8BAACkG,SAAS,EAAC;4BAAyB;8BAAAmC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC9C8C,QAAQ,CAAChF,aAAa;0BAAA;4BAAA+B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAjED8C,QAAQ,CAAC3F,GAAG;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkEP,CAAC;cAEjB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,EAGA3G,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrCX,OAAA,CAACzB,MAAM,CAACmJ,GAAG;YACTc,OAAO,EAAE;cAAEV,OAAO,EAAE,CAAC;cAAEY,KAAK,EAAE;YAAI,CAAE;YACpCf,OAAO,EAAE;cAAEG,OAAO,EAAE,CAAC;cAAEY,KAAK,EAAE;YAAE,CAAE;YAClCX,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE;YAAI,CAAE;YAC1ChD,SAAS,EAAC,wIAAwI;YAAAkC,QAAA,eAElJlH,OAAA;cAAKgF,SAAS,EAAC,aAAa;cAAAkC,QAAA,gBAC1BlH,OAAA;gBAAIgF,SAAS,EAAC,oCAAoC;gBAAAkC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7EtH,OAAA;gBAAKgF,SAAS,EAAC,0CAA0C;gBAAAkC,QAAA,GAAC,GAAC,EAACvG,eAAe;cAAA;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClFtH,OAAA;gBAAGgF,SAAS,EAAC,uBAAuB;gBAAAkC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAGDtH,OAAA,CAACzB,MAAM,CAACmJ,GAAG;YACTc,OAAO,EAAE;cAAEV,OAAO,EAAE,CAAC;cAAEF,CAAC,EAAE;YAAG,CAAE;YAC/BD,OAAO,EAAE;cAAEG,OAAO,EAAE,CAAC;cAAEF,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEI,KAAK,EAAE,CAAC;cAAEH,QAAQ,EAAE;YAAI,CAAE;YACxChD,SAAS,EAAC,mBAAmB;YAAAkC,QAAA,eAE7BlH,OAAA;cAAKgF,SAAS,EAAC,8HAA8H;cAAAkC,QAAA,gBAC3IlH,OAAA,CAACzB,MAAM,CAACmJ,GAAG;gBACTC,OAAO,EAAE;kBAAEe,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;gBAAE,CAAE;gBACjCX,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEC,MAAM,EAAEC;gBAAS,CAAE;gBAAAhB,QAAA,eAE9ClH,OAAA,CAACV,QAAQ;kBAAC0F,SAAS,EAAC;gBAAwC;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACbtH,OAAA;gBAAIgF,SAAS,EAAC,oCAAoC;gBAAAkC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7EtH,OAAA;gBAAGgF,SAAS,EAAC,8CAA8C;gBAAAkC,QAAA,EAAC;cAG5D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJtH,OAAA,CAACzB,MAAM,CAACwL,MAAM;gBACZP,UAAU,EAAE;kBAAEd,KAAK,EAAE;gBAAK,CAAE;gBAC5BsB,QAAQ,EAAE;kBAAEtB,KAAK,EAAE;gBAAK,CAAE;gBAC1B1D,SAAS,EAAC,sJAAsJ;gBAChKiF,OAAO,EAAEA,CAAA,KAAMW,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAa;gBAAA5D,QAAA,EACpD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EAGZd,YAAY,CAAC3C,MAAM,KAAK,CAAC,IAAI,CAACpD,OAAO,iBACpCT,OAAA,CAACzB,MAAM,CAACmJ,GAAG;YACTc,OAAO,EAAE;cAAEV,OAAO,EAAE,CAAC;cAAEY,KAAK,EAAE;YAAI,CAAE;YACpCf,OAAO,EAAE;cAAEG,OAAO,EAAE,CAAC;cAAEY,KAAK,EAAE;YAAE,CAAE;YAClC1D,SAAS,EAAC,mBAAmB;YAAAkC,QAAA,gBAE7BlH,OAAA,CAACf,QAAQ;cAAC+F,SAAS,EAAC;YAAsC;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7DtH,OAAA;cAAIgF,SAAS,EAAC,oCAAoC;cAAAkC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1EtH,OAAA;cAAGgF,SAAS,EAAC,uBAAuB;cAAAkC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpH,EAAA,CA7xBID,kBAAkB;EAAA,QACJxB,WAAW;AAAA;AAAAsM,EAAA,GADzB9K,kBAAkB;AA+xBxB,eAAeA,kBAAkB;AAAC,IAAA8K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}