{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbTarget, TbBrain, TbHome, TbRefresh, TbMedal, TbBolt, TbRocket, TbDiamond, TbHeart, TbEye, TbUsers, TbTrendingUp, TbAward, TbShield } from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const user = userState.user || null;\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Performance tiers with SPECTACULAR visual themes and unique colors\n  const performanceTiers = {\n    legendary: {\n      min: 10000,\n      color: 'from-purple-400 via-pink-400 via-red-400 to-orange-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-pink-900/40 to-red-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FF69B4',\n      shadowColor: 'rgba(147, 51, 234, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery',\n      borderColor: '#8B5CF6',\n      effect: 'legendary-sparkle'\n    },\n    diamond: {\n      min: 7500,\n      color: 'from-cyan-200 via-blue-300 via-indigo-400 to-purple-500',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00E5FF',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 229, 255, 0.9)',\n      glow: 'shadow-cyan-300/80',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance',\n      borderColor: '#00E5FF',\n      effect: 'diamond-shine'\n    },\n    platinum: {\n      min: 5000,\n      color: 'from-slate-200 via-gray-300 via-zinc-400 to-stone-500',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#E8E8E8',\n      nameColor: '#C0C0C0',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-300/80',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam'\n    },\n    gold: {\n      min: 2500,\n      color: 'from-yellow-200 via-amber-300 via-orange-400 to-red-500',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-300/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent',\n      borderColor: '#FFD700',\n      effect: 'gold-glow'\n    },\n    silver: {\n      min: 1000,\n      color: 'from-gray-200 via-slate-300 via-zinc-400 to-gray-500',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#D3D3D3',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(211, 211, 211, 0.9)',\n      glow: 'shadow-gray-300/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress',\n      borderColor: '#D3D3D3',\n      effect: 'silver-shimmer'\n    },\n    bronze: {\n      min: 0,\n      color: 'from-orange-200 via-amber-300 via-yellow-400 to-orange-500',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-300/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = xp => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return {\n        tier,\n        ...config\n      };\n    }\n    return {\n      tier: 'bronze',\n      ...performanceTiers.bronze\n    };\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: (user === null || user === void 0 ? void 0 : user.level) || 'all',\n          includeInactive: false\n        });\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData => userData.totalXP && userData.totalXP > 0 || userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0);\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id));\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n      let rankingResponse, usersResponse;\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n      let transformedData = [];\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            var _item$user;\n            const userId = ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n        transformedData = usersResponse.data.filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n        .map((userData, index) => {\n          // Get reports for this user\n          const userReports = userReportsMap[userData._id] || [];\n\n          // Use existing user data or calculate from reports\n          let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n          let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n          let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n          // For existing users with old data, make intelligent assumptions\n          if (!userReports.length && userData.totalPoints) {\n            // Assume higher points = more exams and better performance\n            const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n            const estimatedAverage = Math.min(95, Math.max(60, 60 + userData.totalPoints / estimatedQuizzes / 10)); // Scale average based on points\n\n            totalQuizzes = estimatedQuizzes;\n            averageScore = Math.round(estimatedAverage);\n            totalScore = Math.round(averageScore * totalQuizzes);\n            console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n          }\n\n          // Calculate XP based on performance (enhanced calculation)\n          let totalXP = userData.totalXP || 0;\n          if (!totalXP) {\n            // Calculate XP from available data\n            if (userData.totalPoints) {\n              // Use existing points as base XP with bonuses\n              totalXP = Math.floor(userData.totalPoints +\n              // Base points\n              totalQuizzes * 25 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 15 : 0) + (\n              // Excellence bonus\n              averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n              );\n            } else if (totalQuizzes > 0) {\n              // Calculate from quiz performance\n              totalXP = Math.floor(averageScore * totalQuizzes * 8 +\n              // Base XP from scores\n              totalQuizzes * 40 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n              );\n            }\n          }\n\n          // Calculate streaks (enhanced logic)\n          let currentStreak = userData.currentStreak || 0;\n          let bestStreak = userData.bestStreak || 0;\n          if (userReports.length > 0) {\n            // Calculate from actual reports\n            let tempStreak = 0;\n            userReports.forEach(report => {\n              if (report.score >= 60) {\n                // Passing score\n                tempStreak++;\n                bestStreak = Math.max(bestStreak, tempStreak);\n              } else {\n                tempStreak = 0;\n              }\n            });\n            currentStreak = tempStreak;\n          } else if (userData.totalPoints && !currentStreak) {\n            // Estimate streaks from points (higher points = likely better streaks)\n            const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n            if (pointsPerQuiz > 80) {\n              currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n              bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n            }\n          }\n\n          return {\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: currentStreak,\n            bestStreak: bestStreak,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(totalXP),\n            isRealUser: true,\n            // Additional tracking fields for future updates\n            originalPoints: userData.totalPoints || 0,\n            hasReports: userReports.length > 0,\n            dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user === null || user === void 0 ? void 0 : user.name,\n          userId: user === null || user === void 0 ? void 0 : user._id,\n          userIdType: typeof (user === null || user === void 0 ? void 0 : user._id),\n          isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin),\n          userXP: user === null || user === void 0 ? void 0 : user.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({\n            id: u._id,\n            type: typeof u._id,\n            name: u.name\n          })),\n          exactMatch: transformedData.find(item => item._id === (user === null || user === void 0 ? void 0 : user._id)),\n          stringMatch: transformedData.find(item => String(item._id) === String(user === null || user === void 0 ? void 0 : user._id)),\n          nameMatch: transformedData.find(item => item.name === (user === null || user === void 0 ? void 0 : user.name))\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh ranking data every 30 seconds for real-time updates\n    const refreshTimer = setInterval(() => {\n      console.log('🔄 Auto-refreshing ranking data...');\n      fetchRankingData();\n    }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData();\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = event => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n      // Immediate refresh after quiz completion\n      setTimeout(() => {\n        fetchRankingData();\n      }, 1000); // Small delay to ensure server has processed the update\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    return () => {\n      clearInterval(animationTimer);\n      clearInterval(refreshTimer);\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Find Me functionality\n  const handleFindMe = () => {\n    console.log('🎯 Find Me clicked!');\n    console.log('Current user:', user === null || user === void 0 ? void 0 : user.name, user === null || user === void 0 ? void 0 : user._id);\n    console.log('Ranking data length:', rankingData.length);\n\n    // Check if user is in the ranking data first\n    const userInRanking = rankingData.find(u => u._id === (user === null || user === void 0 ? void 0 : user._id));\n    console.log('User found in ranking:', userInRanking ? 'Yes' : 'No');\n    if (!userInRanking) {\n      // Check if user is admin\n      if ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || user !== null && user !== void 0 && user.isAdmin) {\n        message.info('🔧 Admin users are excluded from rankings. Switch to a student account to see your ranking position!');\n      } else if ((user === null || user === void 0 ? void 0 : user.totalXP) === 0 || !(user !== null && user !== void 0 && user.totalXP)) {\n        message.info('🎯 You haven\\'t earned any XP yet. Complete some quizzes to appear on the leaderboard!');\n      } else {\n        message.info('🔄 You are not yet ranked. Your ranking may be updating - try refreshing the page!');\n      }\n      return;\n    }\n    const userRank = rankingData.indexOf(userInRanking) + 1;\n    console.log('User rank:', userRank);\n\n    // Determine which ref to use based on user's position\n    let targetRef = null;\n    if (userRank <= 3) {\n      // User is in top 3 podium\n      targetRef = podiumUserRef.current;\n      console.log('Using podium ref:', targetRef ? 'Found' : 'Not found');\n    } else {\n      // User is in the main list\n      targetRef = listUserRef.current;\n      console.log('Using list ref:', targetRef ? 'Found' : 'Not found');\n    }\n    if (targetRef) {\n      console.log('✅ Target ref found, scrolling...');\n      setShowFindMe(true);\n      targetRef.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n\n      // Show success message\n      message.success(`Found you at rank #${userRank}! 🎯`);\n\n      // Hide the find me highlight after 3 seconds\n      setTimeout(() => {\n        setShowFindMe(false);\n      }, 3000);\n    } else {\n      console.log('❌ Target ref not found, trying DOM query fallback...');\n\n      // Fallback: Try to find user element by data attribute or class\n      let userElement = null;\n\n      // Try to find by user ID in the DOM\n      const allUserElements = document.querySelectorAll('[data-user-id]');\n      console.log('Found user elements:', allUserElements.length);\n      for (let element of allUserElements) {\n        if (element.getAttribute('data-user-id') === user._id) {\n          userElement = element;\n          console.log('✅ Found user element via DOM query');\n          break;\n        }\n      }\n      if (userElement) {\n        setShowFindMe(true);\n        userElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center'\n        });\n        message.success(`Found you at rank #${userRank}! 🎯`);\n\n        // Add highlight class directly\n        userElement.classList.add('find-me-highlight');\n        setTimeout(() => {\n          userElement.classList.remove('find-me-highlight');\n          setShowFindMe(false);\n        }, 4500);\n      } else {\n        console.log('❌ DOM query fallback also failed');\n        // Final fallback: scroll to approximate position\n        message.info(`You are ranked #${userRank} in the leaderboard!`);\n\n        // If user is not in top 3, scroll to the main ranking section\n        if (userRank > 3) {\n          const mainRankingSection = document.querySelector('.main-ranking-section');\n          if (mainRankingSection) {\n            console.log('📍 Scrolling to main ranking section');\n            mainRankingSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start'\n            });\n          }\n        }\n      }\n    }\n  };\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981',\n          // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444',\n          // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444',\n        // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading the Hall of Champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 630,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 629,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .find-me-highlight {\n          animation: findMePulse 1.5s ease-in-out 3;\n          border: 3px solid #FFD700 !important;\n          background: linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.2)) !important;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 0 15px rgba(255, 215, 0, 0), 0 0 30px rgba(255, 215, 0, 0.8);\n            transform: scale(1.02);\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 648,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 668,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n        children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n          animate: {\n            y: [0, -100, 0],\n            x: [0, Math.random() * 100 - 50, 0],\n            opacity: [0.2, 0.8, 0.2]\n          },\n          transition: {\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            delay: Math.random() * 2\n          },\n          style: {\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl p-3 sm:p-4 border border-white/10\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => navigate('/user/hub'),\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbHome, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Hub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 721,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: handleFindMe,\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-8 py-3 md:py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-black rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                    color: '#000000',\n                    textShadow: 'none',\n                    fontWeight: '900',\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: currentUserRank ? `Find Me #${currentUserRank}` : (user === null || user === void 0 ? void 0 : user.role) === 'admin' || user !== null && user !== void 0 && user.isAdmin ? 'Admin View' : 'Find Me'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 739,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => {\n                    console.log('🔍 Enhanced Debug Info:');\n                    console.log('Current user object:', user);\n                    console.log('User ID:', user === null || user === void 0 ? void 0 : user._id);\n                    console.log('User name:', user === null || user === void 0 ? void 0 : user.name);\n                    console.log('User isAdmin:', user === null || user === void 0 ? void 0 : user.isAdmin);\n                    console.log('User role:', user === null || user === void 0 ? void 0 : user.role);\n                    console.log('Ranking data length:', rankingData.length);\n                    console.log('First 5 users in ranking:', rankingData.slice(0, 5).map(u => ({\n                      id: u._id,\n                      name: u.name,\n                      rank: u.rank,\n                      totalXP: u.totalXP\n                    })));\n                    console.log('All user IDs in ranking:', rankingData.map(u => u._id));\n                    console.log('Looking for user ID:', user === null || user === void 0 ? void 0 : user._id);\n                    console.log('User found in ranking:', rankingData.find(u => u._id === (user === null || user === void 0 ? void 0 : user._id)));\n                    console.log('podiumUserRef.current:', podiumUserRef.current);\n                    console.log('listUserRef.current:', listUserRef.current);\n                    console.log('currentUserRank:', currentUserRank);\n\n                    // Test DOM query\n                    const userElements = document.querySelectorAll(`[data-user-id=\"${user === null || user === void 0 ? void 0 : user._id}\"]`);\n                    console.log('User elements found:', userElements.length);\n                    userElements.forEach((el, i) => {\n                      console.log(`Element ${i}:`, el, 'rank:', el.getAttribute('data-user-rank'));\n                    });\n                  },\n                  className: \"px-3 py-2 bg-purple-600 text-white rounded-lg text-sm\",\n                  children: \"\\uD83D\\uDD0D Debug\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05,\n                    rotate: 180\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: fetchRankingData,\n                  disabled: loading,\n                  className: \"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                    className: `w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 793,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 700,\n          columnNumber: 9\n        }, this), ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin)) && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold text-sm\",\n                    children: \"\\uD83D\\uDC51\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-bold text-white\",\n                    children: \"Admin View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 816,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-white/80\",\n                    children: \"You're viewing as an admin. Admin accounts are excluded from student rankings.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 817,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 803,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            ease: \"easeOut\"\n          },\n          className: \"relative overflow-hidden mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-purple-600 via-pink-500 via-red-500 via-orange-500 to-yellow-500 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-16\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-7xl mx-auto text-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      },\n                      transition: {\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      },\n                      className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                      style: {\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      },\n                      children: \"HALL OF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 857,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 876,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        textShadow: ['0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)', '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)', '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)']\n                      },\n                      transition: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      style: {\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      },\n                      children: \"CHAMPIONS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 877,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 844,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 0.8\n                  },\n                  className: \"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\",\n                  style: {\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  },\n                  children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 902,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 0.8\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\",\n                    style: {\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontStyle: 'italic'\n                    },\n                    children: motivationalQuote\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 925,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 30\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 1,\n                    duration: 0.8\n                  },\n                  className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\",\n                  children: [{\n                    icon: TbUsers,\n                    value: rankingData.length,\n                    label: 'Champions',\n                    bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                    iconColor: '#60A5FA',\n                    borderColor: '#3B82F6'\n                  }, {\n                    icon: TbTrophy,\n                    value: topPerformers.length,\n                    label: 'Top Performers',\n                    bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                    iconColor: '#FBBF24',\n                    borderColor: '#F59E0B'\n                  }, {\n                    icon: TbFlame,\n                    value: rankingData.filter(u => u.currentStreak > 0).length,\n                    label: 'Active Streaks',\n                    bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                    iconColor: '#F87171',\n                    borderColor: '#EF4444'\n                  }, {\n                    icon: TbStar,\n                    value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                    label: 'Total XP',\n                    bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                    iconColor: '#34D399',\n                    borderColor: '#10B981'\n                  }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    transition: {\n                      delay: 1.2 + index * 0.1,\n                      duration: 0.6\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -5\n                    },\n                    className: `bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`,\n                    style: {\n                      border: `2px solid ${stat.borderColor}40`,\n                      boxShadow: `0 8px 32px ${stat.borderColor}20`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 987,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                      className: \"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 988,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                        filter: 'drop-shadow(0 0 10px currentColor)',\n                        fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                      },\n                      children: stat.value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 992,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs sm:text-sm font-bold relative z-10\",\n                      style: {\n                        color: '#FFFFFF',\n                        textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                        fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                      },\n                      children: stat.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1003,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 975,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 935,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 841,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 835,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 9\n        }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          className: \"flex flex-col items-center justify-center py-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: 360\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1028,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white/80 text-lg font-medium\",\n            children: \"Loading champions...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1033,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1023,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.3,\n            duration: 0.8\n          },\n          className: \"px-4 sm:px-6 lg:px-8 pb-20\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 0.5,\n                duration: 0.8\n              },\n              className: \"mb-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl font-black text-center mb-6 md:mb-8 px-4\",\n                style: {\n                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  filter: 'drop-shadow(0 0 15px #FFD700)'\n                },\n                children: \"\\uD83C\\uDFC6 CHAMPIONS PODIUM \\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1055,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 md:gap-6 max-w-6xl mx-auto px-2 sm:px-4\",\n                children: topPerformers.map((champion, index) => {\n                  const position = index + 1;\n                  const isCurrentUser = user && champion._id === user._id;\n                  return /*#__PURE__*/_jsxDEV(motion.div, {\n                    ref: isCurrentUser ? podiumUserRef : null,\n                    \"data-user-id\": champion._id,\n                    \"data-user-rank\": position,\n                    initial: {\n                      opacity: 0,\n                      y: 50\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    transition: {\n                      delay: 0.7 + index * 0.2,\n                      duration: 0.8\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -10\n                    },\n                    className: `relative ${position === 1 ? 'md:order-2 md:scale-110' : position === 2 ? 'md:order-1' : 'md:order-3'} ${isCurrentUser ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow} ${champion.tier.effect} shadow-xl`,\n                      style: {\n                        boxShadow: `0 12px 24px ${champion.tier.shadowColor}, 0 0 30px ${champion.tier.shadowColor}`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-3 sm:p-4 text-center relative overflow-hidden`,\n                        style: {\n                          border: `2px solid ${champion.tier.borderColor}60`\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1098,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `absolute -top-3 left-1/2 transform -translate-x-1/2 w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-20`,\n                          style: {\n                            color: position === 1 ? '#FFD700' : position === 2 ? '#C0C0C0' : position === 3 ? '#CD7F32' : '#FFFFFF',\n                            textShadow: '1px 1px 2px rgba(0,0,0,0.9)',\n                            border: `2px solid ${champion.tier.borderColor}`,\n                            boxShadow: `0 4px 8px ${champion.tier.shadowColor}, 0 0 12px ${champion.tier.shadowColor}`,\n                            fontSize: 'clamp(0.75rem, 2vw, 1rem)',\n                            fontWeight: '900'\n                          },\n                          children: position === 1 ? '👑' : position === 2 ? '🥈' : position === 3 ? '🥉' : position\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1101,\n                          columnNumber: 31\n                        }, this), position === 1 && /*#__PURE__*/_jsxDEV(motion.div, {\n                          animate: {\n                            rotate: [0, 10, -10, 0]\n                          },\n                          transition: {\n                            duration: 2,\n                            repeat: Infinity\n                          },\n                          className: \"absolute -top-8 left-1/2 transform -translate-x-1/2\",\n                          children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                            className: \"w-8 h-8 text-yellow-400\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1122,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1117,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `relative mx-auto mb-2 ${isCurrentUser ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-6 h-6 sm:w-8 sm:h-8 rounded-full overflow-hidden mx-auto relative\",\n                            style: {\n                              background: `linear-gradient(45deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                              boxShadow: `0 1px 3px ${champion.tier.shadowColor}, 0 0 4px ${champion.tier.shadowColor}`,\n                              padding: '1px'\n                            },\n                            children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                              src: champion.profilePicture,\n                              alt: champion.name,\n                              className: \"w-full h-full object-cover rounded-full\",\n                              style: {\n                                filter: 'brightness(1.1) contrast(1.1)',\n                                aspectRatio: '1/1'\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1137,\n                              columnNumber: 37\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"w-full h-full rounded-full flex items-center justify-center font-black text-2xl\",\n                              style: {\n                                background: `linear-gradient(135deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                aspectRatio: '1/1'\n                              },\n                              children: champion.name.charAt(0).toUpperCase()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1147,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1128,\n                            columnNumber: 33\n                          }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\",\n                            style: {\n                              background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                              boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                            },\n                            children: /*#__PURE__*/_jsxDEV(TbStar, {\n                              className: \"w-5 h-5 text-black\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1168,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1161,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1127,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-xl font-black mb-2 relative z-10\",\n                          style: {\n                            color: champion.tier.nameColor,\n                            textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                            fontSize: '1.5rem',\n                            filter: 'drop-shadow(0 0 10px currentColor)'\n                          },\n                          children: champion.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1174,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `inline-flex items-center gap-2 px-5 py-3 bg-gradient-to-r ${champion.tier.color} rounded-full text-sm font-black mb-3 relative z-10 animate-pulse`,\n                          style: {\n                            color: '#FFFFFF',\n                            textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                            border: `3px solid ${champion.tier.borderColor}`,\n                            boxShadow: `0 6px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                            fontSize: '0.9rem',\n                            letterSpacing: '0.5px'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                            className: \"w-6 h-6\",\n                            style: {\n                              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))',\n                              color: champion.tier.textColor\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1196,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#FFFFFF'\n                            },\n                            children: champion.tier.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1203,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1185,\n                          columnNumber: 31\n                        }, this), (() => {\n                          const badge = getSubscriptionBadge(champion.subscriptionStatus, champion.subscriptionEndDate, champion.subscriptionPlan, champion.activePlanTitle, index);\n                          return /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-bold mb-3 relative z-10\",\n                            style: {\n                              backgroundColor: badge.bgColor,\n                              color: badge.color,\n                              border: `2px solid ${badge.borderColor}`,\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                              fontSize: '0.75rem',\n                              letterSpacing: '0.5px'\n                            },\n                            children: badge.text\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1216,\n                            columnNumber: 35\n                          }, this);\n                        })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"space-y-3 relative z-10\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex justify-between text-base\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.textColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '700',\n                                fontSize: '1rem'\n                              },\n                              children: \"\\uD83D\\uDC8E XP:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1235,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '900',\n                                fontSize: '1.1rem',\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              },\n                              children: champion.totalXP.toLocaleString()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1241,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1234,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex justify-between text-base\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.textColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '700',\n                                fontSize: '1rem'\n                              },\n                              children: \"\\uD83E\\uDDE0 Quizzes:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1250,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '900',\n                                fontSize: '1.1rem',\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              },\n                              children: champion.totalQuizzesTaken\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1256,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1249,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex justify-between text-base\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.textColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '700',\n                                fontSize: '1rem'\n                              },\n                              children: \"\\uD83D\\uDD25 Streak:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1265,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '900',\n                                fontSize: '1.1rem',\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              },\n                              className: \"flex items-center gap-1\",\n                              children: champion.currentStreak\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1271,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1264,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center mt-2\",\n                            children: /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.textColor,\n                                textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                fontWeight: '600',\n                                fontSize: '0.7rem',\n                                opacity: 0.8\n                              },\n                              children: champion.dataSource === 'reports' ? '📊 Live Data' : champion.dataSource === 'legacy_points' ? '📈 Legacy Points' : '🔮 Estimated'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1284,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1283,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1233,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1092,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1086,\n                      columnNumber: 27\n                    }, this)\n                  }, champion._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1071,\n                    columnNumber: 25\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1049,\n              columnNumber: 17\n            }, this), otherPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-12 main-ranking-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl sm:text-2xl md:text-3xl font-black text-center mb-6 md:mb-8 px-4\",\n                style: {\n                  background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                },\n                children: \"\\u26A1 RISING CHAMPIONS \\u26A1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3 md:space-y-4 px-2 md:px-0\",\n                children: otherPerformers.map((champion, index) => {\n                  const actualRank = index + 4; // Since top 3 are shown separately\n                  const isCurrentUser = user && champion._id === user._id;\n                  return /*#__PURE__*/_jsxDEV(motion.div, {\n                    ref: isCurrentUser ? listUserRef : null,\n                    \"data-user-id\": champion._id,\n                    \"data-user-rank\": actualRank,\n                    initial: {\n                      opacity: 0,\n                      x: -50\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0\n                    },\n                    transition: {\n                      delay: 1.2 + index * 0.1,\n                      duration: 0.6\n                    },\n                    whileHover: {\n                      scale: 1.02,\n                      x: 10\n                    },\n                    className: `relative ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow} ${champion.tier.effect}`,\n                      style: {\n                        boxShadow: `0 8px 24px ${champion.tier.shadowColor}`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-3 md:p-4 flex items-center gap-3 md:gap-4 relative overflow-hidden`,\n                        style: {\n                          border: `1px solid ${champion.tier.borderColor}40`\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute inset-0 bg-gradient-to-r from-white/5 to-transparent\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1353,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-xs shadow-lg relative z-10`,\n                          style: {\n                            color: actualRank <= 10 ? champion.tier.textColor : '#FFFFFF',\n                            textShadow: '3px 3px 6px rgba(0,0,0,0.9)',\n                            border: `3px solid ${champion.tier.borderColor}`,\n                            boxShadow: `0 8px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                            fontSize: actualRank <= 10 ? '1.3rem' : '1.1rem',\n                            fontWeight: '900'\n                          },\n                          children: actualRank <= 10 ? `#${actualRank}` : actualRank\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1356,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 relative\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-6 h-6 sm:w-8 sm:h-8 rounded-full overflow-hidden p-0.5 relative\",\n                            style: {\n                              background: `linear-gradient(45deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                              boxShadow: `0 1px 3px ${champion.tier.shadowColor}, 0 0 4px ${champion.tier.shadowColor}`\n                            },\n                            children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                              src: champion.profilePicture,\n                              alt: champion.name,\n                              className: \"w-full h-full object-cover rounded-full\",\n                              style: {\n                                filter: 'brightness(1.1) contrast(1.1) saturate(1.2)',\n                                aspectRatio: '1/1'\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1380,\n                              columnNumber: 37\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"w-full h-full rounded-full flex items-center justify-center font-black text-xs\",\n                              style: {\n                                background: `linear-gradient(135deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                aspectRatio: '1/1'\n                              },\n                              children: champion.name.charAt(0).toUpperCase()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1390,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1372,\n                            columnNumber: 33\n                          }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute -top-1 -right-1 rounded-full p-1 animate-pulse\",\n                            style: {\n                              background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                              boxShadow: '0 2px 8px rgba(255,215,0,0.6)'\n                            },\n                            children: /*#__PURE__*/_jsxDEV(TbStar, {\n                              className: \"w-3 h-3 text-black\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1411,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1404,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1371,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 min-w-0 relative z-10\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center gap-2 mb-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                              className: \"text-sm sm:text-base md:text-lg font-black truncate\",\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontSize: 'clamp(0.875rem, 3vw, 1.25rem)',\n                                filter: 'drop-shadow(0 0 6px currentColor)'\n                              },\n                              children: champion.name\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1419,\n                              columnNumber: 35\n                            }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"px-3 py-1 rounded-full text-xs font-black animate-pulse\",\n                              style: {\n                                background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                color: '#000000',\n                                textShadow: 'none',\n                                border: '2px solid #FFFFFF',\n                                boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                              },\n                              children: \"\\u2B50 YOU \\u2B50\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1431,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1418,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${champion.tier.color} rounded-full text-xs font-black`,\n                            style: {\n                              color: '#FFFFFF',\n                              textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                              border: `2px solid ${champion.tier.borderColor}`,\n                              boxShadow: `0 4px 8px ${champion.tier.shadowColor}, 0 0 12px ${champion.tier.shadowColor}`,\n                              fontSize: '0.8rem',\n                              letterSpacing: '0.3px'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                              className: \"w-4 h-4\",\n                              style: {\n                                filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.5))',\n                                color: champion.tier.textColor\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1456,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: '#FFFFFF'\n                              },\n                              children: champion.tier.title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1463,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1445,\n                            columnNumber: 33\n                          }, this), (() => {\n                            const badge = getSubscriptionBadge(champion.subscriptionStatus, champion.subscriptionEndDate, champion.subscriptionPlan, champion.activePlanTitle, actualRank);\n                            return /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold mt-1\",\n                              style: {\n                                backgroundColor: badge.bgColor,\n                                color: badge.color,\n                                border: `1px solid ${badge.borderColor}`,\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                                fontSize: '0.7rem',\n                                letterSpacing: '0.3px'\n                              },\n                              children: badge.text\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1476,\n                              columnNumber: 37\n                            }, this);\n                          })()]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1417,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 text-right relative z-10\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xl mb-2\",\n                            style: {\n                              color: champion.tier.nameColor,\n                              textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                              fontWeight: '900',\n                              filter: 'drop-shadow(0 0 10px currentColor)',\n                              fontSize: '1.3rem'\n                            },\n                            children: [\"\\uD83D\\uDC8E \", champion.totalXP.toLocaleString(), \" XP\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1495,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center gap-3 text-sm justify-end\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"flex items-center gap-1\",\n                              style: {\n                                color: champion.tier.textColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '700',\n                                fontSize: '0.9rem'\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: champion.tier.textColor\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1511,\n                                columnNumber: 37\n                              }, this), champion.totalQuizzesTaken]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1505,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"flex items-center gap-1\",\n                              style: {\n                                color: champion.tier.textColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '700',\n                                fontSize: '0.9rem'\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: '#FF6B35'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1520,\n                                columnNumber: 37\n                              }, this), champion.currentStreak]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1514,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1504,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1494,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1347,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1341,\n                      columnNumber: 27\n                    }, this)\n                  }, champion._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1330,\n                    columnNumber: 25\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1324,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1308,\n              columnNumber: 17\n            }, this), rankingData.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1.8,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold mb-4\",\n                  style: {\n                    color: '#60A5FA',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"\\uD83D\\uDCCA Real User Data Integration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1543,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'reports').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1550,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCCA Live Quiz Data\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1553,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1549,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'legacy_points').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1556,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCC8 Legacy Points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1559,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1555,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'estimated').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1562,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDD2E Estimated Stats\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1565,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1561,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1548,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm mt-4\",\n                  children: \"Using real database users (admins excluded) with intelligent data processing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1568,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1542,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1536,\n              columnNumber: 17\n            }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 1.5,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold mb-2\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Your Current Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1584,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl font-black mb-2\",\n                  style: {\n                    color: '#fbbf24',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    fontWeight: '900'\n                  },\n                  children: [\"#\", currentUserRank]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1589,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1594,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1583,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1577,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 2,\n                duration: 0.8\n              },\n              className: \"mt-16 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.05, 1]\n                  },\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                    className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1617,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1613,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-3xl font-bold mb-4\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Ready to Rise Higher?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1619,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl mb-6 max-w-2xl mx-auto\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1624,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                  onClick: () => window.location.href = '/user/quiz',\n                  children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1632,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1612,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1606,\n              columnNumber: 15\n            }, this), rankingData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-20\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1650,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"No Champions Yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1651,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Be the first to take a quiz and claim your spot in the Hall of Champions!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1656,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1645,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1045,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1039,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 666,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AmazingRankingPage, \"eziCFNFgjBl7u/MNtZ5ZJJY4XSI=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "useNavigate", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbTarget", "TbBrain", "TbHome", "TbRefresh", "TbMedal", "TbBolt", "TbRocket", "TbDiamond", "TbHeart", "TbEye", "TbUsers", "TbTrendingUp", "TbAward", "TbShield", "getAllReportsForRanking", "getXPLeaderboard", "getUserRanking", "getAllUsers", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AmazingRankingPage", "_s", "userState", "state", "users", "user", "navigate", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "showFindMe", "setShowFindMe", "headerRef", "currentUserRef", "podiumUserRef", "listUserRef", "motivationalQuotes", "performanceTiers", "legendary", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "effect", "diamond", "platinum", "gold", "silver", "bronze", "getUserTier", "xp", "tier", "config", "Object", "entries", "fetchRankingData", "console", "log", "xpLeaderboardResponse", "limit", "levelFilter", "level", "includeInactive", "success", "data", "filteredData", "filter", "userData", "totalXP", "totalQuizzesTaken", "transformedData", "map", "index", "_id", "name", "email", "class", "profilePicture", "profileImage", "averageScore", "currentStreak", "bestStreak", "subscriptionStatus", "rank", "isRealUser", "rankingScore", "currentLevel", "xpToNextLevel", "lifetimeXP", "seasonXP", "achievements", "dataSource", "userRankIndex", "findIndex", "item", "xpError", "rankingResponse", "usersResponse", "error", "userError", "userReportsMap", "for<PERSON>ach", "_item$user", "userId", "reports", "role", "userReports", "totalQuizzes", "length", "totalScore", "reduce", "sum", "report", "score", "Math", "round", "totalPoints", "estimatedQuizzes", "max", "floor", "estimatedAverage", "tempStreak", "pointsPerQuiz", "originalPoints", "hasReports", "sort", "a", "b", "userRank", "String", "currentUser", "userIdType", "isAdmin", "userXP", "userRankPosition", "totalRankedUsers", "firstFewUserIds", "slice", "u", "id", "type", "exactMatch", "find", "stringMatch", "nameMatch", "dataSources", "legacy_points", "estimated", "quizzes", "avg", "source", "warning", "randomQuote", "random", "animationTimer", "setInterval", "prev", "refreshTimer", "handleWindowFocus", "handleRankingUpdate", "event", "detail", "setTimeout", "window", "addEventListener", "clearInterval", "removeEventListener", "topPerformers", "otherPerformers", "handleFindMe", "userInRanking", "info", "indexOf", "targetRef", "current", "scrollIntoView", "behavior", "block", "userElement", "allUserElements", "document", "querySelectorAll", "element", "getAttribute", "classList", "add", "remove", "mainRankingSection", "querySelector", "getSubscriptionBadge", "subscriptionEndDate", "subscriptionPlan", "activePlanTitle", "userIndex", "now", "Date", "endDate", "isActive", "text", "className", "children", "div", "initial", "opacity", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jsx", "Array", "_", "i", "y", "x", "delay", "style", "left", "top", "button", "whileHover", "scale", "whileTap", "onClick", "fontSize", "innerWidth", "background", "textShadow", "fontWeight", "userElements", "el", "disabled", "rotateY", "span", "backgroundPosition", "backgroundSize", "WebkitBackgroundClip", "WebkitTextFillColor", "p", "fontStyle", "value", "label", "bgGradient", "iconColor", "toLocaleString", "stat", "border", "boxShadow", "champion", "position", "isCurrentUser", "ref", "padding", "src", "alt", "aspectRatio", "char<PERSON>t", "toUpperCase", "letterSpacing", "badge", "backgroundColor", "actualRank", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbTarget,\n  TbBrain,\n  TbHome,\n  TbRefresh,\n  TbMedal,\n  TbBolt,\n  TbRocket,\n  TbDiamond,\n  TbHeart,\n  TbEye,\n  TbUsers,\n  TbTrendingUp,\n  TbAward,\n  TbShield\n} from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const user = userState.user || null;\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Performance tiers with SPECTACULAR visual themes and unique colors\n  const performanceTiers = {\n    legendary: {\n      min: 10000,\n      color: 'from-purple-400 via-pink-400 via-red-400 to-orange-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-pink-900/40 to-red-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FF69B4',\n      shadowColor: 'rgba(147, 51, 234, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery',\n      borderColor: '#8B5CF6',\n      effect: 'legendary-sparkle'\n    },\n    diamond: {\n      min: 7500,\n      color: 'from-cyan-200 via-blue-300 via-indigo-400 to-purple-500',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00E5FF',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 229, 255, 0.9)',\n      glow: 'shadow-cyan-300/80',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance',\n      borderColor: '#00E5FF',\n      effect: 'diamond-shine'\n    },\n    platinum: {\n      min: 5000,\n      color: 'from-slate-200 via-gray-300 via-zinc-400 to-stone-500',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#E8E8E8',\n      nameColor: '#C0C0C0',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-300/80',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam'\n    },\n    gold: {\n      min: 2500,\n      color: 'from-yellow-200 via-amber-300 via-orange-400 to-red-500',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-300/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent',\n      borderColor: '#FFD700',\n      effect: 'gold-glow'\n    },\n    silver: {\n      min: 1000,\n      color: 'from-gray-200 via-slate-300 via-zinc-400 to-gray-500',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#D3D3D3',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(211, 211, 211, 0.9)',\n      glow: 'shadow-gray-300/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress',\n      borderColor: '#D3D3D3',\n      effect: 'silver-shimmer'\n    },\n    bronze: {\n      min: 0,\n      color: 'from-orange-200 via-amber-300 via-yellow-400 to-orange-500',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-300/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = (xp) => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return { tier, ...config };\n    }\n    return { tier: 'bronze', ...performanceTiers.bronze };\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: user?.level || 'all',\n          includeInactive: false\n        });\n\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData =>\n            (userData.totalXP && userData.totalXP > 0) ||\n            (userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0)\n          );\n\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === user?._id);\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n\n      let rankingResponse, usersResponse;\n\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Use existing user data or calculate from reports\n            let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n            let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n            // For existing users with old data, make intelligent assumptions\n            if (!userReports.length && userData.totalPoints) {\n              // Assume higher points = more exams and better performance\n              const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n              const estimatedAverage = Math.min(95, Math.max(60, 60 + (userData.totalPoints / estimatedQuizzes / 10))); // Scale average based on points\n\n              totalQuizzes = estimatedQuizzes;\n              averageScore = Math.round(estimatedAverage);\n              totalScore = Math.round(averageScore * totalQuizzes);\n\n              console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n            }\n\n            // Calculate XP based on performance (enhanced calculation)\n            let totalXP = userData.totalXP || 0;\n\n            if (!totalXP) {\n              // Calculate XP from available data\n              if (userData.totalPoints) {\n                // Use existing points as base XP with bonuses\n                totalXP = Math.floor(\n                  userData.totalPoints + // Base points\n                  (totalQuizzes * 25) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 15 : 0) + // Excellence bonus\n                  (averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n                );\n              } else if (totalQuizzes > 0) {\n                // Calculate from quiz performance\n                totalXP = Math.floor(\n                  (averageScore * totalQuizzes * 8) + // Base XP from scores\n                  (totalQuizzes * 40) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n                );\n              }\n            }\n\n            // Calculate streaks (enhanced logic)\n            let currentStreak = userData.currentStreak || 0;\n            let bestStreak = userData.bestStreak || 0;\n\n            if (userReports.length > 0) {\n              // Calculate from actual reports\n              let tempStreak = 0;\n              userReports.forEach(report => {\n                if (report.score >= 60) { // Passing score\n                  tempStreak++;\n                  bestStreak = Math.max(bestStreak, tempStreak);\n                } else {\n                  tempStreak = 0;\n                }\n              });\n              currentStreak = tempStreak;\n            } else if (userData.totalPoints && !currentStreak) {\n              // Estimate streaks from points (higher points = likely better streaks)\n              const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n              if (pointsPerQuiz > 80) {\n                currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n                bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n              }\n            }\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserTier(totalXP),\n              isRealUser: true,\n              // Additional tracking fields for future updates\n              originalPoints: userData.totalPoints || 0,\n              hasReports: userReports.length > 0,\n              dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user?.name,\n          userId: user?._id,\n          userIdType: typeof user?._id,\n          isAdmin: user?.role === 'admin' || user?.isAdmin,\n          userXP: user?.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({ id: u._id, type: typeof u._id, name: u.name })),\n          exactMatch: transformedData.find(item => item._id === user?._id),\n          stringMatch: transformedData.find(item => String(item._id) === String(user?._id)),\n          nameMatch: transformedData.find(item => item.name === user?.name)\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh ranking data every 30 seconds for real-time updates\n    const refreshTimer = setInterval(() => {\n      console.log('🔄 Auto-refreshing ranking data...');\n      fetchRankingData();\n    }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData();\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = (event) => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n      // Immediate refresh after quiz completion\n      setTimeout(() => {\n        fetchRankingData();\n      }, 1000); // Small delay to ensure server has processed the update\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n\n    return () => {\n      clearInterval(animationTimer);\n      clearInterval(refreshTimer);\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Find Me functionality\n  const handleFindMe = () => {\n    console.log('🎯 Find Me clicked!');\n    console.log('Current user:', user?.name, user?._id);\n    console.log('Ranking data length:', rankingData.length);\n\n    // Check if user is in the ranking data first\n    const userInRanking = rankingData.find(u => u._id === user?._id);\n    console.log('User found in ranking:', userInRanking ? 'Yes' : 'No');\n\n    if (!userInRanking) {\n      // Check if user is admin\n      if (user?.role === 'admin' || user?.isAdmin) {\n        message.info('🔧 Admin users are excluded from rankings. Switch to a student account to see your ranking position!');\n      } else if (user?.totalXP === 0 || !user?.totalXP) {\n        message.info('🎯 You haven\\'t earned any XP yet. Complete some quizzes to appear on the leaderboard!');\n      } else {\n        message.info('🔄 You are not yet ranked. Your ranking may be updating - try refreshing the page!');\n      }\n      return;\n    }\n\n    const userRank = rankingData.indexOf(userInRanking) + 1;\n    console.log('User rank:', userRank);\n\n    // Determine which ref to use based on user's position\n    let targetRef = null;\n    if (userRank <= 3) {\n      // User is in top 3 podium\n      targetRef = podiumUserRef.current;\n      console.log('Using podium ref:', targetRef ? 'Found' : 'Not found');\n    } else {\n      // User is in the main list\n      targetRef = listUserRef.current;\n      console.log('Using list ref:', targetRef ? 'Found' : 'Not found');\n    }\n\n    if (targetRef) {\n      console.log('✅ Target ref found, scrolling...');\n      setShowFindMe(true);\n      targetRef.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n\n      // Show success message\n      message.success(`Found you at rank #${userRank}! 🎯`);\n\n      // Hide the find me highlight after 3 seconds\n      setTimeout(() => {\n        setShowFindMe(false);\n      }, 3000);\n    } else {\n      console.log('❌ Target ref not found, trying DOM query fallback...');\n\n      // Fallback: Try to find user element by data attribute or class\n      let userElement = null;\n\n      // Try to find by user ID in the DOM\n      const allUserElements = document.querySelectorAll('[data-user-id]');\n      console.log('Found user elements:', allUserElements.length);\n\n      for (let element of allUserElements) {\n        if (element.getAttribute('data-user-id') === user._id) {\n          userElement = element;\n          console.log('✅ Found user element via DOM query');\n          break;\n        }\n      }\n\n      if (userElement) {\n        setShowFindMe(true);\n        userElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center'\n        });\n        message.success(`Found you at rank #${userRank}! 🎯`);\n\n        // Add highlight class directly\n        userElement.classList.add('find-me-highlight');\n        setTimeout(() => {\n          userElement.classList.remove('find-me-highlight');\n          setShowFindMe(false);\n        }, 4500);\n      } else {\n        console.log('❌ DOM query fallback also failed');\n        // Final fallback: scroll to approximate position\n        message.info(`You are ranked #${userRank} in the leaderboard!`);\n\n        // If user is not in top 3, scroll to the main ranking section\n        if (userRank > 3) {\n          const mainRankingSection = document.querySelector('.main-ranking-section');\n          if (mainRankingSection) {\n            console.log('📍 Scrolling to main ranking section');\n            mainRankingSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start'\n            });\n          }\n        }\n      }\n    }\n  };\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981', // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444', // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444', // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center\"\n        >\n          <motion.div\n            animate={{ rotate: 360 }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n          />\n          <p className=\"text-white/80 text-lg font-medium\">Loading the Hall of Champions...</p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <style jsx>{`\n        .find-me-highlight {\n          animation: findMePulse 1.5s ease-in-out 3;\n          border: 3px solid #FFD700 !important;\n          background: linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.2)) !important;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 0 15px rgba(255, 215, 0, 0), 0 0 30px rgba(255, 215, 0, 0.8);\n            transform: scale(1.02);\n          }\n        }\n      `}</style>\n      <div className=\"ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* TOP CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl p-3 sm:p-4 border border-white/10\">\n              <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 items-center justify-center\">\n\n                {/* Hub Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => navigate('/user/hub')}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbHome className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>Hub</span>\n                </motion.button>\n\n                {/* Find Me Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={handleFindMe}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-8 py-3 md:py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-black rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                    color: '#000000',\n                    textShadow: 'none',\n                    fontWeight: '900',\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbTarget className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>\n                    {currentUserRank ? `Find Me #${currentUserRank}` :\n                     (user?.role === 'admin' || user?.isAdmin) ? 'Admin View' : 'Find Me'}\n                  </span>\n                </motion.button>\n\n                {/* Debug Button - Remove after testing */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => {\n                    console.log('🔍 Enhanced Debug Info:');\n                    console.log('Current user object:', user);\n                    console.log('User ID:', user?._id);\n                    console.log('User name:', user?.name);\n                    console.log('User isAdmin:', user?.isAdmin);\n                    console.log('User role:', user?.role);\n                    console.log('Ranking data length:', rankingData.length);\n                    console.log('First 5 users in ranking:', rankingData.slice(0, 5).map(u => ({\n                      id: u._id,\n                      name: u.name,\n                      rank: u.rank,\n                      totalXP: u.totalXP\n                    })));\n                    console.log('All user IDs in ranking:', rankingData.map(u => u._id));\n                    console.log('Looking for user ID:', user?._id);\n                    console.log('User found in ranking:', rankingData.find(u => u._id === user?._id));\n                    console.log('podiumUserRef.current:', podiumUserRef.current);\n                    console.log('listUserRef.current:', listUserRef.current);\n                    console.log('currentUserRank:', currentUserRank);\n\n                    // Test DOM query\n                    const userElements = document.querySelectorAll(`[data-user-id=\"${user?._id}\"]`);\n                    console.log('User elements found:', userElements.length);\n                    userElements.forEach((el, i) => {\n                      console.log(`Element ${i}:`, el, 'rank:', el.getAttribute('data-user-rank'));\n                    });\n                  }}\n                  className=\"px-3 py-2 bg-purple-600 text-white rounded-lg text-sm\"\n                >\n                  🔍 Debug\n                </motion.button>\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbRefresh className={`w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Admin Notice */}\n        {(user?.role === 'admin' || user?.isAdmin) && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n              <div className=\"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-sm\">👑</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-bold text-white\">Admin View</h3>\n                    <p className=\"text-sm text-white/80\">\n                      You're viewing as an admin. Admin accounts are excluded from student rankings.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* SPECTACULAR RANKING HEADER */}\n        <motion.div\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden mb-8\"\n        >\n          {/* Header Background with SPECTACULAR Gradient */}\n          <div className=\"bg-gradient-to-br from-purple-600 via-pink-500 via-red-500 via-orange-500 to-yellow-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n\n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-16\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n\n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)',\n                          '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)',\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.8, duration: 0.8 }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <p className=\"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\"\n                     style={{\n                       textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                       fontStyle: 'italic'\n                     }}>\n                    {motivationalQuote}\n                  </p>\n                </motion.div>\n\n                {/* Enhanced Stats Grid */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbUsers,\n                      value: rankingData.length,\n                      label: 'Champions',\n                      bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                      iconColor: '#60A5FA',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbTrophy,\n                      value: topPerformers.length,\n                      label: 'Top Performers',\n                      bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                      iconColor: '#FBBF24',\n                      borderColor: '#F59E0B'\n                    },\n                    {\n                      icon: TbFlame,\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      label: 'Active Streaks',\n                      bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                      iconColor: '#F87171',\n                      borderColor: '#EF4444'\n                    },\n                    {\n                      icon: TbStar,\n                      value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                      label: 'Total XP',\n                      bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                      iconColor: '#34D399',\n                      borderColor: '#10B981'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                      whileHover={{ scale: 1.05, y: -5 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: stat.iconColor,\n                          textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-xs sm:text-sm font-bold relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 lg:px-8 pb-20\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-black text-center mb-6 md:mb-8 px-4\" style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 15px #FFD700)'\n                  }}>\n                    🏆 CHAMPIONS PODIUM 🏆\n                  </h2>\n\n                  <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 md:gap-6 max-w-6xl mx-auto px-2 sm:px-4\">\n                    {topPerformers.map((champion, index) => {\n                      const position = index + 1;\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          ref={isCurrentUser ? podiumUserRef : null}\n                          data-user-id={champion._id}\n                          data-user-rank={position}\n                          initial={{ opacity: 0, y: 50 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: 0.7 + index * 0.2, duration: 0.8 }}\n                          whileHover={{ scale: 1.05, y: -10 }}\n                          className={`relative ${\n                            position === 1 ? 'md:order-2 md:scale-110' :\n                            position === 2 ? 'md:order-1' : 'md:order-3'\n                          } ${isCurrentUser ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`}\n                        >\n                          {/* Compact Podium Card */}\n                          <div\n                            className={`relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow} ${champion.tier.effect} shadow-xl`}\n                            style={{\n                              boxShadow: `0 12px 24px ${champion.tier.shadowColor}, 0 0 30px ${champion.tier.shadowColor}`\n                            }}\n                          >\n                            <div\n                              className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-3 sm:p-4 text-center relative overflow-hidden`}\n                              style={{\n                                border: `2px solid ${champion.tier.borderColor}60`\n                              }}\n                            >\n                              <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent\"></div>\n\n                              {/* Tiny Position Badge */}\n                              <div\n                                className={`absolute -top-3 left-1/2 transform -translate-x-1/2 w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-20`}\n                                style={{\n                                  color: position === 1 ? '#FFD700' : position === 2 ? '#C0C0C0' : position === 3 ? '#CD7F32' : '#FFFFFF',\n                                  textShadow: '1px 1px 2px rgba(0,0,0,0.9)',\n                                  border: `2px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 4px 8px ${champion.tier.shadowColor}, 0 0 12px ${champion.tier.shadowColor}`,\n                                  fontSize: 'clamp(0.75rem, 2vw, 1rem)',\n                                  fontWeight: '900'\n                                }}\n                              >\n                                {position === 1 ? '👑' : position === 2 ? '🥈' : position === 3 ? '🥉' : position}\n                              </div>\n\n                              {/* Crown for #1 */}\n                              {position === 1 && (\n                                <motion.div\n                                  animate={{ rotate: [0, 10, -10, 0] }}\n                                  transition={{ duration: 2, repeat: Infinity }}\n                                  className=\"absolute -top-8 left-1/2 transform -translate-x-1/2\"\n                                >\n                                  <TbCrown className=\"w-8 h-8 text-yellow-400\" />\n                                </motion.div>\n                              )}\n\n                              {/* Tiny Profile Picture */}\n                              <div className={`relative mx-auto mb-2 ${isCurrentUser ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                                <div\n                                  className=\"w-6 h-6 sm:w-8 sm:h-8 rounded-full overflow-hidden mx-auto relative\"\n                                  style={{\n                                    background: `linear-gradient(45deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                    boxShadow: `0 1px 3px ${champion.tier.shadowColor}, 0 0 4px ${champion.tier.shadowColor}`,\n                                    padding: '1px'\n                                  }}\n                                >\n                                  {champion.profilePicture ? (\n                                    <img\n                                      src={champion.profilePicture}\n                                      alt={champion.name}\n                                      className=\"w-full h-full object-cover rounded-full\"\n                                      style={{\n                                        filter: 'brightness(1.1) contrast(1.1)',\n                                        aspectRatio: '1/1'\n                                      }}\n                                    />\n                                  ) : (\n                                    <div\n                                      className=\"w-full h-full rounded-full flex items-center justify-center font-black text-2xl\"\n                                      style={{\n                                        background: `linear-gradient(135deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                        color: '#FFFFFF',\n                                        textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                        aspectRatio: '1/1'\n                                      }}\n                                    >\n                                      {champion.name.charAt(0).toUpperCase()}\n                                    </div>\n                                  )}\n                                </div>\n                                {isCurrentUser && (\n                                  <div\n                                    className=\"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\"\n                                    style={{\n                                      background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                      boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                                    }}\n                                  >\n                                    <TbStar className=\"w-5 h-5 text-black\" />\n                                  </div>\n                                )}\n                              </div>\n\n                              {/* Champion Info */}\n                              <h3\n                                className=\"text-xl font-black mb-2 relative z-10\"\n                                style={{\n                                  color: champion.tier.nameColor,\n                                  textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                                  fontSize: '1.5rem',\n                                  filter: 'drop-shadow(0 0 10px currentColor)'\n                                }}\n                              >\n                                {champion.name}\n                              </h3>\n                              <div\n                                className={`inline-flex items-center gap-2 px-5 py-3 bg-gradient-to-r ${champion.tier.color} rounded-full text-sm font-black mb-3 relative z-10 animate-pulse`}\n                                style={{\n                                  color: '#FFFFFF',\n                                  textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                                  border: `3px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 6px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                                  fontSize: '0.9rem',\n                                  letterSpacing: '0.5px'\n                                }}\n                              >\n                                <champion.tier.icon\n                                  className=\"w-6 h-6\"\n                                  style={{\n                                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))',\n                                    color: champion.tier.textColor\n                                  }}\n                                />\n                                <span style={{ color: '#FFFFFF' }}>{champion.tier.title}</span>\n                              </div>\n\n                              {/* Subscription Status Badge */}\n                              {(() => {\n                                const badge = getSubscriptionBadge(\n                                  champion.subscriptionStatus,\n                                  champion.subscriptionEndDate,\n                                  champion.subscriptionPlan,\n                                  champion.activePlanTitle,\n                                  index\n                                );\n                                return (\n                                  <div\n                                    className=\"inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-bold mb-3 relative z-10\"\n                                    style={{\n                                      backgroundColor: badge.bgColor,\n                                      color: badge.color,\n                                      border: `2px solid ${badge.borderColor}`,\n                                      textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                                      fontSize: '0.75rem',\n                                      letterSpacing: '0.5px'\n                                    }}\n                                  >\n                                    {badge.text}\n                                  </div>\n                                );\n                              })()}\n\n                              {/* Enhanced Stats */}\n                              <div className=\"space-y-3 relative z-10\">\n                                <div className=\"flex justify-between text-base\">\n                                  <span style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '1rem'\n                                  }}>💎 XP:</span>\n                                  <span style={{\n                                    color: champion.tier.nameColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '900',\n                                    fontSize: '1.1rem',\n                                    filter: 'drop-shadow(0 0 8px currentColor)'\n                                  }}>{champion.totalXP.toLocaleString()}</span>\n                                </div>\n                                <div className=\"flex justify-between text-base\">\n                                  <span style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '1rem'\n                                  }}>🧠 Quizzes:</span>\n                                  <span style={{\n                                    color: champion.tier.nameColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '900',\n                                    fontSize: '1.1rem',\n                                    filter: 'drop-shadow(0 0 8px currentColor)'\n                                  }}>{champion.totalQuizzesTaken}</span>\n                                </div>\n                                <div className=\"flex justify-between text-base\">\n                                  <span style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '1rem'\n                                  }}>🔥 Streak:</span>\n                                  <span style={{\n                                    color: champion.tier.nameColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '900',\n                                    fontSize: '1.1rem',\n                                    filter: 'drop-shadow(0 0 8px currentColor)'\n                                  }} className=\"flex items-center gap-1\">\n                                    {champion.currentStreak}\n                                  </span>\n                                </div>\n\n                                {/* Data Source Indicator */}\n                                <div className=\"text-center mt-2\">\n                                  <span style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    fontWeight: '600',\n                                    fontSize: '0.7rem',\n                                    opacity: 0.8\n                                  }}>\n                                    {champion.dataSource === 'reports' ? '📊 Live Data' :\n                                     champion.dataSource === 'legacy_points' ? '📈 Legacy Points' :\n                                     '🔮 Estimated'}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* OTHER CHAMPIONS LIST */}\n              {otherPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"mt-12 main-ranking-section\"\n                >\n                  <h2 className=\"text-xl sm:text-2xl md:text-3xl font-black text-center mb-6 md:mb-8 px-4\" style={{\n                    background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                  }}>\n                    ⚡ RISING CHAMPIONS ⚡\n                  </h2>\n\n                  <div className=\"space-y-3 md:space-y-4 px-2 md:px-0\">\n                    {otherPerformers.map((champion, index) => {\n                      const actualRank = index + 4; // Since top 3 are shown separately\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          ref={isCurrentUser ? listUserRef : null}\n                          data-user-id={champion._id}\n                          data-user-rank={actualRank}\n                          initial={{ opacity: 0, x: -50 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                          whileHover={{ scale: 1.02, x: 10 }}\n                          className={`relative ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`}\n                        >\n                          <div\n                            className={`bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow} ${champion.tier.effect}`}\n                            style={{\n                              boxShadow: `0 8px 24px ${champion.tier.shadowColor}`\n                            }}\n                          >\n                            <div\n                              className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-3 md:p-4 flex items-center gap-3 md:gap-4 relative overflow-hidden`}\n                              style={{\n                                border: `1px solid ${champion.tier.borderColor}40`\n                              }}\n                            >\n                              <div className=\"absolute inset-0 bg-gradient-to-r from-white/5 to-transparent\"></div>\n\n                              {/* Enhanced Rank */}\n                              <div\n                                className={`flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-xs shadow-lg relative z-10`}\n                                style={{\n                                  color: actualRank <= 10 ? champion.tier.textColor : '#FFFFFF',\n                                  textShadow: '3px 3px 6px rgba(0,0,0,0.9)',\n                                  border: `3px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 8px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                                  fontSize: actualRank <= 10 ? '1.3rem' : '1.1rem',\n                                  fontWeight: '900'\n                                }}\n                              >\n                                {actualRank <= 10 ? `#${actualRank}` : actualRank}\n                              </div>\n\n                              {/* Tiny Profile Picture */}\n                              <div className=\"flex-shrink-0 relative\">\n                                <div\n                                  className=\"w-6 h-6 sm:w-8 sm:h-8 rounded-full overflow-hidden p-0.5 relative\"\n                                  style={{\n                                    background: `linear-gradient(45deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                    boxShadow: `0 1px 3px ${champion.tier.shadowColor}, 0 0 4px ${champion.tier.shadowColor}`\n                                  }}\n                                >\n                                  {champion.profilePicture ? (\n                                    <img\n                                      src={champion.profilePicture}\n                                      alt={champion.name}\n                                      className=\"w-full h-full object-cover rounded-full\"\n                                      style={{\n                                        filter: 'brightness(1.1) contrast(1.1) saturate(1.2)',\n                                        aspectRatio: '1/1'\n                                      }}\n                                    />\n                                  ) : (\n                                    <div\n                                      className=\"w-full h-full rounded-full flex items-center justify-center font-black text-xs\"\n                                      style={{\n                                        background: `linear-gradient(135deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                        color: '#FFFFFF',\n                                        textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                        aspectRatio: '1/1'\n                                      }}\n                                    >\n                                      {champion.name.charAt(0).toUpperCase()}\n                                    </div>\n                                  )}\n                                </div>\n                                {isCurrentUser && (\n                                  <div\n                                    className=\"absolute -top-1 -right-1 rounded-full p-1 animate-pulse\"\n                                    style={{\n                                      background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                      boxShadow: '0 2px 8px rgba(255,215,0,0.6)'\n                                    }}\n                                  >\n                                    <TbStar className=\"w-3 h-3 text-black\" />\n                                  </div>\n                                )}\n                              </div>\n\n                              {/* Champion Info */}\n                              <div className=\"flex-1 min-w-0 relative z-10\">\n                                <div className=\"flex items-center gap-2 mb-1\">\n                                  <h3\n                                    className=\"text-sm sm:text-base md:text-lg font-black truncate\"\n                                    style={{\n                                      color: champion.tier.nameColor,\n                                      textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                      fontSize: 'clamp(0.875rem, 3vw, 1.25rem)',\n                                      filter: 'drop-shadow(0 0 6px currentColor)'\n                                    }}\n                                  >\n                                    {champion.name}\n                                  </h3>\n                                  {isCurrentUser && (\n                                    <div\n                                      className=\"px-3 py-1 rounded-full text-xs font-black animate-pulse\"\n                                      style={{\n                                        background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                        color: '#000000',\n                                        textShadow: 'none',\n                                        border: '2px solid #FFFFFF',\n                                        boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                                      }}\n                                    >\n                                      ⭐ YOU ⭐\n                                    </div>\n                                  )}\n                                </div>\n                                <div\n                                  className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${champion.tier.color} rounded-full text-xs font-black`}\n                                  style={{\n                                    color: '#FFFFFF',\n                                    textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                                    border: `2px solid ${champion.tier.borderColor}`,\n                                    boxShadow: `0 4px 8px ${champion.tier.shadowColor}, 0 0 12px ${champion.tier.shadowColor}`,\n                                    fontSize: '0.8rem',\n                                    letterSpacing: '0.3px'\n                                  }}\n                                >\n                                  <champion.tier.icon\n                                    className=\"w-4 h-4\"\n                                    style={{\n                                      filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.5))',\n                                      color: champion.tier.textColor\n                                    }}\n                                  />\n                                  <span style={{ color: '#FFFFFF' }}>{champion.tier.title}</span>\n                                </div>\n\n                                {/* Subscription Status Badge */}\n                                {(() => {\n                                  const badge = getSubscriptionBadge(\n                                    champion.subscriptionStatus,\n                                    champion.subscriptionEndDate,\n                                    champion.subscriptionPlan,\n                                    champion.activePlanTitle,\n                                    actualRank\n                                  );\n                                  return (\n                                    <div\n                                      className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold mt-1\"\n                                      style={{\n                                        backgroundColor: badge.bgColor,\n                                        color: badge.color,\n                                        border: `1px solid ${badge.borderColor}`,\n                                        textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                                        fontSize: '0.7rem',\n                                        letterSpacing: '0.3px'\n                                      }}\n                                    >\n                                      {badge.text}\n                                    </div>\n                                  );\n                                })()}\n                              </div>\n\n                              {/* Enhanced Stats */}\n                              <div className=\"flex-shrink-0 text-right relative z-10\">\n                                <div className=\"text-xl mb-2\" style={{\n                                  color: champion.tier.nameColor,\n                                  textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                                  fontWeight: '900',\n                                  filter: 'drop-shadow(0 0 10px currentColor)',\n                                  fontSize: '1.3rem'\n                                }}>\n                                  💎 {champion.totalXP.toLocaleString()} XP\n                                </div>\n                                <div className=\"flex items-center gap-3 text-sm justify-end\">\n                                  <span className=\"flex items-center gap-1\" style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '0.9rem'\n                                  }}>\n                                    <TbBrain className=\"w-4 h-4\" style={{ color: champion.tier.textColor }} />\n                                    {champion.totalQuizzesTaken}\n                                  </span>\n                                  <span className=\"flex items-center gap-1\" style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '0.9rem'\n                                  }}>\n                                    <TbFlame className=\"w-4 h-4\" style={{ color: '#FF6B35' }} />\n                                    {champion.currentStreak}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* DATA INTEGRATION STATUS */}\n              {rankingData.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.8, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-xl font-bold mb-4\" style={{\n                      color: '#60A5FA',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>📊 Real User Data Integration</h3>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\">\n                      <div className=\"bg-green-500/20 rounded-lg p-3\">\n                        <div className=\"text-green-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'reports').length}\n                        </div>\n                        <div className=\"text-white/80\">📊 Live Quiz Data</div>\n                      </div>\n                      <div className=\"bg-blue-500/20 rounded-lg p-3\">\n                        <div className=\"text-blue-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'legacy_points').length}\n                        </div>\n                        <div className=\"text-white/80\">📈 Legacy Points</div>\n                      </div>\n                      <div className=\"bg-purple-500/20 rounded-lg p-3\">\n                        <div className=\"text-purple-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'estimated').length}\n                        </div>\n                        <div className=\"text-white/80\">🔮 Estimated Stats</div>\n                      </div>\n                    </div>\n                    <p className=\"text-white/70 text-sm mt-4\">\n                      Using real database users (admins excluded) with intelligent data processing\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold mb-2\" style={{\n                      color: '#ffffff',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>Your Current Position</h3>\n                    <div className=\"text-6xl font-black mb-2\" style={{\n                      color: '#fbbf24',\n                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                      fontWeight: '900'\n                    }}>#{currentUserRank}</div>\n                    <p className=\"text-lg\" style={{\n                      color: '#e5e7eb',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                      fontWeight: '600'\n                    }}>\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>Ready to Rise Higher?</h3>\n                  <p className=\"text-xl mb-6 max-w-2xl mx-auto\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {rankingData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbTrophy className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Yet</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Be the first to take a quiz and claim your spot in the Hall of Champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,SAASC,uBAAuB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,2BAA2B;AACrG,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG/B,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,IAAI,GAAGH,SAAS,CAACG,IAAI,IAAI,IAAI;EACnC,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACqD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMyD,SAAS,GAAGvD,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMwD,cAAc,GAAGxD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMyD,aAAa,GAAGzD,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM0D,WAAW,GAAG1D,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAM2D,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,gBAAgB,GAAG;IACvBC,SAAS,EAAE;MACTC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE9D,OAAO;MACb+D,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE;MACPZ,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEpD,SAAS;MACfqD,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,mBAAmB;MAChCC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDE,QAAQ,EAAE;MACRb,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE9C,QAAQ;MACd+C,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,aAAa;MAC1BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDG,IAAI,EAAE;MACJd,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE/D,QAAQ;MACdgE,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDI,MAAM,EAAE;MACNf,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEvD,OAAO;MACbwD,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDK,MAAM,EAAE;MACNhB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE7D,MAAM;MACZ8D,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,aAAa;MAC1BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV;EACF,CAAC;;EAED;EACA,MAAMM,WAAW,GAAIC,EAAE,IAAK;IAC1B,KAAK,MAAM,CAACC,IAAI,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACxB,gBAAgB,CAAC,EAAE;MAC7D,IAAIoB,EAAE,IAAIE,MAAM,CAACpB,GAAG,EAAE,OAAO;QAAEmB,IAAI;QAAE,GAAGC;MAAO,CAAC;IAClD;IACA,OAAO;MAAED,IAAI,EAAE,QAAQ;MAAE,GAAGrB,gBAAgB,CAACkB;IAAO,CAAC;EACvD,CAAC;;EAED;EACA,MAAMO,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF3C,UAAU,CAAC,IAAI,CAAC;MAChB4C,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;MAEtD;MACA,IAAI;QACFD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMC,qBAAqB,GAAG,MAAM/D,gBAAgB,CAAC;UACnDgE,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,CAAArD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,KAAK,KAAI,KAAK;UACjCC,eAAe,EAAE;QACnB,CAAC,CAAC;QAEFN,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,qBAAqB,CAAC;QAEhE,IAAIA,qBAAqB,IAAIA,qBAAqB,CAACK,OAAO,IAAIL,qBAAqB,CAACM,IAAI,EAAE;UACxFR,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;UAEhD;UACA,MAAMQ,YAAY,GAAGP,qBAAqB,CAACM,IAAI,CAACE,MAAM,CAACC,QAAQ,IAC5DA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACC,OAAO,GAAG,CAAC,IACxCD,QAAQ,CAACE,iBAAiB,IAAIF,QAAQ,CAACE,iBAAiB,GAAG,CAC9D,CAAC;UAED,MAAMC,eAAe,GAAGL,YAAY,CAACM,GAAG,CAAC,CAACJ,QAAQ,EAAEK,KAAK,MAAM;YAC7DC,GAAG,EAAEN,QAAQ,CAACM,GAAG;YACjBC,IAAI,EAAEP,QAAQ,CAACO,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAER,QAAQ,CAACQ,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAET,QAAQ,CAACS,KAAK,IAAI,EAAE;YAC3Bf,KAAK,EAAEM,QAAQ,CAACN,KAAK,IAAI,EAAE;YAC3BgB,cAAc,EAAEV,QAAQ,CAACW,YAAY,IAAI,EAAE;YAC3CV,OAAO,EAAED,QAAQ,CAACC,OAAO,IAAI,CAAC;YAC9BC,iBAAiB,EAAEF,QAAQ,CAACE,iBAAiB,IAAI,CAAC;YAClDU,YAAY,EAAEZ,QAAQ,CAACY,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAEb,QAAQ,CAACa,aAAa,IAAI,CAAC;YAC1CC,UAAU,EAAEd,QAAQ,CAACc,UAAU,IAAI,CAAC;YACpCC,kBAAkB,EAAEf,QAAQ,CAACe,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEX,KAAK,GAAG,CAAC;YACfrB,IAAI,EAAEF,WAAW,CAACkB,QAAQ,CAACC,OAAO,IAAI,CAAC,CAAC;YACxCgB,UAAU,EAAE,IAAI;YAChBC,YAAY,EAAElB,QAAQ,CAACkB,YAAY,IAAI,CAAC;YACxC;YACAC,YAAY,EAAEnB,QAAQ,CAACmB,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAEpB,QAAQ,CAACoB,aAAa,IAAI,GAAG;YAC5CC,UAAU,EAAErB,QAAQ,CAACqB,UAAU,IAAI,CAAC;YACpCC,QAAQ,EAAEtB,QAAQ,CAACsB,QAAQ,IAAI,CAAC;YAChCC,YAAY,EAAEvB,QAAQ,CAACuB,YAAY,IAAI,EAAE;YACzCC,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;UAEHjF,cAAc,CAAC4D,eAAe,CAAC;;UAE/B;UACA,MAAMsB,aAAa,GAAGtB,eAAe,CAACuB,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACrB,GAAG,MAAKlE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,EAAC;UAC/E3D,kBAAkB,CAAC8E,aAAa,IAAI,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC;UAEjEhF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAOmF,OAAO,EAAE;QAChBvC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEsC,OAAO,CAAC;MACpE;;MAEA;MACAvC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D,IAAIuC,eAAe,EAAEC,aAAa;MAElC,IAAI;QACFzC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDuC,eAAe,GAAG,MAAMtG,uBAAuB,CAAC,CAAC;QACjD8D,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCwC,aAAa,GAAG,MAAMpG,WAAW,CAAC,CAAC;MACrC,CAAC,CAAC,OAAOqG,KAAK,EAAE;QACd1C,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEyC,KAAK,CAAC;QACnD,IAAI;UACFD,aAAa,GAAG,MAAMpG,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC,OAAOsG,SAAS,EAAE;UAClB3C,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE0C,SAAS,CAAC;QACpD;MACF;MAEA,IAAI7B,eAAe,GAAG,EAAE;MAExB,IAAI2B,aAAa,IAAIA,aAAa,CAAClC,OAAO,IAAIkC,aAAa,CAACjC,IAAI,EAAE;QAChER,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;QAEhD;QACA,MAAM2C,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIJ,eAAe,IAAIA,eAAe,CAACjC,OAAO,IAAIiC,eAAe,CAAChC,IAAI,EAAE;UACtEgC,eAAe,CAAChC,IAAI,CAACqC,OAAO,CAACP,IAAI,IAAI;YAAA,IAAAQ,UAAA;YACnC,MAAMC,MAAM,GAAG,EAAAD,UAAA,GAAAR,IAAI,CAACvF,IAAI,cAAA+F,UAAA,uBAATA,UAAA,CAAW7B,GAAG,KAAIqB,IAAI,CAACS,MAAM;YAC5C,IAAIA,MAAM,EAAE;cACVH,cAAc,CAACG,MAAM,CAAC,GAAGT,IAAI,CAACU,OAAO,IAAI,EAAE;YAC7C;UACF,CAAC,CAAC;QACJ;QAEAlC,eAAe,GAAG2B,aAAa,CAACjC,IAAI,CACjCE,MAAM,CAACC,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAACM,GAAG,IAAIN,QAAQ,CAACsC,IAAI,KAAK,OAAO,CAAC,CAAC;QAAA,CAC1ElC,GAAG,CAAC,CAACJ,QAAQ,EAAEK,KAAK,KAAK;UACxB;UACA,MAAMkC,WAAW,GAAGN,cAAc,CAACjC,QAAQ,CAACM,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,IAAIkC,YAAY,GAAGD,WAAW,CAACE,MAAM,IAAIzC,QAAQ,CAACE,iBAAiB,IAAI,CAAC;UACxE,IAAIwC,UAAU,GAAGH,WAAW,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAClF,IAAIlC,YAAY,GAAG4B,YAAY,GAAG,CAAC,GAAGO,IAAI,CAACC,KAAK,CAACN,UAAU,GAAGF,YAAY,CAAC,GAAGxC,QAAQ,CAACY,YAAY,IAAI,CAAC;;UAExG;UACA,IAAI,CAAC2B,WAAW,CAACE,MAAM,IAAIzC,QAAQ,CAACiD,WAAW,EAAE;YAC/C;YACA,MAAMC,gBAAgB,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACK,KAAK,CAACpD,QAAQ,CAACiD,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAMI,gBAAgB,GAAGN,IAAI,CAAClF,GAAG,CAAC,EAAE,EAAEkF,IAAI,CAACI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAInD,QAAQ,CAACiD,WAAW,GAAGC,gBAAgB,GAAG,EAAG,CAAC,CAAC,CAAC,CAAC;;YAE1GV,YAAY,GAAGU,gBAAgB;YAC/BtC,YAAY,GAAGmC,IAAI,CAACC,KAAK,CAACK,gBAAgB,CAAC;YAC3CX,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACpC,YAAY,GAAG4B,YAAY,CAAC;YAEpDnD,OAAO,CAACC,GAAG,CAAE,0BAAyBU,QAAQ,CAACO,IAAK,KAAI2C,gBAAiB,aAAYG,gBAAiB,cAAarD,QAAQ,CAACiD,WAAY,SAAQ,CAAC;UACnJ;;UAEA;UACA,IAAIhD,OAAO,GAAGD,QAAQ,CAACC,OAAO,IAAI,CAAC;UAEnC,IAAI,CAACA,OAAO,EAAE;YACZ;YACA,IAAID,QAAQ,CAACiD,WAAW,EAAE;cACxB;cACAhD,OAAO,GAAG8C,IAAI,CAACK,KAAK,CAClBpD,QAAQ,CAACiD,WAAW;cAAG;cACtBT,YAAY,GAAG,EAAG;cAAG;cACrB5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;cAAG;cAC7C5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAAC,EAAE;cAC3B;cACAvC,OAAO,GAAG8C,IAAI,CAACK,KAAK,CACjBxC,YAAY,GAAG4B,YAAY,GAAG,CAAC;cAAI;cACnCA,YAAY,GAAG,EAAG;cAAG;cACrB5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH;UACF;;UAEA;UACA,IAAI3B,aAAa,GAAGb,QAAQ,CAACa,aAAa,IAAI,CAAC;UAC/C,IAAIC,UAAU,GAAGd,QAAQ,CAACc,UAAU,IAAI,CAAC;UAEzC,IAAIyB,WAAW,CAACE,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAIa,UAAU,GAAG,CAAC;YAClBf,WAAW,CAACL,OAAO,CAACW,MAAM,IAAI;cAC5B,IAAIA,MAAM,CAACC,KAAK,IAAI,EAAE,EAAE;gBAAE;gBACxBQ,UAAU,EAAE;gBACZxC,UAAU,GAAGiC,IAAI,CAACI,GAAG,CAACrC,UAAU,EAAEwC,UAAU,CAAC;cAC/C,CAAC,MAAM;gBACLA,UAAU,GAAG,CAAC;cAChB;YACF,CAAC,CAAC;YACFzC,aAAa,GAAGyC,UAAU;UAC5B,CAAC,MAAM,IAAItD,QAAQ,CAACiD,WAAW,IAAI,CAACpC,aAAa,EAAE;YACjD;YACA,MAAM0C,aAAa,GAAGf,YAAY,GAAG,CAAC,GAAGxC,QAAQ,CAACiD,WAAW,GAAGT,YAAY,GAAG,CAAC;YAChF,IAAIe,aAAa,GAAG,EAAE,EAAE;cACtB1C,aAAa,GAAGkC,IAAI,CAAClF,GAAG,CAAC2E,YAAY,EAAEO,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;cACxEzC,UAAU,GAAGiC,IAAI,CAACI,GAAG,CAACtC,aAAa,EAAEkC,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACxE;UACF;;UAEA,OAAO;YACLjD,GAAG,EAAEN,QAAQ,CAACM,GAAG;YACjBC,IAAI,EAAEP,QAAQ,CAACO,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAER,QAAQ,CAACQ,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAET,QAAQ,CAACS,KAAK,IAAI,EAAE;YAC3Bf,KAAK,EAAEM,QAAQ,CAACN,KAAK,IAAI,EAAE;YAC3BgB,cAAc,EAAEV,QAAQ,CAACU,cAAc,IAAI,EAAE;YAC7CT,OAAO,EAAEA,OAAO;YAChBC,iBAAiB,EAAEsC,YAAY;YAC/B5B,YAAY,EAAEA,YAAY;YAC1BC,aAAa,EAAEA,aAAa;YAC5BC,UAAU,EAAEA,UAAU;YACtBC,kBAAkB,EAAEf,QAAQ,CAACe,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEX,KAAK,GAAG,CAAC;YACfrB,IAAI,EAAEF,WAAW,CAACmB,OAAO,CAAC;YAC1BgB,UAAU,EAAE,IAAI;YAChB;YACAuC,cAAc,EAAExD,QAAQ,CAACiD,WAAW,IAAI,CAAC;YACzCQ,UAAU,EAAElB,WAAW,CAACE,MAAM,GAAG,CAAC;YAClCjB,UAAU,EAAEe,WAAW,CAACE,MAAM,GAAG,CAAC,GAAG,SAAS,GAAGzC,QAAQ,CAACiD,WAAW,GAAG,eAAe,GAAG;UAC5F,CAAC;QACH,CAAC,CAAC;;QAEJ;QACA9C,eAAe,CAACuD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC3D,OAAO,GAAG0D,CAAC,CAAC1D,OAAO,CAAC;;QAErD;QACAE,eAAe,CAAC+B,OAAO,CAAC,CAAC9F,IAAI,EAAEiE,KAAK,KAAK;UACvCjE,IAAI,CAAC4E,IAAI,GAAGX,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEF9D,cAAc,CAAC4D,eAAe,CAAC;;QAE/B;QACA,IAAI0D,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAIzH,IAAI,EAAE;UACR;UACAyH,QAAQ,GAAG1D,eAAe,CAACuB,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACrB,GAAG,KAAKlE,IAAI,CAACkE,GAAG,CAAC;;UAEnE;UACA,IAAIuD,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnBA,QAAQ,GAAG1D,eAAe,CAACuB,SAAS,CAACC,IAAI,IAAImC,MAAM,CAACnC,IAAI,CAACrB,GAAG,CAAC,KAAKwD,MAAM,CAAC1H,IAAI,CAACkE,GAAG,CAAC,CAAC;UACrF;;UAEA;UACA,IAAIuD,QAAQ,KAAK,CAAC,CAAC,IAAIzH,IAAI,CAACmE,IAAI,EAAE;YAChCsD,QAAQ,GAAG1D,eAAe,CAACuB,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACpB,IAAI,KAAKnE,IAAI,CAACmE,IAAI,CAAC;UACvE;QACF;QAEA5D,kBAAkB,CAACkH,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;;QAEvD;QACAxE,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7CyE,WAAW,EAAE3H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,IAAI;UACvB6B,MAAM,EAAEhG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG;UACjB0D,UAAU,EAAE,QAAO5H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG;UAC5B2D,OAAO,EAAE,CAAA7H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,IAAI,MAAK,OAAO,KAAIlG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6H,OAAO;UAChDC,MAAM,EAAE9H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,OAAO;UACrBwB,aAAa,EAAEoC,QAAQ;UACvBM,gBAAgB,EAAEN,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI;UACrDO,gBAAgB,EAAEjE,eAAe,CAACsC,MAAM;UACxC4B,eAAe,EAAElE,eAAe,CAACmE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClE,GAAG,CAACmE,CAAC,KAAK;YAAEC,EAAE,EAAED,CAAC,CAACjE,GAAG;YAAEmE,IAAI,EAAE,OAAOF,CAAC,CAACjE,GAAG;YAAEC,IAAI,EAAEgE,CAAC,CAAChE;UAAK,CAAC,CAAC,CAAC;UACxGmE,UAAU,EAAEvE,eAAe,CAACwE,IAAI,CAAChD,IAAI,IAAIA,IAAI,CAACrB,GAAG,MAAKlE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,EAAC;UAChEsE,WAAW,EAAEzE,eAAe,CAACwE,IAAI,CAAChD,IAAI,IAAImC,MAAM,CAACnC,IAAI,CAACrB,GAAG,CAAC,KAAKwD,MAAM,CAAC1H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,CAAC,CAAC;UACjFuE,SAAS,EAAE1E,eAAe,CAACwE,IAAI,CAAChD,IAAI,IAAIA,IAAI,CAACpB,IAAI,MAAKnE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,IAAI;QAClE,CAAC,CAAC;;QAEF;QACA,MAAMuE,WAAW,GAAG;UAClBzC,OAAO,EAAElC,eAAe,CAACJ,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC/C,UAAU,KAAK,SAAS,CAAC,CAACiB,MAAM;UACvEsC,aAAa,EAAE5E,eAAe,CAACJ,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC/C,UAAU,KAAK,eAAe,CAAC,CAACiB,MAAM;UACnFuC,SAAS,EAAE7E,eAAe,CAACJ,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC/C,UAAU,KAAK,WAAW,CAAC,CAACiB;QACvE,CAAC;QAEDpD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEa,eAAe,CAACsC,MAAM,EAAE,gBAAgB,CAAC;QACxFpD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEwF,WAAW,CAAC;QAC5CzF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEa,eAAe,CAACmE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClE,GAAG,CAACmE,CAAC,KAAK;UACvEhE,IAAI,EAAEgE,CAAC,CAAChE,IAAI;UACZxB,EAAE,EAAEwF,CAAC,CAACtE,OAAO;UACbgF,OAAO,EAAEV,CAAC,CAACrE,iBAAiB;UAC5BgF,GAAG,EAAEX,CAAC,CAAC3D,YAAY;UACnBuE,MAAM,EAAEZ,CAAC,CAAC/C;QACZ,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,MAAM;QACLnC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxC/C,cAAc,CAAC,EAAE,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;QACxBvC,OAAO,CAACgL,OAAO,CAAC,0DAA0D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOrD,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD3H,OAAO,CAAC2H,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACRtF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA3C,SAAS,CAAC,MAAM;IACdsF,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMiG,WAAW,GAAG3H,kBAAkB,CAACqF,IAAI,CAACK,KAAK,CAACL,IAAI,CAACuC,MAAM,CAAC,CAAC,GAAG5H,kBAAkB,CAAC+E,MAAM,CAAC,CAAC;IAC7FtF,oBAAoB,CAACkI,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvCvI,iBAAiB,CAACwI,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;;IAER;IACA,MAAMC,YAAY,GAAGF,WAAW,CAAC,MAAM;MACrCnG,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDF,gBAAgB,CAAC,CAAC;IACpB,CAAC,EAAE,KAAK,CAAC;;IAET;IACA,MAAMuG,iBAAiB,GAAGA,CAAA,KAAM;MAC9BtG,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7DF,gBAAgB,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAMwG,mBAAmB,GAAIC,KAAK,IAAK;MACrCxG,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEuG,KAAK,CAACC,MAAM,CAAC;MACnE;MACAC,UAAU,CAAC,MAAM;QACf3G,gBAAgB,CAAC,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED4G,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEN,iBAAiB,CAAC;IACnDK,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEL,mBAAmB,CAAC;IAE7D,OAAO,MAAM;MACXM,aAAa,CAACX,cAAc,CAAC;MAC7BW,aAAa,CAACR,YAAY,CAAC;MAC3BM,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAER,iBAAiB,CAAC;MACtDK,MAAM,CAACG,mBAAmB,CAAC,eAAe,EAAEP,mBAAmB,CAAC;IAClE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMQ,aAAa,GAAG9J,WAAW,CAACgI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAM+B,eAAe,GAAG/J,WAAW,CAACgI,KAAK,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAMgC,YAAY,GAAGA,CAAA,KAAM;IACzBjH,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClCD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,IAAI,EAAEnE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,CAAC;IACnDjB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEhD,WAAW,CAACmG,MAAM,CAAC;;IAEvD;IACA,MAAM8D,aAAa,GAAGjK,WAAW,CAACqI,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAACjE,GAAG,MAAKlE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,EAAC;IAChEjB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEiH,aAAa,GAAG,KAAK,GAAG,IAAI,CAAC;IAEnE,IAAI,CAACA,aAAa,EAAE;MAClB;MACA,IAAI,CAAAnK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,IAAI,MAAK,OAAO,IAAIlG,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6H,OAAO,EAAE;QAC3C7J,OAAO,CAACoM,IAAI,CAAC,sGAAsG,CAAC;MACtH,CAAC,MAAM,IAAI,CAAApK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,OAAO,MAAK,CAAC,IAAI,EAAC7D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6D,OAAO,GAAE;QAChD7F,OAAO,CAACoM,IAAI,CAAC,wFAAwF,CAAC;MACxG,CAAC,MAAM;QACLpM,OAAO,CAACoM,IAAI,CAAC,oFAAoF,CAAC;MACpG;MACA;IACF;IAEA,MAAM3C,QAAQ,GAAGvH,WAAW,CAACmK,OAAO,CAACF,aAAa,CAAC,GAAG,CAAC;IACvDlH,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEuE,QAAQ,CAAC;;IAEnC;IACA,IAAI6C,SAAS,GAAG,IAAI;IACpB,IAAI7C,QAAQ,IAAI,CAAC,EAAE;MACjB;MACA6C,SAAS,GAAGlJ,aAAa,CAACmJ,OAAO;MACjCtH,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoH,SAAS,GAAG,OAAO,GAAG,WAAW,CAAC;IACrE,CAAC,MAAM;MACL;MACAA,SAAS,GAAGjJ,WAAW,CAACkJ,OAAO;MAC/BtH,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoH,SAAS,GAAG,OAAO,GAAG,WAAW,CAAC;IACnE;IAEA,IAAIA,SAAS,EAAE;MACbrH,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/CjC,aAAa,CAAC,IAAI,CAAC;MACnBqJ,SAAS,CAACE,cAAc,CAAC;QACvBC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACA1M,OAAO,CAACwF,OAAO,CAAE,sBAAqBiE,QAAS,MAAK,CAAC;;MAErD;MACAkC,UAAU,CAAC,MAAM;QACf1I,aAAa,CAAC,KAAK,CAAC;MACtB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACLgC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;;MAEnE;MACA,IAAIyH,WAAW,GAAG,IAAI;;MAEtB;MACA,MAAMC,eAAe,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,gBAAgB,CAAC;MACnE7H,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE0H,eAAe,CAACvE,MAAM,CAAC;MAE3D,KAAK,IAAI0E,OAAO,IAAIH,eAAe,EAAE;QACnC,IAAIG,OAAO,CAACC,YAAY,CAAC,cAAc,CAAC,KAAKhL,IAAI,CAACkE,GAAG,EAAE;UACrDyG,WAAW,GAAGI,OAAO;UACrB9H,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjD;QACF;MACF;MAEA,IAAIyH,WAAW,EAAE;QACf1J,aAAa,CAAC,IAAI,CAAC;QACnB0J,WAAW,CAACH,cAAc,CAAC;UACzBC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;QACT,CAAC,CAAC;QACF1M,OAAO,CAACwF,OAAO,CAAE,sBAAqBiE,QAAS,MAAK,CAAC;;QAErD;QACAkD,WAAW,CAACM,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;QAC9CvB,UAAU,CAAC,MAAM;UACfgB,WAAW,CAACM,SAAS,CAACE,MAAM,CAAC,mBAAmB,CAAC;UACjDlK,aAAa,CAAC,KAAK,CAAC;QACtB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLgC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C;QACAlF,OAAO,CAACoM,IAAI,CAAE,mBAAkB3C,QAAS,sBAAqB,CAAC;;QAE/D;QACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;UAChB,MAAM2D,kBAAkB,GAAGP,QAAQ,CAACQ,aAAa,CAAC,uBAAuB,CAAC;UAC1E,IAAID,kBAAkB,EAAE;YACtBnI,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;YACnDkI,kBAAkB,CAACZ,cAAc,CAAC;cAChCC,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAE;YACT,CAAC,CAAC;UACJ;QACF;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAMY,oBAAoB,GAAGA,CAAC3G,kBAAkB,EAAE4G,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,GAAG,CAAC,KAAK;IAC1H,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,OAAO,GAAGN,mBAAmB,GAAG,IAAIK,IAAI,CAACL,mBAAmB,CAAC,GAAG,IAAI;IAE1EtI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCyB,kBAAkB;MAClB4G,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfI,OAAO;MACPF,GAAG;MACHG,QAAQ,EAAED,OAAO,IAAIA,OAAO,GAAGF,GAAG;MAClCD;IACF,CAAC,CAAC;;IAEF;IACA,IAAI/G,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,KAAK,SAAS,EAAE;MACvE;MACA,IAAI,CAACkH,OAAO,IAAIA,OAAO,GAAGF,GAAG,EAAE;QAC7B;QACA,OAAO;UACLI,IAAI,EAAE,WAAW;UACjBrK,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,yBAAyB;UAClCQ,WAAW,EAAE;QACf,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACL4J,IAAI,EAAE,SAAS;UACfrK,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,wBAAwB;UACjCQ,WAAW,EAAE;QACf,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA,OAAO;QACL4J,IAAI,EAAE,SAAS;QACfrK,KAAK,EAAE,SAAS;QAAE;QAClBC,OAAO,EAAE,wBAAwB;QACjCQ,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,IAAI/B,OAAO,IAAIF,WAAW,CAACmG,MAAM,KAAK,CAAC,EAAE;IACvC,oBACE7G,OAAA;MAAKwM,SAAS,EAAC,4GAA4G;MAAAC,QAAA,eACzHzM,OAAA,CAAC5B,MAAM,CAACsO,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvBzM,OAAA,CAAC5B,MAAM,CAACsO,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DX,SAAS,EAAC;QAAqF;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACFvN,OAAA;UAAGwM,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAgC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACEvN,OAAA,CAAAE,SAAA;IAAAuM,QAAA,gBACEzM,OAAA;MAAOwN,GAAG;MAAAf,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACVvN,OAAA;MAAKwM,SAAS,EAAC,iHAAiH;MAAAC,QAAA,gBAEhIzM,OAAA;QAAKwM,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CzM,OAAA;UAAKwM,SAAS,EAAC;QAA2H;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjJvN,OAAA;UAAKwM,SAAS,EAAC;QAAkJ;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxKvN,OAAA;UAAKwM,SAAS,EAAC;QAA2I;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9J,CAAC,eAGNvN,OAAA;QAAKwM,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,GAAGgB,KAAK,CAAC,EAAE,CAAC,CAAC,CAACjJ,GAAG,CAAC,CAACkJ,CAAC,EAAEC,CAAC,kBACvB3N,OAAA,CAAC5B,MAAM,CAACsO,GAAG;UAETF,SAAS,EAAC,mDAAmD;UAC7DK,OAAO,EAAE;YACPe,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACfC,CAAC,EAAE,CAAC,CAAC,EAAE1G,IAAI,CAACuC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;YACnCkD,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UACzB,CAAE;UACFG,UAAU,EAAE;YACVC,QAAQ,EAAE,CAAC,GAAG7F,IAAI,CAACuC,MAAM,CAAC,CAAC,GAAG,CAAC;YAC/BuD,MAAM,EAAEC,QAAQ;YAChBY,KAAK,EAAE3G,IAAI,CAACuC,MAAM,CAAC,CAAC,GAAG;UACzB,CAAE;UACFqE,KAAK,EAAE;YACLC,IAAI,EAAG,GAAE7G,IAAI,CAACuC,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC/BuE,GAAG,EAAG,GAAE9G,IAAI,CAACuC,MAAM,CAAC,CAAC,GAAG,GAAI;UAC9B;QAAE,GAfGiE,CAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENvN,OAAA;QAAKwM,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5BzM,OAAA,CAAC5B,MAAM,CAACsO,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCf,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BR,SAAS,EAAC,2CAA2C;UAAAC,QAAA,eAErDzM,OAAA;YAAKwM,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCzM,OAAA;cAAKwM,SAAS,EAAC,yFAAyF;cAAAC,QAAA,eACtGzM,OAAA;gBAAKwM,SAAS,EAAC,+EAA+E;gBAAAC,QAAA,gBAG5FzM,OAAA,CAAC5B,MAAM,CAAC8P,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM7N,QAAQ,CAAC,WAAW,CAAE;kBACrC+L,SAAS,EAAC,gNAAgN;kBAC1NuB,KAAK,EAAE;oBACLQ,QAAQ,EAAEnE,MAAM,CAACoE,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA/B,QAAA,gBAEFzM,OAAA,CAACjB,MAAM;oBAACyN,SAAS,EAAC;kBAAuB;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CvN,OAAA;oBAAAyM,QAAA,EAAM;kBAAG;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGhBvN,OAAA,CAAC5B,MAAM,CAAC8P,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAE5D,YAAa;kBACtB8B,SAAS,EAAC,kNAAkN;kBAC5NuB,KAAK,EAAE;oBACLU,UAAU,EAAE,0CAA0C;oBACtDvM,KAAK,EAAE,SAAS;oBAChBwM,UAAU,EAAE,MAAM;oBAClBC,UAAU,EAAE,KAAK;oBACjBJ,QAAQ,EAAEnE,MAAM,CAACoE,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA/B,QAAA,gBAEFzM,OAAA,CAACnB,QAAQ;oBAAC2N,SAAS,EAAC;kBAAuB;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9CvN,OAAA;oBAAAyM,QAAA,EACG3L,eAAe,GAAI,YAAWA,eAAgB,EAAC,GAC9C,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,IAAI,MAAK,OAAO,IAAIlG,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6H,OAAO,GAAI,YAAY,GAAG;kBAAS;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGhBvN,OAAA,CAAC5B,MAAM,CAAC8P,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM;oBACb7K,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;oBACtCD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAElD,IAAI,CAAC;oBACzCiD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,CAAC;oBAClCjB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,IAAI,CAAC;oBACrClB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6H,OAAO,CAAC;oBAC3C5E,OAAO,CAACC,GAAG,CAAC,YAAY,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,IAAI,CAAC;oBACrCjD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEhD,WAAW,CAACmG,MAAM,CAAC;oBACvDpD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEhD,WAAW,CAACgI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClE,GAAG,CAACmE,CAAC,KAAK;sBACzEC,EAAE,EAAED,CAAC,CAACjE,GAAG;sBACTC,IAAI,EAAEgE,CAAC,CAAChE,IAAI;sBACZS,IAAI,EAAEuD,CAAC,CAACvD,IAAI;sBACZf,OAAO,EAAEsE,CAAC,CAACtE;oBACb,CAAC,CAAC,CAAC,CAAC;oBACJZ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEhD,WAAW,CAAC8D,GAAG,CAACmE,CAAC,IAAIA,CAAC,CAACjE,GAAG,CAAC,CAAC;oBACpEjB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,CAAC;oBAC9CjB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhD,WAAW,CAACqI,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAACjE,GAAG,MAAKlE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,EAAC,CAAC;oBACjFjB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE9B,aAAa,CAACmJ,OAAO,CAAC;oBAC5DtH,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE7B,WAAW,CAACkJ,OAAO,CAAC;oBACxDtH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE5C,eAAe,CAAC;;oBAEhD;oBACA,MAAM8N,YAAY,GAAGvD,QAAQ,CAACC,gBAAgB,CAAE,kBAAiB9K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAI,IAAG,CAAC;oBAC/EjB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEkL,YAAY,CAAC/H,MAAM,CAAC;oBACxD+H,YAAY,CAACtI,OAAO,CAAC,CAACuI,EAAE,EAAElB,CAAC,KAAK;sBAC9BlK,OAAO,CAACC,GAAG,CAAE,WAAUiK,CAAE,GAAE,EAAEkB,EAAE,EAAE,OAAO,EAAEA,EAAE,CAACrD,YAAY,CAAC,gBAAgB,CAAC,CAAC;oBAC9E,CAAC,CAAC;kBACJ,CAAE;kBACFgB,SAAS,EAAC,uDAAuD;kBAAAC,QAAA,EAClE;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAGhBvN,OAAA,CAAC5B,MAAM,CAAC8P,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEtB,MAAM,EAAE;kBAAI,CAAE;kBACzCuB,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAE9K,gBAAiB;kBAC1BsL,QAAQ,EAAElO,OAAQ;kBAClB4L,SAAS,EAAC,qNAAqN;kBAC/NuB,KAAK,EAAE;oBACLQ,QAAQ,EAAEnE,MAAM,CAACoE,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA/B,QAAA,gBAEFzM,OAAA,CAAChB,SAAS;oBAACwN,SAAS,EAAG,yBAAwB5L,OAAO,GAAG,cAAc,GAAG,EAAG;kBAAE;oBAAAwM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClFvN,OAAA;oBAAAyM,QAAA,EAAM;kBAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZ,CAAC,CAAA/M,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,IAAI,MAAK,OAAO,KAAIlG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6H,OAAO,mBACvCrI,OAAA,CAAC5B,MAAM,CAACsO,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCf,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BR,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CzM,OAAA;YAAKwM,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCzM,OAAA;cAAKwM,SAAS,EAAC,gHAAgH;cAAAC,QAAA,eAC7HzM,OAAA;gBAAKwM,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCzM,OAAA;kBAAKwM,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClFzM,OAAA;oBAAMwM,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAAC;kBAAE;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNvN,OAAA;kBAAAyM,QAAA,gBACEzM,OAAA;oBAAIwM,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpDvN,OAAA;oBAAGwM,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,eAGDvN,OAAA,CAAC5B,MAAM,CAACsO,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCf,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEG,IAAI,EAAE;UAAU,CAAE;UAC7CX,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAGzCzM,OAAA;YAAKwM,SAAS,EAAC,kGAAkG;YAAAC,QAAA,gBAC/GzM,OAAA;cAAKwM,SAAS,EAAC;YAA6E;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnGvN,OAAA;cAAKwM,SAAS,EAAC;YAA+E;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGrGvN,OAAA;cAAKwM,SAAS,EAAC,2EAA2E;cAAAC,QAAA,eACxFzM,OAAA;gBAAKwM,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAG5CzM,OAAA,CAAC5B,MAAM,CAACsO,GAAG;kBACTG,OAAO,EAAE;oBACPuB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBW,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFhC,UAAU,EAAE;oBACVC,QAAQ,EAAE,CAAC;oBACXC,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR,CAAE;kBACFX,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBzM,OAAA;oBAAIwM,SAAS,EAAC,iGAAiG;oBAAAC,QAAA,gBAC7GzM,OAAA,CAAC5B,MAAM,CAAC4Q,IAAI;sBACVnC,OAAO,EAAE;wBACPoC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;sBACrD,CAAE;sBACFlC,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFX,SAAS,EAAC,+HAA+H;sBACzIuB,KAAK,EAAE;wBACLmB,cAAc,EAAE,WAAW;wBAC3BC,oBAAoB,EAAE,MAAM;wBAC5BC,mBAAmB,EAAE,aAAa;wBAClCjL,MAAM,EAAE;sBACV,CAAE;sBAAAsI,QAAA,EACH;oBAED;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACdvN,OAAA;sBAAAoN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNvN,OAAA,CAAC5B,MAAM,CAAC4Q,IAAI;sBACVnC,OAAO,EAAE;wBACP6B,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;sBAEhE,CAAE;sBACF3B,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACbC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFY,KAAK,EAAE;wBACL7L,KAAK,EAAE,SAAS;wBAChByM,UAAU,EAAE,KAAK;wBACjBD,UAAU,EAAE;sBACd,CAAE;sBAAAjC,QAAA,EACH;oBAED;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGbvN,OAAA,CAAC5B,MAAM,CAACiR,CAAC;kBACP1C,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAG,CAAE;kBAC/Bf,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAE,CAAE;kBAC9Bb,UAAU,EAAE;oBAAEe,KAAK,EAAE,GAAG;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBAC1CR,SAAS,EAAC,8GAA8G;kBACxHuB,KAAK,EAAE;oBACL7L,KAAK,EAAE,SAAS;oBAChBwM,UAAU,EAAE,6BAA6B;oBACzCD,UAAU,EAAE,0CAA0C;oBACtDU,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE;kBACvB,CAAE;kBAAA3C,QAAA,EACH;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAGXvN,OAAA,CAAC5B,MAAM,CAACsO,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEwB,KAAK,EAAE;kBAAI,CAAE;kBACpCvB,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEwB,KAAK,EAAE;kBAAE,CAAE;kBAClCrB,UAAU,EAAE;oBAAEe,KAAK,EAAE,GAAG;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBAC1CR,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBzM,OAAA;oBAAGwM,SAAS,EAAC,6JAA6J;oBACvKuB,KAAK,EAAE;sBACLW,UAAU,EAAE,6BAA6B;sBACzCY,SAAS,EAAE;oBACb,CAAE;oBAAA7C,QAAA,EACFnL;kBAAiB;oBAAA8L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGbvN,OAAA,CAAC5B,MAAM,CAACsO,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAG,CAAE;kBAC/Bf,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAE,CAAE;kBAC9Bb,UAAU,EAAE;oBAAEe,KAAK,EAAE,CAAC;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBACxCR,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,EAEpF,CACC;oBACEjK,IAAI,EAAEjD,OAAO;oBACbgQ,KAAK,EAAE7O,WAAW,CAACmG,MAAM;oBACzB2I,KAAK,EAAE,WAAW;oBAClBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpB/M,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE/D,QAAQ;oBACd8Q,KAAK,EAAE/E,aAAa,CAAC3D,MAAM;oBAC3B2I,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,oDAAoD;oBAChEC,SAAS,EAAE,SAAS;oBACpB/M,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE5D,OAAO;oBACb2Q,KAAK,EAAE7O,WAAW,CAACyD,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC1D,aAAa,GAAG,CAAC,CAAC,CAAC4B,MAAM;oBAC1D2I,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,gDAAgD;oBAC5DC,SAAS,EAAE,SAAS;oBACpB/M,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE7D,MAAM;oBACZ4Q,KAAK,EAAE7O,WAAW,CAACqG,MAAM,CAAC,CAACC,GAAG,EAAE2B,CAAC,KAAK3B,GAAG,IAAI2B,CAAC,CAACtE,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACsL,cAAc,CAAC,CAAC;oBACjFH,KAAK,EAAE,UAAU;oBACjBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpB/M,WAAW,EAAE;kBACf,CAAC,CACF,CAAC6B,GAAG,CAAC,CAACoL,IAAI,EAAEnL,KAAK,kBAChBzE,OAAA,CAAC5B,MAAM,CAACsO,GAAG;oBAETC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEwB,KAAK,EAAE;oBAAI,CAAE;oBACpCvB,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEwB,KAAK,EAAE;oBAAE,CAAE;oBAClCrB,UAAU,EAAE;sBAAEe,KAAK,EAAE,GAAG,GAAGrJ,KAAK,GAAG,GAAG;sBAAEuI,QAAQ,EAAE;oBAAI,CAAE;oBACxDmB,UAAU,EAAE;sBAAEC,KAAK,EAAE,IAAI;sBAAER,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCpB,SAAS,EAAG,qBAAoBoD,IAAI,CAACH,UAAW,8EAA8E;oBAC9H1B,KAAK,EAAE;sBACL8B,MAAM,EAAG,aAAYD,IAAI,CAACjN,WAAY,IAAG;sBACzCmN,SAAS,EAAG,cAAaF,IAAI,CAACjN,WAAY;oBAC5C,CAAE;oBAAA8J,QAAA,gBAEFzM,OAAA;sBAAKwM,SAAS,EAAC;oBAAgE;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtFvN,OAAA,CAAC4P,IAAI,CAACpN,IAAI;sBACRgK,SAAS,EAAC,kDAAkD;sBAC5DuB,KAAK,EAAE;wBAAE7L,KAAK,EAAE0N,IAAI,CAACF,SAAS;wBAAEvL,MAAM,EAAE;sBAAyC;oBAAE;sBAAAiJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC,eACFvN,OAAA;sBACEwM,SAAS,EAAC,0EAA0E;sBACpFuB,KAAK,EAAE;wBACL7L,KAAK,EAAE0N,IAAI,CAACF,SAAS;wBACrBhB,UAAU,EAAG,6BAA4B;wBACzCvK,MAAM,EAAE,oCAAoC;wBAC5CoK,QAAQ,EAAE;sBACZ,CAAE;sBAAA9B,QAAA,EAEDmD,IAAI,CAACL;oBAAK;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACNvN,OAAA;sBACEwM,SAAS,EAAC,4CAA4C;sBACtDuB,KAAK,EAAE;wBACL7L,KAAK,EAAE,SAAS;wBAChBwM,UAAU,EAAE,6BAA6B;wBACzCH,QAAQ,EAAE;sBACZ,CAAE;sBAAA9B,QAAA,EAEDmD,IAAI,CAACJ;oBAAK;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA,GApCD9I,KAAK;oBAAA2I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqCA,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZ3M,OAAO,iBACNZ,OAAA,CAAC5B,MAAM,CAACsO,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBJ,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAE3DzM,OAAA,CAAC5B,MAAM,CAACsO,GAAG;YACTG,OAAO,EAAE;cAAEC,MAAM,EAAE;YAAI,CAAE;YACzBC,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAEC,IAAI,EAAE;YAAS,CAAE;YAC9DX,SAAS,EAAC;UAA6E;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eACFvN,OAAA;YAAGwM,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CACb,EAGA,CAAC3M,OAAO,iBACPZ,OAAA,CAAC5B,MAAM,CAACsO,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAG,CAAE;UAC/Bf,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEe,KAAK,EAAE,GAAG;YAAEd,QAAQ,EAAE;UAAI,CAAE;UAC1CR,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eAEtCzM,OAAA;YAAKwM,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAG/BjC,aAAa,CAAC3D,MAAM,GAAG,CAAC,iBACvB7G,OAAA,CAAC5B,MAAM,CAACsO,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEwB,KAAK,EAAE;cAAI,CAAE;cACpCvB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEwB,KAAK,EAAE;cAAE,CAAE;cAClCrB,UAAU,EAAE;gBAAEe,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CR,SAAS,EAAC,OAAO;cAAAC,QAAA,gBAEjBzM,OAAA;gBAAIwM,SAAS,EAAC,2EAA2E;gBAACuB,KAAK,EAAE;kBAC/FU,UAAU,EAAE,mDAAmD;kBAC/DU,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCV,UAAU,EAAE,6BAA6B;kBACzCvK,MAAM,EAAE;gBACV,CAAE;gBAAAsI,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELvN,OAAA;gBAAKwM,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,EACpGjC,aAAa,CAAChG,GAAG,CAAC,CAACuL,QAAQ,EAAEtL,KAAK,KAAK;kBACtC,MAAMuL,QAAQ,GAAGvL,KAAK,GAAG,CAAC;kBAC1B,MAAMwL,aAAa,GAAGzP,IAAI,IAAIuP,QAAQ,CAACrL,GAAG,KAAKlE,IAAI,CAACkE,GAAG;kBAEvD,oBACE1E,OAAA,CAAC5B,MAAM,CAACsO,GAAG;oBAETwD,GAAG,EAAED,aAAa,GAAGrO,aAAa,GAAG,IAAK;oBAC1C,gBAAcmO,QAAQ,CAACrL,GAAI;oBAC3B,kBAAgBsL,QAAS;oBACzBrD,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE;oBAAG,CAAE;oBAC/Bf,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE;oBAAE,CAAE;oBAC9Bb,UAAU,EAAE;sBAAEe,KAAK,EAAE,GAAG,GAAGrJ,KAAK,GAAG,GAAG;sBAAEuI,QAAQ,EAAE;oBAAI,CAAE;oBACxDmB,UAAU,EAAE;sBAAEC,KAAK,EAAE,IAAI;sBAAER,CAAC,EAAE,CAAC;oBAAG,CAAE;oBACpCpB,SAAS,EAAG,YACVwD,QAAQ,KAAK,CAAC,GAAG,yBAAyB,GAC1CA,QAAQ,KAAK,CAAC,GAAG,YAAY,GAAG,YACjC,IAAGC,aAAa,GAAG,wBAAwB,GAAG,EAAG,IAAGzO,UAAU,IAAIyO,aAAa,GAAG,mBAAmB,GAAG,EAAG,EAAE;oBAAAxD,QAAA,eAG9GzM,OAAA;sBACEwM,SAAS,EAAG,8BAA6BuD,QAAQ,CAAC3M,IAAI,CAAClB,KAAM,mBAAkB6N,QAAQ,CAAC3M,IAAI,CAACb,IAAK,IAAGwN,QAAQ,CAAC3M,IAAI,CAACR,MAAO,YAAY;sBACtImL,KAAK,EAAE;wBACL+B,SAAS,EAAG,eAAcC,QAAQ,CAAC3M,IAAI,CAACd,WAAY,cAAayN,QAAQ,CAAC3M,IAAI,CAACd,WAAY;sBAC7F,CAAE;sBAAAmK,QAAA,eAEFzM,OAAA;wBACEwM,SAAS,EAAG,GAAEuD,QAAQ,CAAC3M,IAAI,CAACjB,OAAQ,8EAA8E;wBAClH4L,KAAK,EAAE;0BACL8B,MAAM,EAAG,aAAYE,QAAQ,CAAC3M,IAAI,CAACT,WAAY;wBACjD,CAAE;wBAAA8J,QAAA,gBAEFzM,OAAA;0BAAKwM,SAAS,EAAC;wBAAiE;0BAAAY,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAGvFvN,OAAA;0BACEwM,SAAS,EAAG,iGAAgGuD,QAAQ,CAAC3M,IAAI,CAAClB,KAAM,2FAA2F;0BAC3N6L,KAAK,EAAE;4BACL7L,KAAK,EAAE8N,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAGA,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAGA,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;4BACvGtB,UAAU,EAAE,6BAA6B;4BACzCmB,MAAM,EAAG,aAAYE,QAAQ,CAAC3M,IAAI,CAACT,WAAY,EAAC;4BAChDmN,SAAS,EAAG,aAAYC,QAAQ,CAAC3M,IAAI,CAACd,WAAY,cAAayN,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;4BAC1FiM,QAAQ,EAAE,2BAA2B;4BACrCI,UAAU,EAAE;0BACd,CAAE;0BAAAlC,QAAA,EAEDuD,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAGA,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAGA,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAGA;wBAAQ;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9E,CAAC,EAGLyC,QAAQ,KAAK,CAAC,iBACbhQ,OAAA,CAAC5B,MAAM,CAACsO,GAAG;0BACTG,OAAO,EAAE;4BAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;0BAAE,CAAE;0BACrCC,UAAU,EAAE;4BAAEC,QAAQ,EAAE,CAAC;4BAAEC,MAAM,EAAEC;0BAAS,CAAE;0BAC9CV,SAAS,EAAC,qDAAqD;0BAAAC,QAAA,eAE/DzM,OAAA,CAACtB,OAAO;4BAAC8N,SAAS,EAAC;0BAAyB;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CACb,eAGDvN,OAAA;0BAAKwM,SAAS,EAAG,yBAAwByD,aAAa,GAAG,wCAAwC,GAAG,EAAG,EAAE;0BAAAxD,QAAA,gBACvGzM,OAAA;4BACEwM,SAAS,EAAC,qEAAqE;4BAC/EuB,KAAK,EAAE;8BACLU,UAAU,EAAG,0BAAyBsB,QAAQ,CAAC3M,IAAI,CAACT,WAAY,KAAIoN,QAAQ,CAAC3M,IAAI,CAAChB,SAAU,GAAE;8BAC9F0N,SAAS,EAAG,aAAYC,QAAQ,CAAC3M,IAAI,CAACd,WAAY,aAAYyN,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;8BACzF6N,OAAO,EAAE;4BACX,CAAE;4BAAA1D,QAAA,EAEDsD,QAAQ,CAACjL,cAAc,gBACtB9E,OAAA;8BACEoQ,GAAG,EAAEL,QAAQ,CAACjL,cAAe;8BAC7BuL,GAAG,EAAEN,QAAQ,CAACpL,IAAK;8BACnB6H,SAAS,EAAC,yCAAyC;8BACnDuB,KAAK,EAAE;gCACL5J,MAAM,EAAE,+BAA+B;gCACvCmM,WAAW,EAAE;8BACf;4BAAE;8BAAAlD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,gBAEFvN,OAAA;8BACEwM,SAAS,EAAC,iFAAiF;8BAC3FuB,KAAK,EAAE;gCACLU,UAAU,EAAG,2BAA0BsB,QAAQ,CAAC3M,IAAI,CAACT,WAAY,KAAIoN,QAAQ,CAAC3M,IAAI,CAAChB,SAAU,GAAE;gCAC/FF,KAAK,EAAE,SAAS;gCAChBwM,UAAU,EAAE,6BAA6B;gCACzC4B,WAAW,EAAE;8BACf,CAAE;8BAAA7D,QAAA,EAEDsD,QAAQ,CAACpL,IAAI,CAAC4L,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;4BAAC;8BAAApD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnC;0BACN;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC,EACL0C,aAAa,iBACZjQ,OAAA;4BACEwM,SAAS,EAAC,4DAA4D;4BACtEuB,KAAK,EAAE;8BACLU,UAAU,EAAE,0CAA0C;8BACtDqB,SAAS,EAAE;4BACb,CAAE;4BAAArD,QAAA,eAEFzM,OAAA,CAACrB,MAAM;8BAAC6N,SAAS,EAAC;4BAAoB;8BAAAY,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eAGNvN,OAAA;0BACEwM,SAAS,EAAC,uCAAuC;0BACjDuB,KAAK,EAAE;4BACL7L,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAACf,SAAS;4BAC9BqM,UAAU,EAAG,eAAcqB,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;4BACtDiM,QAAQ,EAAE,QAAQ;4BAClBpK,MAAM,EAAE;0BACV,CAAE;0BAAAsI,QAAA,EAEDsD,QAAQ,CAACpL;wBAAI;0BAAAyI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACLvN,OAAA;0BACEwM,SAAS,EAAG,6DAA4DuD,QAAQ,CAAC3M,IAAI,CAAClB,KAAM,mEAAmE;0BAC/J6L,KAAK,EAAE;4BACL7L,KAAK,EAAE,SAAS;4BAChBwM,UAAU,EAAE,6BAA6B;4BACzCmB,MAAM,EAAG,aAAYE,QAAQ,CAAC3M,IAAI,CAACT,WAAY,EAAC;4BAChDmN,SAAS,EAAG,cAAaC,QAAQ,CAAC3M,IAAI,CAACd,WAAY,cAAayN,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;4BAC3FiM,QAAQ,EAAE,QAAQ;4BAClBkC,aAAa,EAAE;0BACjB,CAAE;0BAAAhE,QAAA,gBAEFzM,OAAA,CAAC+P,QAAQ,CAAC3M,IAAI,CAACZ,IAAI;4BACjBgK,SAAS,EAAC,SAAS;4BACnBuB,KAAK,EAAE;8BACL5J,MAAM,EAAE,wCAAwC;8BAChDjC,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAAChB;4BACvB;0BAAE;4BAAAgL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eACFvN,OAAA;4BAAM+N,KAAK,EAAE;8BAAE7L,KAAK,EAAE;4BAAU,CAAE;4BAAAuK,QAAA,EAAEsD,QAAQ,CAAC3M,IAAI,CAACX;0BAAK;4BAAA2K,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5D,CAAC,EAGL,CAAC,MAAM;0BACN,MAAMmD,KAAK,GAAG5E,oBAAoB,CAChCiE,QAAQ,CAAC5K,kBAAkB,EAC3B4K,QAAQ,CAAChE,mBAAmB,EAC5BgE,QAAQ,CAAC/D,gBAAgB,EACzB+D,QAAQ,CAAC9D,eAAe,EACxBxH,KACF,CAAC;0BACD,oBACEzE,OAAA;4BACEwM,SAAS,EAAC,4FAA4F;4BACtGuB,KAAK,EAAE;8BACL4C,eAAe,EAAED,KAAK,CAACvO,OAAO;8BAC9BD,KAAK,EAAEwO,KAAK,CAACxO,KAAK;8BAClB2N,MAAM,EAAG,aAAYa,KAAK,CAAC/N,WAAY,EAAC;8BACxC+L,UAAU,EAAE,6BAA6B;8BACzCH,QAAQ,EAAE,SAAS;8BACnBkC,aAAa,EAAE;4BACjB,CAAE;4BAAAhE,QAAA,EAEDiE,KAAK,CAACnE;0BAAI;4BAAAa,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACR,CAAC;wBAEV,CAAC,EAAE,CAAC,eAGJvN,OAAA;0BAAKwM,SAAS,EAAC,yBAAyB;0BAAAC,QAAA,gBACtCzM,OAAA;4BAAKwM,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC7CzM,OAAA;8BAAM+N,KAAK,EAAE;gCACX7L,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAAChB,SAAS;gCAC9BsM,UAAU,EAAG,eAAcqB,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;gCACtDqM,UAAU,EAAE,KAAK;gCACjBJ,QAAQ,EAAE;8BACZ,CAAE;8BAAA9B,QAAA,EAAC;4BAAM;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eAChBvN,OAAA;8BAAM+N,KAAK,EAAE;gCACX7L,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAACf,SAAS;gCAC9BqM,UAAU,EAAG,eAAcqB,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;gCACtDqM,UAAU,EAAE,KAAK;gCACjBJ,QAAQ,EAAE,QAAQ;gCAClBpK,MAAM,EAAE;8BACV,CAAE;8BAAAsI,QAAA,EAAEsD,QAAQ,CAAC1L,OAAO,CAACsL,cAAc,CAAC;4BAAC;8BAAAvC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1C,CAAC,eACNvN,OAAA;4BAAKwM,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC7CzM,OAAA;8BAAM+N,KAAK,EAAE;gCACX7L,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAAChB,SAAS;gCAC9BsM,UAAU,EAAG,eAAcqB,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;gCACtDqM,UAAU,EAAE,KAAK;gCACjBJ,QAAQ,EAAE;8BACZ,CAAE;8BAAA9B,QAAA,EAAC;4BAAW;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACrBvN,OAAA;8BAAM+N,KAAK,EAAE;gCACX7L,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAACf,SAAS;gCAC9BqM,UAAU,EAAG,eAAcqB,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;gCACtDqM,UAAU,EAAE,KAAK;gCACjBJ,QAAQ,EAAE,QAAQ;gCAClBpK,MAAM,EAAE;8BACV,CAAE;8BAAAsI,QAAA,EAAEsD,QAAQ,CAACzL;4BAAiB;8BAAA8I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnC,CAAC,eACNvN,OAAA;4BAAKwM,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC7CzM,OAAA;8BAAM+N,KAAK,EAAE;gCACX7L,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAAChB,SAAS;gCAC9BsM,UAAU,EAAG,eAAcqB,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;gCACtDqM,UAAU,EAAE,KAAK;gCACjBJ,QAAQ,EAAE;8BACZ,CAAE;8BAAA9B,QAAA,EAAC;4BAAU;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACpBvN,OAAA;8BAAM+N,KAAK,EAAE;gCACX7L,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAACf,SAAS;gCAC9BqM,UAAU,EAAG,eAAcqB,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;gCACtDqM,UAAU,EAAE,KAAK;gCACjBJ,QAAQ,EAAE,QAAQ;gCAClBpK,MAAM,EAAE;8BACV,CAAE;8BAACqI,SAAS,EAAC,yBAAyB;8BAAAC,QAAA,EACnCsD,QAAQ,CAAC9K;4BAAa;8BAAAmI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnB,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eAGNvN,OAAA;4BAAKwM,SAAS,EAAC,kBAAkB;4BAAAC,QAAA,eAC/BzM,OAAA;8BAAM+N,KAAK,EAAE;gCACX7L,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAAChB,SAAS;gCAC9BsM,UAAU,EAAG,eAAcqB,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;gCACtDqM,UAAU,EAAE,KAAK;gCACjBJ,QAAQ,EAAE,QAAQ;gCAClB3B,OAAO,EAAE;8BACX,CAAE;8BAAAH,QAAA,EACCsD,QAAQ,CAACnK,UAAU,KAAK,SAAS,GAAG,cAAc,GAClDmK,QAAQ,CAACnK,UAAU,KAAK,eAAe,GAAG,kBAAkB,GAC5D;4BAAc;8BAAAwH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACX;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAlODwC,QAAQ,CAACrL,GAAG;oBAAA0I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmOP,CAAC;gBAEjB,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGA9C,eAAe,CAAC5D,MAAM,GAAG,CAAC,iBACzB7G,OAAA,CAAC5B,MAAM,CAACsO,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAG,CAAE;cAC/Bf,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAE,CAAE;cAC9Bb,UAAU,EAAE;gBAAEe,KAAK,EAAE,CAAC;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cACxCR,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAEtCzM,OAAA;gBAAIwM,SAAS,EAAC,0EAA0E;gBAACuB,KAAK,EAAE;kBAC9FU,UAAU,EAAE,mDAAmD;kBAC/DU,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCV,UAAU,EAAE,6BAA6B;kBACzCvK,MAAM,EAAE;gBACV,CAAE;gBAAAsI,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELvN,OAAA;gBAAKwM,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EACjDhC,eAAe,CAACjG,GAAG,CAAC,CAACuL,QAAQ,EAAEtL,KAAK,KAAK;kBACxC,MAAMmM,UAAU,GAAGnM,KAAK,GAAG,CAAC,CAAC,CAAC;kBAC9B,MAAMwL,aAAa,GAAGzP,IAAI,IAAIuP,QAAQ,CAACrL,GAAG,KAAKlE,IAAI,CAACkE,GAAG;kBAEvD,oBACE1E,OAAA,CAAC5B,MAAM,CAACsO,GAAG;oBAETwD,GAAG,EAAED,aAAa,GAAGpO,WAAW,GAAG,IAAK;oBACxC,gBAAckO,QAAQ,CAACrL,GAAI;oBAC3B,kBAAgBkM,UAAW;oBAC3BjE,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEiB,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAChChB,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEiB,CAAC,EAAE;oBAAE,CAAE;oBAC9Bd,UAAU,EAAE;sBAAEe,KAAK,EAAE,GAAG,GAAGrJ,KAAK,GAAG,GAAG;sBAAEuI,QAAQ,EAAE;oBAAI,CAAE;oBACxDmB,UAAU,EAAE;sBAAEC,KAAK,EAAE,IAAI;sBAAEP,CAAC,EAAE;oBAAG,CAAE;oBACnCrB,SAAS,EAAG,YAAWyD,aAAa,GAAG,wBAAwB,GAAG,EAAG,IAAGzO,UAAU,IAAIyO,aAAa,GAAG,mBAAmB,GAAG,EAAG,EAAE;oBAAAxD,QAAA,eAEjIzM,OAAA;sBACEwM,SAAS,EAAG,oBAAmBuD,QAAQ,CAAC3M,IAAI,CAAClB,KAAM,mBAAkB6N,QAAQ,CAAC3M,IAAI,CAACb,IAAK,IAAGwN,QAAQ,CAAC3M,IAAI,CAACR,MAAO,EAAE;sBAClHmL,KAAK,EAAE;wBACL+B,SAAS,EAAG,cAAaC,QAAQ,CAAC3M,IAAI,CAACd,WAAY;sBACrD,CAAE;sBAAAmK,QAAA,eAEFzM,OAAA;wBACEwM,SAAS,EAAG,GAAEuD,QAAQ,CAAC3M,IAAI,CAACjB,OAAQ,mGAAmG;wBACvI4L,KAAK,EAAE;0BACL8B,MAAM,EAAG,aAAYE,QAAQ,CAAC3M,IAAI,CAACT,WAAY;wBACjD,CAAE;wBAAA8J,QAAA,gBAEFzM,OAAA;0BAAKwM,SAAS,EAAC;wBAA+D;0BAAAY,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAGrFvN,OAAA;0BACEwM,SAAS,EAAG,yDAAwDuD,QAAQ,CAAC3M,IAAI,CAAClB,KAAM,2FAA2F;0BACnL6L,KAAK,EAAE;4BACL7L,KAAK,EAAE0O,UAAU,IAAI,EAAE,GAAGb,QAAQ,CAAC3M,IAAI,CAAChB,SAAS,GAAG,SAAS;4BAC7DsM,UAAU,EAAE,6BAA6B;4BACzCmB,MAAM,EAAG,aAAYE,QAAQ,CAAC3M,IAAI,CAACT,WAAY,EAAC;4BAChDmN,SAAS,EAAG,cAAaC,QAAQ,CAAC3M,IAAI,CAACd,WAAY,cAAayN,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;4BAC3FiM,QAAQ,EAAEqC,UAAU,IAAI,EAAE,GAAG,QAAQ,GAAG,QAAQ;4BAChDjC,UAAU,EAAE;0BACd,CAAE;0BAAAlC,QAAA,EAEDmE,UAAU,IAAI,EAAE,GAAI,IAAGA,UAAW,EAAC,GAAGA;wBAAU;0BAAAxD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C,CAAC,eAGNvN,OAAA;0BAAKwM,SAAS,EAAC,wBAAwB;0BAAAC,QAAA,gBACrCzM,OAAA;4BACEwM,SAAS,EAAC,mEAAmE;4BAC7EuB,KAAK,EAAE;8BACLU,UAAU,EAAG,0BAAyBsB,QAAQ,CAAC3M,IAAI,CAACT,WAAY,KAAIoN,QAAQ,CAAC3M,IAAI,CAAChB,SAAU,GAAE;8BAC9F0N,SAAS,EAAG,aAAYC,QAAQ,CAAC3M,IAAI,CAACd,WAAY,aAAYyN,QAAQ,CAAC3M,IAAI,CAACd,WAAY;4BAC1F,CAAE;4BAAAmK,QAAA,EAEDsD,QAAQ,CAACjL,cAAc,gBACtB9E,OAAA;8BACEoQ,GAAG,EAAEL,QAAQ,CAACjL,cAAe;8BAC7BuL,GAAG,EAAEN,QAAQ,CAACpL,IAAK;8BACnB6H,SAAS,EAAC,yCAAyC;8BACnDuB,KAAK,EAAE;gCACL5J,MAAM,EAAE,6CAA6C;gCACrDmM,WAAW,EAAE;8BACf;4BAAE;8BAAAlD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,gBAEFvN,OAAA;8BACEwM,SAAS,EAAC,gFAAgF;8BAC1FuB,KAAK,EAAE;gCACLU,UAAU,EAAG,2BAA0BsB,QAAQ,CAAC3M,IAAI,CAACT,WAAY,KAAIoN,QAAQ,CAAC3M,IAAI,CAAChB,SAAU,GAAE;gCAC/FF,KAAK,EAAE,SAAS;gCAChBwM,UAAU,EAAE,6BAA6B;gCACzC4B,WAAW,EAAE;8BACf,CAAE;8BAAA7D,QAAA,EAEDsD,QAAQ,CAACpL,IAAI,CAAC4L,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;4BAAC;8BAAApD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnC;0BACN;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC,EACL0C,aAAa,iBACZjQ,OAAA;4BACEwM,SAAS,EAAC,yDAAyD;4BACnEuB,KAAK,EAAE;8BACLU,UAAU,EAAE,0CAA0C;8BACtDqB,SAAS,EAAE;4BACb,CAAE;4BAAArD,QAAA,eAEFzM,OAAA,CAACrB,MAAM;8BAAC6N,SAAS,EAAC;4BAAoB;8BAAAY,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eAGNvN,OAAA;0BAAKwM,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,gBAC3CzM,OAAA;4BAAKwM,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,gBAC3CzM,OAAA;8BACEwM,SAAS,EAAC,qDAAqD;8BAC/DuB,KAAK,EAAE;gCACL7L,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAACf,SAAS;gCAC9BqM,UAAU,EAAG,eAAcqB,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;gCACtDiM,QAAQ,EAAE,+BAA+B;gCACzCpK,MAAM,EAAE;8BACV,CAAE;8BAAAsI,QAAA,EAEDsD,QAAQ,CAACpL;4BAAI;8BAAAyI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACZ,CAAC,EACJ0C,aAAa,iBACZjQ,OAAA;8BACEwM,SAAS,EAAC,yDAAyD;8BACnEuB,KAAK,EAAE;gCACLU,UAAU,EAAE,0CAA0C;gCACtDvM,KAAK,EAAE,SAAS;gCAChBwM,UAAU,EAAE,MAAM;gCAClBmB,MAAM,EAAE,mBAAmB;gCAC3BC,SAAS,EAAE;8BACb,CAAE;8BAAArD,QAAA,EACH;4BAED;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACN;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC,eACNvN,OAAA;4BACEwM,SAAS,EAAG,6DAA4DuD,QAAQ,CAAC3M,IAAI,CAAClB,KAAM,kCAAkC;4BAC9H6L,KAAK,EAAE;8BACL7L,KAAK,EAAE,SAAS;8BAChBwM,UAAU,EAAE,6BAA6B;8BACzCmB,MAAM,EAAG,aAAYE,QAAQ,CAAC3M,IAAI,CAACT,WAAY,EAAC;8BAChDmN,SAAS,EAAG,aAAYC,QAAQ,CAAC3M,IAAI,CAACd,WAAY,cAAayN,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;8BAC1FiM,QAAQ,EAAE,QAAQ;8BAClBkC,aAAa,EAAE;4BACjB,CAAE;4BAAAhE,QAAA,gBAEFzM,OAAA,CAAC+P,QAAQ,CAAC3M,IAAI,CAACZ,IAAI;8BACjBgK,SAAS,EAAC,SAAS;8BACnBuB,KAAK,EAAE;gCACL5J,MAAM,EAAE,wCAAwC;gCAChDjC,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAAChB;8BACvB;4BAAE;8BAAAgL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,eACFvN,OAAA;8BAAM+N,KAAK,EAAE;gCAAE7L,KAAK,EAAE;8BAAU,CAAE;8BAAAuK,QAAA,EAAEsD,QAAQ,CAAC3M,IAAI,CAACX;4BAAK;8BAAA2K,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5D,CAAC,EAGL,CAAC,MAAM;4BACN,MAAMmD,KAAK,GAAG5E,oBAAoB,CAChCiE,QAAQ,CAAC5K,kBAAkB,EAC3B4K,QAAQ,CAAChE,mBAAmB,EAC5BgE,QAAQ,CAAC/D,gBAAgB,EACzB+D,QAAQ,CAAC9D,eAAe,EACxB2E,UACF,CAAC;4BACD,oBACE5Q,OAAA;8BACEwM,SAAS,EAAC,8EAA8E;8BACxFuB,KAAK,EAAE;gCACL4C,eAAe,EAAED,KAAK,CAACvO,OAAO;gCAC9BD,KAAK,EAAEwO,KAAK,CAACxO,KAAK;gCAClB2N,MAAM,EAAG,aAAYa,KAAK,CAAC/N,WAAY,EAAC;gCACxC+L,UAAU,EAAE,6BAA6B;gCACzCH,QAAQ,EAAE,QAAQ;gCAClBkC,aAAa,EAAE;8BACjB,CAAE;8BAAAhE,QAAA,EAEDiE,KAAK,CAACnE;4BAAI;8BAAAa,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACR,CAAC;0BAEV,CAAC,EAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC,eAGNvN,OAAA;0BAAKwM,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,gBACrDzM,OAAA;4BAAKwM,SAAS,EAAC,cAAc;4BAACuB,KAAK,EAAE;8BACnC7L,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAACf,SAAS;8BAC9BqM,UAAU,EAAG,eAAcqB,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;8BACtDqM,UAAU,EAAE,KAAK;8BACjBxK,MAAM,EAAE,oCAAoC;8BAC5CoK,QAAQ,EAAE;4BACZ,CAAE;4BAAA9B,QAAA,GAAC,eACE,EAACsD,QAAQ,CAAC1L,OAAO,CAACsL,cAAc,CAAC,CAAC,EAAC,KACxC;0BAAA;4BAAAvC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACNvN,OAAA;4BAAKwM,SAAS,EAAC,6CAA6C;4BAAAC,QAAA,gBAC1DzM,OAAA;8BAAMwM,SAAS,EAAC,yBAAyB;8BAACuB,KAAK,EAAE;gCAC/C7L,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAAChB,SAAS;gCAC9BsM,UAAU,EAAG,eAAcqB,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;gCACtDqM,UAAU,EAAE,KAAK;gCACjBJ,QAAQ,EAAE;8BACZ,CAAE;8BAAA9B,QAAA,gBACAzM,OAAA,CAAClB,OAAO;gCAAC0N,SAAS,EAAC,SAAS;gCAACuB,KAAK,EAAE;kCAAE7L,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAAChB;gCAAU;8BAAE;gCAAAgL,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EACzEwC,QAAQ,CAACzL,iBAAiB;4BAAA;8BAAA8I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvB,CAAC,eACPvN,OAAA;8BAAMwM,SAAS,EAAC,yBAAyB;8BAACuB,KAAK,EAAE;gCAC/C7L,KAAK,EAAE6N,QAAQ,CAAC3M,IAAI,CAAChB,SAAS;gCAC9BsM,UAAU,EAAG,eAAcqB,QAAQ,CAAC3M,IAAI,CAACd,WAAY,EAAC;gCACtDqM,UAAU,EAAE,KAAK;gCACjBJ,QAAQ,EAAE;8BACZ,CAAE;8BAAA9B,QAAA,gBACAzM,OAAA,CAACpB,OAAO;gCAAC4N,SAAS,EAAC,SAAS;gCAACuB,KAAK,EAAE;kCAAE7L,KAAK,EAAE;gCAAU;8BAAE;gCAAAkL,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EAC3DwC,QAAQ,CAAC9K,aAAa;4BAAA;8BAAAmI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnB,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAnMDwC,QAAQ,CAACrL,GAAG;oBAAA0I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAoMP,CAAC;gBAEjB,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGA7M,WAAW,CAACmG,MAAM,GAAG,CAAC,iBACrB7G,OAAA,CAAC5B,MAAM,CAACsO,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAG,CAAE;cAC/Bf,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAE,CAAE;cAC9Bb,UAAU,EAAE;gBAAEe,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CR,SAAS,EAAC,sIAAsI;cAAAC,QAAA,eAEhJzM,OAAA;gBAAKwM,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BzM,OAAA;kBAAIwM,SAAS,EAAC,wBAAwB;kBAACuB,KAAK,EAAE;oBAC5C7L,KAAK,EAAE,SAAS;oBAChBwM,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAA6B;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrCvN,OAAA;kBAAKwM,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DzM,OAAA;oBAAKwM,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7CzM,OAAA;sBAAKwM,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9C/L,WAAW,CAACyD,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC/C,UAAU,KAAK,SAAS,CAAC,CAACiB;oBAAM;sBAAAuG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACNvN,OAAA;sBAAKwM,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNvN,OAAA;oBAAKwM,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzM,OAAA;sBAAKwM,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC7C/L,WAAW,CAACyD,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC/C,UAAU,KAAK,eAAe,CAAC,CAACiB;oBAAM;sBAAAuG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNvN,OAAA;sBAAKwM,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACNvN,OAAA;oBAAKwM,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9CzM,OAAA;sBAAKwM,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/C/L,WAAW,CAACyD,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC/C,UAAU,KAAK,WAAW,CAAC,CAACiB;oBAAM;sBAAAuG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACNvN,OAAA;sBAAKwM,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvN,OAAA;kBAAGwM,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGAzM,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrCd,OAAA,CAAC5B,MAAM,CAACsO,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEwB,KAAK,EAAE;cAAI,CAAE;cACpCvB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEwB,KAAK,EAAE;cAAE,CAAE;cAClCrB,UAAU,EAAE;gBAAEe,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CR,SAAS,EAAC,wIAAwI;cAAAC,QAAA,eAElJzM,OAAA;gBAAKwM,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BzM,OAAA;kBAAIwM,SAAS,EAAC,yBAAyB;kBAACuB,KAAK,EAAE;oBAC7C7L,KAAK,EAAE,SAAS;oBAChBwM,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAAqB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BvN,OAAA;kBAAKwM,SAAS,EAAC,0BAA0B;kBAACuB,KAAK,EAAE;oBAC/C7L,KAAK,EAAE,SAAS;oBAChBwM,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,GAAC,GAAC,EAAC3L,eAAe;gBAAA;kBAAAsM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3BvN,OAAA;kBAAGwM,SAAS,EAAC,SAAS;kBAACuB,KAAK,EAAE;oBAC5B7L,KAAK,EAAE,SAAS;oBAChBwM,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAEH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAGDvN,OAAA,CAAC5B,MAAM,CAACsO,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAG,CAAE;cAC/Bf,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAE,CAAE;cAC9Bb,UAAU,EAAE;gBAAEe,KAAK,EAAE,CAAC;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cACxCR,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAE7BzM,OAAA;gBAAKwM,SAAS,EAAC,8HAA8H;gBAAAC,QAAA,gBAC3IzM,OAAA,CAAC5B,MAAM,CAACsO,GAAG;kBACTG,OAAO,EAAE;oBAAEuB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjCrB,UAAU,EAAE;oBAAEC,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAT,QAAA,eAE9CzM,OAAA,CAACb,QAAQ;oBAACqN,SAAS,EAAC;kBAAwC;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACbvN,OAAA;kBAAIwM,SAAS,EAAC,yBAAyB;kBAACuB,KAAK,EAAE;oBAC7C7L,KAAK,EAAE,SAAS;oBAChBwM,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAAqB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BvN,OAAA;kBAAGwM,SAAS,EAAC,gCAAgC;kBAACuB,KAAK,EAAE;oBACnD7L,KAAK,EAAE,SAAS;oBAChBwM,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAGH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJvN,OAAA,CAAC5B,MAAM,CAAC8P,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1B5B,SAAS,EAAC,sJAAsJ;kBAChK8B,OAAO,EAAEA,CAAA,KAAMlE,MAAM,CAACyG,QAAQ,CAACC,IAAI,GAAG,YAAa;kBAAArE,QAAA,EACpD;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAGZ7M,WAAW,CAACmG,MAAM,KAAK,CAAC,IAAI,CAACjG,OAAO,iBACnCZ,OAAA,CAAC5B,MAAM,CAACsO,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEwB,KAAK,EAAE;cAAI,CAAE;cACpCvB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEwB,KAAK,EAAE;cAAE,CAAE;cAClC5B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAE7BzM,OAAA,CAACvB,QAAQ;gBAAC+N,SAAS,EAAC;cAAsC;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7DvN,OAAA;gBAAIwM,SAAS,EAAC,yBAAyB;gBAACuB,KAAK,EAAE;kBAC7C7L,KAAK,EAAE,SAAS;kBAChBwM,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAlC,QAAA,EAAC;cAAgB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBvN,OAAA;gBAAGwM,SAAS,EAAC,SAAS;gBAACuB,KAAK,EAAE;kBAC5B7L,KAAK,EAAE,SAAS;kBAChBwM,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAlC,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAACnN,EAAA,CA3mDID,kBAAkB;EAAA,QACJ7B,WAAW,EAEZC,WAAW;AAAA;AAAAwS,EAAA,GAHxB5Q,kBAAkB;AA6mDxB,eAAeA,kBAAkB;AAAC,IAAA4Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}