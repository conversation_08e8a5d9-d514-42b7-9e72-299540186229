require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/userModel');
const Report = require('./models/reportModel');
const Exam = require('./models/examModel');
const XPTransaction = require('./models/xpTransactionModel');

// XP Calculation Configuration (matching xpCalculationService)
const XP_CONFIG = {
  baseXPPerCorrectAnswer: 10,
  difficultyMultipliers: {
    easy: 0.8,
    medium: 1.0,
    hard: 1.3
  },
  bonusConfig: {
    perfectScore: 0.5,        // 50% bonus for perfect score
    speedBonus: 0.3,          // Up to 30% bonus for speed
    streakBonus: 5,           // 5 XP per streak count
    firstAttemptBonus: 0.2,   // 20% bonus for first attempt
    levelMultiplierBase: 0.1  // 10% per level above 1
  }
};

// Connect to MongoDB
async function connectDB() {
    try {
        const mongoUrl = process.env.MONGO_URL;
        if (!mongoUrl) {
            throw new Error('MONGO_URL environment variable is not set');
        }
        await mongoose.connect(mongoUrl);
        console.log('✅ MongoDB Connected Successfully');
    } catch (error) {
        console.error('❌ MongoDB Connection Failed:', error);
        process.exit(1);
    }
}

// Calculate XP for a single quiz attempt
function calculateQuizXP(report, exam, user, isFirstAttempt = true) {
    const result = report.result;
    const correctAnswers = result.correctAnswers || [];
    const wrongAnswers = result.wrongAnswers || [];
    const totalQuestions = correctAnswers.length + wrongAnswers.length;
    const correctCount = correctAnswers.length;
    
    if (totalQuestions === 0) return 0;
    
    // Base XP calculation
    const baseXP = correctCount * XP_CONFIG.baseXPPerCorrectAnswer;
    
    // Difficulty bonus
    const difficulty = exam.difficulty || exam.difficultyLevel || 'medium';
    const difficultyMultiplier = XP_CONFIG.difficultyMultipliers[difficulty] || 1.0;
    const difficultyBonus = baseXP * (difficultyMultiplier - 1);
    
    // Perfect score bonus
    const perfectScoreBonus = correctCount === totalQuestions ? 
        baseXP * XP_CONFIG.bonusConfig.perfectScore : 0;
    
    // Speed bonus calculation
    const timeSpent = result.timeSpent || 0;
    const totalTimeAllowed = (exam.duration || 30) * 60; // Convert minutes to seconds
    let speedBonus = 0;
    
    if (timeSpent > 0 && timeSpent < totalTimeAllowed) {
        const timeRatio = timeSpent / totalTimeAllowed;
        if (timeRatio < 0.5) {
            speedBonus = baseXP * XP_CONFIG.bonusConfig.speedBonus;
        } else if (timeRatio < 0.75) {
            speedBonus = baseXP * XP_CONFIG.bonusConfig.speedBonus * 0.5;
        }
    }
    
    // First attempt bonus
    const firstAttemptBonus = isFirstAttempt ? 
        baseXP * XP_CONFIG.bonusConfig.firstAttemptBonus : 0;
    
    // Level multiplier (use current level or default to 1)
    const userLevel = user.currentLevel || 1;
    const levelMultiplier = 1 + ((userLevel - 1) * XP_CONFIG.bonusConfig.levelMultiplierBase);
    
    // Calculate total XP
    const totalBonuses = difficultyBonus + perfectScoreBonus + speedBonus + firstAttemptBonus;
    const finalXP = Math.round((baseXP + totalBonuses) * levelMultiplier);
    
    return {
        xpAwarded: finalXP,
        breakdown: {
            baseXP,
            difficultyBonus,
            perfectScoreBonus,
            speedBonus,
            firstAttemptBonus,
            levelMultiplier
        },
        metadata: {
            difficulty,
            correctCount,
            totalQuestions,
            scorePercentage: Math.round((correctCount / totalQuestions) * 100),
            timeSpent,
            verdict: result.verdict
        }
    };
}

// Calculate user level based on total XP
function calculateLevel(totalXP) {
    // Level progression: 100, 250, 450, 700, 1000, 1350, 1750, 2200, 2700, 3250...
    // Formula: XP needed for level n = 100 + (n-1) * 50 + ((n-1) * (n-2) / 2) * 25
    
    let level = 1;
    let xpNeeded = 0;
    
    while (xpNeeded <= totalXP) {
        level++;
        const baseXP = 100;
        const linearIncrease = (level - 1) * 50;
        const exponentialIncrease = ((level - 1) * (level - 2) / 2) * 25;
        xpNeeded += baseXP + linearIncrease + exponentialIncrease;
    }
    
    // Calculate XP needed for next level
    const currentLevelXP = level > 1 ? xpNeeded - (100 + (level - 1) * 50 + ((level - 1) * (level - 2) / 2) * 25) : 0;
    const nextLevelXP = 100 + level * 50 + (level * (level - 1) / 2) * 25;
    const xpToNextLevel = nextLevelXP - (totalXP - currentLevelXP);
    
    return {
        currentLevel: level - 1,
        xpToNextLevel: Math.max(0, xpToNextLevel)
    };
}

// Migrate a single user's points to XP
async function migrateUserXP(user) {
    try {
        console.log(`\n👤 Processing user: ${user.name} (${user.email})`);
        
        // Get all reports for this user
        const reports = await Report.find({ user: user._id })
            .populate('exam')
            .sort({ createdAt: 1 }); // Process in chronological order
        
        if (reports.length === 0) {
            console.log(`   ⚠️  No quiz reports found for ${user.name}`);
            return;
        }
        
        console.log(`   📊 Found ${reports.length} quiz reports`);
        
        let totalXP = 0;
        let lifetimeXP = 0;
        let seasonXP = 0;
        const xpTransactions = [];
        const examAttempts = new Map(); // Track attempts per exam
        
        // Process each report to calculate XP
        for (const report of reports) {
            if (!report.exam) {
                console.log(`   ⚠️  Skipping report with missing exam data`);
                continue;
            }
            
            const examId = report.exam._id.toString();
            const attemptCount = (examAttempts.get(examId) || 0) + 1;
            examAttempts.set(examId, attemptCount);
            
            const isFirstAttempt = attemptCount === 1;
            
            // Calculate XP for this quiz
            const xpResult = calculateQuizXP(report, report.exam, user, isFirstAttempt);
            
            totalXP += xpResult.xpAwarded;
            lifetimeXP += xpResult.xpAwarded;
            
            // Assume all historical XP is from current season for simplicity
            seasonXP += xpResult.xpAwarded;
            
            // Create XP transaction record
            xpTransactions.push({
                user: user._id,
                amount: xpResult.xpAwarded,
                transactionType: 'quiz_completion',
                sourceId: report.exam._id,
                sourceModel: 'exams',
                breakdown: xpResult.breakdown,
                quizData: {
                    examId: report.exam._id,
                    subject: report.exam.subject,
                    difficulty: xpResult.metadata.difficulty,
                    questionsTotal: xpResult.metadata.totalQuestions,
                    questionsCorrect: xpResult.metadata.correctCount,
                    timeSpent: xpResult.metadata.timeSpent,
                    score: xpResult.metadata.scorePercentage,
                    isFirstAttempt: isFirstAttempt,
                },
                userStateAtTransaction: {
                    levelBefore: user.currentLevel || 1,
                    xpBefore: totalXP - xpResult.xpAwarded,
                    xpAfter: totalXP,
                },
                season: "2024-S1",
                metadata: {
                    migratedFromLegacyPoints: true,
                    originalReportId: report._id,
                    migrationDate: new Date(),
                    ...xpResult.metadata
                }
            });
        }
        
        // Calculate level based on total XP
        const levelInfo = calculateLevel(totalXP);
        
        // Calculate achievements count (if user has achievements)
        const achievementCount = user.achievements ? user.achievements.length : 0;
        
        // Update user with XP data
        const updateData = {
            totalXP: totalXP,
            lifetimeXP: lifetimeXP,
            seasonXP: seasonXP,
            currentLevel: levelInfo.currentLevel,
            xpToNextLevel: levelInfo.xpToNextLevel,
            currentSeason: "2024-S1",
            
            // Update XP statistics
            'xpStats.lastXPGain': new Date(),
            'xpStats.averageXPPerQuiz': reports.length > 0 ? Math.round(totalXP / reports.length) : 0,
            'xpStats.bestXPGain': Math.max(...xpTransactions.map(t => t.amount), 0),
            
            // Ensure other stats are preserved/calculated
            totalQuizzesTaken: reports.length,
            achievements: user.achievements || [],
            
            // Add migration metadata
            migrationData: {
                migratedAt: new Date(),
                originalTotalPoints: user.totalPointsEarned || 0,
                calculatedTotalXP: totalXP,
                reportsProcessed: reports.length,
                xpTransactionsCreated: xpTransactions.length
            }
        };
        
        await User.findByIdAndUpdate(user._id, updateData);
        
        // Save XP transactions (in batches to avoid memory issues)
        if (xpTransactions.length > 0) {
            await XPTransaction.insertMany(xpTransactions);
        }
        
        console.log(`   ✅ Migration completed for ${user.name}:`);
        console.log(`      - Total XP: ${totalXP.toLocaleString()}`);
        console.log(`      - Current Level: ${levelInfo.currentLevel}`);
        console.log(`      - XP Transactions: ${xpTransactions.length}`);
        console.log(`      - Original Points: ${user.totalPointsEarned || 0}`);
        
        return {
            userId: user._id,
            userName: user.name,
            originalPoints: user.totalPointsEarned || 0,
            calculatedXP: totalXP,
            level: levelInfo.currentLevel,
            reportsProcessed: reports.length,
            transactionsCreated: xpTransactions.length
        };
        
    } catch (error) {
        console.error(`   ❌ Error migrating user ${user.name}:`, error);
        return null;
    }
}

// Main migration function
async function migrateAllUsersToXP() {
    console.log('🚀 Starting Points to XP Migration...\n');
    
    try {
        // Get all non-admin users (we'll check for quiz reports separately)
        const users = await User.find({
            isAdmin: { $ne: true },
            isBlocked: { $ne: true }
        }).sort({ createdAt: 1 });
        
        console.log(`📊 Found ${users.length} total users to check\n`);

        // Filter users who actually have quiz reports
        const usersWithReports = [];
        for (const user of users) {
            const reportCount = await Report.countDocuments({ user: user._id });
            if (reportCount > 0) {
                usersWithReports.push(user);
            }
        }

        console.log(`📊 Found ${usersWithReports.length} users with quiz reports to migrate\n`);
        
        const migrationResults = [];
        let successCount = 0;
        let errorCount = 0;
        
        // Process users in batches to avoid memory issues
        const batchSize = 10;
        for (let i = 0; i < usersWithReports.length; i += batchSize) {
            const batch = usersWithReports.slice(i, i + batchSize);
            console.log(`\n📦 Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(usersWithReports.length/batchSize)}`);

            for (const user of batch) {
                const result = await migrateUserXP(user);
                if (result) {
                    migrationResults.push(result);
                    successCount++;
                } else {
                    errorCount++;
                }
            }

            // Small delay between batches
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // Generate migration summary
        console.log('\n🎉 Migration Summary:');
        console.log(`✅ Successfully migrated: ${successCount} users`);
        console.log(`❌ Failed migrations: ${errorCount} users`);
        
        if (migrationResults.length > 0) {
            const totalOriginalPoints = migrationResults.reduce((sum, r) => sum + r.originalPoints, 0);
            const totalCalculatedXP = migrationResults.reduce((sum, r) => sum + r.calculatedXP, 0);
            const totalTransactions = migrationResults.reduce((sum, r) => sum + r.transactionsCreated, 0);
            
            console.log(`\n📈 Migration Statistics:`);
            console.log(`   - Total Original Points: ${totalOriginalPoints.toLocaleString()}`);
            console.log(`   - Total Calculated XP: ${totalCalculatedXP.toLocaleString()}`);
            console.log(`   - XP Transactions Created: ${totalTransactions.toLocaleString()}`);
            console.log(`   - Average XP per User: ${Math.round(totalCalculatedXP / migrationResults.length).toLocaleString()}`);
            
            // Show top 5 users by XP
            const topUsers = migrationResults
                .sort((a, b) => b.calculatedXP - a.calculatedXP)
                .slice(0, 5);
            
            console.log(`\n🏆 Top 5 Users by XP:`);
            topUsers.forEach((user, index) => {
                console.log(`   ${index + 1}. ${user.userName} - ${user.calculatedXP.toLocaleString()} XP (Level ${user.level})`);
            });
        }
        
        console.log('\n✅ Migration completed successfully!');
        return migrationResults;
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        throw error;
    }
}

// Main execution function
async function main() {
    try {
        await connectDB();
        
        console.log('⚠️  WARNING: This will migrate all user points to XP based on quiz history.');
        console.log('⚠️  Make sure you have a database backup before proceeding.\n');
        
        // Add a small delay to allow reading the warning
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const results = await migrateAllUsersToXP();
        
        console.log('\n📄 Migration completed. Results saved to migration log.');
        
        // Save migration results to a file for reference
        const fs = require('fs');
        const migrationLog = {
            migrationDate: new Date(),
            totalUsers: results.length,
            results: results
        };
        
        fs.writeFileSync(
            `migration-log-${Date.now()}.json`, 
            JSON.stringify(migrationLog, null, 2)
        );
        
    } catch (error) {
        console.error('❌ Migration script failed:', error);
    } finally {
        await mongoose.connection.close();
        console.log('\n🔌 Database connection closed.');
        process.exit(0);
    }
}

// Export for testing
module.exports = {
    migrateAllUsersToXP,
    migrateUserXP,
    calculateQuizXP,
    calculateLevel
};

// Run migration if this file is executed directly
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Script execution failed:', error);
        process.exit(1);
    });
}
