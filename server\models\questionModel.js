const mongoose = require("mongoose");

const questionSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    // New unified type field for quiz questions
    type: {
      type: String,
      enum: ["mcq", "fill", "image"],
      default: function() {
        // Auto-migrate from answerType for backward compatibility
        if (this.answerType === "Options") return "mcq";
        if (this.answerType === "Fill in the Blank") return "fill";
        if (this.answerType === "Free Text") return "fill";
        return "mcq";
      }
    },
    // Legacy field for backward compatibility
    answerType: {
      type: String,
      enum: ["Options", "Free Text", "Fill in the Blank"],
      required: function() {
        // Only required if type is not set (for legacy data)
        return !this.type;
      },
    },
    // For MCQ and image-based questions
    options: {
      type: Object,
      required: function () {
        return this.type === "mcq" || this.answerType === "Options";
      },
    },
    // Unified correct answer field (string for all types)
    correctAnswer: {
      type: String,
      required: true,
    },
    // Legacy field for backward compatibility
    correctOption: {
      type: String,
      required: function () {
        return this.answerType === "Options" && !this.correctAnswer;
      },
    },
    // Image URL for image-based questions
    imageUrl: {
      type: String,
      required: function() {
        return this.type === "image";
      }
    },
    // Legacy image field for backward compatibility
    image: {
      type: String,
    },
    // Topic and classification
    topic: {
      type: String,
      required: true,
    },
    classLevel: {
      type: String,
      required: true,
    },
    // Duration per question in seconds
    duration: {
      type: Number,
      default: 90,
    },
    // Creation source
    createdBy: {
      type: String,
      enum: ["admin", "ai"],
      default: "admin",
    },
    exam: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "exams",
    },
    // AI Generation fields
    isAIGenerated: {
      type: Boolean,
      default: false,
    },
    generationSource: {
      type: String,
      enum: ["manual", "ai_bulk", "ai_single"],
      default: "manual",
    },
    difficultyLevel: {
      type: String,
      enum: ["easy", "medium", "hard"],
    },
    syllabusTopics: [{
      type: String,
    }],
    questionType: {
      type: String,
      enum: ["multiple_choice", "fill_blank", "picture_based", "text_based"],
    },
  },
  {
    timestamps: true,
  }
);

// Pre-save middleware to handle migration from old schema to new schema
questionSchema.pre('save', function(next) {
  // Auto-migrate answerType to type if type is not set
  if (!this.type && this.answerType) {
    if (this.answerType === "Options") {
      this.type = "mcq";
    } else if (this.answerType === "Fill in the Blank") {
      this.type = "fill";
    } else if (this.answerType === "Free Text") {
      this.type = "fill";
    }
  }

  // Migrate correctOption to correctAnswer for MCQ questions
  if (this.type === "mcq" && this.correctOption && !this.correctAnswer) {
    this.correctAnswer = this.correctOption;
  }

  // Migrate image to imageUrl
  if (this.image && !this.imageUrl) {
    this.imageUrl = this.image;
    // If there's an image, set type to image if not already set
    if (!this.type) {
      this.type = "image";
    }
  }

  // Set default values if missing
  if (!this.topic) {
    this.topic = "General";
  }
  if (!this.classLevel) {
    this.classLevel = "General";
  }

  next();
});

const Question = mongoose.model("questions", questionSchema);
module.exports = Question;
