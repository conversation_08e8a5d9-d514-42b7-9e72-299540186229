const jwt = require("jsonwebtoken");
const User = require("../models/userModel");

/**
 * Enhanced authentication middleware specifically designed for AI operations
 * Provides better error handling, token refresh, and user validation
 */
const enhancedAuthMiddleware = async (req, res, next) => {
  try {
    // Check if authorization header exists
    if (!req.headers.authorization) {
      return res.status(401).send({
        message: "Authentication required. Please login to access AI features.",
        code: "NO_AUTH_HEADER",
        success: false,
        requiresLogin: true
      });
    }

    // Check if authorization header has the correct format
    const authParts = req.headers.authorization.split(" ");
    if (authParts.length !== 2 || authParts[0] !== "Bearer") {
      return res.status(401).send({
        message: "Invalid authentication format. Please login again.",
        code: "INVALID_AUTH_FORMAT",
        success: false,
        requiresLogin: true
      });
    }

    const token = authParts[1];
    
    // Verify the token
    let decodedToken;
    try {
      decodedToken = jwt.verify(token, process.env.JWT_SECRET);
    } catch (jwtError) {
      let message = "Authentication failed. Please login again.";
      let code = "INVALID_TOKEN";
      
      if (jwtError.name === "TokenExpiredError") {
        message = "Your session has expired. Please login again to continue using AI features.";
        code = "TOKEN_EXPIRED";
      } else if (jwtError.name === "JsonWebTokenError") {
        message = "Invalid authentication token. Please login again.";
        code = "MALFORMED_TOKEN";
      }
      
      return res.status(401).send({
        message,
        code,
        success: false,
        requiresLogin: true,
        expiredAt: jwtError.expiredAt || null
      });
    }

    const userId = decodedToken.userId;
    
    // Validate user still exists and is active
    const user = await User.findById(userId);
    if (!user) {
      return res.status(401).send({
        message: "User account not found. Please login again.",
        code: "USER_NOT_FOUND",
        success: false,
        requiresLogin: true
      });
    }

    if (user.isBlocked) {
      return res.status(403).send({
        message: "Your account has been blocked. Please contact support.",
        code: "ACCOUNT_BLOCKED",
        success: false,
        requiresLogin: false
      });
    }

    // Check if user has permission for AI features
    if (!user.isAdmin && user.paymentRequired) {
      return res.status(403).send({
        message: "AI question generation requires a premium subscription. Please upgrade your account.",
        code: "SUBSCRIPTION_REQUIRED",
        success: false,
        requiresLogin: false,
        upgradeRequired: true
      });
    }

    // Add user info to request
    req.user = {
      userId: user._id,
      email: user.email,
      name: user.name,
      isAdmin: user.isAdmin,
      level: user.level,
      class: user.class,
      school: user.school
    };
    
    // Add userId to request body for backward compatibility
    req.body.userId = userId;
    
    console.log(`✅ Enhanced Auth Success: ${user.name} (${user.email}) authenticated for AI features`);
    next();

  } catch (error) {
    console.error("❌ Enhanced Auth Error:", error.message);
    
    res.status(500).send({
      message: "Authentication service error. Please try again.",
      code: "AUTH_SERVICE_ERROR",
      success: false,
      requiresLogin: true
    });
  }
};

module.exports = enhancedAuthMiddleware;
