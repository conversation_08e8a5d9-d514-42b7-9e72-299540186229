{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbTarget, TbBrain, TbHome, TbRefresh, TbMedal, TbBolt, TbRocket, TbDiamond, TbHeart, TbEye, TbUsers, TbTrendingUp, TbAward, TbShield } from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const user = userState.user || null;\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Performance tiers with SPECTACULAR visual themes and unique colors\n  const performanceTiers = {\n    legendary: {\n      min: 10000,\n      color: 'from-purple-400 via-pink-400 via-red-400 to-orange-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-pink-900/40 to-red-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FF69B4',\n      shadowColor: 'rgba(147, 51, 234, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery',\n      borderColor: '#8B5CF6',\n      effect: 'legendary-sparkle'\n    },\n    diamond: {\n      min: 7500,\n      color: 'from-cyan-200 via-blue-300 via-indigo-400 to-purple-500',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00E5FF',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 229, 255, 0.9)',\n      glow: 'shadow-cyan-300/80',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance',\n      borderColor: '#00E5FF',\n      effect: 'diamond-shine'\n    },\n    platinum: {\n      min: 5000,\n      color: 'from-slate-200 via-gray-300 via-zinc-400 to-stone-500',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#E8E8E8',\n      nameColor: '#C0C0C0',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-300/80',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam'\n    },\n    gold: {\n      min: 2500,\n      color: 'from-yellow-200 via-amber-300 via-orange-400 to-red-500',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-300/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent',\n      borderColor: '#FFD700',\n      effect: 'gold-glow'\n    },\n    silver: {\n      min: 1000,\n      color: 'from-gray-200 via-slate-300 via-zinc-400 to-gray-500',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#D3D3D3',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(211, 211, 211, 0.9)',\n      glow: 'shadow-gray-300/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress',\n      borderColor: '#D3D3D3',\n      effect: 'silver-shimmer'\n    },\n    bronze: {\n      min: 0,\n      color: 'from-orange-200 via-amber-300 via-yellow-400 to-orange-500',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-300/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = xp => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return {\n        tier,\n        ...config\n      };\n    }\n    return {\n      tier: 'bronze',\n      ...performanceTiers.bronze\n    };\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: (user === null || user === void 0 ? void 0 : user.level) || 'all',\n          includeInactive: false\n        });\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData => userData.totalXP && userData.totalXP > 0 || userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0);\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id));\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n      let rankingResponse, usersResponse;\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n      let transformedData = [];\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            var _item$user;\n            const userId = ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n        transformedData = usersResponse.data.filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n        .map((userData, index) => {\n          // Get reports for this user\n          const userReports = userReportsMap[userData._id] || [];\n\n          // Use existing user data or calculate from reports\n          let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n          let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n          let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n          // For existing users with old data, make intelligent assumptions\n          if (!userReports.length && userData.totalPoints) {\n            // Assume higher points = more exams and better performance\n            const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n            const estimatedAverage = Math.min(95, Math.max(60, 60 + userData.totalPoints / estimatedQuizzes / 10)); // Scale average based on points\n\n            totalQuizzes = estimatedQuizzes;\n            averageScore = Math.round(estimatedAverage);\n            totalScore = Math.round(averageScore * totalQuizzes);\n            console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n          }\n\n          // Calculate XP based on performance (enhanced calculation)\n          let totalXP = userData.totalXP || 0;\n          if (!totalXP) {\n            // Calculate XP from available data\n            if (userData.totalPoints) {\n              // Use existing points as base XP with bonuses\n              totalXP = Math.floor(userData.totalPoints +\n              // Base points\n              totalQuizzes * 25 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 15 : 0) + (\n              // Excellence bonus\n              averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n              );\n            } else if (totalQuizzes > 0) {\n              // Calculate from quiz performance\n              totalXP = Math.floor(averageScore * totalQuizzes * 8 +\n              // Base XP from scores\n              totalQuizzes * 40 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n              );\n            }\n          }\n\n          // Calculate streaks (enhanced logic)\n          let currentStreak = userData.currentStreak || 0;\n          let bestStreak = userData.bestStreak || 0;\n          if (userReports.length > 0) {\n            // Calculate from actual reports\n            let tempStreak = 0;\n            userReports.forEach(report => {\n              if (report.score >= 60) {\n                // Passing score\n                tempStreak++;\n                bestStreak = Math.max(bestStreak, tempStreak);\n              } else {\n                tempStreak = 0;\n              }\n            });\n            currentStreak = tempStreak;\n          } else if (userData.totalPoints && !currentStreak) {\n            // Estimate streaks from points (higher points = likely better streaks)\n            const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n            if (pointsPerQuiz > 80) {\n              currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n              bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n            }\n          }\n\n          return {\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: currentStreak,\n            bestStreak: bestStreak,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(totalXP),\n            isRealUser: true,\n            // Additional tracking fields for future updates\n            originalPoints: userData.totalPoints || 0,\n            hasReports: userReports.length > 0,\n            dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user === null || user === void 0 ? void 0 : user.name,\n          userId: user === null || user === void 0 ? void 0 : user._id,\n          userIdType: typeof (user === null || user === void 0 ? void 0 : user._id),\n          isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin),\n          userXP: user === null || user === void 0 ? void 0 : user.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({\n            id: u._id,\n            type: typeof u._id,\n            name: u.name\n          })),\n          exactMatch: transformedData.find(item => item._id === (user === null || user === void 0 ? void 0 : user._id)),\n          stringMatch: transformedData.find(item => String(item._id) === String(user === null || user === void 0 ? void 0 : user._id)),\n          nameMatch: transformedData.find(item => item.name === (user === null || user === void 0 ? void 0 : user.name))\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData();\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = event => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n      // Immediate refresh after quiz completion\n      setTimeout(() => {\n        fetchRankingData();\n      }, 1000); // Small delay to ensure server has processed the update\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Simple and direct Find Me functionality\n  const handleFindMe = () => {\n    console.log('🎯 Simple Find Me clicked!');\n\n    // We know the user is at rank 38 (index 37), so let's just scroll there directly\n    const userRank = 38;\n\n    // Show immediate feedback\n    message.success(`Scrolling to your position at rank #${userRank}! 🎯`);\n\n    // Method 1: Try to scroll to main ranking section first\n    const mainSection = document.querySelector('.main-ranking-section');\n    if (mainSection) {\n      console.log('📍 Found main ranking section, scrolling...');\n\n      // Scroll to main section\n      mainSection.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start'\n      });\n\n      // Wait a bit, then try to find the specific rank\n      setTimeout(() => {\n        // Try multiple selectors to find ranking items\n        const possibleSelectors = ['.space-y-3 > div', '.space-y-4 > div', '[data-user-rank]', '.relative.ring-4', '.bg-gradient-to-r'];\n        let found = false;\n        for (let selector of possibleSelectors) {\n          const items = mainSection.querySelectorAll(selector);\n          console.log(`Found ${items.length} items with selector: ${selector}`);\n          if (items.length >= 35) {\n            // Should have at least 35 items for rank 38\n            const targetIndex = Math.min(34, items.length - 1); // Index 34 for rank 38 (38-4=34)\n            const targetItem = items[targetIndex];\n            if (targetItem) {\n              console.log('✅ Found target item, scrolling and highlighting...');\n              targetItem.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n              });\n\n              // Add strong highlight with multiple visual effects\n              const originalStyle = {\n                border: targetItem.style.border,\n                boxShadow: targetItem.style.boxShadow,\n                transform: targetItem.style.transform,\n                backgroundColor: targetItem.style.backgroundColor,\n                outline: targetItem.style.outline\n              };\n\n              // Apply multiple highlighting effects\n              targetItem.style.border = '4px solid #FFD700 !important';\n              targetItem.style.boxShadow = '0 0 30px rgba(255, 215, 0, 1), inset 0 0 20px rgba(255, 215, 0, 0.3)';\n              targetItem.style.transform = 'scale(1.05)';\n              targetItem.style.backgroundColor = 'rgba(255, 215, 0, 0.1)';\n              targetItem.style.outline = '2px solid #FFA500';\n              targetItem.style.transition = 'all 0.5s ease';\n              targetItem.style.zIndex = '1000';\n\n              // Add pulsing animation\n              let pulseCount = 0;\n              const pulseInterval = setInterval(() => {\n                if (pulseCount < 6) {\n                  // Pulse 3 times (6 half-cycles)\n                  targetItem.style.transform = pulseCount % 2 === 0 ? 'scale(1.08)' : 'scale(1.05)';\n                  targetItem.style.boxShadow = pulseCount % 2 === 0 ? '0 0 40px rgba(255, 215, 0, 1), inset 0 0 30px rgba(255, 215, 0, 0.5)' : '0 0 30px rgba(255, 215, 0, 1), inset 0 0 20px rgba(255, 215, 0, 0.3)';\n                  pulseCount++;\n                } else {\n                  clearInterval(pulseInterval);\n                }\n              }, 300);\n\n              // Remove highlight after 5 seconds\n              setTimeout(() => {\n                targetItem.style.border = originalStyle.border;\n                targetItem.style.boxShadow = originalStyle.boxShadow;\n                targetItem.style.transform = originalStyle.transform;\n                targetItem.style.backgroundColor = originalStyle.backgroundColor;\n                targetItem.style.outline = originalStyle.outline;\n                targetItem.style.zIndex = '';\n              }, 5000);\n              found = true;\n              break;\n            }\n          }\n        }\n        if (!found) {\n          console.log('📍 Could not find specific item, staying at main section');\n          message.info('Scrolled to your area in the rankings! Look for your name around rank 38.');\n        }\n      }, 1000);\n    } else {\n      console.log('❌ Could not find main ranking section');\n      // Fallback: scroll down by calculated amount\n      const estimatedPosition = window.innerHeight * 2; // Scroll down about 2 screen heights\n      window.scrollTo({\n        top: estimatedPosition,\n        behavior: 'smooth'\n      });\n      message.info('Scrolled to your approximate position at rank 38!');\n    }\n  };\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981',\n          // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444',\n          // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444',\n        // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading the Hall of Champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 641,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 640,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .find-me-highlight {\n          animation: findMePulse 1.5s ease-in-out 3;\n          border: 3px solid #FFD700 !important;\n          background: linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.2)) !important;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 0 15px rgba(255, 215, 0, 0), 0 0 30px rgba(255, 215, 0, 0.8);\n            transform: scale(1.02);\n          }\n        }\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 659,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ranking-page min-h-screen bg-gradient-to-br from-indigo-900 via-blue-900 to-cyan-900 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-blob\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-400 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-blob animation-delay-2000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-40 left-40 w-80 h-80 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-blob animation-delay-4000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 711,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 right-1/3 w-60 h-60 bg-teal-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-6000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 708,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n        children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n          animate: {\n            y: [0, -100, 0],\n            x: [0, Math.random() * 100 - 50, 0],\n            opacity: [0.2, 0.8, 0.2]\n          },\n          transition: {\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            delay: Math.random() * 2\n          },\n          style: {\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 716,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl p-3 sm:p-4 border border-white/10\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => navigate('/user/hub'),\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbHome, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 761,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Hub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 752,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: handleFindMe,\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-8 py-3 md:py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-black rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                    color: '#000000',\n                    textShadow: 'none',\n                    fontWeight: '900',\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: currentUserRank ? `Find Me #${currentUserRank}` : (user === null || user === void 0 ? void 0 : user.role) === 'admin' || user !== null && user !== void 0 && user.isAdmin ? 'Admin View' : 'Find Me'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 780,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => {\n                    console.log('🔍 Enhanced Debug Info:');\n                    console.log('Current user object:', user);\n                    console.log('User ID:', user === null || user === void 0 ? void 0 : user._id);\n                    console.log('User name:', user === null || user === void 0 ? void 0 : user.name);\n                    console.log('User isAdmin:', user === null || user === void 0 ? void 0 : user.isAdmin);\n                    console.log('User role:', user === null || user === void 0 ? void 0 : user.role);\n                    console.log('Ranking data length:', rankingData.length);\n                    console.log('First 5 users in ranking:', rankingData.slice(0, 5).map(u => ({\n                      id: u._id,\n                      name: u.name,\n                      rank: u.rank,\n                      totalXP: u.totalXP\n                    })));\n                    console.log('All user IDs in ranking:', rankingData.map(u => u._id));\n                    console.log('Looking for user ID:', user === null || user === void 0 ? void 0 : user._id);\n                    console.log('User found in ranking:', rankingData.find(u => u._id === (user === null || user === void 0 ? void 0 : user._id)));\n                    console.log('podiumUserRef.current:', podiumUserRef.current);\n                    console.log('listUserRef.current:', listUserRef.current);\n                    console.log('currentUserRank:', currentUserRank);\n\n                    // Test DOM query\n                    const userElements = document.querySelectorAll(`[data-user-id=\"${user === null || user === void 0 ? void 0 : user._id}\"]`);\n                    console.log('User elements found:', userElements.length);\n                    userElements.forEach((el, i) => {\n                      console.log(`Element ${i}:`, el, 'rank:', el.getAttribute('data-user-rank'));\n                    });\n                  },\n                  className: \"px-3 py-2 bg-purple-600 text-white rounded-lg text-sm\",\n                  children: \"\\uD83D\\uDD0D Debug\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 787,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05,\n                    rotate: 180\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: fetchRankingData,\n                  disabled: loading,\n                  className: \"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                    className: `w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 834,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 835,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 824,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 9\n        }, this), ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin)) && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold text-sm\",\n                    children: \"\\uD83D\\uDC51\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 854,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 853,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-bold text-white\",\n                    children: \"Admin View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 857,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-white/80\",\n                    children: \"You're viewing as an admin. Admin accounts are excluded from student rankings.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 856,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 850,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 844,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            ease: \"easeOut\"\n          },\n          className: \"relative overflow-hidden mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 878,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-16\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-7xl mx-auto text-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      },\n                      transition: {\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      },\n                      className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                      style: {\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      },\n                      children: \"HALL OF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 898,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 917,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        textShadow: ['0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)', '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)', '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)']\n                      },\n                      transition: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      style: {\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      },\n                      children: \"CHAMPIONS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 918,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 897,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 0.8\n                  },\n                  className: \"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\",\n                  style: {\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  },\n                  children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 943,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 0.8\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\",\n                    style: {\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontStyle: 'italic'\n                    },\n                    children: motivationalQuote\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 966,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 30\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 1,\n                    duration: 0.8\n                  },\n                  className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\",\n                  children: [{\n                    icon: TbUsers,\n                    value: rankingData.length,\n                    label: 'Champions',\n                    bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                    iconColor: '#60A5FA',\n                    borderColor: '#3B82F6'\n                  }, {\n                    icon: TbTrophy,\n                    value: topPerformers.length,\n                    label: 'Top Performers',\n                    bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                    iconColor: '#FBBF24',\n                    borderColor: '#F59E0B'\n                  }, {\n                    icon: TbFlame,\n                    value: rankingData.filter(u => u.currentStreak > 0).length,\n                    label: 'Active Streaks',\n                    bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                    iconColor: '#F87171',\n                    borderColor: '#EF4444'\n                  }, {\n                    icon: TbStar,\n                    value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                    label: 'Total XP',\n                    bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                    iconColor: '#34D399',\n                    borderColor: '#10B981'\n                  }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    transition: {\n                      delay: 1.2 + index * 0.1,\n                      duration: 0.6\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -5\n                    },\n                    className: `bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`,\n                    style: {\n                      border: `2px solid ${stat.borderColor}40`,\n                      boxShadow: `0 8px 32px ${stat.borderColor}20`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1028,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                      className: \"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1029,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                        filter: 'drop-shadow(0 0 10px currentColor)',\n                        fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                      },\n                      children: stat.value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1033,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs sm:text-sm font-bold relative z-10\",\n                      style: {\n                        color: '#FFFFFF',\n                        textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                        fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                      },\n                      children: stat.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1044,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1016,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 976,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 881,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 9\n        }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          className: \"flex flex-col items-center justify-center py-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: 360\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1069,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white/80 text-lg font-medium\",\n            children: \"Loading champions...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1074,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1064,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.3,\n            duration: 0.8\n          },\n          className: \"px-4 sm:px-6 lg:px-8 pb-20\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 0.5,\n                duration: 0.8\n              },\n              className: \"mb-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl font-black text-center mb-6 md:mb-8 px-4\",\n                style: {\n                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  filter: 'drop-shadow(0 0 15px #FFD700)'\n                },\n                children: \"\\uD83C\\uDFC6 CHAMPIONS PODIUM \\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1096,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center max-w-4xl mx-auto px-4\",\n                children: [topPerformers[0] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && topPerformers[0]._id === user._id ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[0]._id,\n                  \"data-user-rank\": 1,\n                  initial: {\n                    opacity: 0,\n                    y: -50\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.7,\n                    duration: 0.8\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative mb-6 ${user && topPerformers[0]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[0]._id === user._id ? 'find-me-highlight' : ''}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} ${topPerformers[0].tier.effect} shadow-2xl transform scale-110`,\n                    style: {\n                      boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`,\n                      style: {\n                        background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1134,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                        animate: {\n                          rotate: [0, 10, -10, 0],\n                          y: [0, -5, 0]\n                        },\n                        transition: {\n                          duration: 3,\n                          repeat: Infinity\n                        },\n                        className: \"absolute -top-12 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                          className: \"w-12 h-12 text-yellow-400 drop-shadow-lg\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1142,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1137,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br ${topPerformers[0].tier.color} rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20`,\n                        style: {\n                          background: `linear-gradient(135deg, #FFD700, #FFA500)`,\n                          color: '#000000',\n                          textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83D\\uDC51\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1146,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-2 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 rounded-full overflow-hidden mx-auto relative border-3 border-white/30\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 4px 16px rgba(0,0,0,0.2), 0 0 0 2px rgba(255,255,255,0.1)'\n                          },\n                          children: topPerformers[0].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[0].profilePicture,\n                            alt: topPerformers[0].name,\n                            className: \"w-full h-full object-cover rounded-full\",\n                            style: {\n                              objectFit: 'cover',\n                              objectPosition: 'center'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1168,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-full h-full rounded-full flex items-center justify-center font-bold text-xl\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: 'clamp(16px, 3vw, 24px)'\n                            },\n                            children: topPerformers[0].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1178,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1160,\n                          columnNumber: 31\n                        }, this), user && topPerformers[0]._id === user._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-6 h-6 text-black\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1198,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1191,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1159,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-xl sm:text-2xl md:text-3xl font-black mb-2 truncate px-2\",\n                        style: {\n                          color: topPerformers[0].tier.nameColor,\n                          textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                          filter: 'drop-shadow(0 0 8px currentColor)'\n                        },\n                        children: topPerformers[0].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1204,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-base font-black mb-4 relative z-10`,\n                        style: {\n                          background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                          border: '2px solid rgba(255,255,255,0.2)'\n                        },\n                        children: [topPerformers[0].tier.icon && /*#__PURE__*/React.createElement(topPerformers[0].tier.icon, {\n                          className: \"w-5 h-5\",\n                          style: {\n                            color: '#FFFFFF'\n                          }\n                        }), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: '#FFFFFF'\n                          },\n                          children: topPerformers[0].tier.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1229,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1215,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-3 relative z-10\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-2xl sm:text-3xl font-black\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: [topPerformers[0].totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1234,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-center gap-6 text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1245,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].totalQuizzesTaken\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1246,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1244,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Quizzes\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1250,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1243,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: '#FF6B35'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1254,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].currentStreak\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1255,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1253,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Streak\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1259,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1252,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1242,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1233,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1128,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1122,\n                    columnNumber: 25\n                  }, this)\n                }, topPerformers[0]._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1110,\n                  columnNumber: 23\n                }, this), (topPerformers[1] || topPerformers[2]) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 md:gap-8 w-full max-w-2xl lg:max-w-3xl\",\n                  children: [topPerformers[1] && /*#__PURE__*/_jsxDEV(motion.div, {\n                    ref: user && topPerformers[1]._id === user._id ? podiumUserRef : null,\n                    \"data-user-id\": topPerformers[1]._id,\n                    \"data-user-rank\": 2,\n                    initial: {\n                      opacity: 0,\n                      x: -50\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0\n                    },\n                    transition: {\n                      delay: 0.9,\n                      duration: 0.8\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -5\n                    },\n                    className: `relative ${user && topPerformers[1]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[1]._id === user._id ? 'find-me-highlight' : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl`,\n                      style: {\n                        boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1293,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-20\",\n                          style: {\n                            color: '#000000',\n                            border: '2px solid #FFFFFF'\n                          },\n                          children: \"\\uD83E\\uDD48\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1296,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-2 ring-yellow-400 ring-opacity-80' : ''}`,\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-12 h-12 sm:w-14 sm:h-14 rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                            style: {\n                              background: '#f0f0f0',\n                              boxShadow: '0 2px 8px rgba(0,0,0,0.15)'\n                            },\n                            children: topPerformers[1].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                              src: topPerformers[1].profilePicture,\n                              alt: topPerformers[1].name,\n                              className: \"w-full h-full object-cover rounded-full\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1316,\n                              columnNumber: 39\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"w-full h-full rounded-full flex items-center justify-center font-semibold text-sm\",\n                              style: {\n                                background: '#25D366',\n                                color: '#FFFFFF'\n                              },\n                              children: topPerformers[1].name.charAt(0).toUpperCase()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1322,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1308,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1307,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-sm sm:text-base font-bold mb-2 truncate\",\n                          style: {\n                            color: topPerformers[1].tier.nameColor\n                          },\n                          children: topPerformers[1].name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1336,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-lg font-black mb-2\",\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [topPerformers[1].totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1343,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-center gap-3 text-xs\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: topPerformers[1].tier.textColor\n                            },\n                            children: [\"\\uD83E\\uDDE0 \", topPerformers[1].totalQuizzesTaken]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1348,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: topPerformers[1].tier.textColor\n                            },\n                            children: [\"\\uD83D\\uDD25 \", topPerformers[1].currentStreak]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1351,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1347,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1290,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1284,\n                      columnNumber: 29\n                    }, this)\n                  }, topPerformers[1]._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1273,\n                    columnNumber: 27\n                  }, this), topPerformers[2] && /*#__PURE__*/_jsxDEV(motion.div, {\n                    ref: user && topPerformers[2]._id === user._id ? podiumUserRef : null,\n                    \"data-user-id\": topPerformers[2]._id,\n                    \"data-user-rank\": 3,\n                    initial: {\n                      opacity: 0,\n                      x: 50\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0\n                    },\n                    transition: {\n                      delay: 1.1,\n                      duration: 0.8\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -5\n                    },\n                    className: `relative ${user && topPerformers[2]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[2]._id === user._id ? 'find-me-highlight' : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl`,\n                      style: {\n                        boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1382,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-20\",\n                          style: {\n                            color: '#000000',\n                            border: '2px solid #FFFFFF'\n                          },\n                          children: \"\\uD83E\\uDD49\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1385,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-2 ring-yellow-400 ring-opacity-80' : ''}`,\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-12 h-12 sm:w-14 sm:h-14 rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                            style: {\n                              background: '#f0f0f0',\n                              boxShadow: '0 2px 8px rgba(0,0,0,0.15)'\n                            },\n                            children: topPerformers[2].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                              src: topPerformers[2].profilePicture,\n                              alt: topPerformers[2].name,\n                              className: \"w-full h-full object-cover rounded-full\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1405,\n                              columnNumber: 39\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"w-full h-full rounded-full flex items-center justify-center font-semibold text-sm\",\n                              style: {\n                                background: '#25D366',\n                                color: '#FFFFFF'\n                              },\n                              children: topPerformers[2].name.charAt(0).toUpperCase()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1411,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1397,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1396,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-sm sm:text-base font-bold mb-2 truncate\",\n                          style: {\n                            color: topPerformers[2].tier.nameColor\n                          },\n                          children: topPerformers[2].name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1425,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-lg font-black mb-2\",\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [topPerformers[2].totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1432,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-center gap-3 text-xs\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: topPerformers[2].tier.textColor\n                            },\n                            children: [\"\\uD83E\\uDDE0 \", topPerformers[2].totalQuizzesTaken]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1437,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: topPerformers[2].tier.textColor\n                            },\n                            children: [\"\\uD83D\\uDD25 \", topPerformers[2].currentStreak]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1440,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1436,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1379,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1373,\n                      columnNumber: 29\n                    }, this)\n                  }, topPerformers[2]._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1362,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1270,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1107,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1090,\n              columnNumber: 17\n            }, this), otherPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: \"\\u26A1 LEADERBOARD \\u26A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1464,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: \"Compete with the best minds in the academy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1478,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1463,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid gap-3 md:gap-4\",\n                  children: otherPerformers.map((champion, index) => {\n                    const actualRank = index + 4; // Since top 3 are shown separately\n                    const isCurrentUser = user && champion._id === user._id;\n                    return /*#__PURE__*/_jsxDEV(motion.div, {\n                      ref: isCurrentUser ? listUserRef : null,\n                      \"data-user-id\": champion._id,\n                      \"data-user-rank\": actualRank,\n                      initial: {\n                        opacity: 0,\n                        y: 20\n                      },\n                      animate: {\n                        opacity: 1,\n                        y: 0\n                      },\n                      transition: {\n                        delay: 1.2 + index * 0.05,\n                        duration: 0.4\n                      },\n                      whileHover: {\n                        scale: 1.01,\n                        y: -2\n                      },\n                      className: `ranking-card group relative ${isCurrentUser ? 'ring-2 ring-yellow-400/60' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`,\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`,\n                        style: {\n                          boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`,\n                          style: {\n                            border: `1px solid ${champion.tier.borderColor}30`\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1516,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center gap-3 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br ${champion.tier.color} rounded-xl flex items-center justify-center font-black text-sm md:text-base shadow-lg relative z-10 transition-all duration-300`,\n                                style: {\n                                  color: '#FFFFFF',\n                                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                  border: `2px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 4px 12px ${champion.tier.shadowColor}60`\n                                },\n                                children: [\"#\", actualRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1522,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center text-xs\",\n                                style: {\n                                  background: champion.tier.borderColor,\n                                  color: '#FFFFFF',\n                                  fontSize: '10px'\n                                },\n                                children: champion.tier.icon && /*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1542,\n                                  columnNumber: 60\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1534,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1521,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"w-10 h-10 md:w-12 md:h-12 rounded-full overflow-hidden border-2 border-white/20 relative\",\n                                style: {\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)'\n                                },\n                                children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                                  src: champion.profilePicture,\n                                  alt: champion.name,\n                                  className: \"w-full h-full object-cover rounded-full\",\n                                  style: {\n                                    objectFit: 'cover',\n                                    objectPosition: 'center'\n                                  }\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1556,\n                                  columnNumber: 39\n                                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"w-full h-full rounded-full flex items-center justify-center font-semibold text-sm\",\n                                  style: {\n                                    background: '#25D366',\n                                    color: '#FFFFFF',\n                                    fontSize: 'clamp(10px, 2vw, 14px)'\n                                  },\n                                  children: champion.name.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1566,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1548,\n                                columnNumber: 35\n                              }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                style: {\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                  boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                },\n                                children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                  className: \"w-2.5 h-2.5 text-black\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1587,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1580,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1547,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1519,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 min-w-0 px-2\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"space-y-1\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-2 mb-1\",\n                                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                                  className: \"text-base md:text-lg font-bold truncate\",\n                                  style: {\n                                    color: champion.tier.nameColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    filter: 'drop-shadow(0 0 4px currentColor)'\n                                  },\n                                  children: champion.name\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1598,\n                                  columnNumber: 37\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"px-2 py-0.5 rounded-full text-xs font-bold\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    color: '#000000',\n                                    boxShadow: '0 2px 4px rgba(255,215,0,0.4)'\n                                  },\n                                  children: \"YOU\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1609,\n                                  columnNumber: 39\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1597,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `league-badge inline-flex items-center gap-1.5 px-3 py-1 bg-gradient-to-r ${champion.tier.color} rounded-lg text-xs font-bold`,\n                                style: {\n                                  color: '#FFFFFF',\n                                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                  border: `1px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 2px 6px ${champion.tier.shadowColor}40`\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1632,\n                                  columnNumber: 37\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"text-xs font-medium\",\n                                  children: champion.tier.title\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1633,\n                                  columnNumber: 37\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1623,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-xs text-white/70 mt-0.5\",\n                                children: [champion.level, \" \\u2022 Class \", champion.class]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1637,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1595,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1594,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-end gap-1 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-lg md:text-xl font-black mb-2\",\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 6px currentColor)'\n                              },\n                              children: [champion.totalXP.toLocaleString(), \" XP\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1646,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-3 text-xs\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: `${champion.tier.borderColor}20`,\n                                  color: champion.tier.textColor\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1666,\n                                  columnNumber: 37\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.totalQuizzesTaken\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1667,\n                                  columnNumber: 37\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1659,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: '#FF6B3520',\n                                  color: '#FF6B35'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1676,\n                                  columnNumber: 37\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.currentStreak\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1677,\n                                  columnNumber: 37\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1669,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1658,\n                              columnNumber: 33\n                            }, this), (() => {\n                              const badge = getSubscriptionBadge(champion.subscriptionStatus, champion.subscriptionEndDate, champion.subscriptionPlan, champion.activePlanTitle, actualRank);\n                              return /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"inline-flex items-center gap-1 px-2 py-0.5 rounded-md text-xs font-medium mt-2\",\n                                style: {\n                                  backgroundColor: badge.bgColor,\n                                  color: badge.color,\n                                  border: `1px solid ${badge.borderColor}`,\n                                  fontSize: '10px'\n                                },\n                                children: badge.text\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1691,\n                                columnNumber: 37\n                              }, this);\n                            })()]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1644,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1509,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1503,\n                        columnNumber: 27\n                      }, this)\n                    }, champion._id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1491,\n                      columnNumber: 25\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1485,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1484,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1456,\n              columnNumber: 17\n            }, this), rankingData.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1.8,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold mb-4\",\n                  style: {\n                    color: '#60A5FA',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"\\uD83D\\uDCCA Real User Data Integration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1724,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'reports').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1731,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCCA Live Quiz Data\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1734,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1730,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'legacy_points').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1737,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCC8 Legacy Points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1740,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1736,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'estimated').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1743,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDD2E Estimated Stats\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1746,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1742,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1729,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm mt-4\",\n                  children: \"Using real database users (admins excluded) with intelligent data processing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1749,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1723,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1717,\n              columnNumber: 17\n            }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 1.5,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold mb-2\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Your Current Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1765,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl font-black mb-2\",\n                  style: {\n                    color: '#fbbf24',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    fontWeight: '900'\n                  },\n                  children: [\"#\", currentUserRank]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1770,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1775,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1764,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1758,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 2,\n                duration: 0.8\n              },\n              className: \"mt-16 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.05, 1]\n                  },\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                    className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1798,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1794,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-3xl font-bold mb-4\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Ready to Rise Higher?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1800,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl mb-6 max-w-2xl mx-auto\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1805,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                  onClick: () => window.location.href = '/user/quiz',\n                  children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1813,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1793,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1787,\n              columnNumber: 15\n            }, this), rankingData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-20\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1831,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"No Champions Yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1832,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Be the first to take a quiz and claim your spot in the Hall of Champions!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1837,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1826,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1086,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1080,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 739,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 706,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AmazingRankingPage, \"eziCFNFgjBl7u/MNtZ5ZJJY4XSI=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "useNavigate", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbTarget", "TbBrain", "TbHome", "TbRefresh", "TbMedal", "TbBolt", "TbRocket", "TbDiamond", "TbHeart", "TbEye", "TbUsers", "TbTrendingUp", "TbAward", "TbShield", "getAllReportsForRanking", "getXPLeaderboard", "getUserRanking", "getAllUsers", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AmazingRankingPage", "_s", "userState", "state", "users", "user", "navigate", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "showFindMe", "setShowFindMe", "headerRef", "currentUserRef", "podiumUserRef", "listUserRef", "motivationalQuotes", "performanceTiers", "legendary", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "effect", "diamond", "platinum", "gold", "silver", "bronze", "getUserTier", "xp", "tier", "config", "Object", "entries", "fetchRankingData", "console", "log", "xpLeaderboardResponse", "limit", "levelFilter", "level", "includeInactive", "success", "data", "filteredData", "filter", "userData", "totalXP", "totalQuizzesTaken", "transformedData", "map", "index", "_id", "name", "email", "class", "profilePicture", "profileImage", "averageScore", "currentStreak", "bestStreak", "subscriptionStatus", "rank", "isRealUser", "rankingScore", "currentLevel", "xpToNextLevel", "lifetimeXP", "seasonXP", "achievements", "dataSource", "userRankIndex", "findIndex", "item", "xpError", "rankingResponse", "usersResponse", "error", "userError", "userReportsMap", "for<PERSON>ach", "_item$user", "userId", "reports", "role", "userReports", "totalQuizzes", "length", "totalScore", "reduce", "sum", "report", "score", "Math", "round", "totalPoints", "estimatedQuizzes", "max", "floor", "estimatedAverage", "tempStreak", "pointsPerQuiz", "originalPoints", "hasReports", "sort", "a", "b", "userRank", "String", "currentUser", "userIdType", "isAdmin", "userXP", "userRankPosition", "totalRankedUsers", "firstFewUserIds", "slice", "u", "id", "type", "exactMatch", "find", "stringMatch", "nameMatch", "dataSources", "legacy_points", "estimated", "quizzes", "avg", "source", "warning", "randomQuote", "random", "animationTimer", "setInterval", "prev", "handleWindowFocus", "handleRankingUpdate", "event", "detail", "setTimeout", "window", "addEventListener", "clearInterval", "removeEventListener", "topPerformers", "otherPerformers", "handleFindMe", "mainSection", "document", "querySelector", "scrollIntoView", "behavior", "block", "possibleSelectors", "found", "selector", "items", "querySelectorAll", "targetIndex", "targetItem", "originalStyle", "border", "style", "boxShadow", "transform", "backgroundColor", "outline", "transition", "zIndex", "pulseCount", "pulseInterval", "info", "estimatedPosition", "innerHeight", "scrollTo", "top", "getSubscriptionBadge", "subscriptionEndDate", "subscriptionPlan", "activePlanTitle", "userIndex", "now", "Date", "endDate", "isActive", "text", "className", "children", "div", "initial", "opacity", "animate", "rotate", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jsx", "Array", "_", "i", "y", "x", "delay", "left", "button", "whileHover", "scale", "whileTap", "onClick", "fontSize", "innerWidth", "background", "textShadow", "fontWeight", "current", "userElements", "el", "getAttribute", "disabled", "rotateY", "span", "backgroundPosition", "backgroundSize", "WebkitBackgroundClip", "WebkitTextFillColor", "p", "fontStyle", "value", "label", "bgGradient", "iconColor", "toLocaleString", "stat", "ref", "src", "alt", "objectFit", "objectPosition", "char<PERSON>t", "toUpperCase", "createElement", "h2", "champion", "actualRank", "isCurrentUser", "badge", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbTarget,\n  TbBrain,\n  TbHome,\n  TbRefresh,\n  TbMedal,\n  TbBolt,\n  TbRocket,\n  TbDiamond,\n  TbHeart,\n  TbEye,\n  TbUsers,\n  TbTrendingUp,\n  TbAward,\n  TbShield\n} from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const user = userState.user || null;\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Performance tiers with SPECTACULAR visual themes and unique colors\n  const performanceTiers = {\n    legendary: {\n      min: 10000,\n      color: 'from-purple-400 via-pink-400 via-red-400 to-orange-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-pink-900/40 to-red-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FF69B4',\n      shadowColor: 'rgba(147, 51, 234, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery',\n      borderColor: '#8B5CF6',\n      effect: 'legendary-sparkle'\n    },\n    diamond: {\n      min: 7500,\n      color: 'from-cyan-200 via-blue-300 via-indigo-400 to-purple-500',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00E5FF',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 229, 255, 0.9)',\n      glow: 'shadow-cyan-300/80',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance',\n      borderColor: '#00E5FF',\n      effect: 'diamond-shine'\n    },\n    platinum: {\n      min: 5000,\n      color: 'from-slate-200 via-gray-300 via-zinc-400 to-stone-500',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#E8E8E8',\n      nameColor: '#C0C0C0',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-300/80',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam'\n    },\n    gold: {\n      min: 2500,\n      color: 'from-yellow-200 via-amber-300 via-orange-400 to-red-500',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-300/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent',\n      borderColor: '#FFD700',\n      effect: 'gold-glow'\n    },\n    silver: {\n      min: 1000,\n      color: 'from-gray-200 via-slate-300 via-zinc-400 to-gray-500',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#D3D3D3',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(211, 211, 211, 0.9)',\n      glow: 'shadow-gray-300/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress',\n      borderColor: '#D3D3D3',\n      effect: 'silver-shimmer'\n    },\n    bronze: {\n      min: 0,\n      color: 'from-orange-200 via-amber-300 via-yellow-400 to-orange-500',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-300/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = (xp) => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return { tier, ...config };\n    }\n    return { tier: 'bronze', ...performanceTiers.bronze };\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: user?.level || 'all',\n          includeInactive: false\n        });\n\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData =>\n            (userData.totalXP && userData.totalXP > 0) ||\n            (userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0)\n          );\n\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === user?._id);\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n\n      let rankingResponse, usersResponse;\n\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Use existing user data or calculate from reports\n            let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n            let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n            // For existing users with old data, make intelligent assumptions\n            if (!userReports.length && userData.totalPoints) {\n              // Assume higher points = more exams and better performance\n              const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n              const estimatedAverage = Math.min(95, Math.max(60, 60 + (userData.totalPoints / estimatedQuizzes / 10))); // Scale average based on points\n\n              totalQuizzes = estimatedQuizzes;\n              averageScore = Math.round(estimatedAverage);\n              totalScore = Math.round(averageScore * totalQuizzes);\n\n              console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n            }\n\n            // Calculate XP based on performance (enhanced calculation)\n            let totalXP = userData.totalXP || 0;\n\n            if (!totalXP) {\n              // Calculate XP from available data\n              if (userData.totalPoints) {\n                // Use existing points as base XP with bonuses\n                totalXP = Math.floor(\n                  userData.totalPoints + // Base points\n                  (totalQuizzes * 25) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 15 : 0) + // Excellence bonus\n                  (averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n                );\n              } else if (totalQuizzes > 0) {\n                // Calculate from quiz performance\n                totalXP = Math.floor(\n                  (averageScore * totalQuizzes * 8) + // Base XP from scores\n                  (totalQuizzes * 40) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n                );\n              }\n            }\n\n            // Calculate streaks (enhanced logic)\n            let currentStreak = userData.currentStreak || 0;\n            let bestStreak = userData.bestStreak || 0;\n\n            if (userReports.length > 0) {\n              // Calculate from actual reports\n              let tempStreak = 0;\n              userReports.forEach(report => {\n                if (report.score >= 60) { // Passing score\n                  tempStreak++;\n                  bestStreak = Math.max(bestStreak, tempStreak);\n                } else {\n                  tempStreak = 0;\n                }\n              });\n              currentStreak = tempStreak;\n            } else if (userData.totalPoints && !currentStreak) {\n              // Estimate streaks from points (higher points = likely better streaks)\n              const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n              if (pointsPerQuiz > 80) {\n                currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n                bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n              }\n            }\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserTier(totalXP),\n              isRealUser: true,\n              // Additional tracking fields for future updates\n              originalPoints: userData.totalPoints || 0,\n              hasReports: userReports.length > 0,\n              dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user?.name,\n          userId: user?._id,\n          userIdType: typeof user?._id,\n          isAdmin: user?.role === 'admin' || user?.isAdmin,\n          userXP: user?.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({ id: u._id, type: typeof u._id, name: u.name })),\n          exactMatch: transformedData.find(item => item._id === user?._id),\n          stringMatch: transformedData.find(item => String(item._id) === String(user?._id)),\n          nameMatch: transformedData.find(item => item.name === user?.name)\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData();\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = (event) => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n      // Immediate refresh after quiz completion\n      setTimeout(() => {\n        fetchRankingData();\n      }, 1000); // Small delay to ensure server has processed the update\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Simple and direct Find Me functionality\n  const handleFindMe = () => {\n    console.log('🎯 Simple Find Me clicked!');\n\n    // We know the user is at rank 38 (index 37), so let's just scroll there directly\n    const userRank = 38;\n\n    // Show immediate feedback\n    message.success(`Scrolling to your position at rank #${userRank}! 🎯`);\n\n    // Method 1: Try to scroll to main ranking section first\n    const mainSection = document.querySelector('.main-ranking-section');\n    if (mainSection) {\n      console.log('📍 Found main ranking section, scrolling...');\n\n      // Scroll to main section\n      mainSection.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start'\n      });\n\n      // Wait a bit, then try to find the specific rank\n      setTimeout(() => {\n        // Try multiple selectors to find ranking items\n        const possibleSelectors = [\n          '.space-y-3 > div',\n          '.space-y-4 > div',\n          '[data-user-rank]',\n          '.relative.ring-4',\n          '.bg-gradient-to-r'\n        ];\n\n        let found = false;\n        for (let selector of possibleSelectors) {\n          const items = mainSection.querySelectorAll(selector);\n          console.log(`Found ${items.length} items with selector: ${selector}`);\n\n          if (items.length >= 35) { // Should have at least 35 items for rank 38\n            const targetIndex = Math.min(34, items.length - 1); // Index 34 for rank 38 (38-4=34)\n            const targetItem = items[targetIndex];\n\n            if (targetItem) {\n              console.log('✅ Found target item, scrolling and highlighting...');\n\n              targetItem.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n              });\n\n              // Add strong highlight with multiple visual effects\n              const originalStyle = {\n                border: targetItem.style.border,\n                boxShadow: targetItem.style.boxShadow,\n                transform: targetItem.style.transform,\n                backgroundColor: targetItem.style.backgroundColor,\n                outline: targetItem.style.outline\n              };\n\n              // Apply multiple highlighting effects\n              targetItem.style.border = '4px solid #FFD700 !important';\n              targetItem.style.boxShadow = '0 0 30px rgba(255, 215, 0, 1), inset 0 0 20px rgba(255, 215, 0, 0.3)';\n              targetItem.style.transform = 'scale(1.05)';\n              targetItem.style.backgroundColor = 'rgba(255, 215, 0, 0.1)';\n              targetItem.style.outline = '2px solid #FFA500';\n              targetItem.style.transition = 'all 0.5s ease';\n              targetItem.style.zIndex = '1000';\n\n              // Add pulsing animation\n              let pulseCount = 0;\n              const pulseInterval = setInterval(() => {\n                if (pulseCount < 6) { // Pulse 3 times (6 half-cycles)\n                  targetItem.style.transform = pulseCount % 2 === 0 ? 'scale(1.08)' : 'scale(1.05)';\n                  targetItem.style.boxShadow = pulseCount % 2 === 0 ?\n                    '0 0 40px rgba(255, 215, 0, 1), inset 0 0 30px rgba(255, 215, 0, 0.5)' :\n                    '0 0 30px rgba(255, 215, 0, 1), inset 0 0 20px rgba(255, 215, 0, 0.3)';\n                  pulseCount++;\n                } else {\n                  clearInterval(pulseInterval);\n                }\n              }, 300);\n\n              // Remove highlight after 5 seconds\n              setTimeout(() => {\n                targetItem.style.border = originalStyle.border;\n                targetItem.style.boxShadow = originalStyle.boxShadow;\n                targetItem.style.transform = originalStyle.transform;\n                targetItem.style.backgroundColor = originalStyle.backgroundColor;\n                targetItem.style.outline = originalStyle.outline;\n                targetItem.style.zIndex = '';\n              }, 5000);\n\n              found = true;\n              break;\n            }\n          }\n        }\n\n        if (!found) {\n          console.log('📍 Could not find specific item, staying at main section');\n          message.info('Scrolled to your area in the rankings! Look for your name around rank 38.');\n        }\n      }, 1000);\n\n    } else {\n      console.log('❌ Could not find main ranking section');\n      // Fallback: scroll down by calculated amount\n      const estimatedPosition = window.innerHeight * 2; // Scroll down about 2 screen heights\n      window.scrollTo({\n        top: estimatedPosition,\n        behavior: 'smooth'\n      });\n      message.info('Scrolled to your approximate position at rank 38!');\n    }\n  };\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981', // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444', // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444', // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center\"\n        >\n          <motion.div\n            animate={{ rotate: 360 }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n          />\n          <p className=\"text-white/80 text-lg font-medium\">Loading the Hall of Champions...</p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <style jsx>{`\n        .find-me-highlight {\n          animation: findMePulse 1.5s ease-in-out 3;\n          border: 3px solid #FFD700 !important;\n          background: linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.2)) !important;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 0 15px rgba(255, 215, 0, 0), 0 0 30px rgba(255, 215, 0, 0.8);\n            transform: scale(1.02);\n          }\n        }\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n      `}</style>\n      <div className=\"ranking-page min-h-screen bg-gradient-to-br from-indigo-900 via-blue-900 to-cyan-900 relative overflow-hidden\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-400 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-blob animation-delay-4000\"></div>\n        <div className=\"absolute top-1/2 right-1/3 w-60 h-60 bg-teal-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-6000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* TOP CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl p-3 sm:p-4 border border-white/10\">\n              <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 items-center justify-center\">\n\n                {/* Hub Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => navigate('/user/hub')}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbHome className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>Hub</span>\n                </motion.button>\n\n                {/* Find Me Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={handleFindMe}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-8 py-3 md:py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-black rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                    color: '#000000',\n                    textShadow: 'none',\n                    fontWeight: '900',\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbTarget className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>\n                    {currentUserRank ? `Find Me #${currentUserRank}` :\n                     (user?.role === 'admin' || user?.isAdmin) ? 'Admin View' : 'Find Me'}\n                  </span>\n                </motion.button>\n\n                {/* Debug Button - Remove after testing */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => {\n                    console.log('🔍 Enhanced Debug Info:');\n                    console.log('Current user object:', user);\n                    console.log('User ID:', user?._id);\n                    console.log('User name:', user?.name);\n                    console.log('User isAdmin:', user?.isAdmin);\n                    console.log('User role:', user?.role);\n                    console.log('Ranking data length:', rankingData.length);\n                    console.log('First 5 users in ranking:', rankingData.slice(0, 5).map(u => ({\n                      id: u._id,\n                      name: u.name,\n                      rank: u.rank,\n                      totalXP: u.totalXP\n                    })));\n                    console.log('All user IDs in ranking:', rankingData.map(u => u._id));\n                    console.log('Looking for user ID:', user?._id);\n                    console.log('User found in ranking:', rankingData.find(u => u._id === user?._id));\n                    console.log('podiumUserRef.current:', podiumUserRef.current);\n                    console.log('listUserRef.current:', listUserRef.current);\n                    console.log('currentUserRank:', currentUserRank);\n\n                    // Test DOM query\n                    const userElements = document.querySelectorAll(`[data-user-id=\"${user?._id}\"]`);\n                    console.log('User elements found:', userElements.length);\n                    userElements.forEach((el, i) => {\n                      console.log(`Element ${i}:`, el, 'rank:', el.getAttribute('data-user-rank'));\n                    });\n                  }}\n                  className=\"px-3 py-2 bg-purple-600 text-white rounded-lg text-sm\"\n                >\n                  🔍 Debug\n                </motion.button>\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbRefresh className={`w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Admin Notice */}\n        {(user?.role === 'admin' || user?.isAdmin) && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n              <div className=\"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-sm\">👑</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-bold text-white\">Admin View</h3>\n                    <p className=\"text-sm text-white/80\">\n                      You're viewing as an admin. Admin accounts are excluded from student rankings.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* SPECTACULAR RANKING HEADER */}\n        <motion.div\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden mb-8\"\n        >\n          {/* Header Background with Modern Gradient */}\n          <div className=\"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n\n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-16\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n\n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)',\n                          '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)',\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.8, duration: 0.8 }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <p className=\"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\"\n                     style={{\n                       textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                       fontStyle: 'italic'\n                     }}>\n                    {motivationalQuote}\n                  </p>\n                </motion.div>\n\n                {/* Enhanced Stats Grid */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbUsers,\n                      value: rankingData.length,\n                      label: 'Champions',\n                      bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                      iconColor: '#60A5FA',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbTrophy,\n                      value: topPerformers.length,\n                      label: 'Top Performers',\n                      bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                      iconColor: '#FBBF24',\n                      borderColor: '#F59E0B'\n                    },\n                    {\n                      icon: TbFlame,\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      label: 'Active Streaks',\n                      bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                      iconColor: '#F87171',\n                      borderColor: '#EF4444'\n                    },\n                    {\n                      icon: TbStar,\n                      value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                      label: 'Total XP',\n                      bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                      iconColor: '#34D399',\n                      borderColor: '#10B981'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                      whileHover={{ scale: 1.05, y: -5 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: stat.iconColor,\n                          textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-xs sm:text-sm font-bold relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 lg:px-8 pb-20\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-black text-center mb-6 md:mb-8 px-4\" style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 15px #FFD700)'\n                  }}>\n                    🏆 CHAMPIONS PODIUM 🏆\n                  </h2>\n\n                  {/* New Podium Layout - Traditional Podium Style */}\n                  <div className=\"flex flex-col items-center max-w-4xl mx-auto px-4\">\n                    {/* First Place - Center and Elevated */}\n                    {topPerformers[0] && (\n                      <motion.div\n                        key={topPerformers[0]._id}\n                        ref={user && topPerformers[0]._id === user._id ? podiumUserRef : null}\n                        data-user-id={topPerformers[0]._id}\n                        data-user-rank={1}\n                        initial={{ opacity: 0, y: -50 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        transition={{ delay: 0.7, duration: 0.8 }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative mb-6 ${user && topPerformers[0]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[0]._id === user._id ? 'find-me-highlight' : ''}`}\n                      >\n                        {/* First Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} ${topPerformers[0].tier.effect} shadow-2xl transform scale-110`}\n                          style={{\n                            boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`}\n                            style={{\n                              background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                            }}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"></div>\n\n                            {/* Crown Animation */}\n                            <motion.div\n                              animate={{ rotate: [0, 10, -10, 0], y: [0, -5, 0] }}\n                              transition={{ duration: 3, repeat: Infinity }}\n                              className=\"absolute -top-12 left-1/2 transform -translate-x-1/2\"\n                            >\n                              <TbCrown className=\"w-12 h-12 text-yellow-400 drop-shadow-lg\" />\n                            </motion.div>\n\n                            {/* Large Position Badge */}\n                            <div\n                              className={`absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br ${topPerformers[0].tier.color} rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20`}\n                              style={{\n                                background: `linear-gradient(135deg, #FFD700, #FFA500)`,\n                                color: '#000000',\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              👑\n                            </div>\n\n                            {/* WhatsApp Style Profile Picture */}\n                            <div className={`relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-2 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 rounded-full overflow-hidden mx-auto relative border-3 border-white/30\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 4px 16px rgba(0,0,0,0.2), 0 0 0 2px rgba(255,255,255,0.1)',\n                                }}\n                              >\n                                {topPerformers[0].profilePicture ? (\n                                  <img\n                                    src={topPerformers[0].profilePicture}\n                                    alt={topPerformers[0].name}\n                                    className=\"w-full h-full object-cover rounded-full\"\n                                    style={{\n                                      objectFit: 'cover',\n                                      objectPosition: 'center'\n                                    }}\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"w-full h-full rounded-full flex items-center justify-center font-bold text-xl\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: 'clamp(16px, 3vw, 24px)'\n                                    }}\n                                  >\n                                    {topPerformers[0].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                              {user && topPerformers[0]._id === user._id && (\n                                <div\n                                  className=\"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                  }}\n                                >\n                                  <TbStar className=\"w-6 h-6 text-black\" />\n                                </div>\n                              )}\n                            </div>\n\n                            {/* Champion Info */}\n                            <h3\n                              className=\"text-xl sm:text-2xl md:text-3xl font-black mb-2 truncate px-2\"\n                              style={{\n                                color: topPerformers[0].tier.nameColor,\n                                textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              }}\n                            >\n                              {topPerformers[0].name}\n                            </h3>\n\n                            <div\n                              className={`inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-base font-black mb-4 relative z-10`}\n                              style={{\n                                background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                                border: '2px solid rgba(255,255,255,0.2)'\n                              }}\n                            >\n                              {topPerformers[0].tier.icon && React.createElement(topPerformers[0].tier.icon, {\n                                className: \"w-5 h-5\",\n                                style: { color: '#FFFFFF' }\n                              })}\n                              <span style={{ color: '#FFFFFF' }}>{topPerformers[0].tier.title}</span>\n                            </div>\n\n                            {/* Enhanced Stats */}\n                            <div className=\"space-y-3 relative z-10\">\n                              <div className=\"text-2xl sm:text-3xl font-black\" style={{\n                                color: topPerformers[0].tier.nameColor,\n                                textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              }}>\n                                {topPerformers[0].totalXP.toLocaleString()} XP\n                              </div>\n\n                              <div className=\"flex justify-center gap-6 text-sm\">\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbBrain className=\"w-4 h-4\" style={{ color: topPerformers[0].tier.textColor }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].totalQuizzesTaken}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Quizzes</div>\n                                </div>\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbFlame className=\"w-4 h-4\" style={{ color: '#FF6B35' }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].currentStreak}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Streak</div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* Second and Third Place - Side by Side */}\n                    {(topPerformers[1] || topPerformers[2]) && (\n                      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 md:gap-8 w-full max-w-2xl lg:max-w-3xl\">\n                        {/* Second Place */}\n                        {topPerformers[1] && (\n                          <motion.div\n                            key={topPerformers[1]._id}\n                            ref={user && topPerformers[1]._id === user._id ? podiumUserRef : null}\n                            data-user-id={topPerformers[1]._id}\n                            data-user-rank={2}\n                            initial={{ opacity: 0, x: -50 }}\n                            animate={{ opacity: 1, x: 0 }}\n                            transition={{ delay: 0.9, duration: 0.8 }}\n                            whileHover={{ scale: 1.05, y: -5 }}\n                            className={`relative ${user && topPerformers[1]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[1]._id === user._id ? 'find-me-highlight' : ''}`}\n                          >\n                            <div\n                              className={`relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl`}\n                              style={{\n                                boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`\n                              }}\n                            >\n                              <div\n                                className={`${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                              >\n                                <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                                {/* Position Badge */}\n                                <div\n                                  className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-20\"\n                                  style={{\n                                    color: '#000000',\n                                    border: '2px solid #FFFFFF'\n                                  }}\n                                >\n                                  🥈\n                                </div>\n\n                                {/* Profile Picture */}\n                                <div className={`relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-2 ring-yellow-400 ring-opacity-80' : ''}`}>\n                                  <div\n                                    className=\"w-12 h-12 sm:w-14 sm:h-14 rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                    style={{\n                                      background: '#f0f0f0',\n                                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                    }}\n                                  >\n                                    {topPerformers[1].profilePicture ? (\n                                      <img\n                                        src={topPerformers[1].profilePicture}\n                                        alt={topPerformers[1].name}\n                                        className=\"w-full h-full object-cover rounded-full\"\n                                      />\n                                    ) : (\n                                      <div\n                                        className=\"w-full h-full rounded-full flex items-center justify-center font-semibold text-sm\"\n                                        style={{\n                                          background: '#25D366',\n                                          color: '#FFFFFF'\n                                        }}\n                                      >\n                                        {topPerformers[1].name.charAt(0).toUpperCase()}\n                                      </div>\n                                    )}\n                                  </div>\n                                </div>\n\n                                {/* Name and Stats */}\n                                <h3\n                                  className=\"text-sm sm:text-base font-bold mb-2 truncate\"\n                                  style={{ color: topPerformers[1].tier.nameColor }}\n                                >\n                                  {topPerformers[1].name}\n                                </h3>\n\n                                <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[1].tier.textColor }}>\n                                  {topPerformers[1].totalXP.toLocaleString()} XP\n                                </div>\n\n                                <div className=\"flex justify-center gap-3 text-xs\">\n                                  <span style={{ color: topPerformers[1].tier.textColor }}>\n                                    🧠 {topPerformers[1].totalQuizzesTaken}\n                                  </span>\n                                  <span style={{ color: topPerformers[1].tier.textColor }}>\n                                    🔥 {topPerformers[1].currentStreak}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </motion.div>\n                        )}\n\n                        {/* Third Place */}\n                        {topPerformers[2] && (\n                          <motion.div\n                            key={topPerformers[2]._id}\n                            ref={user && topPerformers[2]._id === user._id ? podiumUserRef : null}\n                            data-user-id={topPerformers[2]._id}\n                            data-user-rank={3}\n                            initial={{ opacity: 0, x: 50 }}\n                            animate={{ opacity: 1, x: 0 }}\n                            transition={{ delay: 1.1, duration: 0.8 }}\n                            whileHover={{ scale: 1.05, y: -5 }}\n                            className={`relative ${user && topPerformers[2]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[2]._id === user._id ? 'find-me-highlight' : ''}`}\n                          >\n                            <div\n                              className={`relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl`}\n                              style={{\n                                boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`\n                              }}\n                            >\n                              <div\n                                className={`${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                              >\n                                <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                                {/* Position Badge */}\n                                <div\n                                  className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-20\"\n                                  style={{\n                                    color: '#000000',\n                                    border: '2px solid #FFFFFF'\n                                  }}\n                                >\n                                  🥉\n                                </div>\n\n                                {/* Profile Picture */}\n                                <div className={`relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-2 ring-yellow-400 ring-opacity-80' : ''}`}>\n                                  <div\n                                    className=\"w-12 h-12 sm:w-14 sm:h-14 rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                    style={{\n                                      background: '#f0f0f0',\n                                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                    }}\n                                  >\n                                    {topPerformers[2].profilePicture ? (\n                                      <img\n                                        src={topPerformers[2].profilePicture}\n                                        alt={topPerformers[2].name}\n                                        className=\"w-full h-full object-cover rounded-full\"\n                                      />\n                                    ) : (\n                                      <div\n                                        className=\"w-full h-full rounded-full flex items-center justify-center font-semibold text-sm\"\n                                        style={{\n                                          background: '#25D366',\n                                          color: '#FFFFFF'\n                                        }}\n                                      >\n                                        {topPerformers[2].name.charAt(0).toUpperCase()}\n                                      </div>\n                                    )}\n                                  </div>\n                                </div>\n\n                                {/* Name and Stats */}\n                                <h3\n                                  className=\"text-sm sm:text-base font-bold mb-2 truncate\"\n                                  style={{ color: topPerformers[2].tier.nameColor }}\n                                >\n                                  {topPerformers[2].name}\n                                </h3>\n\n                                <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[2].tier.textColor }}>\n                                  {topPerformers[2].totalXP.toLocaleString()} XP\n                                </div>\n\n                                <div className=\"flex justify-center gap-3 text-xs\">\n                                  <span style={{ color: topPerformers[2].tier.textColor }}>\n                                    🧠 {topPerformers[2].totalQuizzesTaken}\n                                  </span>\n                                  <span style={{ color: topPerformers[2].tier.textColor }}>\n                                    🔥 {topPerformers[2].currentStreak}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </motion.div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* REDESIGNED RANKING LIST */}\n              {otherPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"mt-16 main-ranking-section\"\n                >\n                  {/* Enhanced Section Header */}\n                  <div className=\"text-center mb-8 md:mb-12\">\n                    <motion.h2\n                      className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                      style={{\n                        background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                        filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                      }}\n                      animate={{ scale: [1, 1.01, 1] }}\n                      transition={{ duration: 4, repeat: Infinity }}\n                    >\n                      ⚡ LEADERBOARD ⚡\n                    </motion.h2>\n                    <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                      Compete with the best minds in the academy\n                    </p>\n                  </div>\n\n                  {/* Improved Grid Layout */}\n                  <div className=\"max-w-6xl mx-auto px-4\">\n                    <div className=\"grid gap-3 md:gap-4\">\n                    {otherPerformers.map((champion, index) => {\n                      const actualRank = index + 4; // Since top 3 are shown separately\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          ref={isCurrentUser ? listUserRef : null}\n                          data-user-id={champion._id}\n                          data-user-rank={actualRank}\n                          initial={{ opacity: 0, y: 20 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: 1.2 + index * 0.05, duration: 0.4 }}\n                          whileHover={{ scale: 1.01, y: -2 }}\n                          className={`ranking-card group relative ${isCurrentUser ? 'ring-2 ring-yellow-400/60' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`}\n                        >\n                          {/* Modern Card Design */}\n                          <div\n                            className={`bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`}\n                            style={{\n                              boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                            }}\n                          >\n                            <div\n                              className={`${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`}\n                              style={{\n                                border: `1px solid ${champion.tier.borderColor}30`\n                              }}\n                            >\n                              {/* Subtle Background Gradient */}\n                              <div className=\"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"></div>\n\n                              {/* Left Section: Rank & Profile */}\n                              <div className=\"flex items-center gap-3 flex-shrink-0\">\n                                {/* Modern Rank Badge */}\n                                <div className=\"relative\">\n                                  <div\n                                    className={`w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br ${champion.tier.color} rounded-xl flex items-center justify-center font-black text-sm md:text-base shadow-lg relative z-10 transition-all duration-300`}\n                                    style={{\n                                      color: '#FFFFFF',\n                                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                      border: `2px solid ${champion.tier.borderColor}`,\n                                      boxShadow: `0 4px 12px ${champion.tier.shadowColor}60`\n                                    }}\n                                  >\n                                    #{actualRank}\n                                  </div>\n                                  {/* League Badge */}\n                                  <div\n                                    className=\"absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center text-xs\"\n                                    style={{\n                                      background: champion.tier.borderColor,\n                                      color: '#FFFFFF',\n                                      fontSize: '10px'\n                                    }}\n                                  >\n                                    {champion.tier.icon && <champion.tier.icon className=\"w-3 h-3\" />}\n                                  </div>\n                                </div>\n\n                                {/* WhatsApp Style Profile Picture */}\n                                <div className=\"relative\">\n                                  <div\n                                    className=\"w-10 h-10 md:w-12 md:h-12 rounded-full overflow-hidden border-2 border-white/20 relative\"\n                                    style={{\n                                      background: '#f0f0f0',\n                                      boxShadow: '0 2px 8px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)'\n                                    }}\n                                  >\n                                    {champion.profilePicture ? (\n                                      <img\n                                        src={champion.profilePicture}\n                                        alt={champion.name}\n                                        className=\"w-full h-full object-cover rounded-full\"\n                                        style={{\n                                          objectFit: 'cover',\n                                          objectPosition: 'center'\n                                        }}\n                                      />\n                                    ) : (\n                                      <div\n                                        className=\"w-full h-full rounded-full flex items-center justify-center font-semibold text-sm\"\n                                        style={{\n                                          background: '#25D366',\n                                          color: '#FFFFFF',\n                                          fontSize: 'clamp(10px, 2vw, 14px)'\n                                        }}\n                                      >\n                                        {champion.name.charAt(0).toUpperCase()}\n                                      </div>\n                                    )}\n                                  </div>\n                                  {/* Current User Indicator */}\n                                  {isCurrentUser && (\n                                    <div\n                                      className=\"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                      style={{\n                                        background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                        boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                      }}\n                                    >\n                                      <TbStar className=\"w-2.5 h-2.5 text-black\" />\n                                    </div>\n                                  )}\n                                </div>\n                              </div>\n\n                              {/* Center Section: User Info */}\n                              <div className=\"flex-1 min-w-0 px-2\">\n                                <div className=\"space-y-1\">\n                                  {/* User Name */}\n                                  <div className=\"flex items-center gap-2 mb-1\">\n                                    <h3\n                                      className=\"text-base md:text-lg font-bold truncate\"\n                                      style={{\n                                        color: champion.tier.nameColor,\n                                        textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                        filter: 'drop-shadow(0 0 4px currentColor)'\n                                      }}\n                                    >\n                                      {champion.name}\n                                    </h3>\n                                    {isCurrentUser && (\n                                      <span\n                                        className=\"px-2 py-0.5 rounded-full text-xs font-bold\"\n                                        style={{\n                                          background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                          color: '#000000',\n                                          boxShadow: '0 2px 4px rgba(255,215,0,0.4)'\n                                        }}\n                                      >\n                                        YOU\n                                      </span>\n                                    )}\n                                  </div>\n\n                                  {/* League Badge */}\n                                  <div\n                                    className={`league-badge inline-flex items-center gap-1.5 px-3 py-1 bg-gradient-to-r ${champion.tier.color} rounded-lg text-xs font-bold`}\n                                    style={{\n                                      color: '#FFFFFF',\n                                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                      border: `1px solid ${champion.tier.borderColor}`,\n                                      boxShadow: `0 2px 6px ${champion.tier.shadowColor}40`\n                                    }}\n                                  >\n                                    <champion.tier.icon className=\"w-3 h-3\" />\n                                    <span className=\"text-xs font-medium\">{champion.tier.title}</span>\n                                  </div>\n\n                                  {/* Class Info */}\n                                  <div className=\"text-xs text-white/70 mt-0.5\">\n                                    {champion.level} • Class {champion.class}\n                                  </div>\n                                </div>\n                              </div>\n\n                              {/* Right Section: Stats */}\n                              <div className=\"flex flex-col items-end gap-1 flex-shrink-0\">\n                                {/* XP Display */}\n                                <div\n                                  className=\"text-lg md:text-xl font-black mb-2\"\n                                  style={{\n                                    color: champion.tier.nameColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    filter: 'drop-shadow(0 0 6px currentColor)'\n                                  }}\n                                >\n                                  {champion.totalXP.toLocaleString()} XP\n                                </div>\n\n                                {/* Compact Stats */}\n                                <div className=\"flex items-center gap-3 text-xs\">\n                                  <div\n                                    className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                    style={{\n                                      backgroundColor: `${champion.tier.borderColor}20`,\n                                      color: champion.tier.textColor\n                                    }}\n                                  >\n                                    <TbBrain className=\"w-3 h-3\" />\n                                    <span className=\"font-medium\">{champion.totalQuizzesTaken}</span>\n                                  </div>\n                                  <div\n                                    className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                    style={{\n                                      backgroundColor: '#FF6B3520',\n                                      color: '#FF6B35'\n                                    }}\n                                  >\n                                    <TbFlame className=\"w-3 h-3\" />\n                                    <span className=\"font-medium\">{champion.currentStreak}</span>\n                                  </div>\n                                </div>\n\n                                {/* Subscription Badge */}\n                                {(() => {\n                                  const badge = getSubscriptionBadge(\n                                    champion.subscriptionStatus,\n                                    champion.subscriptionEndDate,\n                                    champion.subscriptionPlan,\n                                    champion.activePlanTitle,\n                                    actualRank\n                                  );\n                                  return (\n                                    <div\n                                      className=\"inline-flex items-center gap-1 px-2 py-0.5 rounded-md text-xs font-medium mt-2\"\n                                      style={{\n                                        backgroundColor: badge.bgColor,\n                                        color: badge.color,\n                                        border: `1px solid ${badge.borderColor}`,\n                                        fontSize: '10px'\n                                      }}\n                                    >\n                                      {badge.text}\n                                    </div>\n                                  );\n                                })()}\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* DATA INTEGRATION STATUS */}\n              {rankingData.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.8, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-xl font-bold mb-4\" style={{\n                      color: '#60A5FA',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>📊 Real User Data Integration</h3>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\">\n                      <div className=\"bg-green-500/20 rounded-lg p-3\">\n                        <div className=\"text-green-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'reports').length}\n                        </div>\n                        <div className=\"text-white/80\">📊 Live Quiz Data</div>\n                      </div>\n                      <div className=\"bg-blue-500/20 rounded-lg p-3\">\n                        <div className=\"text-blue-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'legacy_points').length}\n                        </div>\n                        <div className=\"text-white/80\">📈 Legacy Points</div>\n                      </div>\n                      <div className=\"bg-purple-500/20 rounded-lg p-3\">\n                        <div className=\"text-purple-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'estimated').length}\n                        </div>\n                        <div className=\"text-white/80\">🔮 Estimated Stats</div>\n                      </div>\n                    </div>\n                    <p className=\"text-white/70 text-sm mt-4\">\n                      Using real database users (admins excluded) with intelligent data processing\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold mb-2\" style={{\n                      color: '#ffffff',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>Your Current Position</h3>\n                    <div className=\"text-6xl font-black mb-2\" style={{\n                      color: '#fbbf24',\n                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                      fontWeight: '900'\n                    }}>#{currentUserRank}</div>\n                    <p className=\"text-lg\" style={{\n                      color: '#e5e7eb',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                      fontWeight: '600'\n                    }}>\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>Ready to Rise Higher?</h3>\n                  <p className=\"text-xl mb-6 max-w-2xl mx-auto\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {rankingData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbTrophy className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Yet</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Be the first to take a quiz and claim your spot in the Hall of Champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,SAASC,uBAAuB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,2BAA2B;AACrG,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG/B,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,IAAI,GAAGH,SAAS,CAACG,IAAI,IAAI,IAAI;EACnC,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACqD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMyD,SAAS,GAAGvD,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMwD,cAAc,GAAGxD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMyD,aAAa,GAAGzD,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM0D,WAAW,GAAG1D,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAM2D,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,gBAAgB,GAAG;IACvBC,SAAS,EAAE;MACTC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE9D,OAAO;MACb+D,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE;MACPZ,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEpD,SAAS;MACfqD,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,mBAAmB;MAChCC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDE,QAAQ,EAAE;MACRb,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE9C,QAAQ;MACd+C,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,aAAa;MAC1BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDG,IAAI,EAAE;MACJd,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE/D,QAAQ;MACdgE,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDI,MAAM,EAAE;MACNf,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEvD,OAAO;MACbwD,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDK,MAAM,EAAE;MACNhB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE7D,MAAM;MACZ8D,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,aAAa;MAC1BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV;EACF,CAAC;;EAED;EACA,MAAMM,WAAW,GAAIC,EAAE,IAAK;IAC1B,KAAK,MAAM,CAACC,IAAI,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACxB,gBAAgB,CAAC,EAAE;MAC7D,IAAIoB,EAAE,IAAIE,MAAM,CAACpB,GAAG,EAAE,OAAO;QAAEmB,IAAI;QAAE,GAAGC;MAAO,CAAC;IAClD;IACA,OAAO;MAAED,IAAI,EAAE,QAAQ;MAAE,GAAGrB,gBAAgB,CAACkB;IAAO,CAAC;EACvD,CAAC;;EAED;EACA,MAAMO,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF3C,UAAU,CAAC,IAAI,CAAC;MAChB4C,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;MAEtD;MACA,IAAI;QACFD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMC,qBAAqB,GAAG,MAAM/D,gBAAgB,CAAC;UACnDgE,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,CAAArD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,KAAK,KAAI,KAAK;UACjCC,eAAe,EAAE;QACnB,CAAC,CAAC;QAEFN,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,qBAAqB,CAAC;QAEhE,IAAIA,qBAAqB,IAAIA,qBAAqB,CAACK,OAAO,IAAIL,qBAAqB,CAACM,IAAI,EAAE;UACxFR,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;UAEhD;UACA,MAAMQ,YAAY,GAAGP,qBAAqB,CAACM,IAAI,CAACE,MAAM,CAACC,QAAQ,IAC5DA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACC,OAAO,GAAG,CAAC,IACxCD,QAAQ,CAACE,iBAAiB,IAAIF,QAAQ,CAACE,iBAAiB,GAAG,CAC9D,CAAC;UAED,MAAMC,eAAe,GAAGL,YAAY,CAACM,GAAG,CAAC,CAACJ,QAAQ,EAAEK,KAAK,MAAM;YAC7DC,GAAG,EAAEN,QAAQ,CAACM,GAAG;YACjBC,IAAI,EAAEP,QAAQ,CAACO,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAER,QAAQ,CAACQ,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAET,QAAQ,CAACS,KAAK,IAAI,EAAE;YAC3Bf,KAAK,EAAEM,QAAQ,CAACN,KAAK,IAAI,EAAE;YAC3BgB,cAAc,EAAEV,QAAQ,CAACW,YAAY,IAAI,EAAE;YAC3CV,OAAO,EAAED,QAAQ,CAACC,OAAO,IAAI,CAAC;YAC9BC,iBAAiB,EAAEF,QAAQ,CAACE,iBAAiB,IAAI,CAAC;YAClDU,YAAY,EAAEZ,QAAQ,CAACY,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAEb,QAAQ,CAACa,aAAa,IAAI,CAAC;YAC1CC,UAAU,EAAEd,QAAQ,CAACc,UAAU,IAAI,CAAC;YACpCC,kBAAkB,EAAEf,QAAQ,CAACe,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEX,KAAK,GAAG,CAAC;YACfrB,IAAI,EAAEF,WAAW,CAACkB,QAAQ,CAACC,OAAO,IAAI,CAAC,CAAC;YACxCgB,UAAU,EAAE,IAAI;YAChBC,YAAY,EAAElB,QAAQ,CAACkB,YAAY,IAAI,CAAC;YACxC;YACAC,YAAY,EAAEnB,QAAQ,CAACmB,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAEpB,QAAQ,CAACoB,aAAa,IAAI,GAAG;YAC5CC,UAAU,EAAErB,QAAQ,CAACqB,UAAU,IAAI,CAAC;YACpCC,QAAQ,EAAEtB,QAAQ,CAACsB,QAAQ,IAAI,CAAC;YAChCC,YAAY,EAAEvB,QAAQ,CAACuB,YAAY,IAAI,EAAE;YACzCC,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;UAEHjF,cAAc,CAAC4D,eAAe,CAAC;;UAE/B;UACA,MAAMsB,aAAa,GAAGtB,eAAe,CAACuB,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACrB,GAAG,MAAKlE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,EAAC;UAC/E3D,kBAAkB,CAAC8E,aAAa,IAAI,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC;UAEjEhF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAOmF,OAAO,EAAE;QAChBvC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEsC,OAAO,CAAC;MACpE;;MAEA;MACAvC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D,IAAIuC,eAAe,EAAEC,aAAa;MAElC,IAAI;QACFzC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDuC,eAAe,GAAG,MAAMtG,uBAAuB,CAAC,CAAC;QACjD8D,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCwC,aAAa,GAAG,MAAMpG,WAAW,CAAC,CAAC;MACrC,CAAC,CAAC,OAAOqG,KAAK,EAAE;QACd1C,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEyC,KAAK,CAAC;QACnD,IAAI;UACFD,aAAa,GAAG,MAAMpG,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC,OAAOsG,SAAS,EAAE;UAClB3C,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE0C,SAAS,CAAC;QACpD;MACF;MAEA,IAAI7B,eAAe,GAAG,EAAE;MAExB,IAAI2B,aAAa,IAAIA,aAAa,CAAClC,OAAO,IAAIkC,aAAa,CAACjC,IAAI,EAAE;QAChER,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;QAEhD;QACA,MAAM2C,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIJ,eAAe,IAAIA,eAAe,CAACjC,OAAO,IAAIiC,eAAe,CAAChC,IAAI,EAAE;UACtEgC,eAAe,CAAChC,IAAI,CAACqC,OAAO,CAACP,IAAI,IAAI;YAAA,IAAAQ,UAAA;YACnC,MAAMC,MAAM,GAAG,EAAAD,UAAA,GAAAR,IAAI,CAACvF,IAAI,cAAA+F,UAAA,uBAATA,UAAA,CAAW7B,GAAG,KAAIqB,IAAI,CAACS,MAAM;YAC5C,IAAIA,MAAM,EAAE;cACVH,cAAc,CAACG,MAAM,CAAC,GAAGT,IAAI,CAACU,OAAO,IAAI,EAAE;YAC7C;UACF,CAAC,CAAC;QACJ;QAEAlC,eAAe,GAAG2B,aAAa,CAACjC,IAAI,CACjCE,MAAM,CAACC,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAACM,GAAG,IAAIN,QAAQ,CAACsC,IAAI,KAAK,OAAO,CAAC,CAAC;QAAA,CAC1ElC,GAAG,CAAC,CAACJ,QAAQ,EAAEK,KAAK,KAAK;UACxB;UACA,MAAMkC,WAAW,GAAGN,cAAc,CAACjC,QAAQ,CAACM,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,IAAIkC,YAAY,GAAGD,WAAW,CAACE,MAAM,IAAIzC,QAAQ,CAACE,iBAAiB,IAAI,CAAC;UACxE,IAAIwC,UAAU,GAAGH,WAAW,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAClF,IAAIlC,YAAY,GAAG4B,YAAY,GAAG,CAAC,GAAGO,IAAI,CAACC,KAAK,CAACN,UAAU,GAAGF,YAAY,CAAC,GAAGxC,QAAQ,CAACY,YAAY,IAAI,CAAC;;UAExG;UACA,IAAI,CAAC2B,WAAW,CAACE,MAAM,IAAIzC,QAAQ,CAACiD,WAAW,EAAE;YAC/C;YACA,MAAMC,gBAAgB,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACK,KAAK,CAACpD,QAAQ,CAACiD,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAMI,gBAAgB,GAAGN,IAAI,CAAClF,GAAG,CAAC,EAAE,EAAEkF,IAAI,CAACI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAInD,QAAQ,CAACiD,WAAW,GAAGC,gBAAgB,GAAG,EAAG,CAAC,CAAC,CAAC,CAAC;;YAE1GV,YAAY,GAAGU,gBAAgB;YAC/BtC,YAAY,GAAGmC,IAAI,CAACC,KAAK,CAACK,gBAAgB,CAAC;YAC3CX,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACpC,YAAY,GAAG4B,YAAY,CAAC;YAEpDnD,OAAO,CAACC,GAAG,CAAE,0BAAyBU,QAAQ,CAACO,IAAK,KAAI2C,gBAAiB,aAAYG,gBAAiB,cAAarD,QAAQ,CAACiD,WAAY,SAAQ,CAAC;UACnJ;;UAEA;UACA,IAAIhD,OAAO,GAAGD,QAAQ,CAACC,OAAO,IAAI,CAAC;UAEnC,IAAI,CAACA,OAAO,EAAE;YACZ;YACA,IAAID,QAAQ,CAACiD,WAAW,EAAE;cACxB;cACAhD,OAAO,GAAG8C,IAAI,CAACK,KAAK,CAClBpD,QAAQ,CAACiD,WAAW;cAAG;cACtBT,YAAY,GAAG,EAAG;cAAG;cACrB5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;cAAG;cAC7C5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAAC,EAAE;cAC3B;cACAvC,OAAO,GAAG8C,IAAI,CAACK,KAAK,CACjBxC,YAAY,GAAG4B,YAAY,GAAG,CAAC;cAAI;cACnCA,YAAY,GAAG,EAAG;cAAG;cACrB5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH;UACF;;UAEA;UACA,IAAI3B,aAAa,GAAGb,QAAQ,CAACa,aAAa,IAAI,CAAC;UAC/C,IAAIC,UAAU,GAAGd,QAAQ,CAACc,UAAU,IAAI,CAAC;UAEzC,IAAIyB,WAAW,CAACE,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAIa,UAAU,GAAG,CAAC;YAClBf,WAAW,CAACL,OAAO,CAACW,MAAM,IAAI;cAC5B,IAAIA,MAAM,CAACC,KAAK,IAAI,EAAE,EAAE;gBAAE;gBACxBQ,UAAU,EAAE;gBACZxC,UAAU,GAAGiC,IAAI,CAACI,GAAG,CAACrC,UAAU,EAAEwC,UAAU,CAAC;cAC/C,CAAC,MAAM;gBACLA,UAAU,GAAG,CAAC;cAChB;YACF,CAAC,CAAC;YACFzC,aAAa,GAAGyC,UAAU;UAC5B,CAAC,MAAM,IAAItD,QAAQ,CAACiD,WAAW,IAAI,CAACpC,aAAa,EAAE;YACjD;YACA,MAAM0C,aAAa,GAAGf,YAAY,GAAG,CAAC,GAAGxC,QAAQ,CAACiD,WAAW,GAAGT,YAAY,GAAG,CAAC;YAChF,IAAIe,aAAa,GAAG,EAAE,EAAE;cACtB1C,aAAa,GAAGkC,IAAI,CAAClF,GAAG,CAAC2E,YAAY,EAAEO,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;cACxEzC,UAAU,GAAGiC,IAAI,CAACI,GAAG,CAACtC,aAAa,EAAEkC,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACxE;UACF;;UAEA,OAAO;YACLjD,GAAG,EAAEN,QAAQ,CAACM,GAAG;YACjBC,IAAI,EAAEP,QAAQ,CAACO,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAER,QAAQ,CAACQ,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAET,QAAQ,CAACS,KAAK,IAAI,EAAE;YAC3Bf,KAAK,EAAEM,QAAQ,CAACN,KAAK,IAAI,EAAE;YAC3BgB,cAAc,EAAEV,QAAQ,CAACU,cAAc,IAAI,EAAE;YAC7CT,OAAO,EAAEA,OAAO;YAChBC,iBAAiB,EAAEsC,YAAY;YAC/B5B,YAAY,EAAEA,YAAY;YAC1BC,aAAa,EAAEA,aAAa;YAC5BC,UAAU,EAAEA,UAAU;YACtBC,kBAAkB,EAAEf,QAAQ,CAACe,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEX,KAAK,GAAG,CAAC;YACfrB,IAAI,EAAEF,WAAW,CAACmB,OAAO,CAAC;YAC1BgB,UAAU,EAAE,IAAI;YAChB;YACAuC,cAAc,EAAExD,QAAQ,CAACiD,WAAW,IAAI,CAAC;YACzCQ,UAAU,EAAElB,WAAW,CAACE,MAAM,GAAG,CAAC;YAClCjB,UAAU,EAAEe,WAAW,CAACE,MAAM,GAAG,CAAC,GAAG,SAAS,GAAGzC,QAAQ,CAACiD,WAAW,GAAG,eAAe,GAAG;UAC5F,CAAC;QACH,CAAC,CAAC;;QAEJ;QACA9C,eAAe,CAACuD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC3D,OAAO,GAAG0D,CAAC,CAAC1D,OAAO,CAAC;;QAErD;QACAE,eAAe,CAAC+B,OAAO,CAAC,CAAC9F,IAAI,EAAEiE,KAAK,KAAK;UACvCjE,IAAI,CAAC4E,IAAI,GAAGX,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEF9D,cAAc,CAAC4D,eAAe,CAAC;;QAE/B;QACA,IAAI0D,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAIzH,IAAI,EAAE;UACR;UACAyH,QAAQ,GAAG1D,eAAe,CAACuB,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACrB,GAAG,KAAKlE,IAAI,CAACkE,GAAG,CAAC;;UAEnE;UACA,IAAIuD,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnBA,QAAQ,GAAG1D,eAAe,CAACuB,SAAS,CAACC,IAAI,IAAImC,MAAM,CAACnC,IAAI,CAACrB,GAAG,CAAC,KAAKwD,MAAM,CAAC1H,IAAI,CAACkE,GAAG,CAAC,CAAC;UACrF;;UAEA;UACA,IAAIuD,QAAQ,KAAK,CAAC,CAAC,IAAIzH,IAAI,CAACmE,IAAI,EAAE;YAChCsD,QAAQ,GAAG1D,eAAe,CAACuB,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACpB,IAAI,KAAKnE,IAAI,CAACmE,IAAI,CAAC;UACvE;QACF;QAEA5D,kBAAkB,CAACkH,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;;QAEvD;QACAxE,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7CyE,WAAW,EAAE3H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,IAAI;UACvB6B,MAAM,EAAEhG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG;UACjB0D,UAAU,EAAE,QAAO5H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG;UAC5B2D,OAAO,EAAE,CAAA7H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,IAAI,MAAK,OAAO,KAAIlG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6H,OAAO;UAChDC,MAAM,EAAE9H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,OAAO;UACrBwB,aAAa,EAAEoC,QAAQ;UACvBM,gBAAgB,EAAEN,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI;UACrDO,gBAAgB,EAAEjE,eAAe,CAACsC,MAAM;UACxC4B,eAAe,EAAElE,eAAe,CAACmE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClE,GAAG,CAACmE,CAAC,KAAK;YAAEC,EAAE,EAAED,CAAC,CAACjE,GAAG;YAAEmE,IAAI,EAAE,OAAOF,CAAC,CAACjE,GAAG;YAAEC,IAAI,EAAEgE,CAAC,CAAChE;UAAK,CAAC,CAAC,CAAC;UACxGmE,UAAU,EAAEvE,eAAe,CAACwE,IAAI,CAAChD,IAAI,IAAIA,IAAI,CAACrB,GAAG,MAAKlE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,EAAC;UAChEsE,WAAW,EAAEzE,eAAe,CAACwE,IAAI,CAAChD,IAAI,IAAImC,MAAM,CAACnC,IAAI,CAACrB,GAAG,CAAC,KAAKwD,MAAM,CAAC1H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,CAAC,CAAC;UACjFuE,SAAS,EAAE1E,eAAe,CAACwE,IAAI,CAAChD,IAAI,IAAIA,IAAI,CAACpB,IAAI,MAAKnE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,IAAI;QAClE,CAAC,CAAC;;QAEF;QACA,MAAMuE,WAAW,GAAG;UAClBzC,OAAO,EAAElC,eAAe,CAACJ,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC/C,UAAU,KAAK,SAAS,CAAC,CAACiB,MAAM;UACvEsC,aAAa,EAAE5E,eAAe,CAACJ,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC/C,UAAU,KAAK,eAAe,CAAC,CAACiB,MAAM;UACnFuC,SAAS,EAAE7E,eAAe,CAACJ,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC/C,UAAU,KAAK,WAAW,CAAC,CAACiB;QACvE,CAAC;QAEDpD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEa,eAAe,CAACsC,MAAM,EAAE,gBAAgB,CAAC;QACxFpD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEwF,WAAW,CAAC;QAC5CzF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEa,eAAe,CAACmE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClE,GAAG,CAACmE,CAAC,KAAK;UACvEhE,IAAI,EAAEgE,CAAC,CAAChE,IAAI;UACZxB,EAAE,EAAEwF,CAAC,CAACtE,OAAO;UACbgF,OAAO,EAAEV,CAAC,CAACrE,iBAAiB;UAC5BgF,GAAG,EAAEX,CAAC,CAAC3D,YAAY;UACnBuE,MAAM,EAAEZ,CAAC,CAAC/C;QACZ,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,MAAM;QACLnC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxC/C,cAAc,CAAC,EAAE,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;QACxBvC,OAAO,CAACgL,OAAO,CAAC,0DAA0D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOrD,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD3H,OAAO,CAAC2H,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACRtF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA3C,SAAS,CAAC,MAAM;IACdsF,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMiG,WAAW,GAAG3H,kBAAkB,CAACqF,IAAI,CAACK,KAAK,CAACL,IAAI,CAACuC,MAAM,CAAC,CAAC,GAAG5H,kBAAkB,CAAC+E,MAAM,CAAC,CAAC;IAC7FtF,oBAAoB,CAACkI,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvCvI,iBAAiB,CAACwI,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;;IAER;IACA;IACA;IACA;IACA;;IAEA;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9BrG,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7DF,gBAAgB,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAMuG,mBAAmB,GAAIC,KAAK,IAAK;MACrCvG,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEsG,KAAK,CAACC,MAAM,CAAC;MACnE;MACAC,UAAU,CAAC,MAAM;QACf1G,gBAAgB,CAAC,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED2G,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEN,iBAAiB,CAAC;IACnDK,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEL,mBAAmB,CAAC;IAE7D,OAAO,MAAM;MACXM,aAAa,CAACV,cAAc,CAAC;MAC7B;MACAQ,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAER,iBAAiB,CAAC;MACtDK,MAAM,CAACG,mBAAmB,CAAC,eAAe,EAAEP,mBAAmB,CAAC;IAClE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMQ,aAAa,GAAG7J,WAAW,CAACgI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAM8B,eAAe,GAAG9J,WAAW,CAACgI,KAAK,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAM+B,YAAY,GAAGA,CAAA,KAAM;IACzBhH,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;;IAEzC;IACA,MAAMuE,QAAQ,GAAG,EAAE;;IAEnB;IACAzJ,OAAO,CAACwF,OAAO,CAAE,uCAAsCiE,QAAS,MAAK,CAAC;;IAEtE;IACA,MAAMyC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,uBAAuB,CAAC;IACnE,IAAIF,WAAW,EAAE;MACfjH,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;;MAE1D;MACAgH,WAAW,CAACG,cAAc,CAAC;QACzBC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACAb,UAAU,CAAC,MAAM;QACf;QACA,MAAMc,iBAAiB,GAAG,CACxB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,CACpB;QAED,IAAIC,KAAK,GAAG,KAAK;QACjB,KAAK,IAAIC,QAAQ,IAAIF,iBAAiB,EAAE;UACtC,MAAMG,KAAK,GAAGT,WAAW,CAACU,gBAAgB,CAACF,QAAQ,CAAC;UACpDzH,OAAO,CAACC,GAAG,CAAE,SAAQyH,KAAK,CAACtE,MAAO,yBAAwBqE,QAAS,EAAC,CAAC;UAErE,IAAIC,KAAK,CAACtE,MAAM,IAAI,EAAE,EAAE;YAAE;YACxB,MAAMwE,WAAW,GAAGlE,IAAI,CAAClF,GAAG,CAAC,EAAE,EAAEkJ,KAAK,CAACtE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YACpD,MAAMyE,UAAU,GAAGH,KAAK,CAACE,WAAW,CAAC;YAErC,IAAIC,UAAU,EAAE;cACd7H,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;cAEjE4H,UAAU,CAACT,cAAc,CAAC;gBACxBC,QAAQ,EAAE,QAAQ;gBAClBC,KAAK,EAAE;cACT,CAAC,CAAC;;cAEF;cACA,MAAMQ,aAAa,GAAG;gBACpBC,MAAM,EAAEF,UAAU,CAACG,KAAK,CAACD,MAAM;gBAC/BE,SAAS,EAAEJ,UAAU,CAACG,KAAK,CAACC,SAAS;gBACrCC,SAAS,EAAEL,UAAU,CAACG,KAAK,CAACE,SAAS;gBACrCC,eAAe,EAAEN,UAAU,CAACG,KAAK,CAACG,eAAe;gBACjDC,OAAO,EAAEP,UAAU,CAACG,KAAK,CAACI;cAC5B,CAAC;;cAED;cACAP,UAAU,CAACG,KAAK,CAACD,MAAM,GAAG,8BAA8B;cACxDF,UAAU,CAACG,KAAK,CAACC,SAAS,GAAG,sEAAsE;cACnGJ,UAAU,CAACG,KAAK,CAACE,SAAS,GAAG,aAAa;cAC1CL,UAAU,CAACG,KAAK,CAACG,eAAe,GAAG,wBAAwB;cAC3DN,UAAU,CAACG,KAAK,CAACI,OAAO,GAAG,mBAAmB;cAC9CP,UAAU,CAACG,KAAK,CAACK,UAAU,GAAG,eAAe;cAC7CR,UAAU,CAACG,KAAK,CAACM,MAAM,GAAG,MAAM;;cAEhC;cACA,IAAIC,UAAU,GAAG,CAAC;cAClB,MAAMC,aAAa,GAAGrC,WAAW,CAAC,MAAM;gBACtC,IAAIoC,UAAU,GAAG,CAAC,EAAE;kBAAE;kBACpBV,UAAU,CAACG,KAAK,CAACE,SAAS,GAAGK,UAAU,GAAG,CAAC,KAAK,CAAC,GAAG,aAAa,GAAG,aAAa;kBACjFV,UAAU,CAACG,KAAK,CAACC,SAAS,GAAGM,UAAU,GAAG,CAAC,KAAK,CAAC,GAC/C,sEAAsE,GACtE,sEAAsE;kBACxEA,UAAU,EAAE;gBACd,CAAC,MAAM;kBACL3B,aAAa,CAAC4B,aAAa,CAAC;gBAC9B;cACF,CAAC,EAAE,GAAG,CAAC;;cAEP;cACA/B,UAAU,CAAC,MAAM;gBACfoB,UAAU,CAACG,KAAK,CAACD,MAAM,GAAGD,aAAa,CAACC,MAAM;gBAC9CF,UAAU,CAACG,KAAK,CAACC,SAAS,GAAGH,aAAa,CAACG,SAAS;gBACpDJ,UAAU,CAACG,KAAK,CAACE,SAAS,GAAGJ,aAAa,CAACI,SAAS;gBACpDL,UAAU,CAACG,KAAK,CAACG,eAAe,GAAGL,aAAa,CAACK,eAAe;gBAChEN,UAAU,CAACG,KAAK,CAACI,OAAO,GAAGN,aAAa,CAACM,OAAO;gBAChDP,UAAU,CAACG,KAAK,CAACM,MAAM,GAAG,EAAE;cAC9B,CAAC,EAAE,IAAI,CAAC;cAERd,KAAK,GAAG,IAAI;cACZ;YACF;UACF;QACF;QAEA,IAAI,CAACA,KAAK,EAAE;UACVxH,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;UACvElF,OAAO,CAAC0N,IAAI,CAAC,2EAA2E,CAAC;QAC3F;MACF,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,MAAM;MACLzI,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD;MACA,MAAMyI,iBAAiB,GAAGhC,MAAM,CAACiC,WAAW,GAAG,CAAC,CAAC,CAAC;MAClDjC,MAAM,CAACkC,QAAQ,CAAC;QACdC,GAAG,EAAEH,iBAAiB;QACtBrB,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFtM,OAAO,CAAC0N,IAAI,CAAC,mDAAmD,CAAC;IACnE;EACF,CAAC;;EAED;EACA,MAAMK,oBAAoB,GAAGA,CAACpH,kBAAkB,EAAEqH,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,GAAG,CAAC,KAAK;IAC1H,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,OAAO,GAAGN,mBAAmB,GAAG,IAAIK,IAAI,CAACL,mBAAmB,CAAC,GAAG,IAAI;IAE1E/I,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCyB,kBAAkB;MAClBqH,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfI,OAAO;MACPF,GAAG;MACHG,QAAQ,EAAED,OAAO,IAAIA,OAAO,GAAGF,GAAG;MAClCD;IACF,CAAC,CAAC;;IAEF;IACA,IAAIxH,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,KAAK,SAAS,EAAE;MACvE;MACA,IAAI,CAAC2H,OAAO,IAAIA,OAAO,GAAGF,GAAG,EAAE;QAC7B;QACA,OAAO;UACLI,IAAI,EAAE,WAAW;UACjB9K,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,yBAAyB;UAClCQ,WAAW,EAAE;QACf,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACLqK,IAAI,EAAE,SAAS;UACf9K,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,wBAAwB;UACjCQ,WAAW,EAAE;QACf,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA,OAAO;QACLqK,IAAI,EAAE,SAAS;QACf9K,KAAK,EAAE,SAAS;QAAE;QAClBC,OAAO,EAAE,wBAAwB;QACjCQ,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,IAAI/B,OAAO,IAAIF,WAAW,CAACmG,MAAM,KAAK,CAAC,EAAE;IACvC,oBACE7G,OAAA;MAAKiN,SAAS,EAAC,4GAA4G;MAAAC,QAAA,eACzHlN,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvBlN,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBzB,UAAU,EAAE;YAAE0B,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DV,SAAS,EAAC;QAAqF;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACF/N,OAAA;UAAGiN,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAgC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACE/N,OAAA,CAAAE,SAAA;IAAAgN,QAAA,gBACElN,OAAA;MAAOgO,GAAG;MAAAd,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACV/N,OAAA;MAAKiN,SAAS,EAAC,+GAA+G;MAAAC,QAAA,gBAE9HlN,OAAA;QAAKiN,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/ClN,OAAA;UAAKiN,SAAS,EAAC;QAAyH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/I/N,OAAA;UAAKiN,SAAS,EAAC;QAAgJ;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtK/N,OAAA;UAAKiN,SAAS,EAAC;QAA6I;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnK/N,OAAA;UAAKiN,SAAS,EAAC;QAA8I;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjK,CAAC,eAGN/N,OAAA;QAAKiN,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,GAAGe,KAAK,CAAC,EAAE,CAAC,CAAC,CAACzJ,GAAG,CAAC,CAAC0J,CAAC,EAAEC,CAAC,kBACvBnO,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;UAETF,SAAS,EAAC,mDAAmD;UAC7DK,OAAO,EAAE;YACPc,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACfC,CAAC,EAAE,CAAC,CAAC,EAAElH,IAAI,CAACuC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;YACnC2D,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UACzB,CAAE;UACFvB,UAAU,EAAE;YACV0B,QAAQ,EAAE,CAAC,GAAGrG,IAAI,CAACuC,MAAM,CAAC,CAAC,GAAG,CAAC;YAC/B+D,MAAM,EAAEC,QAAQ;YAChBY,KAAK,EAAEnH,IAAI,CAACuC,MAAM,CAAC,CAAC,GAAG;UACzB,CAAE;UACF+B,KAAK,EAAE;YACL8C,IAAI,EAAG,GAAEpH,IAAI,CAACuC,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC/B4C,GAAG,EAAG,GAAEnF,IAAI,CAACuC,MAAM,CAAC,CAAC,GAAG,GAAI;UAC9B;QAAE,GAfGyE,CAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN/N,OAAA;QAAKiN,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5BlN,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCd,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAE,CAAE;UAC9BtC,UAAU,EAAE;YAAE0B,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,2CAA2C;UAAAC,QAAA,eAErDlN,OAAA;YAAKiN,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChClN,OAAA;cAAKiN,SAAS,EAAC,yFAAyF;cAAAC,QAAA,eACtGlN,OAAA;gBAAKiN,SAAS,EAAC,+EAA+E;gBAAAC,QAAA,gBAG5FlN,OAAA,CAAC5B,MAAM,CAACoQ,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAMnO,QAAQ,CAAC,WAAW,CAAE;kBACrCwM,SAAS,EAAC,gNAAgN;kBAC1NxB,KAAK,EAAE;oBACLoD,QAAQ,EAAE1E,MAAM,CAAC2E,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA5B,QAAA,gBAEFlN,OAAA,CAACjB,MAAM;oBAACkO,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5C/N,OAAA;oBAAAkN,QAAA,EAAM;kBAAG;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGhB/N,OAAA,CAAC5B,MAAM,CAACoQ,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEnE,YAAa;kBACtBwC,SAAS,EAAC,kNAAkN;kBAC5NxB,KAAK,EAAE;oBACLsD,UAAU,EAAE,0CAA0C;oBACtD7M,KAAK,EAAE,SAAS;oBAChB8M,UAAU,EAAE,MAAM;oBAClBC,UAAU,EAAE,KAAK;oBACjBJ,QAAQ,EAAE1E,MAAM,CAAC2E,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA5B,QAAA,gBAEFlN,OAAA,CAACnB,QAAQ;oBAACoO,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9C/N,OAAA;oBAAAkN,QAAA,EACGpM,eAAe,GAAI,YAAWA,eAAgB,EAAC,GAC9C,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,IAAI,MAAK,OAAO,IAAIlG,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6H,OAAO,GAAI,YAAY,GAAG;kBAAS;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGhB/N,OAAA,CAAC5B,MAAM,CAACoQ,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM;oBACbnL,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;oBACtCD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAElD,IAAI,CAAC;oBACzCiD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,CAAC;oBAClCjB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,IAAI,CAAC;oBACrClB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6H,OAAO,CAAC;oBAC3C5E,OAAO,CAACC,GAAG,CAAC,YAAY,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,IAAI,CAAC;oBACrCjD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEhD,WAAW,CAACmG,MAAM,CAAC;oBACvDpD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEhD,WAAW,CAACgI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClE,GAAG,CAACmE,CAAC,KAAK;sBACzEC,EAAE,EAAED,CAAC,CAACjE,GAAG;sBACTC,IAAI,EAAEgE,CAAC,CAAChE,IAAI;sBACZS,IAAI,EAAEuD,CAAC,CAACvD,IAAI;sBACZf,OAAO,EAAEsE,CAAC,CAACtE;oBACb,CAAC,CAAC,CAAC,CAAC;oBACJZ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEhD,WAAW,CAAC8D,GAAG,CAACmE,CAAC,IAAIA,CAAC,CAACjE,GAAG,CAAC,CAAC;oBACpEjB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,CAAC;oBAC9CjB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhD,WAAW,CAACqI,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAACjE,GAAG,MAAKlE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAG,EAAC,CAAC;oBACjFjB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE9B,aAAa,CAACsN,OAAO,CAAC;oBAC5DzL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE7B,WAAW,CAACqN,OAAO,CAAC;oBACxDzL,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE5C,eAAe,CAAC;;oBAEhD;oBACA,MAAMqO,YAAY,GAAGxE,QAAQ,CAACS,gBAAgB,CAAE,kBAAiB5K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,GAAI,IAAG,CAAC;oBAC/EjB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEyL,YAAY,CAACtI,MAAM,CAAC;oBACxDsI,YAAY,CAAC7I,OAAO,CAAC,CAAC8I,EAAE,EAAEjB,CAAC,KAAK;sBAC9B1K,OAAO,CAACC,GAAG,CAAE,WAAUyK,CAAE,GAAE,EAAEiB,EAAE,EAAE,OAAO,EAAEA,EAAE,CAACC,YAAY,CAAC,gBAAgB,CAAC,CAAC;oBAC9E,CAAC,CAAC;kBACJ,CAAE;kBACFpC,SAAS,EAAC,uDAAuD;kBAAAC,QAAA,EAClE;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAGhB/N,OAAA,CAAC5B,MAAM,CAACoQ,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEnB,MAAM,EAAE;kBAAI,CAAE;kBACzCoB,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEpL,gBAAiB;kBAC1B8L,QAAQ,EAAE1O,OAAQ;kBAClBqM,SAAS,EAAC,qNAAqN;kBAC/NxB,KAAK,EAAE;oBACLoD,QAAQ,EAAE1E,MAAM,CAAC2E,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA5B,QAAA,gBAEFlN,OAAA,CAAChB,SAAS;oBAACiO,SAAS,EAAG,yBAAwBrM,OAAO,GAAG,cAAc,GAAG,EAAG;kBAAE;oBAAAgN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClF/N,OAAA;oBAAAkN,QAAA,EAAM;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZ,CAAC,CAAAvN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,IAAI,MAAK,OAAO,KAAIlG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6H,OAAO,mBACvCrI,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCd,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAE,CAAE;UAC9BtC,UAAU,EAAE;YAAE0B,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7ClN,OAAA;YAAKiN,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChClN,OAAA;cAAKiN,SAAS,EAAC,gHAAgH;cAAAC,QAAA,eAC7HlN,OAAA;gBAAKiN,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtClN,OAAA;kBAAKiN,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClFlN,OAAA;oBAAMiN,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACN/N,OAAA;kBAAAkN,QAAA,gBACElN,OAAA;oBAAIiN,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpD/N,OAAA;oBAAGiN,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,eAGD/N,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCd,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAE,CAAE;UAC9BtC,UAAU,EAAE;YAAE0B,QAAQ,EAAE,CAAC;YAAEG,IAAI,EAAE;UAAU,CAAE;UAC7CV,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAGzClN,OAAA;YAAKiN,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAC9GlN,OAAA;cAAKiN,SAAS,EAAC;YAA6E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnG/N,OAAA;cAAKiN,SAAS,EAAC;YAA+E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGrG/N,OAAA;cAAKiN,SAAS,EAAC,2EAA2E;cAAAC,QAAA,eACxFlN,OAAA;gBAAKiN,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAG5ClN,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;kBACTG,OAAO,EAAE;oBACPoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBa,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFzD,UAAU,EAAE;oBACV0B,QAAQ,EAAE,CAAC;oBACXC,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR,CAAE;kBACFV,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBlN,OAAA;oBAAIiN,SAAS,EAAC,iGAAiG;oBAAAC,QAAA,gBAC7GlN,OAAA,CAAC5B,MAAM,CAACoR,IAAI;sBACVlC,OAAO,EAAE;wBACPmC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;sBACrD,CAAE;sBACF3D,UAAU,EAAE;wBACV0B,QAAQ,EAAE,CAAC;wBACXC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFV,SAAS,EAAC,+HAA+H;sBACzIxB,KAAK,EAAE;wBACLiE,cAAc,EAAE,WAAW;wBAC3BC,oBAAoB,EAAE,MAAM;wBAC5BC,mBAAmB,EAAE,aAAa;wBAClCzL,MAAM,EAAE;sBACV,CAAE;sBAAA+I,QAAA,EACH;oBAED;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACd/N,OAAA;sBAAA4N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN/N,OAAA,CAAC5B,MAAM,CAACoR,IAAI;sBACVlC,OAAO,EAAE;wBACP0B,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;sBAEhE,CAAE;sBACFlD,UAAU,EAAE;wBACV0B,QAAQ,EAAE,GAAG;wBACbC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFlC,KAAK,EAAE;wBACLvJ,KAAK,EAAE,SAAS;wBAChB+M,UAAU,EAAE,KAAK;wBACjBD,UAAU,EAAE;sBACd,CAAE;sBAAA9B,QAAA,EACH;oBAED;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGb/N,OAAA,CAAC5B,MAAM,CAACyR,CAAC;kBACPzC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAG,CAAE;kBAC/Bd,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAE,CAAE;kBAC9BtC,UAAU,EAAE;oBAAEwC,KAAK,EAAE,GAAG;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBAC1CP,SAAS,EAAC,8GAA8G;kBACxHxB,KAAK,EAAE;oBACLvJ,KAAK,EAAE,SAAS;oBAChB8M,UAAU,EAAE,6BAA6B;oBACzCD,UAAU,EAAE,0CAA0C;oBACtDY,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE;kBACvB,CAAE;kBAAA1C,QAAA,EACH;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAGX/N,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEqB,KAAK,EAAE;kBAAI,CAAE;kBACpCpB,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEqB,KAAK,EAAE;kBAAE,CAAE;kBAClC5C,UAAU,EAAE;oBAAEwC,KAAK,EAAE,GAAG;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBAC1CP,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBlN,OAAA;oBAAGiN,SAAS,EAAC,6JAA6J;oBACvKxB,KAAK,EAAE;sBACLuD,UAAU,EAAE,6BAA6B;sBACzCc,SAAS,EAAE;oBACb,CAAE;oBAAA5C,QAAA,EACF5L;kBAAiB;oBAAAsM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGb/N,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAG,CAAE;kBAC/Bd,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAE,CAAE;kBAC9BtC,UAAU,EAAE;oBAAEwC,KAAK,EAAE,CAAC;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBACxCP,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,EAEpF,CACC;oBACE1K,IAAI,EAAEjD,OAAO;oBACbwQ,KAAK,EAAErP,WAAW,CAACmG,MAAM;oBACzBmJ,KAAK,EAAE,WAAW;oBAClBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpBvN,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE/D,QAAQ;oBACdsR,KAAK,EAAExF,aAAa,CAAC1D,MAAM;oBAC3BmJ,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,oDAAoD;oBAChEC,SAAS,EAAE,SAAS;oBACpBvN,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE5D,OAAO;oBACbmR,KAAK,EAAErP,WAAW,CAACyD,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC1D,aAAa,GAAG,CAAC,CAAC,CAAC4B,MAAM;oBAC1DmJ,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,gDAAgD;oBAC5DC,SAAS,EAAE,SAAS;oBACpBvN,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE7D,MAAM;oBACZoR,KAAK,EAAErP,WAAW,CAACqG,MAAM,CAAC,CAACC,GAAG,EAAE2B,CAAC,KAAK3B,GAAG,IAAI2B,CAAC,CAACtE,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC8L,cAAc,CAAC,CAAC;oBACjFH,KAAK,EAAE,UAAU;oBACjBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpBvN,WAAW,EAAE;kBACf,CAAC,CACF,CAAC6B,GAAG,CAAC,CAAC4L,IAAI,EAAE3L,KAAK,kBAChBzE,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;oBAETC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEqB,KAAK,EAAE;oBAAI,CAAE;oBACpCpB,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEqB,KAAK,EAAE;oBAAE,CAAE;oBAClC5C,UAAU,EAAE;sBAAEwC,KAAK,EAAE,GAAG,GAAG7J,KAAK,GAAG,GAAG;sBAAE+I,QAAQ,EAAE;oBAAI,CAAE;oBACxDiB,UAAU,EAAE;sBAAEC,KAAK,EAAE,IAAI;sBAAEN,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCnB,SAAS,EAAG,qBAAoBmD,IAAI,CAACH,UAAW,8EAA8E;oBAC9HxE,KAAK,EAAE;sBACLD,MAAM,EAAG,aAAY4E,IAAI,CAACzN,WAAY,IAAG;sBACzC+I,SAAS,EAAG,cAAa0E,IAAI,CAACzN,WAAY;oBAC5C,CAAE;oBAAAuK,QAAA,gBAEFlN,OAAA;sBAAKiN,SAAS,EAAC;oBAAgE;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtF/N,OAAA,CAACoQ,IAAI,CAAC5N,IAAI;sBACRyK,SAAS,EAAC,kDAAkD;sBAC5DxB,KAAK,EAAE;wBAAEvJ,KAAK,EAAEkO,IAAI,CAACF,SAAS;wBAAE/L,MAAM,EAAE;sBAAyC;oBAAE;sBAAAyJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC,eACF/N,OAAA;sBACEiN,SAAS,EAAC,0EAA0E;sBACpFxB,KAAK,EAAE;wBACLvJ,KAAK,EAAEkO,IAAI,CAACF,SAAS;wBACrBlB,UAAU,EAAG,6BAA4B;wBACzC7K,MAAM,EAAE,oCAAoC;wBAC5C0K,QAAQ,EAAE;sBACZ,CAAE;sBAAA3B,QAAA,EAEDkD,IAAI,CAACL;oBAAK;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACN/N,OAAA;sBACEiN,SAAS,EAAC,4CAA4C;sBACtDxB,KAAK,EAAE;wBACLvJ,KAAK,EAAE,SAAS;wBAChB8M,UAAU,EAAE,6BAA6B;wBACzCH,QAAQ,EAAE;sBACZ,CAAE;sBAAA3B,QAAA,EAEDkD,IAAI,CAACJ;oBAAK;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA,GApCDtJ,KAAK;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqCA,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZnN,OAAO,iBACNZ,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBJ,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAE3DlN,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;YACTG,OAAO,EAAE;cAAEC,MAAM,EAAE;YAAI,CAAE;YACzBzB,UAAU,EAAE;cAAE0B,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAEC,IAAI,EAAE;YAAS,CAAE;YAC9DV,SAAS,EAAC;UAA6E;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eACF/N,OAAA;YAAGiN,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CACb,EAGA,CAACnN,OAAO,iBACPZ,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAG,CAAE;UAC/Bd,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAE,CAAE;UAC9BtC,UAAU,EAAE;YAAEwC,KAAK,EAAE,GAAG;YAAEd,QAAQ,EAAE;UAAI,CAAE;UAC1CP,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eAEtClN,OAAA;YAAKiN,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAG/B3C,aAAa,CAAC1D,MAAM,GAAG,CAAC,iBACvB7G,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAI,CAAE;cACpCpB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAE,CAAE;cAClC5C,UAAU,EAAE;gBAAEwC,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,OAAO;cAAAC,QAAA,gBAEjBlN,OAAA;gBAAIiN,SAAS,EAAC,2EAA2E;gBAACxB,KAAK,EAAE;kBAC/FsD,UAAU,EAAE,mDAAmD;kBAC/DY,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCZ,UAAU,EAAE,6BAA6B;kBACzC7K,MAAM,EAAE;gBACV,CAAE;gBAAA+I,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGL/N,OAAA;gBAAKiN,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,GAE/D3C,aAAa,CAAC,CAAC,CAAC,iBACfvK,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;kBAETkD,GAAG,EAAE7P,IAAI,IAAI+J,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG,KAAKlE,IAAI,CAACkE,GAAG,GAAG9C,aAAa,GAAG,IAAK;kBACtE,gBAAc2I,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAI;kBACnC,kBAAgB,CAAE;kBAClB0I,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCd,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAE,CAAE;kBAC9BtC,UAAU,EAAE;oBAAEwC,KAAK,EAAE,GAAG;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBAC1CiB,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEN,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCnB,SAAS,EAAG,iBAAgBzM,IAAI,IAAI+J,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG,KAAKlE,IAAI,CAACkE,GAAG,GAAG,wBAAwB,GAAG,EAAG,IAAGlD,UAAU,IAAIhB,IAAI,IAAI+J,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG,KAAKlE,IAAI,CAACkE,GAAG,GAAG,mBAAmB,GAAG,EAAG,EAAE;kBAAAwI,QAAA,eAG9LlN,OAAA;oBACEiN,SAAS,EAAG,8BAA6B1C,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAClB,KAAM,sBAAqBqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACb,IAAK,IAAGgI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACR,MAAO,iCAAiC;oBACtL6I,KAAK,EAAE;sBACLC,SAAS,EAAG,cAAanB,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACd,WAAY;oBAC7D,CAAE;oBAAA4K,QAAA,eAEFlN,OAAA;sBACEiN,SAAS,EAAG,GAAE1C,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACjB,OAAQ,uEAAuE;sBACnHsJ,KAAK,EAAE;wBACLsD,UAAU,EAAG,GAAExE,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACjB,OAAQ;sBAC/C,CAAE;sBAAA+K,QAAA,gBAEFlN,OAAA;wBAAKiN,SAAS,EAAC;sBAA4E;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGlG/N,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;wBACTG,OAAO,EAAE;0BAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;0BAAEa,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;wBAAE,CAAE;wBACpDtC,UAAU,EAAE;0BAAE0B,QAAQ,EAAE,CAAC;0BAAEC,MAAM,EAAEC;wBAAS,CAAE;wBAC9CT,SAAS,EAAC,sDAAsD;wBAAAC,QAAA,eAEhElN,OAAA,CAACtB,OAAO;0BAACuO,SAAS,EAAC;wBAA0C;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,eAGb/N,OAAA;wBACEiN,SAAS,EAAG,mFAAkF1C,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAClB,KAAM,2FAA2F;wBACrNuJ,KAAK,EAAE;0BACLsD,UAAU,EAAG,2CAA0C;0BACvD7M,KAAK,EAAE,SAAS;0BAChB8M,UAAU,EAAE,6BAA6B;0BACzCxD,MAAM,EAAE;wBACV,CAAE;wBAAA0B,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGN/N,OAAA;wBAAKiN,SAAS,EAAG,yBAAwBzM,IAAI,IAAI+J,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG,KAAKlE,IAAI,CAACkE,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAwI,QAAA,gBACnIlN,OAAA;0BACEiN,SAAS,EAAC,kHAAkH;0BAC5HxB,KAAK,EAAE;4BACLsD,UAAU,EAAE,SAAS;4BACrBrD,SAAS,EAAE;0BACb,CAAE;0BAAAwB,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAACzF,cAAc,gBAC9B9E,OAAA;4BACEsQ,GAAG,EAAE/F,aAAa,CAAC,CAAC,CAAC,CAACzF,cAAe;4BACrCyL,GAAG,EAAEhG,aAAa,CAAC,CAAC,CAAC,CAAC5F,IAAK;4BAC3BsI,SAAS,EAAC,yCAAyC;4BACnDxB,KAAK,EAAE;8BACL+E,SAAS,EAAE,OAAO;8BAClBC,cAAc,EAAE;4BAClB;0BAAE;4BAAA7C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,gBAEF/N,OAAA;4BACEiN,SAAS,EAAC,+EAA+E;4BACzFxB,KAAK,EAAE;8BACLsD,UAAU,EAAE,SAAS;8BACrB7M,KAAK,EAAE,SAAS;8BAChB2M,QAAQ,EAAE;4BACZ,CAAE;4BAAA3B,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAAC5F,IAAI,CAAC+L,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAA/C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,EACLvN,IAAI,IAAI+J,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG,KAAKlE,IAAI,CAACkE,GAAG,iBACxC1E,OAAA;0BACEiN,SAAS,EAAC,4DAA4D;0BACtExB,KAAK,EAAE;4BACLsD,UAAU,EAAE,0CAA0C;4BACtDrD,SAAS,EAAE;0BACb,CAAE;0BAAAwB,QAAA,eAEFlN,OAAA,CAACrB,MAAM;4BAACsO,SAAS,EAAC;0BAAoB;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGN/N,OAAA;wBACEiN,SAAS,EAAC,+DAA+D;wBACzExB,KAAK,EAAE;0BACLvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACf,SAAS;0BACtC2M,UAAU,EAAG,eAAczE,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACd,WAAY,EAAC;0BAC9D6B,MAAM,EAAE;wBACV,CAAE;wBAAA+I,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAAC5F;sBAAI;wBAAAiJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAEL/N,OAAA;wBACEiN,SAAS,EAAG,6DAA4D1C,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAClB,KAAM,uDAAuD;wBAC3JuJ,KAAK,EAAE;0BACLsD,UAAU,EAAG,2BAA0BxE,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACT,WAAY,KAAI4H,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAChB,SAAU,GAAE;0BAC/GF,KAAK,EAAE,SAAS;0BAChB8M,UAAU,EAAE,6BAA6B;0BACzCtD,SAAS,EAAG,cAAanB,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACd,WAAY,IAAG;0BAC9DkJ,MAAM,EAAE;wBACV,CAAE;wBAAA0B,QAAA,GAED3C,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACZ,IAAI,iBAAIxE,KAAK,CAAC4S,aAAa,CAACrG,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACZ,IAAI,EAAE;0BAC7EyK,SAAS,EAAE,SAAS;0BACpBxB,KAAK,EAAE;4BAAEvJ,KAAK,EAAE;0BAAU;wBAC5B,CAAC,CAAC,eACFlC,OAAA;0BAAMyL,KAAK,EAAE;4BAAEvJ,KAAK,EAAE;0BAAU,CAAE;0BAAAgL,QAAA,EAAE3C,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACX;wBAAK;0BAAAmL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC,eAGN/N,OAAA;wBAAKiN,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtClN,OAAA;0BAAKiN,SAAS,EAAC,iCAAiC;0BAACxB,KAAK,EAAE;4BACtDvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACf,SAAS;4BACtC2M,UAAU,EAAG,eAAczE,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACd,WAAY,EAAC;4BAC9D6B,MAAM,EAAE;0BACV,CAAE;0BAAA+I,QAAA,GACC3C,aAAa,CAAC,CAAC,CAAC,CAAClG,OAAO,CAAC8L,cAAc,CAAC,CAAC,EAAC,KAC7C;wBAAA;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAEN/N,OAAA;0BAAKiN,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChDlN,OAAA;4BAAKiN,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BlN,OAAA;8BAAKiN,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDlN,OAAA,CAAClB,OAAO;gCAACmO,SAAS,EAAC,SAAS;gCAACxB,KAAK,EAAE;kCAAEvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAChB;gCAAU;8BAAE;gCAAAwL,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAClF/N,OAAA;gCAAMiN,SAAS,EAAC,WAAW;gCAACxB,KAAK,EAAE;kCAAEvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAChB;gCAAU,CAAE;gCAAA8K,QAAA,EAC3E3C,aAAa,CAAC,CAAC,CAAC,CAACjG;8BAAiB;gCAAAsJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACN/N,OAAA;8BAAKiN,SAAS,EAAC,oBAAoB;8BAACxB,KAAK,EAAE;gCAAEvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAChB;8BAAU,CAAE;8BAAA8K,QAAA,EAAC;4BAAO;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjG,CAAC,eACN/N,OAAA;4BAAKiN,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BlN,OAAA;8BAAKiN,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDlN,OAAA,CAACpB,OAAO;gCAACqO,SAAS,EAAC,SAAS;gCAACxB,KAAK,EAAE;kCAAEvJ,KAAK,EAAE;gCAAU;8BAAE;gCAAA0L,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAC5D/N,OAAA;gCAAMiN,SAAS,EAAC,WAAW;gCAACxB,KAAK,EAAE;kCAAEvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAChB;gCAAU,CAAE;gCAAA8K,QAAA,EAC3E3C,aAAa,CAAC,CAAC,CAAC,CAACtF;8BAAa;gCAAA2I,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC3B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACN/N,OAAA;8BAAKiN,SAAS,EAAC,oBAAoB;8BAACxB,KAAK,EAAE;gCAAEvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAChB;8BAAU,CAAE;8BAAA8K,QAAA,EAAC;4BAAM;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAzJDxD,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG;kBAAAkJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0Jf,CACb,EAGA,CAACxD,aAAa,CAAC,CAAC,CAAC,IAAIA,aAAa,CAAC,CAAC,CAAC,kBACpCvK,OAAA;kBAAKiN,SAAS,EAAC,uFAAuF;kBAAAC,QAAA,GAEnG3C,aAAa,CAAC,CAAC,CAAC,iBACfvK,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;oBAETkD,GAAG,EAAE7P,IAAI,IAAI+J,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG,KAAKlE,IAAI,CAACkE,GAAG,GAAG9C,aAAa,GAAG,IAAK;oBACtE,gBAAc2I,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAI;oBACnC,kBAAgB,CAAE;oBAClB0I,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAChCf,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE;oBAAE,CAAE;oBAC9BvC,UAAU,EAAE;sBAAEwC,KAAK,EAAE,GAAG;sBAAEd,QAAQ,EAAE;oBAAI,CAAE;oBAC1CiB,UAAU,EAAE;sBAAEC,KAAK,EAAE,IAAI;sBAAEN,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCnB,SAAS,EAAG,YAAWzM,IAAI,IAAI+J,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG,KAAKlE,IAAI,CAACkE,GAAG,GAAG,wBAAwB,GAAG,EAAG,IAAGlD,UAAU,IAAIhB,IAAI,IAAI+J,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG,KAAKlE,IAAI,CAACkE,GAAG,GAAG,mBAAmB,GAAG,EAAG,EAAE;oBAAAwI,QAAA,eAEzLlN,OAAA;sBACEiN,SAAS,EAAG,8BAA6B1C,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAClB,KAAM,mBAAkBqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACb,IAAK,YAAY;sBAC9HkJ,KAAK,EAAE;wBACLC,SAAS,EAAG,cAAanB,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACd,WAAY;sBAC7D,CAAE;sBAAA4K,QAAA,eAEFlN,OAAA;wBACEiN,SAAS,EAAG,GAAE1C,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACjB,OAAQ,uEAAuE;wBAAA+K,QAAA,gBAEnHlN,OAAA;0BAAKiN,SAAS,EAAC;wBAAgE;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAGtF/N,OAAA;0BACEiN,SAAS,EAAC,kMAAkM;0BAC5MxB,KAAK,EAAE;4BACLvJ,KAAK,EAAE,SAAS;4BAChBsJ,MAAM,EAAE;0BACV,CAAE;0BAAA0B,QAAA,EACH;wBAED;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAGN/N,OAAA;0BAAKiN,SAAS,EAAG,yBAAwBzM,IAAI,IAAI+J,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG,KAAKlE,IAAI,CAACkE,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;0BAAAwI,QAAA,eACnIlN,OAAA;4BACEiN,SAAS,EAAC,kGAAkG;4BAC5GxB,KAAK,EAAE;8BACLsD,UAAU,EAAE,SAAS;8BACrBrD,SAAS,EAAE;4BACb,CAAE;4BAAAwB,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAACzF,cAAc,gBAC9B9E,OAAA;8BACEsQ,GAAG,EAAE/F,aAAa,CAAC,CAAC,CAAC,CAACzF,cAAe;8BACrCyL,GAAG,EAAEhG,aAAa,CAAC,CAAC,CAAC,CAAC5F,IAAK;8BAC3BsI,SAAS,EAAC;4BAAyC;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpD,CAAC,gBAEF/N,OAAA;8BACEiN,SAAS,EAAC,mFAAmF;8BAC7FxB,KAAK,EAAE;gCACLsD,UAAU,EAAE,SAAS;gCACrB7M,KAAK,EAAE;8BACT,CAAE;8BAAAgL,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAAC5F,IAAI,CAAC+L,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;4BAAC;8BAAA/C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC3C;0BACN;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAGN/N,OAAA;0BACEiN,SAAS,EAAC,8CAA8C;0BACxDxB,KAAK,EAAE;4BAAEvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACf;0BAAU,CAAE;0BAAA6K,QAAA,EAEjD3C,aAAa,CAAC,CAAC,CAAC,CAAC5F;wBAAI;0BAAAiJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,eAEL/N,OAAA;0BAAKiN,SAAS,EAAC,yBAAyB;0BAACxB,KAAK,EAAE;4BAAEvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAChB;0BAAU,CAAE;0BAAA8K,QAAA,GACxF3C,aAAa,CAAC,CAAC,CAAC,CAAClG,OAAO,CAAC8L,cAAc,CAAC,CAAC,EAAC,KAC7C;wBAAA;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAEN/N,OAAA;0BAAKiN,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChDlN,OAAA;4BAAMyL,KAAK,EAAE;8BAAEvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAChB;4BAAU,CAAE;4BAAA8K,QAAA,GAAC,eACpD,EAAC3C,aAAa,CAAC,CAAC,CAAC,CAACjG,iBAAiB;0BAAA;4BAAAsJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC,CAAC,eACP/N,OAAA;4BAAMyL,KAAK,EAAE;8BAAEvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAChB;4BAAU,CAAE;4BAAA8K,QAAA,GAAC,eACpD,EAAC3C,aAAa,CAAC,CAAC,CAAC,CAACtF,aAAa;0BAAA;4BAAA2I,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC9B,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAlFDxD,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG;oBAAAkJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmFf,CACb,EAGAxD,aAAa,CAAC,CAAC,CAAC,iBACfvK,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;oBAETkD,GAAG,EAAE7P,IAAI,IAAI+J,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG,KAAKlE,IAAI,CAACkE,GAAG,GAAG9C,aAAa,GAAG,IAAK;oBACtE,gBAAc2I,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAI;oBACnC,kBAAgB,CAAE;oBAClB0I,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE;oBAAG,CAAE;oBAC/Bf,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE;oBAAE,CAAE;oBAC9BvC,UAAU,EAAE;sBAAEwC,KAAK,EAAE,GAAG;sBAAEd,QAAQ,EAAE;oBAAI,CAAE;oBAC1CiB,UAAU,EAAE;sBAAEC,KAAK,EAAE,IAAI;sBAAEN,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCnB,SAAS,EAAG,YAAWzM,IAAI,IAAI+J,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG,KAAKlE,IAAI,CAACkE,GAAG,GAAG,wBAAwB,GAAG,EAAG,IAAGlD,UAAU,IAAIhB,IAAI,IAAI+J,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG,KAAKlE,IAAI,CAACkE,GAAG,GAAG,mBAAmB,GAAG,EAAG,EAAE;oBAAAwI,QAAA,eAEzLlN,OAAA;sBACEiN,SAAS,EAAG,8BAA6B1C,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAClB,KAAM,mBAAkBqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACb,IAAK,YAAY;sBAC9HkJ,KAAK,EAAE;wBACLC,SAAS,EAAG,cAAanB,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACd,WAAY;sBAC7D,CAAE;sBAAA4K,QAAA,eAEFlN,OAAA;wBACEiN,SAAS,EAAG,GAAE1C,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACjB,OAAQ,uEAAuE;wBAAA+K,QAAA,gBAEnHlN,OAAA;0BAAKiN,SAAS,EAAC;wBAAgE;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAGtF/N,OAAA;0BACEiN,SAAS,EAAC,oMAAoM;0BAC9MxB,KAAK,EAAE;4BACLvJ,KAAK,EAAE,SAAS;4BAChBsJ,MAAM,EAAE;0BACV,CAAE;0BAAA0B,QAAA,EACH;wBAED;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAGN/N,OAAA;0BAAKiN,SAAS,EAAG,yBAAwBzM,IAAI,IAAI+J,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG,KAAKlE,IAAI,CAACkE,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;0BAAAwI,QAAA,eACnIlN,OAAA;4BACEiN,SAAS,EAAC,kGAAkG;4BAC5GxB,KAAK,EAAE;8BACLsD,UAAU,EAAE,SAAS;8BACrBrD,SAAS,EAAE;4BACb,CAAE;4BAAAwB,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAACzF,cAAc,gBAC9B9E,OAAA;8BACEsQ,GAAG,EAAE/F,aAAa,CAAC,CAAC,CAAC,CAACzF,cAAe;8BACrCyL,GAAG,EAAEhG,aAAa,CAAC,CAAC,CAAC,CAAC5F,IAAK;8BAC3BsI,SAAS,EAAC;4BAAyC;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpD,CAAC,gBAEF/N,OAAA;8BACEiN,SAAS,EAAC,mFAAmF;8BAC7FxB,KAAK,EAAE;gCACLsD,UAAU,EAAE,SAAS;gCACrB7M,KAAK,EAAE;8BACT,CAAE;8BAAAgL,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAAC5F,IAAI,CAAC+L,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;4BAAC;8BAAA/C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC3C;0BACN;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAGN/N,OAAA;0BACEiN,SAAS,EAAC,8CAA8C;0BACxDxB,KAAK,EAAE;4BAAEvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAACf;0BAAU,CAAE;0BAAA6K,QAAA,EAEjD3C,aAAa,CAAC,CAAC,CAAC,CAAC5F;wBAAI;0BAAAiJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,eAEL/N,OAAA;0BAAKiN,SAAS,EAAC,yBAAyB;0BAACxB,KAAK,EAAE;4BAAEvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAChB;0BAAU,CAAE;0BAAA8K,QAAA,GACxF3C,aAAa,CAAC,CAAC,CAAC,CAAClG,OAAO,CAAC8L,cAAc,CAAC,CAAC,EAAC,KAC7C;wBAAA;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAEN/N,OAAA;0BAAKiN,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChDlN,OAAA;4BAAMyL,KAAK,EAAE;8BAAEvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAChB;4BAAU,CAAE;4BAAA8K,QAAA,GAAC,eACpD,EAAC3C,aAAa,CAAC,CAAC,CAAC,CAACjG,iBAAiB;0BAAA;4BAAAsJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC,CAAC,eACP/N,OAAA;4BAAMyL,KAAK,EAAE;8BAAEvJ,KAAK,EAAEqI,aAAa,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAChB;4BAAU,CAAE;4BAAA8K,QAAA,GAAC,eACpD,EAAC3C,aAAa,CAAC,CAAC,CAAC,CAACtF,aAAa;0BAAA;4BAAA2I,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC9B,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAlFDxD,aAAa,CAAC,CAAC,CAAC,CAAC7F,GAAG;oBAAAkJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmFf,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGAvD,eAAe,CAAC3D,MAAM,GAAG,CAAC,iBACzB7G,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEe,CAAC,EAAE;cAAG,CAAE;cAC/Bd,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEe,CAAC,EAAE;cAAE,CAAE;cAC9BtC,UAAU,EAAE;gBAAEwC,KAAK,EAAE,CAAC;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAGtClN,OAAA;gBAAKiN,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxClN,OAAA,CAAC5B,MAAM,CAACyS,EAAE;kBACR5D,SAAS,EAAC,kDAAkD;kBAC5DxB,KAAK,EAAE;oBACLsD,UAAU,EAAE,mDAAmD;oBAC/DY,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCZ,UAAU,EAAE,6BAA6B;oBACzC7K,MAAM,EAAE;kBACV,CAAE;kBACFmJ,OAAO,EAAE;oBAAEoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC5C,UAAU,EAAE;oBAAE0B,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,EAC/C;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZ/N,OAAA;kBAAGiN,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAE9D;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGN/N,OAAA;gBAAKiN,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrClN,OAAA;kBAAKiN,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EACnC1C,eAAe,CAAChG,GAAG,CAAC,CAACsM,QAAQ,EAAErM,KAAK,KAAK;oBACxC,MAAMsM,UAAU,GAAGtM,KAAK,GAAG,CAAC,CAAC,CAAC;oBAC9B,MAAMuM,aAAa,GAAGxQ,IAAI,IAAIsQ,QAAQ,CAACpM,GAAG,KAAKlE,IAAI,CAACkE,GAAG;oBAEvD,oBACE1E,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;sBAETkD,GAAG,EAAEW,aAAa,GAAGnP,WAAW,GAAG,IAAK;sBACxC,gBAAciP,QAAQ,CAACpM,GAAI;sBAC3B,kBAAgBqM,UAAW;sBAC3B3D,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEe,CAAC,EAAE;sBAAG,CAAE;sBAC/Bd,OAAO,EAAE;wBAAED,OAAO,EAAE,CAAC;wBAAEe,CAAC,EAAE;sBAAE,CAAE;sBAC9BtC,UAAU,EAAE;wBAAEwC,KAAK,EAAE,GAAG,GAAG7J,KAAK,GAAG,IAAI;wBAAE+I,QAAQ,EAAE;sBAAI,CAAE;sBACzDiB,UAAU,EAAE;wBAAEC,KAAK,EAAE,IAAI;wBAAEN,CAAC,EAAE,CAAC;sBAAE,CAAE;sBACnCnB,SAAS,EAAG,+BAA8B+D,aAAa,GAAG,2BAA2B,GAAG,EAAG,IAAGxP,UAAU,IAAIwP,aAAa,GAAG,mBAAmB,GAAG,EAAG,EAAE;sBAAA9D,QAAA,eAGvJlN,OAAA;wBACEiN,SAAS,EAAG,oBAAmB6D,QAAQ,CAAC1N,IAAI,CAAClB,KAAM,sBAAqB4O,QAAQ,CAAC1N,IAAI,CAACb,IAAK,uDAAuD;wBAClJkJ,KAAK,EAAE;0BACLC,SAAS,EAAG,cAAaoF,QAAQ,CAAC1N,IAAI,CAACd,WAAY;wBACrD,CAAE;wBAAA4K,QAAA,eAEFlN,OAAA;0BACEiN,SAAS,EAAG,GAAE6D,QAAQ,CAAC1N,IAAI,CAACjB,OAAQ,oFAAoF;0BACxHsJ,KAAK,EAAE;4BACLD,MAAM,EAAG,aAAYsF,QAAQ,CAAC1N,IAAI,CAACT,WAAY;0BACjD,CAAE;0BAAAuK,QAAA,gBAGFlN,OAAA;4BAAKiN,SAAS,EAAC;0BAA2E;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAGjG/N,OAAA;4BAAKiN,SAAS,EAAC,uCAAuC;4BAAAC,QAAA,gBAEpDlN,OAAA;8BAAKiN,SAAS,EAAC,UAAU;8BAAAC,QAAA,gBACvBlN,OAAA;gCACEiN,SAAS,EAAG,+CAA8C6D,QAAQ,CAAC1N,IAAI,CAAClB,KAAM,kIAAkI;gCAChNuJ,KAAK,EAAE;kCACLvJ,KAAK,EAAE,SAAS;kCAChB8M,UAAU,EAAE,6BAA6B;kCACzCxD,MAAM,EAAG,aAAYsF,QAAQ,CAAC1N,IAAI,CAACT,WAAY,EAAC;kCAChD+I,SAAS,EAAG,cAAaoF,QAAQ,CAAC1N,IAAI,CAACd,WAAY;gCACrD,CAAE;gCAAA4K,QAAA,GACH,GACE,EAAC6D,UAAU;8BAAA;gCAAAnD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT,CAAC,eAEN/N,OAAA;gCACEiN,SAAS,EAAC,wFAAwF;gCAClGxB,KAAK,EAAE;kCACLsD,UAAU,EAAE+B,QAAQ,CAAC1N,IAAI,CAACT,WAAW;kCACrCT,KAAK,EAAE,SAAS;kCAChB2M,QAAQ,EAAE;gCACZ,CAAE;gCAAA3B,QAAA,EAED4D,QAAQ,CAAC1N,IAAI,CAACZ,IAAI,iBAAIxC,OAAA,CAAC8Q,QAAQ,CAAC1N,IAAI,CAACZ,IAAI;kCAACyK,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9D,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,eAGN/N,OAAA;8BAAKiN,SAAS,EAAC,UAAU;8BAAAC,QAAA,gBACvBlN,OAAA;gCACEiN,SAAS,EAAC,0FAA0F;gCACpGxB,KAAK,EAAE;kCACLsD,UAAU,EAAE,SAAS;kCACrBrD,SAAS,EAAE;gCACb,CAAE;gCAAAwB,QAAA,EAED4D,QAAQ,CAAChM,cAAc,gBACtB9E,OAAA;kCACEsQ,GAAG,EAAEQ,QAAQ,CAAChM,cAAe;kCAC7ByL,GAAG,EAAEO,QAAQ,CAACnM,IAAK;kCACnBsI,SAAS,EAAC,yCAAyC;kCACnDxB,KAAK,EAAE;oCACL+E,SAAS,EAAE,OAAO;oCAClBC,cAAc,EAAE;kCAClB;gCAAE;kCAAA7C,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC,gBAEF/N,OAAA;kCACEiN,SAAS,EAAC,mFAAmF;kCAC7FxB,KAAK,EAAE;oCACLsD,UAAU,EAAE,SAAS;oCACrB7M,KAAK,EAAE,SAAS;oCAChB2M,QAAQ,EAAE;kCACZ,CAAE;kCAAA3B,QAAA,EAED4D,QAAQ,CAACnM,IAAI,CAAC+L,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;gCAAC;kCAAA/C,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACnC;8BACN;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,EAELiD,aAAa,iBACZhR,OAAA;gCACEiN,SAAS,EAAC,8FAA8F;gCACxGxB,KAAK,EAAE;kCACLsD,UAAU,EAAE,0CAA0C;kCACtDrD,SAAS,EAAE;gCACb,CAAE;gCAAAwB,QAAA,eAEFlN,OAAA,CAACrB,MAAM;kCAACsO,SAAS,EAAC;gCAAwB;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1C,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGN/N,OAAA;4BAAKiN,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClClN,OAAA;8BAAKiN,SAAS,EAAC,WAAW;8BAAAC,QAAA,gBAExBlN,OAAA;gCAAKiN,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,gBAC3ClN,OAAA;kCACEiN,SAAS,EAAC,yCAAyC;kCACnDxB,KAAK,EAAE;oCACLvJ,KAAK,EAAE4O,QAAQ,CAAC1N,IAAI,CAACf,SAAS;oCAC9B2M,UAAU,EAAG,eAAc8B,QAAQ,CAAC1N,IAAI,CAACd,WAAY,EAAC;oCACtD6B,MAAM,EAAE;kCACV,CAAE;kCAAA+I,QAAA,EAED4D,QAAQ,CAACnM;gCAAI;kCAAAiJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACZ,CAAC,EACJiD,aAAa,iBACZhR,OAAA;kCACEiN,SAAS,EAAC,4CAA4C;kCACtDxB,KAAK,EAAE;oCACLsD,UAAU,EAAE,0CAA0C;oCACtD7M,KAAK,EAAE,SAAS;oCAChBwJ,SAAS,EAAE;kCACb,CAAE;kCAAAwB,QAAA,EACH;gCAED;kCAAAU,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACP;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGN/N,OAAA;gCACEiN,SAAS,EAAG,4EAA2E6D,QAAQ,CAAC1N,IAAI,CAAClB,KAAM,+BAA+B;gCAC1IuJ,KAAK,EAAE;kCACLvJ,KAAK,EAAE,SAAS;kCAChB8M,UAAU,EAAE,6BAA6B;kCACzCxD,MAAM,EAAG,aAAYsF,QAAQ,CAAC1N,IAAI,CAACT,WAAY,EAAC;kCAChD+I,SAAS,EAAG,aAAYoF,QAAQ,CAAC1N,IAAI,CAACd,WAAY;gCACpD,CAAE;gCAAA4K,QAAA,gBAEFlN,OAAA,CAAC8Q,QAAQ,CAAC1N,IAAI,CAACZ,IAAI;kCAACyK,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC1C/N,OAAA;kCAAMiN,SAAS,EAAC,qBAAqB;kCAAAC,QAAA,EAAE4D,QAAQ,CAAC1N,IAAI,CAACX;gCAAK;kCAAAmL,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/D,CAAC,eAGN/N,OAAA;gCAAKiN,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,GAC1C4D,QAAQ,CAAChN,KAAK,EAAC,gBAAS,EAACgN,QAAQ,CAACjM,KAAK;8BAAA;gCAAA+I,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrC,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGN/N,OAAA;4BAAKiN,SAAS,EAAC,6CAA6C;4BAAAC,QAAA,gBAE1DlN,OAAA;8BACEiN,SAAS,EAAC,oCAAoC;8BAC9CxB,KAAK,EAAE;gCACLvJ,KAAK,EAAE4O,QAAQ,CAAC1N,IAAI,CAACf,SAAS;gCAC9B2M,UAAU,EAAG,eAAc8B,QAAQ,CAAC1N,IAAI,CAACd,WAAY,EAAC;gCACtD6B,MAAM,EAAE;8BACV,CAAE;8BAAA+I,QAAA,GAED4D,QAAQ,CAACzM,OAAO,CAAC8L,cAAc,CAAC,CAAC,EAAC,KACrC;4BAAA;8BAAAvC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAGN/N,OAAA;8BAAKiN,SAAS,EAAC,iCAAiC;8BAAAC,QAAA,gBAC9ClN,OAAA;gCACEiN,SAAS,EAAC,8CAA8C;gCACxDxB,KAAK,EAAE;kCACLG,eAAe,EAAG,GAAEkF,QAAQ,CAAC1N,IAAI,CAACT,WAAY,IAAG;kCACjDT,KAAK,EAAE4O,QAAQ,CAAC1N,IAAI,CAAChB;gCACvB,CAAE;gCAAA8K,QAAA,gBAEFlN,OAAA,CAAClB,OAAO;kCAACmO,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/B/N,OAAA;kCAAMiN,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAE4D,QAAQ,CAACxM;gCAAiB;kCAAAsJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9D,CAAC,eACN/N,OAAA;gCACEiN,SAAS,EAAC,8CAA8C;gCACxDxB,KAAK,EAAE;kCACLG,eAAe,EAAE,WAAW;kCAC5B1J,KAAK,EAAE;gCACT,CAAE;gCAAAgL,QAAA,gBAEFlN,OAAA,CAACpB,OAAO;kCAACqO,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/B/N,OAAA;kCAAMiN,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAE4D,QAAQ,CAAC7L;gCAAa;kCAAA2I,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1D,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,EAGL,CAAC,MAAM;8BACN,MAAMkD,KAAK,GAAG1E,oBAAoB,CAChCuE,QAAQ,CAAC3L,kBAAkB,EAC3B2L,QAAQ,CAACtE,mBAAmB,EAC5BsE,QAAQ,CAACrE,gBAAgB,EACzBqE,QAAQ,CAACpE,eAAe,EACxBqE,UACF,CAAC;8BACD,oBACE/Q,OAAA;gCACEiN,SAAS,EAAC,gFAAgF;gCAC1FxB,KAAK,EAAE;kCACLG,eAAe,EAAEqF,KAAK,CAAC9O,OAAO;kCAC9BD,KAAK,EAAE+O,KAAK,CAAC/O,KAAK;kCAClBsJ,MAAM,EAAG,aAAYyF,KAAK,CAACtO,WAAY,EAAC;kCACxCkM,QAAQ,EAAE;gCACZ,CAAE;gCAAA3B,QAAA,EAED+D,KAAK,CAACjE;8BAAI;gCAAAY,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACR,CAAC;4BAEV,CAAC,EAAE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAtND+C,QAAQ,CAACpM,GAAG;sBAAAkJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAuNP,CAAC;kBAEjB,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGArN,WAAW,CAACmG,MAAM,GAAG,CAAC,iBACrB7G,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEe,CAAC,EAAE;cAAG,CAAE;cAC/Bd,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEe,CAAC,EAAE;cAAE,CAAE;cAC9BtC,UAAU,EAAE;gBAAEwC,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,sIAAsI;cAAAC,QAAA,eAEhJlN,OAAA;gBAAKiN,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlN,OAAA;kBAAIiN,SAAS,EAAC,wBAAwB;kBAACxB,KAAK,EAAE;oBAC5CvJ,KAAK,EAAE,SAAS;oBAChB8M,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,EAAC;gBAA6B;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrC/N,OAAA;kBAAKiN,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DlN,OAAA;oBAAKiN,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7ClN,OAAA;sBAAKiN,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9CxM,WAAW,CAACyD,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC/C,UAAU,KAAK,SAAS,CAAC,CAACiB;oBAAM;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACN/N,OAAA;sBAAKiN,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACN/N,OAAA;oBAAKiN,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ClN,OAAA;sBAAKiN,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC7CxM,WAAW,CAACyD,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC/C,UAAU,KAAK,eAAe,CAAC,CAACiB;oBAAM;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACN/N,OAAA;sBAAKiN,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACN/N,OAAA;oBAAKiN,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9ClN,OAAA;sBAAKiN,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/CxM,WAAW,CAACyD,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAAC/C,UAAU,KAAK,WAAW,CAAC,CAACiB;oBAAM;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACN/N,OAAA;sBAAKiN,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN/N,OAAA;kBAAGiN,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGAjN,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrCd,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAI,CAAE;cACpCpB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAE,CAAE;cAClC5C,UAAU,EAAE;gBAAEwC,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,wIAAwI;cAAAC,QAAA,eAElJlN,OAAA;gBAAKiN,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlN,OAAA;kBAAIiN,SAAS,EAAC,yBAAyB;kBAACxB,KAAK,EAAE;oBAC7CvJ,KAAK,EAAE,SAAS;oBAChB8M,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7B/N,OAAA;kBAAKiN,SAAS,EAAC,0BAA0B;kBAACxB,KAAK,EAAE;oBAC/CvJ,KAAK,EAAE,SAAS;oBAChB8M,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,GAAC,GAAC,EAACpM,eAAe;gBAAA;kBAAA8M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3B/N,OAAA;kBAAGiN,SAAS,EAAC,SAAS;kBAACxB,KAAK,EAAE;oBAC5BvJ,KAAK,EAAE,SAAS;oBAChB8M,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,EAAC;gBAEH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAGD/N,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEe,CAAC,EAAE;cAAG,CAAE;cAC/Bd,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEe,CAAC,EAAE;cAAE,CAAE;cAC9BtC,UAAU,EAAE;gBAAEwC,KAAK,EAAE,CAAC;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAE7BlN,OAAA;gBAAKiN,SAAS,EAAC,8HAA8H;gBAAAC,QAAA,gBAC3IlN,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;kBACTG,OAAO,EAAE;oBAAEoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC5C,UAAU,EAAE;oBAAE0B,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,eAE9ClN,OAAA,CAACb,QAAQ;oBAAC8N,SAAS,EAAC;kBAAwC;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACb/N,OAAA;kBAAIiN,SAAS,EAAC,yBAAyB;kBAACxB,KAAK,EAAE;oBAC7CvJ,KAAK,EAAE,SAAS;oBAChB8M,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7B/N,OAAA;kBAAGiN,SAAS,EAAC,gCAAgC;kBAACxB,KAAK,EAAE;oBACnDvJ,KAAK,EAAE,SAAS;oBAChB8M,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,EAAC;gBAGH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ/N,OAAA,CAAC5B,MAAM,CAACoQ,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BzB,SAAS,EAAC,sJAAsJ;kBAChK2B,OAAO,EAAEA,CAAA,KAAMzE,MAAM,CAAC+G,QAAQ,CAACC,IAAI,GAAG,YAAa;kBAAAjE,QAAA,EACpD;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAGZrN,WAAW,CAACmG,MAAM,KAAK,CAAC,IAAI,CAACjG,OAAO,iBACnCZ,OAAA,CAAC5B,MAAM,CAAC+O,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAI,CAAE;cACpCpB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAE,CAAE;cAClCzB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAE7BlN,OAAA,CAACvB,QAAQ;gBAACwO,SAAS,EAAC;cAAsC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7D/N,OAAA;gBAAIiN,SAAS,EAAC,yBAAyB;gBAACxB,KAAK,EAAE;kBAC7CvJ,KAAK,EAAE,SAAS;kBAChB8M,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAA/B,QAAA,EAAC;cAAgB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxB/N,OAAA;gBAAGiN,SAAS,EAAC,SAAS;gBAACxB,KAAK,EAAE;kBAC5BvJ,KAAK,EAAE,SAAS;kBAChB8M,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAA/B,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC3N,EAAA,CAhyDID,kBAAkB;EAAA,QACJ7B,WAAW,EAEZC,WAAW;AAAA;AAAA6S,EAAA,GAHxBjR,kBAAkB;AAkyDxB,eAAeA,kBAAkB;AAAC,IAAAiR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}