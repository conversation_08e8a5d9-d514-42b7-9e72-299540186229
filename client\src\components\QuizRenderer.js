import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';
import '../pages/user/Quiz/responsive.css';

const QuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  onAnswerChange,
  timeLeft,
  onNext,
  onPrevious,
  examTitle = "Quiz",
  isTimeWarning = false
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);

  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  const handleAnswerSelect = (answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(true);
    onAnswerChange(answer);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;

  // Early return for invalid question
  if (!question || !question.name) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold mb-2 text-gray-900">Question Not Available</h3>
          <p className="text-gray-600">This question could not be loaded. Please try refreshing the page.</p>
        </div>
      </div>
    );
  }

  const renderMCQ = () => {
    if (!question || !question.options || Object.keys(question.options).length === 0) {
      return (
        <div className="text-center bg-red-50 rounded-xl p-6 border border-red-200">
          <div className="text-red-500 text-4xl mb-2">⚠️</div>
          <p className="text-red-700">No options available for this question.</p>
        </div>
      );
    }

    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];

    return (
      <div className="space-y-3 sm:space-y-4 lg:space-y-5">
        {Object.entries(question.options).map(([key, value], index) => {
          const optionKey = String(key).trim();
          const optionValue = String(value || '').trim();
          const label = optionLabels[index] || optionKey;
          const isSelected = currentAnswer === optionKey;

          // Skip empty options
          if (!optionValue) return null;

          return (
            <motion.button
              key={optionKey}
              onClick={() => handleAnswerSelect(optionKey)}
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
              className={`w-full text-left p-4 sm:p-5 lg:p-6 rounded-lg sm:rounded-xl lg:rounded-2xl border-2 transition-all duration-300 touch-manipulation ${
                isSelected
                  ? 'bg-blue-600 text-white border-blue-600 shadow-lg transform scale-[1.02]'
                  : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 hover:shadow-md'
              }`}
            >
              <div className="flex items-center gap-3 sm:gap-4 lg:gap-6">
                <div className={`w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full flex items-center justify-center font-bold text-sm sm:text-base lg:text-lg transition-all flex-shrink-0 ${
                  isSelected
                    ? 'bg-white text-blue-600'
                    : 'bg-blue-100 text-blue-700'
                }`}>
                  {label}
                </div>
                <span className={`flex-1 font-medium text-base sm:text-lg lg:text-xl leading-relaxed ${
                  isSelected ? 'text-white' : 'text-gray-800'
                }`}>
                  {optionValue}
                </span>
                {isSelected && (
                  <TbCheck className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white flex-shrink-0" />
                )}
              </div>
            </motion.button>
          );
        })}
      </div>
    );
  };

  const renderFillBlank = () => (
    <div className="space-y-4 sm:space-y-5 lg:space-y-6">
      <label className="block text-sm sm:text-base lg:text-lg font-medium text-gray-700 mb-3">
        <div className="flex items-center gap-2 lg:gap-3">
          <span className="text-lg sm:text-xl lg:text-2xl">✏️</span>
          <span>Your Answer:</span>
        </div>
      </label>
      <div className="relative">
        <input
          type="text"
          value={currentAnswer}
          onChange={(e) => handleAnswerSelect(e.target.value)}
          placeholder="Type your answer here..."
          className="w-full px-4 sm:px-5 lg:px-6 py-4 sm:py-5 lg:py-6 border-2 border-gray-200 rounded-lg sm:rounded-xl lg:rounded-2xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all text-base sm:text-lg lg:text-xl font-medium bg-white shadow-sm hover:shadow-md focus:shadow-lg"
        />
        <div className="absolute right-4 sm:right-5 lg:right-6 top-1/2 transform -translate-y-1/2">
          {currentAnswer ? (
            <TbCheck className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-green-500" />
          ) : (
            <div className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 bg-gray-200 rounded-full"></div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden">
      {/* Progress Bar */}
      <div className="fixed top-0 left-0 right-0 h-1 bg-gray-200 z-50">
        <motion.div
          className="h-full bg-gradient-to-r from-blue-500 to-indigo-600"
          initial={{ width: 0 }}
          animate={{ width: `${progressPercentage}%` }}
          transition={{ duration: 0.5 }}
        />
      </div>

      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-100 pt-1 flex-shrink-0 z-40">
        <div className="max-w-6xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
          <div className="flex flex-col sm:grid sm:grid-cols-3 items-center gap-3 sm:gap-0">
            {/* Quiz Title */}
            <div className="text-center sm:text-left w-full sm:w-auto">
              <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 truncate">{examTitle}</h1>
              <p className="text-xs sm:text-sm text-gray-600 hidden sm:block">Challenge your brain, beat the rest</p>
            </div>

            {/* Centered Timer */}
            <div className="flex justify-center order-first sm:order-none">
              <div className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 rounded-lg sm:rounded-xl font-mono text-sm sm:text-lg lg:text-xl font-bold transition-all ${
                isTimeWarning
                  ? 'bg-red-100 text-red-700 border-2 border-red-300 animate-pulse'
                  : 'bg-blue-100 text-blue-700 border-2 border-blue-300'
              }`}>
                <TbClock className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />
                <span className="hidden sm:inline">TIME</span>
                <span>{formatTime(timeLeft)}</span>
              </div>
            </div>

            {/* Question Counter */}
            <div className="flex justify-center sm:justify-end w-full sm:w-auto">
              <div className="bg-gray-100 text-gray-700 px-2 sm:px-3 py-1 sm:py-2 rounded-lg text-xs sm:text-sm lg:text-base font-semibold">
                {questionIndex + 1} of {totalQuestions}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Scrollable Area */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-4xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8">
          <motion.div
            key={questionIndex}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-xl sm:rounded-2xl lg:rounded-3xl shadow-lg p-4 sm:p-6 lg:p-10 mb-6 sm:mb-8 lg:mb-12"
          >
            {/* Question Number Badge */}
            <div className="mb-4 sm:mb-6 lg:mb-8">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 sm:px-4 lg:px-6 py-1.5 sm:py-2 lg:py-3 rounded-full text-xs sm:text-sm lg:text-base font-semibold">
                <span>Question {questionIndex + 1}</span>
              </div>
            </div>

            {/* Question Text */}
            <div className="text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 mb-6 sm:mb-8 lg:mb-10 leading-relaxed">
              {question.name}
            </div>

            {/* Question Image */}
            {question.image && (
              <div className="mb-6 sm:mb-8 lg:mb-10 text-center">
                <div className="inline-block bg-gray-50 rounded-lg sm:rounded-xl p-3 sm:p-4 lg:p-6 border border-gray-200">
                  <img
                    src={question.image}
                    alt="Question"
                    className="max-w-full max-h-64 sm:max-h-80 lg:max-h-96 rounded-lg shadow-md"
                  />
                </div>
              </div>
            )}

            {/* Question Content */}
            <div className="quiz-options">
              {question.options ? renderMCQ() : renderFillBlank()}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="bg-white border-t border-gray-200 shadow-lg flex-shrink-0 z-30">
        <div className="max-w-4xl mx-auto px-3 sm:px-4 lg:px-8 py-3 sm:py-4 lg:py-6">
          <div className="flex items-center justify-between gap-3 sm:gap-4 lg:gap-6">
            <button
              onClick={onPrevious}
              disabled={questionIndex === 0}
              className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-6 lg:px-8 py-2 sm:py-3 lg:py-4 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base lg:text-lg touch-manipulation ${
                questionIndex === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:shadow-md'
              }`}
            >
              <TbArrowLeft className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />
              <span className="hidden sm:inline">Previous</span>
            </button>

            <div className="flex items-center gap-2 sm:gap-4 flex-1 justify-center">
              {!isAnswered && (
                <div className="flex items-center gap-1 sm:gap-2 text-amber-600 bg-amber-50 px-2 sm:px-3 lg:px-4 py-1 sm:py-2 lg:py-3 rounded-lg text-xs sm:text-sm lg:text-base border border-amber-200">
                  <span>⚠️</span>
                  <span className="hidden sm:inline">Select an answer</span>
                  <span className="sm:hidden">Answer required</span>
                </div>
              )}
            </div>

            <button
              onClick={onNext}
              disabled={!isAnswered}
              className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-6 lg:px-8 py-2 sm:py-3 lg:py-4 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base lg:text-lg touch-manipulation ${
                !isAnswered
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : questionIndex === totalQuestions - 1
                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg'
                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg'
              }`}
            >
              {questionIndex === totalQuestions - 1 ? (
                <>
                  <TbCheck className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />
                  <span className="hidden sm:inline">Submit Quiz</span>
                  <span className="sm:hidden">Submit</span>
                </>
              ) : (
                <>
                  <span className="hidden sm:inline">Next</span>
                  <span className="sm:hidden">Next</span>
                  <TbArrowRight className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizRenderer;
