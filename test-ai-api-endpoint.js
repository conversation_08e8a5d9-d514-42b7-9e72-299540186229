const axios = require('axios');
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
require('dotenv').config();

async function testAIQuestionGenerationAPI() {
  try {
    console.log('🔍 Testing AI Question Generation API Endpoint...');
    
    // Connect to database to get a user for authentication
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    const User = require('./models/userModel');
    const user = await User.findOne({});
    
    if (!user) {
      console.log('❌ No user found for authentication');
      return;
    }
    
    console.log('✅ Found user for testing:', user.name);
    
    // Generate JWT token
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '1h' });
    console.log('✅ JWT token generated');
    
    // Test the AI question generation endpoint
    console.log('\n📝 Testing AI question generation endpoint...');
    
    const requestData = {
      userId: user._id.toString(),
      questionTypes: ['multiple_choice'],
      subjects: ['Mathematics'],
      level: 'primary',
      class: '3',
      difficultyLevels: ['medium'],
      syllabusTopics: ['Basic arithmetic', 'Addition'],
      totalQuestions: 1,
      questionDistribution: {
        multiple_choice: 1,
        fill_blank: 0,
        picture_based: 0
      }
    };
    
    console.log('📊 Request data:', JSON.stringify(requestData, null, 2));
    
    const response = await axios.post(
      'http://localhost:5000/api/ai-questions/generate-questions',
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 60000 // 1 minute timeout
      }
    );
    
    console.log('\n✅ API Response received!');
    console.log('📊 Response status:', response.status);
    console.log('📊 Response success:', response.data.success);
    console.log('📊 Response message:', response.data.message);
    
    if (response.data.success && response.data.data) {
      const data = response.data.data;
      console.log('\n📝 Generation Results:');
      console.log('  - Generation ID:', data.generationId);
      console.log('  - Questions generated:', data.questions.length);
      console.log('  - Generation time:', data.generationTime, 'ms');
      
      if (data.questions.length > 0) {
        const question = data.questions[0];
        console.log('\n📝 Sample Generated Question:');
        console.log('  Question:', question.name);
        console.log('  Answer Type:', question.answerType);
        console.log('  Options:', JSON.stringify(question.options, null, 4));
        console.log('  Correct Option:', question.correctOption);
        console.log('  Difficulty:', question.difficultyLevel);
        console.log('  Topics:', question.syllabusTopics);
        console.log('  Type:', question.questionType);
        console.log('  AI Generated:', question.isAIGenerated);
      }
    } else {
      console.log('❌ API returned error:', response.data.error || 'Unknown error');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('📊 HTTP Status:', error.response.status);
      console.error('📊 Response data:', error.response.data);
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure the server is running on port 5000');
    }
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
}

testAIQuestionGenerationAPI();
