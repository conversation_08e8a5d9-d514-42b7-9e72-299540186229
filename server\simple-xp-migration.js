require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/userModel');
const Report = require('./models/reportModel');

// Connect to MongoDB
async function connectDB() {
    try {
        const mongoUrl = process.env.MONGO_URL;
        if (!mongoUrl) {
            throw new Error('MONGO_URL environment variable is not set');
        }
        await mongoose.connect(mongoUrl);
        console.log('✅ MongoDB Connected Successfully');
    } catch (error) {
        console.error('❌ MongoDB Connection Failed:', error);
        process.exit(1);
    }
}

// Simple XP calculation based on quiz performance
function calculateSimpleXP(report) {
    try {
        const result = report.result;
        if (!result) return 0;
        
        const correctAnswers = result.correctAnswers || [];
        const wrongAnswers = result.wrongAnswers || [];
        const totalQuestions = correctAnswers.length + wrongAnswers.length;
        const correctCount = correctAnswers.length;
        
        if (totalQuestions === 0) return 0;
        
        // Base XP: 10 points per correct answer
        const baseXP = correctCount * 10;
        
        // Bonus for high performance
        const scorePercentage = (correctCount / totalQuestions) * 100;
        let bonus = 0;
        
        if (scorePercentage >= 90) {
            bonus = baseXP * 0.5; // 50% bonus for 90%+
        } else if (scorePercentage >= 80) {
            bonus = baseXP * 0.3; // 30% bonus for 80%+
        } else if (scorePercentage >= 70) {
            bonus = baseXP * 0.2; // 20% bonus for 70%+
        }
        
        // Speed bonus if completed quickly
        const timeSpent = result.timeSpent || 0;
        if (timeSpent > 0 && timeSpent < 300) { // Less than 5 minutes
            bonus += baseXP * 0.1; // 10% speed bonus
        }
        
        return Math.round(baseXP + bonus);
        
    } catch (error) {
        console.error('Error calculating XP for report:', error);
        return 0;
    }
}

// Calculate user level based on total XP
function calculateLevel(totalXP) {
    if (totalXP < 100) return { currentLevel: 1, xpToNextLevel: 100 - totalXP };
    if (totalXP < 300) return { currentLevel: 2, xpToNextLevel: 300 - totalXP };
    if (totalXP < 600) return { currentLevel: 3, xpToNextLevel: 600 - totalXP };
    if (totalXP < 1000) return { currentLevel: 4, xpToNextLevel: 1000 - totalXP };
    if (totalXP < 1500) return { currentLevel: 5, xpToNextLevel: 1500 - totalXP };
    if (totalXP < 2100) return { currentLevel: 6, xpToNextLevel: 2100 - totalXP };
    if (totalXP < 2800) return { currentLevel: 7, xpToNextLevel: 2800 - totalXP };
    if (totalXP < 3600) return { currentLevel: 8, xpToNextLevel: 3600 - totalXP };
    if (totalXP < 4500) return { currentLevel: 9, xpToNextLevel: 4500 - totalXP };
    
    // Level 10+
    const level = Math.floor((totalXP - 4500) / 1000) + 10;
    const xpToNext = ((level - 9) * 1000 + 4500) - totalXP;
    return { currentLevel: level, xpToNextLevel: Math.max(0, xpToNext) };
}

// Migrate a single user's points to XP
async function migrateUserXP(user) {
    try {
        console.log(`\n👤 Processing user: ${user.name} (${user.email})`);
        
        // Get all reports for this user
        const reports = await Report.find({ user: user._id }).sort({ createdAt: 1 });
        
        if (reports.length === 0) {
            console.log(`   ⚠️  No quiz reports found for ${user.name}`);
            return null;
        }
        
        console.log(`   📊 Found ${reports.length} quiz reports`);
        
        let totalXP = 0;
        let passedQuizzes = 0;
        let totalScore = 0;
        let bestStreak = 0;
        let currentStreak = 0;
        
        // Process each report to calculate XP
        for (const report of reports) {
            const xpEarned = calculateSimpleXP(report);
            totalXP += xpEarned;
            
            // Calculate statistics
            if (report.result && report.result.verdict === 'Pass') {
                passedQuizzes++;
                currentStreak++;
                bestStreak = Math.max(bestStreak, currentStreak);
            } else {
                currentStreak = 0;
            }
            
            // Calculate average score
            if (report.result) {
                const correctAnswers = report.result.correctAnswers || [];
                const wrongAnswers = report.result.wrongAnswers || [];
                const totalQuestions = correctAnswers.length + wrongAnswers.length;
                if (totalQuestions > 0) {
                    const score = (correctAnswers.length / totalQuestions) * 100;
                    totalScore += score;
                }
            }
        }
        
        // Calculate level based on total XP
        const levelInfo = calculateLevel(totalXP);
        
        // Calculate average score
        const averageScore = reports.length > 0 ? Math.round(totalScore / reports.length) : 0;
        
        // Update user with XP data
        const updateData = {
            totalXP: totalXP,
            lifetimeXP: totalXP,
            seasonXP: totalXP,
            currentLevel: levelInfo.currentLevel,
            xpToNextLevel: levelInfo.xpToNextLevel,
            currentSeason: "2024-S1",
            
            // Update statistics
            totalQuizzesTaken: reports.length,
            passedExamsCount: passedQuizzes,
            averageScore: averageScore,
            bestStreak: bestStreak,
            currentStreak: currentStreak,
            
            // Preserve existing data
            achievements: user.achievements || [],
            totalPointsEarned: user.totalPointsEarned || 0,
            
            // Add migration metadata
            migrationData: {
                migratedAt: new Date(),
                originalTotalPoints: user.totalPointsEarned || 0,
                calculatedTotalXP: totalXP,
                reportsProcessed: reports.length,
                migrationVersion: 'simple-v1'
            }
        };
        
        await User.findByIdAndUpdate(user._id, updateData);
        
        console.log(`   ✅ Migration completed for ${user.name}:`);
        console.log(`      - Total XP: ${totalXP.toLocaleString()}`);
        console.log(`      - Current Level: ${levelInfo.currentLevel}`);
        console.log(`      - Quizzes Taken: ${reports.length}`);
        console.log(`      - Passed Quizzes: ${passedQuizzes}`);
        console.log(`      - Average Score: ${averageScore}%`);
        console.log(`      - Original Points: ${user.totalPointsEarned || 0}`);
        
        return {
            userId: user._id,
            userName: user.name,
            originalPoints: user.totalPointsEarned || 0,
            calculatedXP: totalXP,
            level: levelInfo.currentLevel,
            reportsProcessed: reports.length,
            passedQuizzes: passedQuizzes,
            averageScore: averageScore
        };
        
    } catch (error) {
        console.error(`   ❌ Error migrating user ${user.name}:`, error);
        return null;
    }
}

// Main migration function
async function migrateAllUsersToXP() {
    console.log('🚀 Starting Simple XP Migration...\n');
    
    try {
        // Get all non-admin users
        const users = await User.find({
            isAdmin: { $ne: true },
            isBlocked: { $ne: true }
        }).sort({ createdAt: 1 });
        
        console.log(`📊 Found ${users.length} total users to check\n`);
        
        // Filter users who actually have quiz reports
        const usersWithReports = [];
        for (const user of users) {
            const reportCount = await Report.countDocuments({ user: user._id });
            if (reportCount > 0) {
                usersWithReports.push(user);
            }
        }
        
        console.log(`📊 Found ${usersWithReports.length} users with quiz reports to migrate\n`);
        
        const migrationResults = [];
        let successCount = 0;
        let errorCount = 0;
        
        // Process users one by one
        for (const user of usersWithReports) {
            const result = await migrateUserXP(user);
            if (result) {
                migrationResults.push(result);
                successCount++;
            } else {
                errorCount++;
            }
            
            // Small delay to avoid overwhelming the database
            await new Promise(resolve => setTimeout(resolve, 50));
        }
        
        // Generate migration summary
        console.log('\n🎉 Migration Summary:');
        console.log(`✅ Successfully migrated: ${successCount} users`);
        console.log(`❌ Failed migrations: ${errorCount} users`);
        
        if (migrationResults.length > 0) {
            const totalOriginalPoints = migrationResults.reduce((sum, r) => sum + r.originalPoints, 0);
            const totalCalculatedXP = migrationResults.reduce((sum, r) => sum + r.calculatedXP, 0);
            const totalQuizzes = migrationResults.reduce((sum, r) => sum + r.reportsProcessed, 0);
            
            console.log(`\n📈 Migration Statistics:`);
            console.log(`   - Total Original Points: ${totalOriginalPoints.toLocaleString()}`);
            console.log(`   - Total Calculated XP: ${totalCalculatedXP.toLocaleString()}`);
            console.log(`   - Total Quizzes Processed: ${totalQuizzes.toLocaleString()}`);
            console.log(`   - Average XP per User: ${Math.round(totalCalculatedXP / migrationResults.length).toLocaleString()}`);
            console.log(`   - Average Quizzes per User: ${Math.round(totalQuizzes / migrationResults.length)}`);
            
            // Show top 10 users by XP
            const topUsers = migrationResults
                .sort((a, b) => b.calculatedXP - a.calculatedXP)
                .slice(0, 10);
            
            console.log(`\n🏆 Top 10 Users by XP:`);
            topUsers.forEach((user, index) => {
                console.log(`   ${index + 1}. ${user.userName} - ${user.calculatedXP.toLocaleString()} XP (Level ${user.level}) - ${user.reportsProcessed} quizzes`);
            });
        }
        
        console.log('\n✅ Migration completed successfully!');
        return migrationResults;
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        throw error;
    }
}

// Main execution function
async function main() {
    try {
        await connectDB();
        
        console.log('⚠️  WARNING: This will migrate all user points to XP based on quiz history.');
        console.log('⚠️  This is a simplified migration that updates user XP directly.\n');
        
        const results = await migrateAllUsersToXP();
        
        console.log('\n📄 Migration completed successfully!');
        
        // Save migration results to a file for reference
        const fs = require('fs');
        const migrationLog = {
            migrationDate: new Date(),
            migrationVersion: 'simple-v1',
            totalUsers: results.length,
            results: results
        };
        
        fs.writeFileSync(
            `simple-migration-log-${Date.now()}.json`, 
            JSON.stringify(migrationLog, null, 2)
        );
        
        console.log('📄 Migration log saved to file.');
        
    } catch (error) {
        console.error('❌ Migration script failed:', error);
    } finally {
        await mongoose.connection.close();
        console.log('\n🔌 Database connection closed.');
        process.exit(0);
    }
}

// Run migration if this file is executed directly
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Script execution failed:', error);
        process.exit(1);
    });
}

module.exports = { migrateAllUsersToXP, migrateUserXP, calculateSimpleXP, calculateLevel };
