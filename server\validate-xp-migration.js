require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/userModel');
const Report = require('./models/reportModel');
const { calculateQuizXP, calculateLevel } = require('./migrate-points-to-xp');

// Connect to MongoDB
async function connectDB() {
    try {
        const mongoUrl = process.env.MONGO_URL;
        if (!mongoUrl) {
            throw new Error('MONGO_URL environment variable is not set');
        }
        await mongoose.connect(mongoUrl);
        console.log('✅ MongoDB Connected Successfully');
    } catch (error) {
        console.error('❌ MongoDB Connection Failed:', error);
        process.exit(1);
    }
}

// Validate migration for a sample of users
async function validateMigration() {
    console.log('🔍 Validating XP Migration Logic...\n');
    
    try {
        // Get a sample of users with quiz activity
        const sampleUsers = await User.find({
            isAdmin: { $ne: true },
            totalQuizzesTaken: { $gt: 0 }
        }).limit(5).sort({ totalQuizzesTaken: -1 });
        
        console.log(`📊 Testing migration logic with ${sampleUsers.length} sample users\n`);
        
        for (const user of sampleUsers) {
            console.log(`\n👤 Testing user: ${user.name}`);
            console.log(`   Current Points: ${user.totalPointsEarned || 0}`);
            console.log(`   Current XP: ${user.totalXP || 0}`);
            console.log(`   Quizzes Taken: ${user.totalQuizzesTaken || 0}`);
            
            // Get user's reports
            const reports = await Report.find({ user: user._id })
                .populate('exam')
                .sort({ createdAt: 1 });
            
            if (reports.length === 0) {
                console.log(`   ⚠️  No reports found`);
                continue;
            }
            
            let calculatedXP = 0;
            const examAttempts = new Map();
            
            console.log(`   📋 Processing ${reports.length} quiz reports:`);
            
            for (const report of reports) {
                if (!report.exam) continue;
                
                const examId = report.exam._id.toString();
                const attemptCount = (examAttempts.get(examId) || 0) + 1;
                examAttempts.set(examId, attemptCount);
                
                const isFirstAttempt = attemptCount === 1;
                const xpResult = calculateQuizXP(report, report.exam, user, isFirstAttempt);
                
                calculatedXP += xpResult.xpAwarded;
                
                console.log(`      - ${report.exam.subject}: ${xpResult.xpAwarded} XP (${xpResult.metadata.scorePercentage}% score, ${isFirstAttempt ? 'first' : 'retry'} attempt)`);
            }
            
            // Calculate level
            const levelInfo = calculateLevel(calculatedXP);
            
            console.log(`   📈 Migration Results:`);
            console.log(`      - Calculated Total XP: ${calculatedXP.toLocaleString()}`);
            console.log(`      - Calculated Level: ${levelInfo.currentLevel}`);
            console.log(`      - XP to Next Level: ${levelInfo.xpToNextLevel}`);
            console.log(`      - XP per Quiz Average: ${Math.round(calculatedXP / reports.length)}`);
            
            // Compare with current values
            const currentXP = user.totalXP || 0;
            const xpDifference = calculatedXP - currentXP;
            
            if (currentXP > 0) {
                console.log(`   🔄 Comparison with Current XP:`);
                console.log(`      - Current XP: ${currentXP.toLocaleString()}`);
                console.log(`      - Difference: ${xpDifference > 0 ? '+' : ''}${xpDifference.toLocaleString()}`);
                console.log(`      - Change: ${xpDifference === 0 ? 'No change' : (xpDifference > 0 ? 'Increase' : 'Decrease')}`);
            }
            
            // Validate calculation logic
            console.log(`   ✅ Validation: ${calculatedXP > 0 ? 'PASS' : 'FAIL'} - XP calculation successful`);
        }
        
        console.log('\n🎯 Testing Level Calculation Logic...');
        
        // Test level calculation with various XP amounts
        const testXPValues = [0, 50, 100, 250, 500, 1000, 2000, 5000, 10000];
        
        console.log('\n📊 XP to Level Conversion Table:');
        console.log('   XP      | Level | XP to Next');
        console.log('   --------|-------|----------');
        
        for (const xp of testXPValues) {
            const levelInfo = calculateLevel(xp);
            console.log(`   ${xp.toString().padStart(7)} | ${levelInfo.currentLevel.toString().padStart(5)} | ${levelInfo.xpToNextLevel.toString().padStart(8)}`);
        }
        
        console.log('\n🧮 Testing XP Calculation Components...');
        
        // Test XP calculation with sample data
        const testQuizData = {
            correctAnswers: [1, 2, 3, 4, 5], // 5 correct
            wrongAnswers: [6, 7], // 2 wrong
            timeSpent: 300, // 5 minutes
            verdict: 'Pass'
        };
        
        const testExam = {
            duration: 10, // 10 minutes
            difficulty: 'medium',
            subject: 'Mathematics'
        };
        
        const testUser = {
            currentLevel: 1,
            currentStreak: 3
        };
        
        const testReport = { result: testQuizData };
        
        const testXPResult = calculateQuizXP(testReport, testExam, testUser, true);
        
        console.log('\n📝 Sample XP Calculation:');
        console.log(`   Quiz: 5/7 correct (71% score)`);
        console.log(`   Difficulty: Medium`);
        console.log(`   Time: 5/10 minutes (fast completion)`);
        console.log(`   First attempt: Yes`);
        console.log(`   User level: 1`);
        console.log(`   Result: ${testXPResult.xpAwarded} XP`);
        console.log(`   Breakdown:`);
        console.log(`      - Base XP: ${testXPResult.breakdown.baseXP}`);
        console.log(`      - Difficulty Bonus: ${testXPResult.breakdown.difficultyBonus}`);
        console.log(`      - Perfect Score Bonus: ${testXPResult.breakdown.perfectScoreBonus}`);
        console.log(`      - Speed Bonus: ${testXPResult.breakdown.speedBonus}`);
        console.log(`      - First Attempt Bonus: ${testXPResult.breakdown.firstAttemptBonus}`);
        console.log(`      - Level Multiplier: ${testXPResult.breakdown.levelMultiplier}x`);
        
        console.log('\n✅ Validation completed successfully!');
        console.log('\n📋 Migration Readiness Checklist:');
        console.log('   ✅ XP calculation logic working');
        console.log('   ✅ Level calculation logic working');
        console.log('   ✅ Database connection successful');
        console.log('   ✅ Sample user data processed correctly');
        console.log('   ✅ Migration script ready to run');
        
        console.log('\n🚀 Ready to run full migration!');
        console.log('   Run: node migrate-points-to-xp.js');
        
    } catch (error) {
        console.error('❌ Validation failed:', error);
        throw error;
    }
}

// Check current database state
async function checkDatabaseState() {
    console.log('\n📊 Current Database State Analysis...\n');
    
    try {
        // Get user statistics
        const totalUsers = await User.countDocuments({ isAdmin: { $ne: true } });
        const usersWithQuizzes = await User.countDocuments({ 
            isAdmin: { $ne: true }, 
            totalQuizzesTaken: { $gt: 0 } 
        });
        const usersWithXP = await User.countDocuments({ 
            isAdmin: { $ne: true }, 
            totalXP: { $gt: 0 } 
        });
        const usersWithPoints = await User.countDocuments({ 
            isAdmin: { $ne: true }, 
            totalPointsEarned: { $gt: 0 } 
        });
        
        console.log('👥 User Statistics:');
        console.log(`   Total Users: ${totalUsers}`);
        console.log(`   Users with Quizzes: ${usersWithQuizzes}`);
        console.log(`   Users with XP: ${usersWithXP}`);
        console.log(`   Users with Points: ${usersWithPoints}`);
        
        // Get report statistics
        const totalReports = await Report.countDocuments();
        const reportsWithExams = await Report.countDocuments({ exam: { $ne: null } });
        
        console.log('\n📋 Quiz Report Statistics:');
        console.log(`   Total Reports: ${totalReports}`);
        console.log(`   Reports with Exam Data: ${reportsWithExams}`);
        
        // Sample some users to show current state
        const sampleUsers = await User.find({
            isAdmin: { $ne: true },
            totalQuizzesTaken: { $gt: 0 }
        }).limit(3).sort({ totalPointsEarned: -1 });
        
        console.log('\n🔍 Sample User Data:');
        for (const user of sampleUsers) {
            console.log(`   ${user.name}:`);
            console.log(`      Points: ${user.totalPointsEarned || 0}`);
            console.log(`      XP: ${user.totalXP || 0}`);
            console.log(`      Level: ${user.currentLevel || 1}`);
            console.log(`      Quizzes: ${user.totalQuizzesTaken || 0}`);
        }
        
        // Check if migration has been run before
        const usersWithMigrationData = await User.countDocuments({
            'migrationData.migratedAt': { $exists: true }
        });
        
        console.log(`\n🔄 Migration Status:`);
        console.log(`   Users with Migration Data: ${usersWithMigrationData}`);
        
        if (usersWithMigrationData > 0) {
            console.log('   ⚠️  WARNING: Some users appear to have been migrated already!');
            console.log('   ⚠️  Check migration data before proceeding.');
        } else {
            console.log('   ✅ No previous migration detected - safe to proceed');
        }
        
    } catch (error) {
        console.error('❌ Database state check failed:', error);
        throw error;
    }
}

// Main function
async function main() {
    try {
        await connectDB();
        await checkDatabaseState();
        await validateMigration();
        
    } catch (error) {
        console.error('❌ Validation script failed:', error);
    } finally {
        await mongoose.connection.close();
        console.log('\n🔌 Database connection closed.');
        process.exit(0);
    }
}

// Run validation
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Script execution failed:', error);
        process.exit(1);
    });
}

module.exports = { validateMigration, checkDatabaseState };
