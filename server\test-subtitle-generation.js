const axios = require('axios');
const mongoose = require('mongoose');
const Videos = require('./models/studyVideos');
const jwt = require('jsonwebtoken');
const User = require('./models/userModel');
require('dotenv').config();

async function testSubtitleGeneration() {
  try {
    console.log('🧪 Testing subtitle generation...');
    
    // Connect to database to get a test video
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    // Find a video with a valid URL
    const video = await Videos.findOne({ 
      videoUrl: { $exists: true, $ne: null, $ne: '' },
      $or: [
        { videoUrl: { $regex: /\.mp4$/i } },
        { videoUrl: { $regex: /\.mkv$/i } }
      ]
    });
    
    if (!video) {
      console.log('❌ No suitable video found for testing');
      return;
    }
    
    console.log('📹 Testing with video:', video.title);
    console.log('🔗 Video URL:', video.videoUrl);
    console.log('🆔 Video ID:', video._id);
    
    // Generate a test JWT token for authentication
    const user = await User.findOne({});
    
    if (!user) {
      console.log('❌ No user found for authentication');
      return;
    }
    
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '1h' });
    
    // Test the subtitle generation endpoint
    console.log('📤 Sending subtitle generation request...');
    console.log('🔗 Request URL:', `http://localhost:5000/api/study/generate-subtitles/${video._id}`);

    const startTime = Date.now();
    const response = await axios.post(
      `http://localhost:5000/api/study/generate-subtitles/${video._id}`,
      { language: 'en' },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 180000, // 3 minutes timeout
        onUploadProgress: (progressEvent) => {
          console.log('📤 Upload progress:', Math.round((progressEvent.loaded * 100) / progressEvent.total) + '%');
        },
        onDownloadProgress: (progressEvent) => {
          console.log('📥 Download progress:', Math.round((progressEvent.loaded * 100) / progressEvent.total) + '%');
        }
      }
    );

    const endTime = Date.now();
    console.log('⏱️ Request took:', (endTime - startTime) / 1000, 'seconds');
    
    console.log('✅ Subtitle generation response:');
    console.log('Status:', response.status);
    console.log('Success:', response.data.success);
    console.log('Message:', response.data.message);
    
    if (response.data.data) {
      console.log('📝 Subtitle data:', {
        language: response.data.data.subtitle?.language,
        url: response.data.data.subtitle?.url,
        isAutoGenerated: response.data.data.subtitle?.isAutoGenerated
      });
    }
    
  } catch (error) {
    console.error('❌ Subtitle generation failed:');
    console.error('Status:', error.response?.status);
    console.error('Message:', error.response?.data?.message || error.message);
    console.error('Error details:', error.response?.data || error.message);
    
    // Log more detailed error information
    if (error.response?.data) {
      console.error('Full response data:', JSON.stringify(error.response.data, null, 2));
    }
  } finally {
    await mongoose.connection.close();
  }
}

testSubtitleGeneration();
