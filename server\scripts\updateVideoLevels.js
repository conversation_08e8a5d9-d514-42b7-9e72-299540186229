const mongoose = require("mongoose");
require("dotenv").config();

// Import the Videos model
const Videos = require("../models/studyVideos");

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000,
      family: 4,
      maxPoolSize: 10,
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      retryWrites: true,
      retryReads: true,
      directConnection: false,
    });
    console.log("✅ MongoDB Connected Successfully");
  } catch (error) {
    console.error("❌ MongoDB Connection Failed:", error.message);
    process.exit(1);
  }
};

// Function to determine level based on className
const determineLevelFromClassName = (className) => {
  // Primary classes: 1, 2, 3, 4, 5, 6, 7
  const primaryClasses = ["1", "2", "3", "4", "5", "6", "7"];

  // Secondary classes: Form-1, Form-2, Form-3, Form-4
  const secondaryClasses = ["Form-1", "Form-2", "Form-3", "Form-4"];

  // Advance classes: Form-5, Form-6
  const advanceClasses = ["Form-5", "Form-6"];

  if (primaryClasses.includes(className)) {
    return "primary";
  } else if (secondaryClasses.includes(className)) {
    return "secondary";
  } else if (advanceClasses.includes(className)) {
    return "advance";
  } else {
    // Default to primary for unknown classes
    console.log(`⚠️ Unknown className: ${className}, defaulting to primary`);
    return "primary";
  }
};

// Main update function
const updateVideoLevels = async () => {
  try {
    console.log("🔄 Starting video level updates...");

    // Get all videos
    const allVideos = await Videos.find({});
    console.log(`📊 Found ${allVideos.length} videos to process`);

    // First, let's see what we're working with
    console.log("\n🔍 Current video data:");
    allVideos.forEach(video => {
      console.log(`   "${video.title}" - Class: ${video.className} - Level: "${video.level}" (type: ${typeof video.level})`);
    });

    let updatedCount = 0;

    // Update ALL videos to ensure they have the correct level
    for (const video of allVideos) {
      const correctLevel = determineLevelFromClassName(video.className);

      console.log(`🔧 Setting video "${video.title}" (Class: ${video.className}) to level "${correctLevel}"`);

      await Videos.findByIdAndUpdate(video._id, {
        $set: { level: correctLevel }
      });
      updatedCount++;
    }

    console.log(`✅ Update completed!`);
    console.log(`📈 Updated: ${updatedCount} videos`);

    // Verify the updates
    console.log("\n🔍 Verification - Current level distribution:");
    const levelStats = await Videos.aggregate([
      {
        $group: {
          _id: "$level",
          count: { $sum: 1 }
        }
      }
    ]);

    levelStats.forEach(stat => {
      console.log(`   ${stat._id}: ${stat.count} videos`);
    });

    // Show all videos after update
    console.log("\n📋 All videos after update:");
    const updatedVideos = await Videos.find({});
    updatedVideos.forEach(video => {
      console.log(`   Class ${video.className} - ${video.subject} - Level: "${video.level}" - "${video.title}"`);
    });

  } catch (error) {
    console.error("❌ Error updating video levels:", error);
  }
};

// Run the script
const runScript = async () => {
  await connectDB();
  await updateVideoLevels();
  
  console.log("\n🏁 Script completed. Closing database connection...");
  await mongoose.connection.close();
  process.exit(0);
};

// Execute the script
runScript().catch(error => {
  console.error("❌ Script failed:", error);
  process.exit(1);
});
