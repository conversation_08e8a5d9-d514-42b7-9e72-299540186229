const axios = require('axios');
const jwt = require('jsonwebtoken');
require('dotenv').config();

async function simulateFrontendRequest() {
  try {
    console.log('🧪 Simulating exact frontend request...');
    
    // Create a token like the frontend would
    const testUserId = '507f1f77bcf86cd799439011';
    const token = jwt.sign({ userId: testUserId }, process.env.JWT_SECRET, { expiresIn: '1h' });
    
    // Simulate the exact payload structure from the frontend
    const payload = {
      examId: undefined, // Often undefined when not selected
      questionTypes: ['multiple_choice', 'fill_blank'],
      subjects: ['Mathematics'],
      level: 'primary',
      class: '3',
      difficultyLevels: ['medium'],
      syllabusTopics: [], // Often empty array
      totalQuestions: 5,
      questionDistribution: {
        multiple_choice: 3,
        fill_blank: 2,
        picture_based: 0
      },
      userId: testUserId
    };
    
    console.log('📤 Sending payload:', JSON.stringify(payload, null, 2));
    
    const response = await axios.post(
      'http://localhost:5000/api/ai-questions/generate-questions',
      payload,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 120000 // 2 minutes timeout like frontend
      }
    );
    
    console.log('✅ Frontend simulation successful!');
    console.log('Status:', response.status);
    console.log('Success:', response.data.success);
    console.log('Message:', response.data.message);
    console.log('Questions generated:', response.data.data?.questions?.length || 0);
    
    if (response.data.data?.questions?.length > 0) {
      console.log('\n📝 Generated questions:');
      response.data.data.questions.forEach((q, i) => {
        console.log(`${i + 1}. [${q.questionType}] ${q.name}`);
        if (q.options) {
          console.log(`   Correct: ${q.correctOption} - ${q.options[q.correctOption]}`);
        } else if (q.correctAnswer) {
          console.log(`   Answer: ${q.correctAnswer}`);
        }
      });
    }
    
  } catch (error) {
    console.error('❌ Frontend simulation failed:', error.message);
    
    if (error.response) {
      console.error('📊 Response Status:', error.response.status);
      console.error('📊 Response Headers:', error.response.headers);
      console.error('📊 Response Data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.error('📡 Request Error:', error.request);
    } else {
      console.error('⚠️ Setup Error:', error.message);
    }
  }
}

simulateFrontendRequest();
