const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

async function testExamAPI() {
  try {
    console.log('🔍 Testing Exam API...');

    // Connect to database to get a test user
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');

    const User = require('./models/userModel');
    const user = await User.findOne({});

    if (!user) {
      console.log('❌ No user found for authentication');
      return;
    }

    // Generate a test JWT token for authentication
    const jwt = require('jsonwebtoken');
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '1h' });

    console.log('📡 Testing getAllExams API...');

    // Test the getAllExams endpoint
    const response = await axios.post(
      'http://localhost:5000/api/exams/get-all-exams',
      { userId: user._id },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      }
    );

    console.log('✅ getAllExams API Response:');
    console.log('Status:', response.status);
    console.log('Success:', response.data.success);
    console.log('Message:', response.data.message);
    console.log('Data type:', Array.isArray(response.data.data) ? 'Array' : typeof response.data.data);
    console.log('Exams count:', response.data.data ? response.data.data.length : 0);

    if (response.data.data && response.data.data.length > 0) {
      console.log('📋 Sample exam structure:');
      const sampleExam = response.data.data[0];
      console.log('  _id:', sampleExam._id ? 'Present' : 'Missing');
      console.log('  name:', sampleExam.name ? 'Present' : 'Missing');
      console.log('  category:', sampleExam.category ? 'Present' : 'Missing');
      console.log('  level:', sampleExam.level ? 'Present' : 'Missing');
      console.log('  class:', sampleExam.class ? 'Present' : 'Missing');
    }

    // Test creating a new exam
    console.log('\n📝 Testing addExam API...');
    
    const newExamData = {
      name: `Test Exam ${Date.now()}`,
      duration: 3600,
      category: 'Mathematics',
      level: 'primary',
      class: '5',
      totalMarks: 100,
      passingMarks: 50,
      description: 'Test exam for API validation'
    };

    const addResponse = await axios.post(
      'http://localhost:5000/api/exams/add',
      newExamData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      }
    );

    console.log('✅ addExam API Response:');
    console.log('Status:', addResponse.status);
    console.log('Success:', addResponse.data.success);
    console.log('Message:', addResponse.data.message);
    console.log('Data present:', addResponse.data.data ? 'Yes' : 'No');
    
    if (addResponse.data.data) {
      console.log('📋 Created exam structure:');
      console.log('  _id:', addResponse.data.data._id ? 'Present' : 'Missing');
      console.log('  name:', addResponse.data.data.name ? 'Present' : 'Missing');
      console.log('  category:', addResponse.data.data.category ? 'Present' : 'Missing');
    }

  } catch (error) {
    console.error('❌ Test failed:');
    console.error('Status:', error.response?.status);
    console.error('Message:', error.response?.data?.message || error.message);
    console.error('Error details:', error.response?.data || error.message);
  } finally {
    await mongoose.connection.close();
  }
}

testExamAPI();
