{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbTarget, TbBrain, TbRefresh, TbMedal, TbBolt, TbRocket, TbDiamond, TbHeart, TbEye, TbTrendingUp, TbAward, TbShield } from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const user = userState.user || null;\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Performance tiers with SPECTACULAR visual themes and unique colors\n  const performanceTiers = {\n    legendary: {\n      min: 10000,\n      color: 'from-purple-400 via-pink-400 via-red-400 to-orange-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-pink-900/40 to-red-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FF69B4',\n      shadowColor: 'rgba(147, 51, 234, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery',\n      borderColor: '#8B5CF6',\n      effect: 'legendary-sparkle'\n    },\n    diamond: {\n      min: 7500,\n      color: 'from-cyan-200 via-blue-300 via-indigo-400 to-purple-500',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00E5FF',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 229, 255, 0.9)',\n      glow: 'shadow-cyan-300/80',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance',\n      borderColor: '#00E5FF',\n      effect: 'diamond-shine'\n    },\n    platinum: {\n      min: 5000,\n      color: 'from-slate-200 via-gray-300 via-zinc-400 to-stone-500',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#E8E8E8',\n      nameColor: '#C0C0C0',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-300/80',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam'\n    },\n    gold: {\n      min: 2500,\n      color: 'from-yellow-200 via-amber-300 via-orange-400 to-red-500',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-300/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent',\n      borderColor: '#FFD700',\n      effect: 'gold-glow'\n    },\n    silver: {\n      min: 1000,\n      color: 'from-gray-200 via-slate-300 via-zinc-400 to-gray-500',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#D3D3D3',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(211, 211, 211, 0.9)',\n      glow: 'shadow-gray-300/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress',\n      borderColor: '#D3D3D3',\n      effect: 'silver-shimmer'\n    },\n    bronze: {\n      min: 0,\n      color: 'from-orange-200 via-amber-300 via-yellow-400 to-orange-500',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-300/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = xp => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return {\n        tier,\n        ...config\n      };\n    }\n    return {\n      tier: 'bronze',\n      ...performanceTiers.bronze\n    };\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: (user === null || user === void 0 ? void 0 : user.level) || 'all',\n          includeInactive: false\n        });\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n          const transformedData = xpLeaderboardResponse.data.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id));\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n      let rankingResponse, usersResponse;\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n      let transformedData = [];\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            var _item$user;\n            const userId = ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n        transformedData = usersResponse.data.filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n        .map((userData, index) => {\n          // Get reports for this user\n          const userReports = userReportsMap[userData._id] || [];\n\n          // Use existing user data or calculate from reports\n          let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n          let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n          let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n          // For existing users with old data, make intelligent assumptions\n          if (!userReports.length && userData.totalPoints) {\n            // Assume higher points = more exams and better performance\n            const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n            const estimatedAverage = Math.min(95, Math.max(60, 60 + userData.totalPoints / estimatedQuizzes / 10)); // Scale average based on points\n\n            totalQuizzes = estimatedQuizzes;\n            averageScore = Math.round(estimatedAverage);\n            totalScore = Math.round(averageScore * totalQuizzes);\n            console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n          }\n\n          // Calculate XP based on performance (enhanced calculation)\n          let totalXP = userData.totalXP || 0;\n          if (!totalXP) {\n            // Calculate XP from available data\n            if (userData.totalPoints) {\n              // Use existing points as base XP with bonuses\n              totalXP = Math.floor(userData.totalPoints +\n              // Base points\n              totalQuizzes * 25 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 15 : 0) + (\n              // Excellence bonus\n              averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n              );\n            } else if (totalQuizzes > 0) {\n              // Calculate from quiz performance\n              totalXP = Math.floor(averageScore * totalQuizzes * 8 +\n              // Base XP from scores\n              totalQuizzes * 40 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n              );\n            }\n          }\n\n          // Calculate streaks (enhanced logic)\n          let currentStreak = userData.currentStreak || 0;\n          let bestStreak = userData.bestStreak || 0;\n          if (userReports.length > 0) {\n            // Calculate from actual reports\n            let tempStreak = 0;\n            userReports.forEach(report => {\n              if (report.score >= 60) {\n                // Passing score\n                tempStreak++;\n                bestStreak = Math.max(bestStreak, tempStreak);\n              } else {\n                tempStreak = 0;\n              }\n            });\n            currentStreak = tempStreak;\n          } else if (userData.totalPoints && !currentStreak) {\n            // Estimate streaks from points (higher points = likely better streaks)\n            const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n            if (pointsPerQuiz > 80) {\n              currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n              bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n            }\n          }\n\n          return {\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: currentStreak,\n            bestStreak: bestStreak,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(totalXP),\n            isRealUser: true,\n            // Additional tracking fields for future updates\n            originalPoints: userData.totalPoints || 0,\n            hasReports: userReports.length > 0,\n            dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank\n        const userRank = user ? transformedData.findIndex(item => item._id === user._id) : -1;\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n    return () => clearInterval(animationTimer);\n  }, []);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Find Me functionality\n  const handleFindMe = () => {\n    if (currentUserRef.current) {\n      setShowFindMe(true);\n      currentUserRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n\n      // Hide the find me highlight after 3 seconds\n      setTimeout(() => {\n        setShowFindMe(false);\n      }, 3000);\n    } else {\n      // If ref is not found, try to find user in the ranking data and scroll to them\n      const userInRanking = rankingData.find(u => u._id === (user === null || user === void 0 ? void 0 : user._id));\n      if (userInRanking) {\n        const userRank = rankingData.indexOf(userInRanking) + 1;\n        message.info(`You are ranked #${userRank} in the leaderboard!`);\n      } else {\n        message.info('You are not yet ranked. Take some quizzes to appear on the leaderboard!');\n      }\n    }\n  };\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981',\n          // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444',\n          // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444',\n        // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading the Hall of Champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .find-me-highlight {\n          animation: findMePulse 2s ease-in-out infinite;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);\n          }\n          50% {\n            box-shadow: 0 0 0 20px rgba(255, 215, 0, 0);\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n        children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n          animate: {\n            y: [0, -100, 0],\n            x: [0, Math.random() * 100 - 50, 0],\n            opacity: [0.2, 0.8, 0.2]\n          },\n          transition: {\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            delay: Math.random() * 2\n          },\n          style: {\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          ref: headerRef,\n          initial: {\n            opacity: 0,\n            y: -50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            ease: \"easeOut\"\n          },\n          className: \"relative overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-purple-600 via-pink-500 via-red-500 via-orange-500 to-yellow-500 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-8 sm:py-12 md:py-16 lg:py-20\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-7xl mx-auto text-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  className: \"mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-8xl font-black mb-3 md:mb-4 tracking-tight\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      },\n                      transition: {\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      },\n                      className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                      style: {\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(3px 3px 6px rgba(0,0,0,0.8))'\n                      },\n                      children: \"HALL OF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 576,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        textShadow: ['0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)', '0 0 50px rgba(255,215,0,1), 0 0 80px rgba(255,215,0,0.8)', '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)']\n                      },\n                      transition: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      style: {\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '4px 4px 8px rgba(0,0,0,0.9)'\n                      },\n                      children: \"CHAMPIONS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 596,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 0.8\n                  },\n                  className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-semibold mb-6 md:mb-8 max-w-4xl mx-auto leading-relaxed px-4\",\n                  style: {\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  },\n                  children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: 1,\n                    duration: 0.8\n                  },\n                  className: \"relative max-w-2xl mx-auto mb-8\",\n                  style: {\n                    background: 'linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05))',\n                    backdropFilter: 'blur(20px)',\n                    borderRadius: '20px',\n                    padding: '24px',\n                    border: '2px solid rgba(255,255,255,0.2)',\n                    boxShadow: '0 8px 32px rgba(0,0,0,0.3)'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-yellow-500/10 rounded-2xl\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                    initial: {\n                      opacity: 0,\n                      y: 10\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    className: \"text-lg sm:text-xl font-semibold relative z-10\",\n                    style: {\n                      color: '#FBBF24',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      textAlign: 'center'\n                    },\n                    children: motivationalQuote\n                  }, motivationalQuote, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 30\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 1.2,\n                    duration: 0.8\n                  },\n                  className: \"grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto\",\n                  children: [{\n                    icon: TbTrophy,\n                    label: 'Total Champions',\n                    value: rankingData.length,\n                    iconColor: '#FFD700',\n                    bgGradient: 'from-yellow-500/20 to-amber-600/20',\n                    borderColor: '#FFD700'\n                  }, {\n                    icon: TbFlame,\n                    label: 'Active Streaks',\n                    value: rankingData.filter(u => u.currentStreak > 0).length,\n                    iconColor: '#FF6B35',\n                    bgGradient: 'from-orange-500/20 to-red-600/20',\n                    borderColor: '#FF6B35'\n                  }, {\n                    icon: TbBrain,\n                    label: 'Quizzes Taken',\n                    value: rankingData.reduce((sum, u) => sum + u.totalQuizzesTaken, 0),\n                    iconColor: '#3B82F6',\n                    bgGradient: 'from-blue-500/20 to-indigo-600/20',\n                    borderColor: '#3B82F6'\n                  }, {\n                    icon: TbBolt,\n                    label: 'Total XP',\n                    value: rankingData.reduce((sum, u) => sum + u.totalXP, 0).toLocaleString(),\n                    iconColor: '#8B5CF6',\n                    bgGradient: 'from-purple-500/20 to-violet-600/20',\n                    borderColor: '#8B5CF6'\n                  }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    whileHover: {\n                      scale: 1.08,\n                      y: -8\n                    },\n                    className: `bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                    style: {\n                      border: `2px solid ${stat.borderColor}40`,\n                      boxShadow: `0 8px 32px ${stat.borderColor}20`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 718,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                      className: \"w-8 h-8 mx-auto mb-2 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 719,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-2xl sm:text-3xl font-black mb-1 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                        filter: 'drop-shadow(0 0 10px currentColor)',\n                        fontSize: '2.5rem'\n                      },\n                      children: stat.value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 723,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-bold relative z-10\",\n                      style: {\n                        color: '#FFFFFF',\n                        textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                        fontSize: '1rem'\n                      },\n                      children: stat.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 734,\n                      columnNumber: 23\n                    }, this)]\n                  }, stat.label, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 709,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 1.5,\n            duration: 0.8\n          },\n          className: \"px-4 sm:px-6 lg:px-8 py-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-3 md:gap-4 items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: handleFindMe,\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-8 py-3 md:py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-black rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                    color: '#000000',\n                    textShadow: 'none',\n                    fontWeight: '900',\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 777,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: currentUserRank ? `Find Me #${currentUserRank}` : 'Find Me'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 778,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05,\n                    rotate: 180\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: fetchRankingData,\n                  disabled: loading,\n                  className: \"flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50\",\n                  style: {\n                    fontSize: '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                    className: `w-6 h-6 ${loading ? 'animate-spin' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 795,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 784,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 753,\n          columnNumber: 9\n        }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          className: \"flex flex-col items-center justify-center py-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: 360\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white/80 text-lg font-medium\",\n            children: \"Loading champions...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.3,\n            duration: 0.8\n          },\n          className: \"px-4 sm:px-6 lg:px-8 pb-20\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 0.5,\n                duration: 0.8\n              },\n              className: \"mb-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl font-black text-center mb-6 md:mb-8 px-4\",\n                style: {\n                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  filter: 'drop-shadow(0 0 15px #FFD700)'\n                },\n                children: \"\\uD83C\\uDFC6 CHAMPIONS PODIUM \\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 836,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-6 max-w-5xl mx-auto px-4\",\n                children: topPerformers.map((champion, index) => {\n                  const position = index + 1;\n                  const isCurrentUser = user && champion._id === user._id;\n                  return /*#__PURE__*/_jsxDEV(motion.div, {\n                    ref: isCurrentUser ? currentUserRef : null,\n                    initial: {\n                      opacity: 0,\n                      y: 50\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    transition: {\n                      delay: 0.7 + index * 0.2,\n                      duration: 0.8\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -10\n                    },\n                    className: `relative ${position === 1 ? 'md:order-2 md:scale-110' : position === 2 ? 'md:order-1' : 'md:order-3'} ${isCurrentUser ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow} ${champion.tier.effect} shadow-xl`,\n                      style: {\n                        boxShadow: `0 12px 24px ${champion.tier.shadowColor}, 0 0 30px ${champion.tier.shadowColor}`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                        style: {\n                          border: `2px solid ${champion.tier.borderColor}60`\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 877,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20`,\n                          style: {\n                            color: position === 1 ? '#FFD700' : position === 2 ? '#C0C0C0' : position === 3 ? '#CD7F32' : '#FFFFFF',\n                            textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                            border: `3px solid ${champion.tier.borderColor}`,\n                            boxShadow: `0 8px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                            fontSize: '1.2rem',\n                            fontWeight: '900'\n                          },\n                          children: position === 1 ? '👑' : position === 2 ? '🥈' : position === 3 ? '🥉' : position\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 880,\n                          columnNumber: 31\n                        }, this), position === 1 && /*#__PURE__*/_jsxDEV(motion.div, {\n                          animate: {\n                            rotate: [0, 10, -10, 0]\n                          },\n                          transition: {\n                            duration: 2,\n                            repeat: Infinity\n                          },\n                          className: \"absolute -top-8 left-1/2 transform -translate-x-1/2\",\n                          children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                            className: \"w-8 h-8 text-yellow-400\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 901,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 896,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `relative mx-auto mb-3 ${isCurrentUser ? 'ring-2 ring-yellow-400 ring-opacity-80' : ''}`,\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-14 h-14 rounded-full overflow-hidden mx-auto relative\",\n                            style: {\n                              background: `linear-gradient(45deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                              boxShadow: `0 4px 8px ${champion.tier.shadowColor}, 0 0 12px ${champion.tier.shadowColor}`,\n                              padding: '2px'\n                            },\n                            children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                              src: champion.profilePicture,\n                              alt: champion.name,\n                              className: \"w-full h-full object-cover rounded-full\",\n                              style: {\n                                filter: 'brightness(1.1) contrast(1.1)',\n                                aspectRatio: '1/1'\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 916,\n                              columnNumber: 37\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"w-full h-full rounded-full flex items-center justify-center font-black text-2xl\",\n                              style: {\n                                background: `linear-gradient(135deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                aspectRatio: '1/1'\n                              },\n                              children: champion.name.charAt(0).toUpperCase()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 926,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 907,\n                            columnNumber: 33\n                          }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\",\n                            style: {\n                              background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                              boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                            },\n                            children: /*#__PURE__*/_jsxDEV(TbStar, {\n                              className: \"w-5 h-5 text-black\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 947,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 940,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 906,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-xl font-black mb-2 relative z-10\",\n                          style: {\n                            color: champion.tier.nameColor,\n                            textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                            fontSize: '1.5rem',\n                            filter: 'drop-shadow(0 0 10px currentColor)'\n                          },\n                          children: champion.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 953,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `inline-flex items-center gap-2 px-5 py-3 bg-gradient-to-r ${champion.tier.color} rounded-full text-sm font-black mb-3 relative z-10 animate-pulse`,\n                          style: {\n                            color: '#FFFFFF',\n                            textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                            border: `3px solid ${champion.tier.borderColor}`,\n                            boxShadow: `0 6px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                            fontSize: '0.9rem',\n                            letterSpacing: '0.5px'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                            className: \"w-6 h-6\",\n                            style: {\n                              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))',\n                              color: champion.tier.textColor\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 975,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#FFFFFF'\n                            },\n                            children: champion.tier.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 982,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 964,\n                          columnNumber: 31\n                        }, this), (() => {\n                          const badge = getSubscriptionBadge(champion.subscriptionStatus, champion.subscriptionEndDate, champion.subscriptionPlan, champion.activePlanTitle, index);\n                          return /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-bold mb-3 relative z-10\",\n                            style: {\n                              backgroundColor: badge.bgColor,\n                              color: badge.color,\n                              border: `2px solid ${badge.borderColor}`,\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                              fontSize: '0.75rem',\n                              letterSpacing: '0.5px'\n                            },\n                            children: badge.text\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 995,\n                            columnNumber: 35\n                          }, this);\n                        })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"space-y-3 relative z-10\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex justify-between text-base\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.textColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '700',\n                                fontSize: '1rem'\n                              },\n                              children: \"\\uD83D\\uDC8E XP:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1014,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '900',\n                                fontSize: '1.1rem',\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              },\n                              children: champion.totalXP.toLocaleString()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1020,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1013,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex justify-between text-base\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.textColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '700',\n                                fontSize: '1rem'\n                              },\n                              children: \"\\uD83E\\uDDE0 Quizzes:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1029,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '900',\n                                fontSize: '1.1rem',\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              },\n                              children: champion.totalQuizzesTaken\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1035,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1028,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex justify-between text-base\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.textColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '700',\n                                fontSize: '1rem'\n                              },\n                              children: \"\\uD83D\\uDD25 Streak:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1044,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '900',\n                                fontSize: '1.1rem',\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              },\n                              className: \"flex items-center gap-1\",\n                              children: champion.currentStreak\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1050,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1043,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center mt-2\",\n                            children: /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: champion.tier.textColor,\n                                textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                fontWeight: '600',\n                                fontSize: '0.7rem',\n                                opacity: 0.8\n                              },\n                              children: champion.dataSource === 'reports' ? '📊 Live Data' : champion.dataSource === 'legacy_points' ? '📈 Legacy Points' : '🔮 Estimated'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1063,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1062,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1012,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 871,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 865,\n                      columnNumber: 27\n                    }, this)\n                  }, champion._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 852,\n                    columnNumber: 25\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 17\n            }, this), otherPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl sm:text-2xl md:text-3xl font-black text-center mb-6 md:mb-8 px-4\",\n                style: {\n                  background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                },\n                children: \"\\u26A1 RISING CHAMPIONS \\u26A1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1093,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3 md:space-y-4 px-2 md:px-0\",\n                children: otherPerformers.map((champion, index) => {\n                  const actualRank = index + 4; // Since top 3 are shown separately\n                  const isCurrentUser = user && champion._id === user._id;\n                  return /*#__PURE__*/_jsxDEV(motion.div, {\n                    ref: isCurrentUser ? currentUserRef : null,\n                    initial: {\n                      opacity: 0,\n                      x: -50\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0\n                    },\n                    transition: {\n                      delay: 1.2 + index * 0.1,\n                      duration: 0.6\n                    },\n                    whileHover: {\n                      scale: 1.02,\n                      x: 10\n                    },\n                    className: `relative ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow} ${champion.tier.effect}`,\n                      style: {\n                        boxShadow: `0 8px 24px ${champion.tier.shadowColor}`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-3 md:p-4 flex items-center gap-3 md:gap-4 relative overflow-hidden`,\n                        style: {\n                          border: `1px solid ${champion.tier.borderColor}40`\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute inset-0 bg-gradient-to-r from-white/5 to-transparent\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1130,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `flex-shrink-0 w-12 h-12 md:w-14 md:h-14 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-lg md:text-xl shadow-lg relative z-10`,\n                          style: {\n                            color: actualRank <= 10 ? champion.tier.textColor : '#FFFFFF',\n                            textShadow: '3px 3px 6px rgba(0,0,0,0.9)',\n                            border: `3px solid ${champion.tier.borderColor}`,\n                            boxShadow: `0 8px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                            fontSize: actualRank <= 10 ? '1.3rem' : '1.1rem',\n                            fontWeight: '900'\n                          },\n                          children: actualRank <= 10 ? `#${actualRank}` : actualRank\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1133,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 relative\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-12 h-12 rounded-full overflow-hidden p-0.5 relative\",\n                            style: {\n                              background: `linear-gradient(45deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                              boxShadow: `0 4px 8px ${champion.tier.shadowColor}, 0 0 10px ${champion.tier.shadowColor}`\n                            },\n                            children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                              src: champion.profilePicture,\n                              alt: champion.name,\n                              className: \"w-full h-full object-cover rounded-full\",\n                              style: {\n                                filter: 'brightness(1.1) contrast(1.1) saturate(1.2)',\n                                aspectRatio: '1/1'\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1157,\n                              columnNumber: 37\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"w-full h-full rounded-full flex items-center justify-center font-black text-lg\",\n                              style: {\n                                background: `linear-gradient(135deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                aspectRatio: '1/1'\n                              },\n                              children: champion.name.charAt(0).toUpperCase()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1167,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1149,\n                            columnNumber: 33\n                          }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute -top-1 -right-1 rounded-full p-1 animate-pulse\",\n                            style: {\n                              background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                              boxShadow: '0 2px 8px rgba(255,215,0,0.6)'\n                            },\n                            children: /*#__PURE__*/_jsxDEV(TbStar, {\n                              className: \"w-3 h-3 text-black\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1188,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1181,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1148,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 min-w-0 relative z-10\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center gap-2 mb-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                              className: \"text-lg font-black truncate\",\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontSize: '1.25rem',\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              },\n                              children: champion.name\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1196,\n                              columnNumber: 35\n                            }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"px-3 py-1 rounded-full text-xs font-black animate-pulse\",\n                              style: {\n                                background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                color: '#000000',\n                                textShadow: 'none',\n                                border: '2px solid #FFFFFF',\n                                boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                              },\n                              children: \"\\u2B50 YOU \\u2B50\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1208,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1195,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${champion.tier.color} rounded-full text-xs font-black`,\n                            style: {\n                              color: '#FFFFFF',\n                              textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                              border: `2px solid ${champion.tier.borderColor}`,\n                              boxShadow: `0 4px 8px ${champion.tier.shadowColor}, 0 0 12px ${champion.tier.shadowColor}`,\n                              fontSize: '0.8rem',\n                              letterSpacing: '0.3px'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                              className: \"w-4 h-4\",\n                              style: {\n                                filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.5))',\n                                color: champion.tier.textColor\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1233,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: '#FFFFFF'\n                              },\n                              children: champion.tier.title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1240,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1222,\n                            columnNumber: 33\n                          }, this), (() => {\n                            const badge = getSubscriptionBadge(champion.subscriptionStatus, champion.subscriptionEndDate, champion.subscriptionPlan, champion.activePlanTitle, actualRank);\n                            return /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold mt-1\",\n                              style: {\n                                backgroundColor: badge.bgColor,\n                                color: badge.color,\n                                border: `1px solid ${badge.borderColor}`,\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                                fontSize: '0.7rem',\n                                letterSpacing: '0.3px'\n                              },\n                              children: badge.text\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1253,\n                              columnNumber: 37\n                            }, this);\n                          })()]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1194,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 text-right relative z-10\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xl mb-2\",\n                            style: {\n                              color: champion.tier.nameColor,\n                              textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                              fontWeight: '900',\n                              filter: 'drop-shadow(0 0 10px currentColor)',\n                              fontSize: '1.3rem'\n                            },\n                            children: [\"\\uD83D\\uDC8E \", champion.totalXP.toLocaleString(), \" XP\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1272,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center gap-3 text-sm justify-end\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"flex items-center gap-1\",\n                              style: {\n                                color: champion.tier.textColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '700',\n                                fontSize: '0.9rem'\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: champion.tier.textColor\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1288,\n                                columnNumber: 37\n                              }, this), champion.totalQuizzesTaken]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1282,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"flex items-center gap-1\",\n                              style: {\n                                color: champion.tier.textColor,\n                                textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                fontWeight: '700',\n                                fontSize: '0.9rem'\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: '#FF6B35'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1297,\n                                columnNumber: 37\n                              }, this), champion.currentStreak]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1291,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1281,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1271,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1124,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1118,\n                      columnNumber: 27\n                    }, this)\n                  }, champion._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1109,\n                    columnNumber: 25\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1103,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1087,\n              columnNumber: 17\n            }, this), rankingData.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1.8,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold mb-4\",\n                  style: {\n                    color: '#60A5FA',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"\\uD83D\\uDCCA Real User Data Integration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1320,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'reports').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1327,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCCA Live Quiz Data\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1330,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1326,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'legacy_points').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1333,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCC8 Legacy Points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1336,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1332,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'estimated').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1339,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDD2E Estimated Stats\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1342,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1338,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1325,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm mt-4\",\n                  children: \"Using real database users (admins excluded) with intelligent data processing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1345,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1319,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1313,\n              columnNumber: 17\n            }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 1.5,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold mb-2\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Your Current Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1361,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl font-black mb-2\",\n                  style: {\n                    color: '#fbbf24',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    fontWeight: '900'\n                  },\n                  children: [\"#\", currentUserRank]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1366,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1371,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1360,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1354,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 2,\n                duration: 0.8\n              },\n              className: \"mt-16 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.05, 1]\n                  },\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                    className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1394,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1390,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-3xl font-bold mb-4\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Ready to Rise Higher?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1396,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl mb-6 max-w-2xl mx-auto\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1401,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                  onClick: () => window.location.href = '/user/quiz',\n                  children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1409,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1389,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1383,\n              columnNumber: 15\n            }, this), rankingData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-20\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1427,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"No Champions Yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1428,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Be the first to take a quiz and claim your spot in the Hall of Champions!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1433,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1422,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 820,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AmazingRankingPage, \"E8ll/bLiGD01L4wq4q4H2p2Oohc=\", false, function () {\n  return [useSelector];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbTarget", "TbBrain", "TbRefresh", "TbMedal", "TbBolt", "TbRocket", "TbDiamond", "TbHeart", "TbEye", "TbTrendingUp", "TbAward", "TbShield", "getAllReportsForRanking", "getXPLeaderboard", "getUserRanking", "getAllUsers", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AmazingRankingPage", "_s", "userState", "state", "users", "user", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "showFindMe", "setShowFindMe", "headerRef", "currentUserRef", "motivationalQuotes", "performanceTiers", "legendary", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "effect", "diamond", "platinum", "gold", "silver", "bronze", "getUserTier", "xp", "tier", "config", "Object", "entries", "fetchRankingData", "console", "log", "xpLeaderboardResponse", "limit", "levelFilter", "level", "includeInactive", "success", "data", "transformedData", "map", "userData", "index", "_id", "name", "email", "class", "profilePicture", "profileImage", "totalXP", "totalQuizzesTaken", "averageScore", "currentStreak", "bestStreak", "subscriptionStatus", "rank", "isRealUser", "rankingScore", "currentLevel", "xpToNextLevel", "lifetimeXP", "seasonXP", "achievements", "dataSource", "userRankIndex", "findIndex", "item", "xpError", "rankingResponse", "usersResponse", "error", "userError", "userReportsMap", "for<PERSON>ach", "_item$user", "userId", "reports", "filter", "role", "userReports", "totalQuizzes", "length", "totalScore", "reduce", "sum", "report", "score", "Math", "round", "totalPoints", "estimatedQuizzes", "max", "floor", "estimatedAverage", "tempStreak", "pointsPerQuiz", "originalPoints", "hasReports", "sort", "a", "b", "userRank", "dataSources", "u", "legacy_points", "estimated", "slice", "quizzes", "avg", "source", "warning", "randomQuote", "random", "animationTimer", "setInterval", "prev", "clearInterval", "topPerformers", "otherPerformers", "handleFindMe", "current", "scrollIntoView", "behavior", "block", "setTimeout", "userInRanking", "find", "indexOf", "info", "getSubscriptionBadge", "subscriptionEndDate", "subscriptionPlan", "activePlanTitle", "userIndex", "now", "Date", "endDate", "isActive", "text", "className", "children", "div", "initial", "opacity", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jsx", "Array", "_", "i", "y", "x", "delay", "style", "left", "top", "ref", "scale", "rotateY", "span", "backgroundPosition", "backgroundSize", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "fontWeight", "p", "background", "<PERSON><PERSON>ilter", "borderRadius", "padding", "border", "boxShadow", "textAlign", "label", "value", "iconColor", "bgGradient", "toLocaleString", "stat", "whileHover", "fontSize", "button", "whileTap", "onClick", "window", "innerWidth", "disabled", "champion", "position", "isCurrentUser", "src", "alt", "aspectRatio", "char<PERSON>t", "toUpperCase", "letterSpacing", "badge", "backgroundColor", "actualRank", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbTarget,\n  TbBrain,\n\n  TbRefresh,\n  TbMedal,\n  TbBolt,\n  TbRocket,\n  TbDiamond,\n  TbHeart,\n  TbEye,\n  TbTrendingUp,\n  TbAward,\n  TbShield\n} from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const user = userState.user || null;\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Performance tiers with SPECTACULAR visual themes and unique colors\n  const performanceTiers = {\n    legendary: {\n      min: 10000,\n      color: 'from-purple-400 via-pink-400 via-red-400 to-orange-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-pink-900/40 to-red-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FF69B4',\n      shadowColor: 'rgba(147, 51, 234, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery',\n      borderColor: '#8B5CF6',\n      effect: 'legendary-sparkle'\n    },\n    diamond: {\n      min: 7500,\n      color: 'from-cyan-200 via-blue-300 via-indigo-400 to-purple-500',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00E5FF',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 229, 255, 0.9)',\n      glow: 'shadow-cyan-300/80',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance',\n      borderColor: '#00E5FF',\n      effect: 'diamond-shine'\n    },\n    platinum: {\n      min: 5000,\n      color: 'from-slate-200 via-gray-300 via-zinc-400 to-stone-500',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#E8E8E8',\n      nameColor: '#C0C0C0',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-300/80',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam'\n    },\n    gold: {\n      min: 2500,\n      color: 'from-yellow-200 via-amber-300 via-orange-400 to-red-500',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-300/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent',\n      borderColor: '#FFD700',\n      effect: 'gold-glow'\n    },\n    silver: {\n      min: 1000,\n      color: 'from-gray-200 via-slate-300 via-zinc-400 to-gray-500',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#D3D3D3',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(211, 211, 211, 0.9)',\n      glow: 'shadow-gray-300/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress',\n      borderColor: '#D3D3D3',\n      effect: 'silver-shimmer'\n    },\n    bronze: {\n      min: 0,\n      color: 'from-orange-200 via-amber-300 via-yellow-400 to-orange-500',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-300/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = (xp) => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return { tier, ...config };\n    }\n    return { tier: 'bronze', ...performanceTiers.bronze };\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: user?.level || 'all',\n          includeInactive: false\n        });\n\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          const transformedData = xpLeaderboardResponse.data.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === user?._id);\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n\n      let rankingResponse, usersResponse;\n\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Use existing user data or calculate from reports\n            let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n            let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n            // For existing users with old data, make intelligent assumptions\n            if (!userReports.length && userData.totalPoints) {\n              // Assume higher points = more exams and better performance\n              const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n              const estimatedAverage = Math.min(95, Math.max(60, 60 + (userData.totalPoints / estimatedQuizzes / 10))); // Scale average based on points\n\n              totalQuizzes = estimatedQuizzes;\n              averageScore = Math.round(estimatedAverage);\n              totalScore = Math.round(averageScore * totalQuizzes);\n\n              console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n            }\n\n            // Calculate XP based on performance (enhanced calculation)\n            let totalXP = userData.totalXP || 0;\n\n            if (!totalXP) {\n              // Calculate XP from available data\n              if (userData.totalPoints) {\n                // Use existing points as base XP with bonuses\n                totalXP = Math.floor(\n                  userData.totalPoints + // Base points\n                  (totalQuizzes * 25) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 15 : 0) + // Excellence bonus\n                  (averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n                );\n              } else if (totalQuizzes > 0) {\n                // Calculate from quiz performance\n                totalXP = Math.floor(\n                  (averageScore * totalQuizzes * 8) + // Base XP from scores\n                  (totalQuizzes * 40) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n                );\n              }\n            }\n\n            // Calculate streaks (enhanced logic)\n            let currentStreak = userData.currentStreak || 0;\n            let bestStreak = userData.bestStreak || 0;\n\n            if (userReports.length > 0) {\n              // Calculate from actual reports\n              let tempStreak = 0;\n              userReports.forEach(report => {\n                if (report.score >= 60) { // Passing score\n                  tempStreak++;\n                  bestStreak = Math.max(bestStreak, tempStreak);\n                } else {\n                  tempStreak = 0;\n                }\n              });\n              currentStreak = tempStreak;\n            } else if (userData.totalPoints && !currentStreak) {\n              // Estimate streaks from points (higher points = likely better streaks)\n              const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n              if (pointsPerQuiz > 80) {\n                currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n                bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n              }\n            }\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserTier(totalXP),\n              isRealUser: true,\n              // Additional tracking fields for future updates\n              originalPoints: userData.totalPoints || 0,\n              hasReports: userReports.length > 0,\n              dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank\n        const userRank = user ? transformedData.findIndex(item => item._id === user._id) : -1;\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n    \n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    return () => clearInterval(animationTimer);\n  }, []);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Find Me functionality\n  const handleFindMe = () => {\n    if (currentUserRef.current) {\n      setShowFindMe(true);\n      currentUserRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n\n      // Hide the find me highlight after 3 seconds\n      setTimeout(() => {\n        setShowFindMe(false);\n      }, 3000);\n    } else {\n      // If ref is not found, try to find user in the ranking data and scroll to them\n      const userInRanking = rankingData.find(u => u._id === user?._id);\n      if (userInRanking) {\n        const userRank = rankingData.indexOf(userInRanking) + 1;\n        message.info(`You are ranked #${userRank} in the leaderboard!`);\n      } else {\n        message.info('You are not yet ranked. Take some quizzes to appear on the leaderboard!');\n      }\n    }\n  };\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981', // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444', // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444', // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center\"\n        >\n          <motion.div\n            animate={{ rotate: 360 }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n          />\n          <p className=\"text-white/80 text-lg font-medium\">Loading the Hall of Champions...</p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <style jsx>{`\n        .find-me-highlight {\n          animation: findMePulse 2s ease-in-out infinite;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);\n          }\n          50% {\n            box-shadow: 0 0 0 20px rgba(255, 215, 0, 0);\n          }\n        }\n      `}</style>\n      <div className=\"ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* SPECTACULAR HEADER */}\n        <motion.div\n          ref={headerRef}\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden\"\n        >\n          {/* Header Background with SPECTACULAR Gradient */}\n          <div className=\"bg-gradient-to-br from-purple-600 via-pink-500 via-red-500 via-orange-500 to-yellow-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n            \n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-8 sm:py-12 md:py-16 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n                \n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-8\"\n                >\n                  <h1 className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-8xl font-black mb-3 md:mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(3px 3px 6px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)',\n                          '0 0 50px rgba(255,215,0,1), 0 0 80px rgba(255,215,0,0.8)',\n                          '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '4px 4px 8px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-semibold mb-6 md:mb-8 max-w-4xl mx-auto leading-relaxed px-4\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"relative max-w-2xl mx-auto mb-8\"\n                  style={{\n                    background: 'linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05))',\n                    backdropFilter: 'blur(20px)',\n                    borderRadius: '20px',\n                    padding: '24px',\n                    border: '2px solid rgba(255,255,255,0.2)',\n                    boxShadow: '0 8px 32px rgba(0,0,0,0.3)'\n                  }}\n                >\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-yellow-500/10 rounded-2xl\"></div>\n                  <motion.p\n                    key={motivationalQuote}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"text-lg sm:text-xl font-semibold relative z-10\"\n                    style={{\n                      color: '#FBBF24',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      textAlign: 'center'\n                    }}\n                  >\n                    {motivationalQuote}\n                  </motion.p>\n                </motion.div>\n\n                {/* Stats Overview */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.2, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbTrophy,\n                      label: 'Total Champions',\n                      value: rankingData.length,\n                      iconColor: '#FFD700',\n                      bgGradient: 'from-yellow-500/20 to-amber-600/20',\n                      borderColor: '#FFD700'\n                    },\n                    {\n                      icon: TbFlame,\n                      label: 'Active Streaks',\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      iconColor: '#FF6B35',\n                      bgGradient: 'from-orange-500/20 to-red-600/20',\n                      borderColor: '#FF6B35'\n                    },\n                    {\n                      icon: TbBrain,\n                      label: 'Quizzes Taken',\n                      value: rankingData.reduce((sum, u) => sum + u.totalQuizzesTaken, 0),\n                      iconColor: '#3B82F6',\n                      bgGradient: 'from-blue-500/20 to-indigo-600/20',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbBolt,\n                      label: 'Total XP',\n                      value: rankingData.reduce((sum, u) => sum + u.totalXP, 0).toLocaleString(),\n                      iconColor: '#8B5CF6',\n                      bgGradient: 'from-purple-500/20 to-violet-600/20',\n                      borderColor: '#8B5CF6'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={stat.label}\n                      whileHover={{ scale: 1.08, y: -8 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-8 h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-2xl sm:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: stat.iconColor,\n                          textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          fontSize: '2.5rem'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-sm font-bold relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          fontSize: '1rem'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* INTERACTIVE CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1.5, duration: 0.8 }}\n          className=\"px-4 sm:px-6 lg:px-8 py-8\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10\">\n              <div className=\"flex flex-col sm:flex-row gap-3 md:gap-4 items-center justify-center\">\n\n                {/* Find Me Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={handleFindMe}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-8 py-3 md:py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-black rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                    color: '#000000',\n                    textShadow: 'none',\n                    fontWeight: '900',\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbTarget className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>\n                    {currentUserRank ? `Find Me #${currentUserRank}` : 'Find Me'}\n                  </span>\n                </motion.button>\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50\"\n                  style={{\n                    fontSize: '1.1rem'\n                  }}\n                >\n                  <TbRefresh className={`w-6 h-6 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 lg:px-8 pb-20\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-black text-center mb-6 md:mb-8 px-4\" style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 15px #FFD700)'\n                  }}>\n                    🏆 CHAMPIONS PODIUM 🏆\n                  </h2>\n\n                  <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-6 max-w-5xl mx-auto px-4\">\n                    {topPerformers.map((champion, index) => {\n                      const position = index + 1;\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          ref={isCurrentUser ? currentUserRef : null}\n                          initial={{ opacity: 0, y: 50 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: 0.7 + index * 0.2, duration: 0.8 }}\n                          whileHover={{ scale: 1.05, y: -10 }}\n                          className={`relative ${\n                            position === 1 ? 'md:order-2 md:scale-110' :\n                            position === 2 ? 'md:order-1' : 'md:order-3'\n                          } ${isCurrentUser ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`}\n                        >\n                          {/* Compact Podium Card */}\n                          <div\n                            className={`relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow} ${champion.tier.effect} shadow-xl`}\n                            style={{\n                              boxShadow: `0 12px 24px ${champion.tier.shadowColor}, 0 0 30px ${champion.tier.shadowColor}`\n                            }}\n                          >\n                            <div\n                              className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                              style={{\n                                border: `2px solid ${champion.tier.borderColor}60`\n                              }}\n                            >\n                              <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent\"></div>\n\n                              {/* Compact Position Badge */}\n                              <div\n                                className={`absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20`}\n                                style={{\n                                  color: position === 1 ? '#FFD700' : position === 2 ? '#C0C0C0' : position === 3 ? '#CD7F32' : '#FFFFFF',\n                                  textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                                  border: `3px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 8px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                                  fontSize: '1.2rem',\n                                  fontWeight: '900'\n                                }}\n                              >\n                                {position === 1 ? '👑' : position === 2 ? '🥈' : position === 3 ? '🥉' : position}\n                              </div>\n\n                              {/* Crown for #1 */}\n                              {position === 1 && (\n                                <motion.div\n                                  animate={{ rotate: [0, 10, -10, 0] }}\n                                  transition={{ duration: 2, repeat: Infinity }}\n                                  className=\"absolute -top-8 left-1/2 transform -translate-x-1/2\"\n                                >\n                                  <TbCrown className=\"w-8 h-8 text-yellow-400\" />\n                                </motion.div>\n                              )}\n\n                              {/* WhatsApp-Style Profile Picture */}\n                              <div className={`relative mx-auto mb-3 ${isCurrentUser ? 'ring-2 ring-yellow-400 ring-opacity-80' : ''}`}>\n                                <div\n                                  className=\"w-14 h-14 rounded-full overflow-hidden mx-auto relative\"\n                                  style={{\n                                    background: `linear-gradient(45deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                    boxShadow: `0 4px 8px ${champion.tier.shadowColor}, 0 0 12px ${champion.tier.shadowColor}`,\n                                    padding: '2px'\n                                  }}\n                                >\n                                  {champion.profilePicture ? (\n                                    <img\n                                      src={champion.profilePicture}\n                                      alt={champion.name}\n                                      className=\"w-full h-full object-cover rounded-full\"\n                                      style={{\n                                        filter: 'brightness(1.1) contrast(1.1)',\n                                        aspectRatio: '1/1'\n                                      }}\n                                    />\n                                  ) : (\n                                    <div\n                                      className=\"w-full h-full rounded-full flex items-center justify-center font-black text-2xl\"\n                                      style={{\n                                        background: `linear-gradient(135deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                        color: '#FFFFFF',\n                                        textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                        aspectRatio: '1/1'\n                                      }}\n                                    >\n                                      {champion.name.charAt(0).toUpperCase()}\n                                    </div>\n                                  )}\n                                </div>\n                                {isCurrentUser && (\n                                  <div\n                                    className=\"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\"\n                                    style={{\n                                      background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                      boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                                    }}\n                                  >\n                                    <TbStar className=\"w-5 h-5 text-black\" />\n                                  </div>\n                                )}\n                              </div>\n\n                              {/* Champion Info */}\n                              <h3\n                                className=\"text-xl font-black mb-2 relative z-10\"\n                                style={{\n                                  color: champion.tier.nameColor,\n                                  textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                                  fontSize: '1.5rem',\n                                  filter: 'drop-shadow(0 0 10px currentColor)'\n                                }}\n                              >\n                                {champion.name}\n                              </h3>\n                              <div\n                                className={`inline-flex items-center gap-2 px-5 py-3 bg-gradient-to-r ${champion.tier.color} rounded-full text-sm font-black mb-3 relative z-10 animate-pulse`}\n                                style={{\n                                  color: '#FFFFFF',\n                                  textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                                  border: `3px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 6px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                                  fontSize: '0.9rem',\n                                  letterSpacing: '0.5px'\n                                }}\n                              >\n                                <champion.tier.icon\n                                  className=\"w-6 h-6\"\n                                  style={{\n                                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))',\n                                    color: champion.tier.textColor\n                                  }}\n                                />\n                                <span style={{ color: '#FFFFFF' }}>{champion.tier.title}</span>\n                              </div>\n\n                              {/* Subscription Status Badge */}\n                              {(() => {\n                                const badge = getSubscriptionBadge(\n                                  champion.subscriptionStatus,\n                                  champion.subscriptionEndDate,\n                                  champion.subscriptionPlan,\n                                  champion.activePlanTitle,\n                                  index\n                                );\n                                return (\n                                  <div\n                                    className=\"inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-bold mb-3 relative z-10\"\n                                    style={{\n                                      backgroundColor: badge.bgColor,\n                                      color: badge.color,\n                                      border: `2px solid ${badge.borderColor}`,\n                                      textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                                      fontSize: '0.75rem',\n                                      letterSpacing: '0.5px'\n                                    }}\n                                  >\n                                    {badge.text}\n                                  </div>\n                                );\n                              })()}\n\n                              {/* Enhanced Stats */}\n                              <div className=\"space-y-3 relative z-10\">\n                                <div className=\"flex justify-between text-base\">\n                                  <span style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '1rem'\n                                  }}>💎 XP:</span>\n                                  <span style={{\n                                    color: champion.tier.nameColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '900',\n                                    fontSize: '1.1rem',\n                                    filter: 'drop-shadow(0 0 8px currentColor)'\n                                  }}>{champion.totalXP.toLocaleString()}</span>\n                                </div>\n                                <div className=\"flex justify-between text-base\">\n                                  <span style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '1rem'\n                                  }}>🧠 Quizzes:</span>\n                                  <span style={{\n                                    color: champion.tier.nameColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '900',\n                                    fontSize: '1.1rem',\n                                    filter: 'drop-shadow(0 0 8px currentColor)'\n                                  }}>{champion.totalQuizzesTaken}</span>\n                                </div>\n                                <div className=\"flex justify-between text-base\">\n                                  <span style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '1rem'\n                                  }}>🔥 Streak:</span>\n                                  <span style={{\n                                    color: champion.tier.nameColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '900',\n                                    fontSize: '1.1rem',\n                                    filter: 'drop-shadow(0 0 8px currentColor)'\n                                  }} className=\"flex items-center gap-1\">\n                                    {champion.currentStreak}\n                                  </span>\n                                </div>\n\n                                {/* Data Source Indicator */}\n                                <div className=\"text-center mt-2\">\n                                  <span style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    fontWeight: '600',\n                                    fontSize: '0.7rem',\n                                    opacity: 0.8\n                                  }}>\n                                    {champion.dataSource === 'reports' ? '📊 Live Data' :\n                                     champion.dataSource === 'legacy_points' ? '📈 Legacy Points' :\n                                     '🔮 Estimated'}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* OTHER CHAMPIONS LIST */}\n              {otherPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"mt-12\"\n                >\n                  <h2 className=\"text-xl sm:text-2xl md:text-3xl font-black text-center mb-6 md:mb-8 px-4\" style={{\n                    background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                  }}>\n                    ⚡ RISING CHAMPIONS ⚡\n                  </h2>\n\n                  <div className=\"space-y-3 md:space-y-4 px-2 md:px-0\">\n                    {otherPerformers.map((champion, index) => {\n                      const actualRank = index + 4; // Since top 3 are shown separately\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          ref={isCurrentUser ? currentUserRef : null}\n                          initial={{ opacity: 0, x: -50 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                          whileHover={{ scale: 1.02, x: 10 }}\n                          className={`relative ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`}\n                        >\n                          <div\n                            className={`bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow} ${champion.tier.effect}`}\n                            style={{\n                              boxShadow: `0 8px 24px ${champion.tier.shadowColor}`\n                            }}\n                          >\n                            <div\n                              className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-3 md:p-4 flex items-center gap-3 md:gap-4 relative overflow-hidden`}\n                              style={{\n                                border: `1px solid ${champion.tier.borderColor}40`\n                              }}\n                            >\n                              <div className=\"absolute inset-0 bg-gradient-to-r from-white/5 to-transparent\"></div>\n\n                              {/* Enhanced Rank */}\n                              <div\n                                className={`flex-shrink-0 w-12 h-12 md:w-14 md:h-14 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-lg md:text-xl shadow-lg relative z-10`}\n                                style={{\n                                  color: actualRank <= 10 ? champion.tier.textColor : '#FFFFFF',\n                                  textShadow: '3px 3px 6px rgba(0,0,0,0.9)',\n                                  border: `3px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 8px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                                  fontSize: actualRank <= 10 ? '1.3rem' : '1.1rem',\n                                  fontWeight: '900'\n                                }}\n                              >\n                                {actualRank <= 10 ? `#${actualRank}` : actualRank}\n                              </div>\n\n                              {/* WhatsApp-Style Profile Picture */}\n                              <div className=\"flex-shrink-0 relative\">\n                                <div\n                                  className=\"w-12 h-12 rounded-full overflow-hidden p-0.5 relative\"\n                                  style={{\n                                    background: `linear-gradient(45deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                    boxShadow: `0 4px 8px ${champion.tier.shadowColor}, 0 0 10px ${champion.tier.shadowColor}`\n                                  }}\n                                >\n                                  {champion.profilePicture ? (\n                                    <img\n                                      src={champion.profilePicture}\n                                      alt={champion.name}\n                                      className=\"w-full h-full object-cover rounded-full\"\n                                      style={{\n                                        filter: 'brightness(1.1) contrast(1.1) saturate(1.2)',\n                                        aspectRatio: '1/1'\n                                      }}\n                                    />\n                                  ) : (\n                                    <div\n                                      className=\"w-full h-full rounded-full flex items-center justify-center font-black text-lg\"\n                                      style={{\n                                        background: `linear-gradient(135deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                        color: '#FFFFFF',\n                                        textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                        aspectRatio: '1/1'\n                                      }}\n                                    >\n                                      {champion.name.charAt(0).toUpperCase()}\n                                    </div>\n                                  )}\n                                </div>\n                                {isCurrentUser && (\n                                  <div\n                                    className=\"absolute -top-1 -right-1 rounded-full p-1 animate-pulse\"\n                                    style={{\n                                      background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                      boxShadow: '0 2px 8px rgba(255,215,0,0.6)'\n                                    }}\n                                  >\n                                    <TbStar className=\"w-3 h-3 text-black\" />\n                                  </div>\n                                )}\n                              </div>\n\n                              {/* Champion Info */}\n                              <div className=\"flex-1 min-w-0 relative z-10\">\n                                <div className=\"flex items-center gap-2 mb-1\">\n                                  <h3\n                                    className=\"text-lg font-black truncate\"\n                                    style={{\n                                      color: champion.tier.nameColor,\n                                      textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                      fontSize: '1.25rem',\n                                      filter: 'drop-shadow(0 0 8px currentColor)'\n                                    }}\n                                  >\n                                    {champion.name}\n                                  </h3>\n                                  {isCurrentUser && (\n                                    <div\n                                      className=\"px-3 py-1 rounded-full text-xs font-black animate-pulse\"\n                                      style={{\n                                        background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                        color: '#000000',\n                                        textShadow: 'none',\n                                        border: '2px solid #FFFFFF',\n                                        boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                                      }}\n                                    >\n                                      ⭐ YOU ⭐\n                                    </div>\n                                  )}\n                                </div>\n                                <div\n                                  className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${champion.tier.color} rounded-full text-xs font-black`}\n                                  style={{\n                                    color: '#FFFFFF',\n                                    textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                                    border: `2px solid ${champion.tier.borderColor}`,\n                                    boxShadow: `0 4px 8px ${champion.tier.shadowColor}, 0 0 12px ${champion.tier.shadowColor}`,\n                                    fontSize: '0.8rem',\n                                    letterSpacing: '0.3px'\n                                  }}\n                                >\n                                  <champion.tier.icon\n                                    className=\"w-4 h-4\"\n                                    style={{\n                                      filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.5))',\n                                      color: champion.tier.textColor\n                                    }}\n                                  />\n                                  <span style={{ color: '#FFFFFF' }}>{champion.tier.title}</span>\n                                </div>\n\n                                {/* Subscription Status Badge */}\n                                {(() => {\n                                  const badge = getSubscriptionBadge(\n                                    champion.subscriptionStatus,\n                                    champion.subscriptionEndDate,\n                                    champion.subscriptionPlan,\n                                    champion.activePlanTitle,\n                                    actualRank\n                                  );\n                                  return (\n                                    <div\n                                      className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold mt-1\"\n                                      style={{\n                                        backgroundColor: badge.bgColor,\n                                        color: badge.color,\n                                        border: `1px solid ${badge.borderColor}`,\n                                        textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                                        fontSize: '0.7rem',\n                                        letterSpacing: '0.3px'\n                                      }}\n                                    >\n                                      {badge.text}\n                                    </div>\n                                  );\n                                })()}\n                              </div>\n\n                              {/* Enhanced Stats */}\n                              <div className=\"flex-shrink-0 text-right relative z-10\">\n                                <div className=\"text-xl mb-2\" style={{\n                                  color: champion.tier.nameColor,\n                                  textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                                  fontWeight: '900',\n                                  filter: 'drop-shadow(0 0 10px currentColor)',\n                                  fontSize: '1.3rem'\n                                }}>\n                                  💎 {champion.totalXP.toLocaleString()} XP\n                                </div>\n                                <div className=\"flex items-center gap-3 text-sm justify-end\">\n                                  <span className=\"flex items-center gap-1\" style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '0.9rem'\n                                  }}>\n                                    <TbBrain className=\"w-4 h-4\" style={{ color: champion.tier.textColor }} />\n                                    {champion.totalQuizzesTaken}\n                                  </span>\n                                  <span className=\"flex items-center gap-1\" style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '0.9rem'\n                                  }}>\n                                    <TbFlame className=\"w-4 h-4\" style={{ color: '#FF6B35' }} />\n                                    {champion.currentStreak}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* DATA INTEGRATION STATUS */}\n              {rankingData.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.8, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-xl font-bold mb-4\" style={{\n                      color: '#60A5FA',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>📊 Real User Data Integration</h3>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\">\n                      <div className=\"bg-green-500/20 rounded-lg p-3\">\n                        <div className=\"text-green-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'reports').length}\n                        </div>\n                        <div className=\"text-white/80\">📊 Live Quiz Data</div>\n                      </div>\n                      <div className=\"bg-blue-500/20 rounded-lg p-3\">\n                        <div className=\"text-blue-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'legacy_points').length}\n                        </div>\n                        <div className=\"text-white/80\">📈 Legacy Points</div>\n                      </div>\n                      <div className=\"bg-purple-500/20 rounded-lg p-3\">\n                        <div className=\"text-purple-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'estimated').length}\n                        </div>\n                        <div className=\"text-white/80\">🔮 Estimated Stats</div>\n                      </div>\n                    </div>\n                    <p className=\"text-white/70 text-sm mt-4\">\n                      Using real database users (admins excluded) with intelligent data processing\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold mb-2\" style={{\n                      color: '#ffffff',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>Your Current Position</h3>\n                    <div className=\"text-6xl font-black mb-2\" style={{\n                      color: '#fbbf24',\n                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                      fontWeight: '900'\n                    }}>#{currentUserRank}</div>\n                    <p className=\"text-lg\" style={{\n                      color: '#e5e7eb',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                      fontWeight: '600'\n                    }}>\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>Ready to Rise Higher?</h3>\n                  <p className=\"text-xl mb-6 max-w-2xl mx-auto\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {rankingData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbTrophy className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Yet</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Be the first to take a quiz and claim your spot in the Hall of Champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,OAAO,EAEPC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,SAASC,uBAAuB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,2BAA2B;AACrG,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG5B,WAAW,CAAE6B,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,IAAI,GAAGH,SAAS,CAACG,IAAI,IAAI,IAAI;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACiD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMqD,SAAS,GAAGnD,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMoD,cAAc,GAAGpD,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMqD,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,gBAAgB,GAAG;IACvBC,SAAS,EAAE;MACTC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEzD,OAAO;MACb0D,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE;MACPZ,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEhD,SAAS;MACfiD,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,mBAAmB;MAChCC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDE,QAAQ,EAAE;MACRb,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE3C,QAAQ;MACd4C,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,aAAa;MAC1BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDG,IAAI,EAAE;MACJd,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE1D,QAAQ;MACd2D,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDI,MAAM,EAAE;MACNf,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEnD,OAAO;MACboD,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDK,MAAM,EAAE;MACNhB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAExD,MAAM;MACZyD,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,aAAa;MAC1BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV;EACF,CAAC;;EAED;EACA,MAAMM,WAAW,GAAIC,EAAE,IAAK;IAC1B,KAAK,MAAM,CAACC,IAAI,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACxB,gBAAgB,CAAC,EAAE;MAC7D,IAAIoB,EAAE,IAAIE,MAAM,CAACpB,GAAG,EAAE,OAAO;QAAEmB,IAAI;QAAE,GAAGC;MAAO,CAAC;IAClD;IACA,OAAO;MAAED,IAAI,EAAE,QAAQ;MAAE,GAAGrB,gBAAgB,CAACkB;IAAO,CAAC;EACvD,CAAC;;EAED;EACA,MAAMO,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFzC,UAAU,CAAC,IAAI,CAAC;MAChB0C,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;MAEtD;MACA,IAAI;QACFD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMC,qBAAqB,GAAG,MAAM5D,gBAAgB,CAAC;UACnD6D,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,KAAK,KAAI,KAAK;UACjCC,eAAe,EAAE;QACnB,CAAC,CAAC;QAEFN,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,qBAAqB,CAAC;QAEhE,IAAIA,qBAAqB,IAAIA,qBAAqB,CAACK,OAAO,IAAIL,qBAAqB,CAACM,IAAI,EAAE;UACxFR,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;UAEhD,MAAMQ,eAAe,GAAGP,qBAAqB,CAACM,IAAI,CAACE,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,MAAM;YAC3EC,GAAG,EAAEF,QAAQ,CAACE,GAAG;YACjBC,IAAI,EAAEH,QAAQ,CAACG,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAEJ,QAAQ,CAACI,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEL,QAAQ,CAACK,KAAK,IAAI,EAAE;YAC3BX,KAAK,EAAEM,QAAQ,CAACN,KAAK,IAAI,EAAE;YAC3BY,cAAc,EAAEN,QAAQ,CAACO,YAAY,IAAI,EAAE;YAC3CC,OAAO,EAAER,QAAQ,CAACQ,OAAO,IAAI,CAAC;YAC9BC,iBAAiB,EAAET,QAAQ,CAACS,iBAAiB,IAAI,CAAC;YAClDC,YAAY,EAAEV,QAAQ,CAACU,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAEX,QAAQ,CAACW,aAAa,IAAI,CAAC;YAC1CC,UAAU,EAAEZ,QAAQ,CAACY,UAAU,IAAI,CAAC;YACpCC,kBAAkB,EAAEb,QAAQ,CAACa,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEb,KAAK,GAAG,CAAC;YACfjB,IAAI,EAAEF,WAAW,CAACkB,QAAQ,CAACQ,OAAO,IAAI,CAAC,CAAC;YACxCO,UAAU,EAAE,IAAI;YAChBC,YAAY,EAAEhB,QAAQ,CAACgB,YAAY,IAAI,CAAC;YACxC;YACAC,YAAY,EAAEjB,QAAQ,CAACiB,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAElB,QAAQ,CAACkB,aAAa,IAAI,GAAG;YAC5CC,UAAU,EAAEnB,QAAQ,CAACmB,UAAU,IAAI,CAAC;YACpCC,QAAQ,EAAEpB,QAAQ,CAACoB,QAAQ,IAAI,CAAC;YAChCC,YAAY,EAAErB,QAAQ,CAACqB,YAAY,IAAI,EAAE;YACzCC,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;UAEH7E,cAAc,CAACqD,eAAe,CAAC;;UAE/B;UACA,MAAMyB,aAAa,GAAGzB,eAAe,CAAC0B,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACvB,GAAG,MAAK3D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,GAAG,EAAC;UAC/ErD,kBAAkB,CAAC0E,aAAa,IAAI,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC;UAEjE5E,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAO+E,OAAO,EAAE;QAChBrC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEoC,OAAO,CAAC;MACpE;;MAEA;MACArC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D,IAAIqC,eAAe,EAAEC,aAAa;MAElC,IAAI;QACFvC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDqC,eAAe,GAAG,MAAMjG,uBAAuB,CAAC,CAAC;QACjD2D,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCsC,aAAa,GAAG,MAAM/F,WAAW,CAAC,CAAC;MACrC,CAAC,CAAC,OAAOgG,KAAK,EAAE;QACdxC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuC,KAAK,CAAC;QACnD,IAAI;UACFD,aAAa,GAAG,MAAM/F,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC,OAAOiG,SAAS,EAAE;UAClBzC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEwC,SAAS,CAAC;QACpD;MACF;MAEA,IAAIhC,eAAe,GAAG,EAAE;MAExB,IAAI8B,aAAa,IAAIA,aAAa,CAAChC,OAAO,IAAIgC,aAAa,CAAC/B,IAAI,EAAE;QAChER,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;QAEhD;QACA,MAAMyC,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIJ,eAAe,IAAIA,eAAe,CAAC/B,OAAO,IAAI+B,eAAe,CAAC9B,IAAI,EAAE;UACtE8B,eAAe,CAAC9B,IAAI,CAACmC,OAAO,CAACP,IAAI,IAAI;YAAA,IAAAQ,UAAA;YACnC,MAAMC,MAAM,GAAG,EAAAD,UAAA,GAAAR,IAAI,CAAClF,IAAI,cAAA0F,UAAA,uBAATA,UAAA,CAAW/B,GAAG,KAAIuB,IAAI,CAACS,MAAM;YAC5C,IAAIA,MAAM,EAAE;cACVH,cAAc,CAACG,MAAM,CAAC,GAAGT,IAAI,CAACU,OAAO,IAAI,EAAE;YAC7C;UACF,CAAC,CAAC;QACJ;QAEArC,eAAe,GAAG8B,aAAa,CAAC/B,IAAI,CACjCuC,MAAM,CAACpC,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAACE,GAAG,IAAIF,QAAQ,CAACqC,IAAI,KAAK,OAAO,CAAC,CAAC;QAAA,CAC1EtC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;UACxB;UACA,MAAMqC,WAAW,GAAGP,cAAc,CAAC/B,QAAQ,CAACE,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,IAAIqC,YAAY,GAAGD,WAAW,CAACE,MAAM,IAAIxC,QAAQ,CAACS,iBAAiB,IAAI,CAAC;UACxE,IAAIgC,UAAU,GAAGH,WAAW,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAClF,IAAInC,YAAY,GAAG6B,YAAY,GAAG,CAAC,GAAGO,IAAI,CAACC,KAAK,CAACN,UAAU,GAAGF,YAAY,CAAC,GAAGvC,QAAQ,CAACU,YAAY,IAAI,CAAC;;UAExG;UACA,IAAI,CAAC4B,WAAW,CAACE,MAAM,IAAIxC,QAAQ,CAACgD,WAAW,EAAE;YAC/C;YACA,MAAMC,gBAAgB,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACK,KAAK,CAACnD,QAAQ,CAACgD,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAMI,gBAAgB,GAAGN,IAAI,CAACjF,GAAG,CAAC,EAAE,EAAEiF,IAAI,CAACI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAIlD,QAAQ,CAACgD,WAAW,GAAGC,gBAAgB,GAAG,EAAG,CAAC,CAAC,CAAC,CAAC;;YAE1GV,YAAY,GAAGU,gBAAgB;YAC/BvC,YAAY,GAAGoC,IAAI,CAACC,KAAK,CAACK,gBAAgB,CAAC;YAC3CX,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACrC,YAAY,GAAG6B,YAAY,CAAC;YAEpDlD,OAAO,CAACC,GAAG,CAAE,0BAAyBU,QAAQ,CAACG,IAAK,KAAI8C,gBAAiB,aAAYG,gBAAiB,cAAapD,QAAQ,CAACgD,WAAY,SAAQ,CAAC;UACnJ;;UAEA;UACA,IAAIxC,OAAO,GAAGR,QAAQ,CAACQ,OAAO,IAAI,CAAC;UAEnC,IAAI,CAACA,OAAO,EAAE;YACZ;YACA,IAAIR,QAAQ,CAACgD,WAAW,EAAE;cACxB;cACAxC,OAAO,GAAGsC,IAAI,CAACK,KAAK,CAClBnD,QAAQ,CAACgD,WAAW;cAAG;cACtBT,YAAY,GAAG,EAAG;cAAG;cACrB7B,YAAY,GAAG,EAAE,GAAG6B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;cAAG;cAC7C7B,YAAY,GAAG,EAAE,GAAG6B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAAC,EAAE;cAC3B;cACA/B,OAAO,GAAGsC,IAAI,CAACK,KAAK,CACjBzC,YAAY,GAAG6B,YAAY,GAAG,CAAC;cAAI;cACnCA,YAAY,GAAG,EAAG;cAAG;cACrB7B,YAAY,GAAG,EAAE,GAAG6B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH;UACF;;UAEA;UACA,IAAI5B,aAAa,GAAGX,QAAQ,CAACW,aAAa,IAAI,CAAC;UAC/C,IAAIC,UAAU,GAAGZ,QAAQ,CAACY,UAAU,IAAI,CAAC;UAEzC,IAAI0B,WAAW,CAACE,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAIa,UAAU,GAAG,CAAC;YAClBf,WAAW,CAACN,OAAO,CAACY,MAAM,IAAI;cAC5B,IAAIA,MAAM,CAACC,KAAK,IAAI,EAAE,EAAE;gBAAE;gBACxBQ,UAAU,EAAE;gBACZzC,UAAU,GAAGkC,IAAI,CAACI,GAAG,CAACtC,UAAU,EAAEyC,UAAU,CAAC;cAC/C,CAAC,MAAM;gBACLA,UAAU,GAAG,CAAC;cAChB;YACF,CAAC,CAAC;YACF1C,aAAa,GAAG0C,UAAU;UAC5B,CAAC,MAAM,IAAIrD,QAAQ,CAACgD,WAAW,IAAI,CAACrC,aAAa,EAAE;YACjD;YACA,MAAM2C,aAAa,GAAGf,YAAY,GAAG,CAAC,GAAGvC,QAAQ,CAACgD,WAAW,GAAGT,YAAY,GAAG,CAAC;YAChF,IAAIe,aAAa,GAAG,EAAE,EAAE;cACtB3C,aAAa,GAAGmC,IAAI,CAACjF,GAAG,CAAC0E,YAAY,EAAEO,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;cACxE1C,UAAU,GAAGkC,IAAI,CAACI,GAAG,CAACvC,aAAa,EAAEmC,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACxE;UACF;;UAEA,OAAO;YACLpD,GAAG,EAAEF,QAAQ,CAACE,GAAG;YACjBC,IAAI,EAAEH,QAAQ,CAACG,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAEJ,QAAQ,CAACI,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEL,QAAQ,CAACK,KAAK,IAAI,EAAE;YAC3BX,KAAK,EAAEM,QAAQ,CAACN,KAAK,IAAI,EAAE;YAC3BY,cAAc,EAAEN,QAAQ,CAACM,cAAc,IAAI,EAAE;YAC7CE,OAAO,EAAEA,OAAO;YAChBC,iBAAiB,EAAE8B,YAAY;YAC/B7B,YAAY,EAAEA,YAAY;YAC1BC,aAAa,EAAEA,aAAa;YAC5BC,UAAU,EAAEA,UAAU;YACtBC,kBAAkB,EAAEb,QAAQ,CAACa,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEb,KAAK,GAAG,CAAC;YACfjB,IAAI,EAAEF,WAAW,CAAC0B,OAAO,CAAC;YAC1BO,UAAU,EAAE,IAAI;YAChB;YACAwC,cAAc,EAAEvD,QAAQ,CAACgD,WAAW,IAAI,CAAC;YACzCQ,UAAU,EAAElB,WAAW,CAACE,MAAM,GAAG,CAAC;YAClClB,UAAU,EAAEgB,WAAW,CAACE,MAAM,GAAG,CAAC,GAAG,SAAS,GAAGxC,QAAQ,CAACgD,WAAW,GAAG,eAAe,GAAG;UAC5F,CAAC;QACH,CAAC,CAAC;;QAEJ;QACAlD,eAAe,CAAC2D,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACnD,OAAO,GAAGkD,CAAC,CAAClD,OAAO,CAAC;;QAErD;QACAV,eAAe,CAACkC,OAAO,CAAC,CAACzF,IAAI,EAAE0D,KAAK,KAAK;UACvC1D,IAAI,CAACuE,IAAI,GAAGb,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEFxD,cAAc,CAACqD,eAAe,CAAC;;QAE/B;QACA,MAAM8D,QAAQ,GAAGrH,IAAI,GAAGuD,eAAe,CAAC0B,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACvB,GAAG,KAAK3D,IAAI,CAAC2D,GAAG,CAAC,GAAG,CAAC,CAAC;QACrFrD,kBAAkB,CAAC+G,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;;QAEvD;QACA,MAAMC,WAAW,GAAG;UAClB1B,OAAO,EAAErC,eAAe,CAACsC,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACxC,UAAU,KAAK,SAAS,CAAC,CAACkB,MAAM;UACvEuB,aAAa,EAAEjE,eAAe,CAACsC,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACxC,UAAU,KAAK,eAAe,CAAC,CAACkB,MAAM;UACnFwB,SAAS,EAAElE,eAAe,CAACsC,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACxC,UAAU,KAAK,WAAW,CAAC,CAACkB;QACvE,CAAC;QAEDnD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEQ,eAAe,CAAC0C,MAAM,EAAE,gBAAgB,CAAC;QACxFnD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEuE,WAAW,CAAC;QAC5CxE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEQ,eAAe,CAACmE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClE,GAAG,CAAC+D,CAAC,KAAK;UACvE3D,IAAI,EAAE2D,CAAC,CAAC3D,IAAI;UACZpB,EAAE,EAAE+E,CAAC,CAACtD,OAAO;UACb0D,OAAO,EAAEJ,CAAC,CAACrD,iBAAiB;UAC5B0D,GAAG,EAAEL,CAAC,CAACpD,YAAY;UACnB0D,MAAM,EAAEN,CAAC,CAACxC;QACZ,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,MAAM;QACLjC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxC7C,cAAc,CAAC,EAAE,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;QACxBpC,OAAO,CAAC4J,OAAO,CAAC,0DAA0D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdxC,OAAO,CAACwC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDpH,OAAO,CAACoH,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACRlF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAvC,SAAS,CAAC,MAAM;IACdgF,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMkF,WAAW,GAAG5G,kBAAkB,CAACoF,IAAI,CAACK,KAAK,CAACL,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAG7G,kBAAkB,CAAC8E,MAAM,CAAC,CAAC;IAC7FnF,oBAAoB,CAACiH,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvCtH,iBAAiB,CAACuH,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,cAAc,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,aAAa,GAAGpI,WAAW,CAACyH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAMY,eAAe,GAAGrI,WAAW,CAACyH,KAAK,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIrH,cAAc,CAACsH,OAAO,EAAE;MAC1BxH,aAAa,CAAC,IAAI,CAAC;MACnBE,cAAc,CAACsH,OAAO,CAACC,cAAc,CAAC;QACpCC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACAC,UAAU,CAAC,MAAM;QACf5H,aAAa,CAAC,KAAK,CAAC;MACtB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL;MACA,MAAM6H,aAAa,GAAG5I,WAAW,CAAC6I,IAAI,CAACvB,CAAC,IAAIA,CAAC,CAAC5D,GAAG,MAAK3D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,GAAG,EAAC;MAChE,IAAIkF,aAAa,EAAE;QACjB,MAAMxB,QAAQ,GAAGpH,WAAW,CAAC8I,OAAO,CAACF,aAAa,CAAC,GAAG,CAAC;QACvD3K,OAAO,CAAC8K,IAAI,CAAE,mBAAkB3B,QAAS,sBAAqB,CAAC;MACjE,CAAC,MAAM;QACLnJ,OAAO,CAAC8K,IAAI,CAAC,yEAAyE,CAAC;MACzF;IACF;EACF,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAAC3E,kBAAkB,EAAE4E,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,GAAG,CAAC,KAAK;IAC1H,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,OAAO,GAAGN,mBAAmB,GAAG,IAAIK,IAAI,CAACL,mBAAmB,CAAC,GAAG,IAAI;IAE1EpG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCuB,kBAAkB;MAClB4E,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfI,OAAO;MACPF,GAAG;MACHG,QAAQ,EAAED,OAAO,IAAIA,OAAO,GAAGF,GAAG;MAClCD;IACF,CAAC,CAAC;;IAEF;IACA,IAAI/E,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,KAAK,SAAS,EAAE;MACvE;MACA,IAAI,CAACkF,OAAO,IAAIA,OAAO,GAAGF,GAAG,EAAE;QAC7B;QACA,OAAO;UACLI,IAAI,EAAE,WAAW;UACjBnI,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,yBAAyB;UAClCQ,WAAW,EAAE;QACf,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACL0H,IAAI,EAAE,SAAS;UACfnI,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,wBAAwB;UACjCQ,WAAW,EAAE;QACf,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA,OAAO;QACL0H,IAAI,EAAE,SAAS;QACfnI,KAAK,EAAE,SAAS;QAAE;QAClBC,OAAO,EAAE,wBAAwB;QACjCQ,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,IAAI7B,OAAO,IAAIF,WAAW,CAACgG,MAAM,KAAK,CAAC,EAAE;IACvC,oBACEzG,OAAA;MAAKmK,SAAS,EAAC,4GAA4G;MAAAC,QAAA,eACzHpK,OAAA,CAACzB,MAAM,CAAC8L,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvBpK,OAAA,CAACzB,MAAM,CAAC8L,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DX,SAAS,EAAC;QAAqF;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACFlL,OAAA;UAAGmK,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAgC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACElL,OAAA,CAAAE,SAAA;IAAAkK,QAAA,gBACEpK,OAAA;MAAOmL,GAAG;MAAAf,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACVlL,OAAA;MAAKmK,SAAS,EAAC,iHAAiH;MAAAC,QAAA,gBAEhIpK,OAAA;QAAKmK,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CpK,OAAA;UAAKmK,SAAS,EAAC;QAA2H;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjJlL,OAAA;UAAKmK,SAAS,EAAC;QAAkJ;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxKlL,OAAA;UAAKmK,SAAS,EAAC;QAA2I;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9J,CAAC,eAGNlL,OAAA;QAAKmK,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,GAAGgB,KAAK,CAAC,EAAE,CAAC,CAAC,CAACpH,GAAG,CAAC,CAACqH,CAAC,EAAEC,CAAC,kBACvBtL,OAAA,CAACzB,MAAM,CAAC8L,GAAG;UAETF,SAAS,EAAC,mDAAmD;UAC7DK,OAAO,EAAE;YACPe,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACfC,CAAC,EAAE,CAAC,CAAC,EAAEzE,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;YACnC+B,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UACzB,CAAE;UACFG,UAAU,EAAE;YACVC,QAAQ,EAAE,CAAC,GAAG5D,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAG,CAAC;YAC/BoC,MAAM,EAAEC,QAAQ;YAChBY,KAAK,EAAE1E,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAG;UACzB,CAAE;UACFkD,KAAK,EAAE;YACLC,IAAI,EAAG,GAAE5E,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC/BoD,GAAG,EAAG,GAAE7E,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAG,GAAI;UAC9B;QAAE,GAfG8C,CAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENlL,OAAA;QAAKmK,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5BpK,OAAA,CAACzB,MAAM,CAAC8L,GAAG;UACTwB,GAAG,EAAEpK,SAAU;UACf6I,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCf,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEG,IAAI,EAAE;UAAU,CAAE;UAC7CX,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eAGpCpK,OAAA;YAAKmK,SAAS,EAAC,kGAAkG;YAAAC,QAAA,gBAC/GpK,OAAA;cAAKmK,SAAS,EAAC;YAA6E;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnGlL,OAAA;cAAKmK,SAAS,EAAC;YAA+E;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGrGlL,OAAA;cAAKmK,SAAS,EAAC,4EAA4E;cAAAC,QAAA,eACzFpK,OAAA;gBAAKmK,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAG5CpK,OAAA,CAACzB,MAAM,CAAC8L,GAAG;kBACTG,OAAO,EAAE;oBACPsB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFrB,UAAU,EAAE;oBACVC,QAAQ,EAAE,CAAC;oBACXC,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR,CAAE;kBACFX,SAAS,EAAC,MAAM;kBAAAC,QAAA,eAEhBpK,OAAA;oBAAImK,SAAS,EAAC,iGAAiG;oBAAAC,QAAA,gBAC7GpK,OAAA,CAACzB,MAAM,CAACyN,IAAI;sBACVxB,OAAO,EAAE;wBACPyB,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;sBACrD,CAAE;sBACFvB,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFX,SAAS,EAAC,+HAA+H;sBACzIuB,KAAK,EAAE;wBACLQ,cAAc,EAAE,WAAW;wBAC3BC,oBAAoB,EAAE,MAAM;wBAC5BC,mBAAmB,EAAE,aAAa;wBAClC/F,MAAM,EAAE;sBACV,CAAE;sBAAA+D,QAAA,EACH;oBAED;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACdlL,OAAA;sBAAA+K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNlL,OAAA,CAACzB,MAAM,CAACyN,IAAI;sBACVxB,OAAO,EAAE;wBACP6B,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;sBAEhE,CAAE;sBACF3B,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACbC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFY,KAAK,EAAE;wBACL3J,KAAK,EAAE,SAAS;wBAChBuK,UAAU,EAAE,KAAK;wBACjBD,UAAU,EAAE;sBACd,CAAE;sBAAAjC,QAAA,EACH;oBAED;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGblL,OAAA,CAACzB,MAAM,CAACgO,CAAC;kBACPjC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAG,CAAE;kBAC/Bf,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAE,CAAE;kBAC9Bb,UAAU,EAAE;oBAAEe,KAAK,EAAE,GAAG;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBAC1CR,SAAS,EAAC,8GAA8G;kBACxHuB,KAAK,EAAE;oBACL3J,KAAK,EAAE,SAAS;oBAChBsK,UAAU,EAAE,6BAA6B;oBACzCG,UAAU,EAAE,0CAA0C;oBACtDL,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE;kBACvB,CAAE;kBAAAhC,QAAA,EACH;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAGXlL,OAAA,CAACzB,MAAM,CAAC8L,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEuB,KAAK,EAAE;kBAAI,CAAE;kBACpCtB,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEuB,KAAK,EAAE;kBAAE,CAAE;kBAClCpB,UAAU,EAAE;oBAAEe,KAAK,EAAE,CAAC;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBACxCR,SAAS,EAAC,iCAAiC;kBAC3CuB,KAAK,EAAE;oBACLc,UAAU,EAAE,yEAAyE;oBACrFC,cAAc,EAAE,YAAY;oBAC5BC,YAAY,EAAE,MAAM;oBACpBC,OAAO,EAAE,MAAM;oBACfC,MAAM,EAAE,iCAAiC;oBACzCC,SAAS,EAAE;kBACb,CAAE;kBAAAzC,QAAA,gBAEFpK,OAAA;oBAAKmK,SAAS,EAAC;kBAAmG;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzHlL,OAAA,CAACzB,MAAM,CAACgO,CAAC;oBAEPjC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE;oBAAG,CAAE;oBAC/Bf,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE;oBAAE,CAAE;oBAC9BpB,SAAS,EAAC,gDAAgD;oBAC1DuB,KAAK,EAAE;sBACL3J,KAAK,EAAE,SAAS;sBAChBsK,UAAU,EAAE,6BAA6B;sBACzCS,SAAS,EAAE;oBACb,CAAE;oBAAA1C,QAAA,EAED/I;kBAAiB,GAVbA,iBAAiB;oBAAA0J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAWd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAGblL,OAAA,CAACzB,MAAM,CAAC8L,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAG,CAAE;kBAC/Bf,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAE,CAAE;kBAC9Bb,UAAU,EAAE;oBAAEe,KAAK,EAAE,GAAG;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBAC1CR,SAAS,EAAC,kEAAkE;kBAAAC,QAAA,EAE3E,CACC;oBACE/H,IAAI,EAAE1D,QAAQ;oBACdoO,KAAK,EAAE,iBAAiB;oBACxBC,KAAK,EAAEvM,WAAW,CAACgG,MAAM;oBACzBwG,SAAS,EAAE,SAAS;oBACpBC,UAAU,EAAE,oCAAoC;oBAChD1K,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAEvD,OAAO;oBACbiO,KAAK,EAAE,gBAAgB;oBACvBC,KAAK,EAAEvM,WAAW,CAAC4F,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACnD,aAAa,GAAG,CAAC,CAAC,CAAC6B,MAAM;oBAC1DwG,SAAS,EAAE,SAAS;oBACpBC,UAAU,EAAE,kCAAkC;oBAC9C1K,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAErD,OAAO;oBACb+N,KAAK,EAAE,eAAe;oBACtBC,KAAK,EAAEvM,WAAW,CAACkG,MAAM,CAAC,CAACC,GAAG,EAAEmB,CAAC,KAAKnB,GAAG,GAAGmB,CAAC,CAACrD,iBAAiB,EAAE,CAAC,CAAC;oBACnEuI,SAAS,EAAE,SAAS;oBACpBC,UAAU,EAAE,mCAAmC;oBAC/C1K,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAElD,MAAM;oBACZ4N,KAAK,EAAE,UAAU;oBACjBC,KAAK,EAAEvM,WAAW,CAACkG,MAAM,CAAC,CAACC,GAAG,EAAEmB,CAAC,KAAKnB,GAAG,GAAGmB,CAAC,CAACtD,OAAO,EAAE,CAAC,CAAC,CAAC0I,cAAc,CAAC,CAAC;oBAC1EF,SAAS,EAAE,SAAS;oBACpBC,UAAU,EAAE,qCAAqC;oBACjD1K,WAAW,EAAE;kBACf,CAAC,CACF,CAACwB,GAAG,CAAC,CAACoJ,IAAI,EAAElJ,KAAK,kBAChBlE,OAAA,CAACzB,MAAM,CAAC8L,GAAG;oBAETgD,UAAU,EAAE;sBAAEvB,KAAK,EAAE,IAAI;sBAAEP,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCpB,SAAS,EAAG,qBAAoBiD,IAAI,CAACF,UAAW,uEAAuE;oBACvHxB,KAAK,EAAE;sBACLkB,MAAM,EAAG,aAAYQ,IAAI,CAAC5K,WAAY,IAAG;sBACzCqK,SAAS,EAAG,cAAaO,IAAI,CAAC5K,WAAY;oBAC5C,CAAE;oBAAA4H,QAAA,gBAEFpK,OAAA;sBAAKmK,SAAS,EAAC;oBAAgE;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtFlL,OAAA,CAACoN,IAAI,CAAC/K,IAAI;sBACR8H,SAAS,EAAC,oCAAoC;sBAC9CuB,KAAK,EAAE;wBAAE3J,KAAK,EAAEqL,IAAI,CAACH,SAAS;wBAAE5G,MAAM,EAAE;sBAAyC;oBAAE;sBAAA0E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC,eACFlL,OAAA;sBACEmK,SAAS,EAAC,oDAAoD;sBAC9DuB,KAAK,EAAE;wBACL3J,KAAK,EAAEqL,IAAI,CAACH,SAAS;wBACrBZ,UAAU,EAAG,6BAA4B;wBACzChG,MAAM,EAAE,oCAAoC;wBAC5CiH,QAAQ,EAAE;sBACZ,CAAE;sBAAAlD,QAAA,EAEDgD,IAAI,CAACJ;oBAAK;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACNlL,OAAA;sBACEmK,SAAS,EAAC,iCAAiC;sBAC3CuB,KAAK,EAAE;wBACL3J,KAAK,EAAE,SAAS;wBAChBsK,UAAU,EAAE,6BAA6B;wBACzCiB,QAAQ,EAAE;sBACZ,CAAE;sBAAAlD,QAAA,EAEDgD,IAAI,CAACL;oBAAK;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA,GAjCDkC,IAAI,CAACL,KAAK;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAkCL,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGblL,OAAA,CAACzB,MAAM,CAAC8L,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAG,CAAE;UAC/Bf,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEe,KAAK,EAAE,GAAG;YAAEd,QAAQ,EAAE;UAAI,CAAE;UAC1CR,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eAErCpK,OAAA;YAAKmK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCpK,OAAA;cAAKmK,SAAS,EAAC,oEAAoE;cAAAC,QAAA,eACjFpK,OAAA;gBAAKmK,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,gBAGnFpK,OAAA,CAACzB,MAAM,CAACgP,MAAM;kBACZF,UAAU,EAAE;oBAAEvB,KAAK,EAAE;kBAAK,CAAE;kBAC5B0B,QAAQ,EAAE;oBAAE1B,KAAK,EAAE;kBAAK,CAAE;kBAC1B2B,OAAO,EAAE1E,YAAa;kBACtBoB,SAAS,EAAC,kNAAkN;kBAC5NuB,KAAK,EAAE;oBACLc,UAAU,EAAE,0CAA0C;oBACtDzK,KAAK,EAAE,SAAS;oBAChBsK,UAAU,EAAE,MAAM;oBAClBC,UAAU,EAAE,KAAK;oBACjBgB,QAAQ,EAAEI,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAAvD,QAAA,gBAEFpK,OAAA,CAACjB,QAAQ;oBAACoL,SAAS,EAAC;kBAAuB;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9ClL,OAAA;oBAAAoK,QAAA,EACGvJ,eAAe,GAAI,YAAWA,eAAgB,EAAC,GAAG;kBAAS;oBAAAkK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGhBlL,OAAA,CAACzB,MAAM,CAACgP,MAAM;kBACZF,UAAU,EAAE;oBAAEvB,KAAK,EAAE,IAAI;oBAAErB,MAAM,EAAE;kBAAI,CAAE;kBACzC+C,QAAQ,EAAE;oBAAE1B,KAAK,EAAE;kBAAK,CAAE;kBAC1B2B,OAAO,EAAEpK,gBAAiB;kBAC1BuK,QAAQ,EAAEjN,OAAQ;kBAClBwJ,SAAS,EAAC,4LAA4L;kBACtMuB,KAAK,EAAE;oBACL4B,QAAQ,EAAE;kBACZ,CAAE;kBAAAlD,QAAA,gBAEFpK,OAAA,CAACf,SAAS;oBAACkL,SAAS,EAAG,WAAUxJ,OAAO,GAAG,cAAc,GAAG,EAAG;kBAAE;oBAAAoK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpElL,OAAA;oBAAAoK,QAAA,EAAM;kBAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZvK,OAAO,iBACNX,OAAA,CAACzB,MAAM,CAAC8L,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBJ,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAE3DpK,OAAA,CAACzB,MAAM,CAAC8L,GAAG;YACTG,OAAO,EAAE;cAAEC,MAAM,EAAE;YAAI,CAAE;YACzBC,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAEC,IAAI,EAAE;YAAS,CAAE;YAC9DX,SAAS,EAAC;UAA6E;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eACFlL,OAAA;YAAGmK,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CACb,EAGA,CAACvK,OAAO,iBACPX,OAAA,CAACzB,MAAM,CAAC8L,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAG,CAAE;UAC/Bf,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEe,KAAK,EAAE,GAAG;YAAEd,QAAQ,EAAE;UAAI,CAAE;UAC1CR,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eAEtCpK,OAAA;YAAKmK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAG/BvB,aAAa,CAACpC,MAAM,GAAG,CAAC,iBACvBzG,OAAA,CAACzB,MAAM,CAAC8L,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEuB,KAAK,EAAE;cAAI,CAAE;cACpCtB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEuB,KAAK,EAAE;cAAE,CAAE;cAClCpB,UAAU,EAAE;gBAAEe,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CR,SAAS,EAAC,OAAO;cAAAC,QAAA,gBAEjBpK,OAAA;gBAAImK,SAAS,EAAC,2EAA2E;gBAACuB,KAAK,EAAE;kBAC/Fc,UAAU,EAAE,mDAAmD;kBAC/DL,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,UAAU,EAAE,6BAA6B;kBACzChG,MAAM,EAAE;gBACV,CAAE;gBAAA+D,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELlL,OAAA;gBAAKmK,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,EACnFvB,aAAa,CAAC7E,GAAG,CAAC,CAAC6J,QAAQ,EAAE3J,KAAK,KAAK;kBACtC,MAAM4J,QAAQ,GAAG5J,KAAK,GAAG,CAAC;kBAC1B,MAAM6J,aAAa,GAAGvN,IAAI,IAAIqN,QAAQ,CAAC1J,GAAG,KAAK3D,IAAI,CAAC2D,GAAG;kBAEvD,oBACEnE,OAAA,CAACzB,MAAM,CAAC8L,GAAG;oBAETwB,GAAG,EAAEkC,aAAa,GAAGrM,cAAc,GAAG,IAAK;oBAC3C4I,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE;oBAAG,CAAE;oBAC/Bf,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE;oBAAE,CAAE;oBAC9Bb,UAAU,EAAE;sBAAEe,KAAK,EAAE,GAAG,GAAGvH,KAAK,GAAG,GAAG;sBAAEyG,QAAQ,EAAE;oBAAI,CAAE;oBACxD0C,UAAU,EAAE;sBAAEvB,KAAK,EAAE,IAAI;sBAAEP,CAAC,EAAE,CAAC;oBAAG,CAAE;oBACpCpB,SAAS,EAAG,YACV2D,QAAQ,KAAK,CAAC,GAAG,yBAAyB,GAC1CA,QAAQ,KAAK,CAAC,GAAG,YAAY,GAAG,YACjC,IAAGC,aAAa,GAAG,wBAAwB,GAAG,EAAG,IAAGxM,UAAU,IAAIwM,aAAa,GAAG,mBAAmB,GAAG,EAAG,EAAE;oBAAA3D,QAAA,eAG9GpK,OAAA;sBACEmK,SAAS,EAAG,8BAA6B0D,QAAQ,CAAC5K,IAAI,CAAClB,KAAM,mBAAkB8L,QAAQ,CAAC5K,IAAI,CAACb,IAAK,IAAGyL,QAAQ,CAAC5K,IAAI,CAACR,MAAO,YAAY;sBACtIiJ,KAAK,EAAE;wBACLmB,SAAS,EAAG,eAAcgB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,cAAa0L,QAAQ,CAAC5K,IAAI,CAACd,WAAY;sBAC7F,CAAE;sBAAAiI,QAAA,eAEFpK,OAAA;wBACEmK,SAAS,EAAG,GAAE0D,QAAQ,CAAC5K,IAAI,CAACjB,OAAQ,uEAAuE;wBAC3G0J,KAAK,EAAE;0BACLkB,MAAM,EAAG,aAAYiB,QAAQ,CAAC5K,IAAI,CAACT,WAAY;wBACjD,CAAE;wBAAA4H,QAAA,gBAEFpK,OAAA;0BAAKmK,SAAS,EAAC;wBAAiE;0BAAAY,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAGvFlL,OAAA;0BACEmK,SAAS,EAAG,mFAAkF0D,QAAQ,CAAC5K,IAAI,CAAClB,KAAM,2FAA2F;0BAC7M2J,KAAK,EAAE;4BACL3J,KAAK,EAAE+L,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAGA,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAGA,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;4BACvGzB,UAAU,EAAE,6BAA6B;4BACzCO,MAAM,EAAG,aAAYiB,QAAQ,CAAC5K,IAAI,CAACT,WAAY,EAAC;4BAChDqK,SAAS,EAAG,cAAagB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,cAAa0L,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;4BAC3FmL,QAAQ,EAAE,QAAQ;4BAClBhB,UAAU,EAAE;0BACd,CAAE;0BAAAlC,QAAA,EAED0D,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAGA,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAGA,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAGA;wBAAQ;0BAAA/C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9E,CAAC,EAGL4C,QAAQ,KAAK,CAAC,iBACb9N,OAAA,CAACzB,MAAM,CAAC8L,GAAG;0BACTG,OAAO,EAAE;4BAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;0BAAE,CAAE;0BACrCC,UAAU,EAAE;4BAAEC,QAAQ,EAAE,CAAC;4BAAEC,MAAM,EAAEC;0BAAS,CAAE;0BAC9CV,SAAS,EAAC,qDAAqD;0BAAAC,QAAA,eAE/DpK,OAAA,CAACpB,OAAO;4BAACuL,SAAS,EAAC;0BAAyB;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CACb,eAGDlL,OAAA;0BAAKmK,SAAS,EAAG,yBAAwB4D,aAAa,GAAG,wCAAwC,GAAG,EAAG,EAAE;0BAAA3D,QAAA,gBACvGpK,OAAA;4BACEmK,SAAS,EAAC,yDAAyD;4BACnEuB,KAAK,EAAE;8BACLc,UAAU,EAAG,0BAAyBqB,QAAQ,CAAC5K,IAAI,CAACT,WAAY,KAAIqL,QAAQ,CAAC5K,IAAI,CAAChB,SAAU,GAAE;8BAC9F4K,SAAS,EAAG,aAAYgB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,cAAa0L,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;8BAC1FwK,OAAO,EAAE;4BACX,CAAE;4BAAAvC,QAAA,EAEDyD,QAAQ,CAACtJ,cAAc,gBACtBvE,OAAA;8BACEgO,GAAG,EAAEH,QAAQ,CAACtJ,cAAe;8BAC7B0J,GAAG,EAAEJ,QAAQ,CAACzJ,IAAK;8BACnB+F,SAAS,EAAC,yCAAyC;8BACnDuB,KAAK,EAAE;gCACLrF,MAAM,EAAE,+BAA+B;gCACvC6H,WAAW,EAAE;8BACf;4BAAE;8BAAAnD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,gBAEFlL,OAAA;8BACEmK,SAAS,EAAC,iFAAiF;8BAC3FuB,KAAK,EAAE;gCACLc,UAAU,EAAG,2BAA0BqB,QAAQ,CAAC5K,IAAI,CAACT,WAAY,KAAIqL,QAAQ,CAAC5K,IAAI,CAAChB,SAAU,GAAE;gCAC/FF,KAAK,EAAE,SAAS;gCAChBsK,UAAU,EAAE,6BAA6B;gCACzC6B,WAAW,EAAE;8BACf,CAAE;8BAAA9D,QAAA,EAEDyD,QAAQ,CAACzJ,IAAI,CAAC+J,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;4BAAC;8BAAArD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnC;0BACN;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC,EACL6C,aAAa,iBACZ/N,OAAA;4BACEmK,SAAS,EAAC,4DAA4D;4BACtEuB,KAAK,EAAE;8BACLc,UAAU,EAAE,0CAA0C;8BACtDK,SAAS,EAAE;4BACb,CAAE;4BAAAzC,QAAA,eAEFpK,OAAA,CAACnB,MAAM;8BAACsL,SAAS,EAAC;4BAAoB;8BAAAY,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eAGNlL,OAAA;0BACEmK,SAAS,EAAC,uCAAuC;0BACjDuB,KAAK,EAAE;4BACL3J,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAACf,SAAS;4BAC9BmK,UAAU,EAAG,eAAcwB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;4BACtDmL,QAAQ,EAAE,QAAQ;4BAClBjH,MAAM,EAAE;0BACV,CAAE;0BAAA+D,QAAA,EAEDyD,QAAQ,CAACzJ;wBAAI;0BAAA2G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACLlL,OAAA;0BACEmK,SAAS,EAAG,6DAA4D0D,QAAQ,CAAC5K,IAAI,CAAClB,KAAM,mEAAmE;0BAC/J2J,KAAK,EAAE;4BACL3J,KAAK,EAAE,SAAS;4BAChBsK,UAAU,EAAE,6BAA6B;4BACzCO,MAAM,EAAG,aAAYiB,QAAQ,CAAC5K,IAAI,CAACT,WAAY,EAAC;4BAChDqK,SAAS,EAAG,cAAagB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,cAAa0L,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;4BAC3FmL,QAAQ,EAAE,QAAQ;4BAClBe,aAAa,EAAE;0BACjB,CAAE;0BAAAjE,QAAA,gBAEFpK,OAAA,CAAC6N,QAAQ,CAAC5K,IAAI,CAACZ,IAAI;4BACjB8H,SAAS,EAAC,SAAS;4BACnBuB,KAAK,EAAE;8BACLrF,MAAM,EAAE,wCAAwC;8BAChDtE,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAAChB;4BACvB;0BAAE;4BAAA8I,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eACFlL,OAAA;4BAAM0L,KAAK,EAAE;8BAAE3J,KAAK,EAAE;4BAAU,CAAE;4BAAAqI,QAAA,EAAEyD,QAAQ,CAAC5K,IAAI,CAACX;0BAAK;4BAAAyI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5D,CAAC,EAGL,CAAC,MAAM;0BACN,MAAMoD,KAAK,GAAG7E,oBAAoB,CAChCoE,QAAQ,CAAC/I,kBAAkB,EAC3B+I,QAAQ,CAACnE,mBAAmB,EAC5BmE,QAAQ,CAAClE,gBAAgB,EACzBkE,QAAQ,CAACjE,eAAe,EACxB1F,KACF,CAAC;0BACD,oBACElE,OAAA;4BACEmK,SAAS,EAAC,4FAA4F;4BACtGuB,KAAK,EAAE;8BACL6C,eAAe,EAAED,KAAK,CAACtM,OAAO;8BAC9BD,KAAK,EAAEuM,KAAK,CAACvM,KAAK;8BAClB6K,MAAM,EAAG,aAAY0B,KAAK,CAAC9L,WAAY,EAAC;8BACxC6J,UAAU,EAAE,6BAA6B;8BACzCiB,QAAQ,EAAE,SAAS;8BACnBe,aAAa,EAAE;4BACjB,CAAE;4BAAAjE,QAAA,EAEDkE,KAAK,CAACpE;0BAAI;4BAAAa,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACR,CAAC;wBAEV,CAAC,EAAE,CAAC,eAGJlL,OAAA;0BAAKmK,SAAS,EAAC,yBAAyB;0BAAAC,QAAA,gBACtCpK,OAAA;4BAAKmK,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC7CpK,OAAA;8BAAM0L,KAAK,EAAE;gCACX3J,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAAChB,SAAS;gCAC9BoK,UAAU,EAAG,eAAcwB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;gCACtDmK,UAAU,EAAE,KAAK;gCACjBgB,QAAQ,EAAE;8BACZ,CAAE;8BAAAlD,QAAA,EAAC;4BAAM;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eAChBlL,OAAA;8BAAM0L,KAAK,EAAE;gCACX3J,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAACf,SAAS;gCAC9BmK,UAAU,EAAG,eAAcwB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;gCACtDmK,UAAU,EAAE,KAAK;gCACjBgB,QAAQ,EAAE,QAAQ;gCAClBjH,MAAM,EAAE;8BACV,CAAE;8BAAA+D,QAAA,EAAEyD,QAAQ,CAACpJ,OAAO,CAAC0I,cAAc,CAAC;4BAAC;8BAAApC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1C,CAAC,eACNlL,OAAA;4BAAKmK,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC7CpK,OAAA;8BAAM0L,KAAK,EAAE;gCACX3J,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAAChB,SAAS;gCAC9BoK,UAAU,EAAG,eAAcwB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;gCACtDmK,UAAU,EAAE,KAAK;gCACjBgB,QAAQ,EAAE;8BACZ,CAAE;8BAAAlD,QAAA,EAAC;4BAAW;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACrBlL,OAAA;8BAAM0L,KAAK,EAAE;gCACX3J,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAACf,SAAS;gCAC9BmK,UAAU,EAAG,eAAcwB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;gCACtDmK,UAAU,EAAE,KAAK;gCACjBgB,QAAQ,EAAE,QAAQ;gCAClBjH,MAAM,EAAE;8BACV,CAAE;8BAAA+D,QAAA,EAAEyD,QAAQ,CAACnJ;4BAAiB;8BAAAqG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnC,CAAC,eACNlL,OAAA;4BAAKmK,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC7CpK,OAAA;8BAAM0L,KAAK,EAAE;gCACX3J,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAAChB,SAAS;gCAC9BoK,UAAU,EAAG,eAAcwB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;gCACtDmK,UAAU,EAAE,KAAK;gCACjBgB,QAAQ,EAAE;8BACZ,CAAE;8BAAAlD,QAAA,EAAC;4BAAU;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACpBlL,OAAA;8BAAM0L,KAAK,EAAE;gCACX3J,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAACf,SAAS;gCAC9BmK,UAAU,EAAG,eAAcwB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;gCACtDmK,UAAU,EAAE,KAAK;gCACjBgB,QAAQ,EAAE,QAAQ;gCAClBjH,MAAM,EAAE;8BACV,CAAE;8BAAC8D,SAAS,EAAC,yBAAyB;8BAAAC,QAAA,EACnCyD,QAAQ,CAACjJ;4BAAa;8BAAAmG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnB,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eAGNlL,OAAA;4BAAKmK,SAAS,EAAC,kBAAkB;4BAAAC,QAAA,eAC/BpK,OAAA;8BAAM0L,KAAK,EAAE;gCACX3J,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAAChB,SAAS;gCAC9BoK,UAAU,EAAG,eAAcwB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;gCACtDmK,UAAU,EAAE,KAAK;gCACjBgB,QAAQ,EAAE,QAAQ;gCAClB/C,OAAO,EAAE;8BACX,CAAE;8BAAAH,QAAA,EACCyD,QAAQ,CAACtI,UAAU,KAAK,SAAS,GAAG,cAAc,GAClDsI,QAAQ,CAACtI,UAAU,KAAK,eAAe,GAAG,kBAAkB,GAC5D;4BAAc;8BAAAwF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACX;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAhOD2C,QAAQ,CAAC1J,GAAG;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiOP,CAAC;gBAEjB,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGApC,eAAe,CAACrC,MAAM,GAAG,CAAC,iBACzBzG,OAAA,CAACzB,MAAM,CAAC8L,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAG,CAAE;cAC/Bf,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAE,CAAE;cAC9Bb,UAAU,EAAE;gBAAEe,KAAK,EAAE,CAAC;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cACxCR,SAAS,EAAC,OAAO;cAAAC,QAAA,gBAEjBpK,OAAA;gBAAImK,SAAS,EAAC,0EAA0E;gBAACuB,KAAK,EAAE;kBAC9Fc,UAAU,EAAE,mDAAmD;kBAC/DL,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,UAAU,EAAE,6BAA6B;kBACzChG,MAAM,EAAE;gBACV,CAAE;gBAAA+D,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELlL,OAAA;gBAAKmK,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EACjDtB,eAAe,CAAC9E,GAAG,CAAC,CAAC6J,QAAQ,EAAE3J,KAAK,KAAK;kBACxC,MAAMsK,UAAU,GAAGtK,KAAK,GAAG,CAAC,CAAC,CAAC;kBAC9B,MAAM6J,aAAa,GAAGvN,IAAI,IAAIqN,QAAQ,CAAC1J,GAAG,KAAK3D,IAAI,CAAC2D,GAAG;kBAEvD,oBACEnE,OAAA,CAACzB,MAAM,CAAC8L,GAAG;oBAETwB,GAAG,EAAEkC,aAAa,GAAGrM,cAAc,GAAG,IAAK;oBAC3C4I,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEiB,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAChChB,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEiB,CAAC,EAAE;oBAAE,CAAE;oBAC9Bd,UAAU,EAAE;sBAAEe,KAAK,EAAE,GAAG,GAAGvH,KAAK,GAAG,GAAG;sBAAEyG,QAAQ,EAAE;oBAAI,CAAE;oBACxD0C,UAAU,EAAE;sBAAEvB,KAAK,EAAE,IAAI;sBAAEN,CAAC,EAAE;oBAAG,CAAE;oBACnCrB,SAAS,EAAG,YAAW4D,aAAa,GAAG,wBAAwB,GAAG,EAAG,IAAGxM,UAAU,IAAIwM,aAAa,GAAG,mBAAmB,GAAG,EAAG,EAAE;oBAAA3D,QAAA,eAEjIpK,OAAA;sBACEmK,SAAS,EAAG,oBAAmB0D,QAAQ,CAAC5K,IAAI,CAAClB,KAAM,mBAAkB8L,QAAQ,CAAC5K,IAAI,CAACb,IAAK,IAAGyL,QAAQ,CAAC5K,IAAI,CAACR,MAAO,EAAE;sBAClHiJ,KAAK,EAAE;wBACLmB,SAAS,EAAG,cAAagB,QAAQ,CAAC5K,IAAI,CAACd,WAAY;sBACrD,CAAE;sBAAAiI,QAAA,eAEFpK,OAAA;wBACEmK,SAAS,EAAG,GAAE0D,QAAQ,CAAC5K,IAAI,CAACjB,OAAQ,mGAAmG;wBACvI0J,KAAK,EAAE;0BACLkB,MAAM,EAAG,aAAYiB,QAAQ,CAAC5K,IAAI,CAACT,WAAY;wBACjD,CAAE;wBAAA4H,QAAA,gBAEFpK,OAAA;0BAAKmK,SAAS,EAAC;wBAA+D;0BAAAY,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAGrFlL,OAAA;0BACEmK,SAAS,EAAG,6DAA4D0D,QAAQ,CAAC5K,IAAI,CAAClB,KAAM,sGAAsG;0BAClM2J,KAAK,EAAE;4BACL3J,KAAK,EAAEyM,UAAU,IAAI,EAAE,GAAGX,QAAQ,CAAC5K,IAAI,CAAChB,SAAS,GAAG,SAAS;4BAC7DoK,UAAU,EAAE,6BAA6B;4BACzCO,MAAM,EAAG,aAAYiB,QAAQ,CAAC5K,IAAI,CAACT,WAAY,EAAC;4BAChDqK,SAAS,EAAG,cAAagB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,cAAa0L,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;4BAC3FmL,QAAQ,EAAEkB,UAAU,IAAI,EAAE,GAAG,QAAQ,GAAG,QAAQ;4BAChDlC,UAAU,EAAE;0BACd,CAAE;0BAAAlC,QAAA,EAEDoE,UAAU,IAAI,EAAE,GAAI,IAAGA,UAAW,EAAC,GAAGA;wBAAU;0BAAAzD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C,CAAC,eAGNlL,OAAA;0BAAKmK,SAAS,EAAC,wBAAwB;0BAAAC,QAAA,gBACrCpK,OAAA;4BACEmK,SAAS,EAAC,uDAAuD;4BACjEuB,KAAK,EAAE;8BACLc,UAAU,EAAG,0BAAyBqB,QAAQ,CAAC5K,IAAI,CAACT,WAAY,KAAIqL,QAAQ,CAAC5K,IAAI,CAAChB,SAAU,GAAE;8BAC9F4K,SAAS,EAAG,aAAYgB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,cAAa0L,QAAQ,CAAC5K,IAAI,CAACd,WAAY;4BAC3F,CAAE;4BAAAiI,QAAA,EAEDyD,QAAQ,CAACtJ,cAAc,gBACtBvE,OAAA;8BACEgO,GAAG,EAAEH,QAAQ,CAACtJ,cAAe;8BAC7B0J,GAAG,EAAEJ,QAAQ,CAACzJ,IAAK;8BACnB+F,SAAS,EAAC,yCAAyC;8BACnDuB,KAAK,EAAE;gCACLrF,MAAM,EAAE,6CAA6C;gCACrD6H,WAAW,EAAE;8BACf;4BAAE;8BAAAnD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,gBAEFlL,OAAA;8BACEmK,SAAS,EAAC,gFAAgF;8BAC1FuB,KAAK,EAAE;gCACLc,UAAU,EAAG,2BAA0BqB,QAAQ,CAAC5K,IAAI,CAACT,WAAY,KAAIqL,QAAQ,CAAC5K,IAAI,CAAChB,SAAU,GAAE;gCAC/FF,KAAK,EAAE,SAAS;gCAChBsK,UAAU,EAAE,6BAA6B;gCACzC6B,WAAW,EAAE;8BACf,CAAE;8BAAA9D,QAAA,EAEDyD,QAAQ,CAACzJ,IAAI,CAAC+J,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;4BAAC;8BAAArD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnC;0BACN;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC,EACL6C,aAAa,iBACZ/N,OAAA;4BACEmK,SAAS,EAAC,yDAAyD;4BACnEuB,KAAK,EAAE;8BACLc,UAAU,EAAE,0CAA0C;8BACtDK,SAAS,EAAE;4BACb,CAAE;4BAAAzC,QAAA,eAEFpK,OAAA,CAACnB,MAAM;8BAACsL,SAAS,EAAC;4BAAoB;8BAAAY,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eAGNlL,OAAA;0BAAKmK,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,gBAC3CpK,OAAA;4BAAKmK,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,gBAC3CpK,OAAA;8BACEmK,SAAS,EAAC,6BAA6B;8BACvCuB,KAAK,EAAE;gCACL3J,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAACf,SAAS;gCAC9BmK,UAAU,EAAG,eAAcwB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;gCACtDmL,QAAQ,EAAE,SAAS;gCACnBjH,MAAM,EAAE;8BACV,CAAE;8BAAA+D,QAAA,EAEDyD,QAAQ,CAACzJ;4BAAI;8BAAA2G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACZ,CAAC,EACJ6C,aAAa,iBACZ/N,OAAA;8BACEmK,SAAS,EAAC,yDAAyD;8BACnEuB,KAAK,EAAE;gCACLc,UAAU,EAAE,0CAA0C;gCACtDzK,KAAK,EAAE,SAAS;gCAChBsK,UAAU,EAAE,MAAM;gCAClBO,MAAM,EAAE,mBAAmB;gCAC3BC,SAAS,EAAE;8BACb,CAAE;8BAAAzC,QAAA,EACH;4BAED;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACN;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC,eACNlL,OAAA;4BACEmK,SAAS,EAAG,6DAA4D0D,QAAQ,CAAC5K,IAAI,CAAClB,KAAM,kCAAkC;4BAC9H2J,KAAK,EAAE;8BACL3J,KAAK,EAAE,SAAS;8BAChBsK,UAAU,EAAE,6BAA6B;8BACzCO,MAAM,EAAG,aAAYiB,QAAQ,CAAC5K,IAAI,CAACT,WAAY,EAAC;8BAChDqK,SAAS,EAAG,aAAYgB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,cAAa0L,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;8BAC1FmL,QAAQ,EAAE,QAAQ;8BAClBe,aAAa,EAAE;4BACjB,CAAE;4BAAAjE,QAAA,gBAEFpK,OAAA,CAAC6N,QAAQ,CAAC5K,IAAI,CAACZ,IAAI;8BACjB8H,SAAS,EAAC,SAAS;8BACnBuB,KAAK,EAAE;gCACLrF,MAAM,EAAE,wCAAwC;gCAChDtE,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAAChB;8BACvB;4BAAE;8BAAA8I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,eACFlL,OAAA;8BAAM0L,KAAK,EAAE;gCAAE3J,KAAK,EAAE;8BAAU,CAAE;8BAAAqI,QAAA,EAAEyD,QAAQ,CAAC5K,IAAI,CAACX;4BAAK;8BAAAyI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5D,CAAC,EAGL,CAAC,MAAM;4BACN,MAAMoD,KAAK,GAAG7E,oBAAoB,CAChCoE,QAAQ,CAAC/I,kBAAkB,EAC3B+I,QAAQ,CAACnE,mBAAmB,EAC5BmE,QAAQ,CAAClE,gBAAgB,EACzBkE,QAAQ,CAACjE,eAAe,EACxB4E,UACF,CAAC;4BACD,oBACExO,OAAA;8BACEmK,SAAS,EAAC,8EAA8E;8BACxFuB,KAAK,EAAE;gCACL6C,eAAe,EAAED,KAAK,CAACtM,OAAO;gCAC9BD,KAAK,EAAEuM,KAAK,CAACvM,KAAK;gCAClB6K,MAAM,EAAG,aAAY0B,KAAK,CAAC9L,WAAY,EAAC;gCACxC6J,UAAU,EAAE,6BAA6B;gCACzCiB,QAAQ,EAAE,QAAQ;gCAClBe,aAAa,EAAE;8BACjB,CAAE;8BAAAjE,QAAA,EAEDkE,KAAK,CAACpE;4BAAI;8BAAAa,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACR,CAAC;0BAEV,CAAC,EAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC,eAGNlL,OAAA;0BAAKmK,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,gBACrDpK,OAAA;4BAAKmK,SAAS,EAAC,cAAc;4BAACuB,KAAK,EAAE;8BACnC3J,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAACf,SAAS;8BAC9BmK,UAAU,EAAG,eAAcwB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;8BACtDmK,UAAU,EAAE,KAAK;8BACjBjG,MAAM,EAAE,oCAAoC;8BAC5CiH,QAAQ,EAAE;4BACZ,CAAE;4BAAAlD,QAAA,GAAC,eACE,EAACyD,QAAQ,CAACpJ,OAAO,CAAC0I,cAAc,CAAC,CAAC,EAAC,KACxC;0BAAA;4BAAApC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACNlL,OAAA;4BAAKmK,SAAS,EAAC,6CAA6C;4BAAAC,QAAA,gBAC1DpK,OAAA;8BAAMmK,SAAS,EAAC,yBAAyB;8BAACuB,KAAK,EAAE;gCAC/C3J,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAAChB,SAAS;gCAC9BoK,UAAU,EAAG,eAAcwB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;gCACtDmK,UAAU,EAAE,KAAK;gCACjBgB,QAAQ,EAAE;8BACZ,CAAE;8BAAAlD,QAAA,gBACApK,OAAA,CAAChB,OAAO;gCAACmL,SAAS,EAAC,SAAS;gCAACuB,KAAK,EAAE;kCAAE3J,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAAChB;gCAAU;8BAAE;gCAAA8I,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EACzE2C,QAAQ,CAACnJ,iBAAiB;4BAAA;8BAAAqG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvB,CAAC,eACPlL,OAAA;8BAAMmK,SAAS,EAAC,yBAAyB;8BAACuB,KAAK,EAAE;gCAC/C3J,KAAK,EAAE8L,QAAQ,CAAC5K,IAAI,CAAChB,SAAS;gCAC9BoK,UAAU,EAAG,eAAcwB,QAAQ,CAAC5K,IAAI,CAACd,WAAY,EAAC;gCACtDmK,UAAU,EAAE,KAAK;gCACjBgB,QAAQ,EAAE;8BACZ,CAAE;8BAAAlD,QAAA,gBACApK,OAAA,CAAClB,OAAO;gCAACqL,SAAS,EAAC,SAAS;gCAACuB,KAAK,EAAE;kCAAE3J,KAAK,EAAE;gCAAU;8BAAE;gCAAAgJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EAC3D2C,QAAQ,CAACjJ,aAAa;4BAAA;8BAAAmG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnB,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAjMD2C,QAAQ,CAAC1J,GAAG;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAkMP,CAAC;gBAEjB,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGAzK,WAAW,CAACgG,MAAM,GAAG,CAAC,iBACrBzG,OAAA,CAACzB,MAAM,CAAC8L,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAG,CAAE;cAC/Bf,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAE,CAAE;cAC9Bb,UAAU,EAAE;gBAAEe,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CR,SAAS,EAAC,sIAAsI;cAAAC,QAAA,eAEhJpK,OAAA;gBAAKmK,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BpK,OAAA;kBAAImK,SAAS,EAAC,wBAAwB;kBAACuB,KAAK,EAAE;oBAC5C3J,KAAK,EAAE,SAAS;oBAChBsK,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAA6B;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrClL,OAAA;kBAAKmK,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DpK,OAAA;oBAAKmK,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7CpK,OAAA;sBAAKmK,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9C3J,WAAW,CAAC4F,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACxC,UAAU,KAAK,SAAS,CAAC,CAACkB;oBAAM;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACNlL,OAAA;sBAAKmK,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNlL,OAAA;oBAAKmK,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CpK,OAAA;sBAAKmK,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC7C3J,WAAW,CAAC4F,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACxC,UAAU,KAAK,eAAe,CAAC,CAACkB;oBAAM;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNlL,OAAA;sBAAKmK,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACNlL,OAAA;oBAAKmK,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9CpK,OAAA;sBAAKmK,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/C3J,WAAW,CAAC4F,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACxC,UAAU,KAAK,WAAW,CAAC,CAACkB;oBAAM;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACNlL,OAAA;sBAAKmK,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlL,OAAA;kBAAGmK,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGArK,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrCb,OAAA,CAACzB,MAAM,CAAC8L,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEuB,KAAK,EAAE;cAAI,CAAE;cACpCtB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEuB,KAAK,EAAE;cAAE,CAAE;cAClCpB,UAAU,EAAE;gBAAEe,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CR,SAAS,EAAC,wIAAwI;cAAAC,QAAA,eAElJpK,OAAA;gBAAKmK,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BpK,OAAA;kBAAImK,SAAS,EAAC,yBAAyB;kBAACuB,KAAK,EAAE;oBAC7C3J,KAAK,EAAE,SAAS;oBAChBsK,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAAqB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BlL,OAAA;kBAAKmK,SAAS,EAAC,0BAA0B;kBAACuB,KAAK,EAAE;oBAC/C3J,KAAK,EAAE,SAAS;oBAChBsK,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,GAAC,GAAC,EAACvJ,eAAe;gBAAA;kBAAAkK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3BlL,OAAA;kBAAGmK,SAAS,EAAC,SAAS;kBAACuB,KAAK,EAAE;oBAC5B3J,KAAK,EAAE,SAAS;oBAChBsK,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAEH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAGDlL,OAAA,CAACzB,MAAM,CAAC8L,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAG,CAAE;cAC/Bf,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAE,CAAE;cAC9Bb,UAAU,EAAE;gBAAEe,KAAK,EAAE,CAAC;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cACxCR,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAE7BpK,OAAA;gBAAKmK,SAAS,EAAC,8HAA8H;gBAAAC,QAAA,gBAC3IpK,OAAA,CAACzB,MAAM,CAAC8L,GAAG;kBACTG,OAAO,EAAE;oBAAEsB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjCpB,UAAU,EAAE;oBAAEC,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAT,QAAA,eAE9CpK,OAAA,CAACZ,QAAQ;oBAAC+K,SAAS,EAAC;kBAAwC;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACblL,OAAA;kBAAImK,SAAS,EAAC,yBAAyB;kBAACuB,KAAK,EAAE;oBAC7C3J,KAAK,EAAE,SAAS;oBAChBsK,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAAqB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BlL,OAAA;kBAAGmK,SAAS,EAAC,gCAAgC;kBAACuB,KAAK,EAAE;oBACnD3J,KAAK,EAAE,SAAS;oBAChBsK,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAGH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJlL,OAAA,CAACzB,MAAM,CAACgP,MAAM;kBACZF,UAAU,EAAE;oBAAEvB,KAAK,EAAE;kBAAK,CAAE;kBAC5B0B,QAAQ,EAAE;oBAAE1B,KAAK,EAAE;kBAAK,CAAE;kBAC1B3B,SAAS,EAAC,sJAAsJ;kBAChKsD,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACe,QAAQ,CAACC,IAAI,GAAG,YAAa;kBAAAtE,QAAA,EACpD;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAGZzK,WAAW,CAACgG,MAAM,KAAK,CAAC,IAAI,CAAC9F,OAAO,iBACnCX,OAAA,CAACzB,MAAM,CAAC8L,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEuB,KAAK,EAAE;cAAI,CAAE;cACpCtB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEuB,KAAK,EAAE;cAAE,CAAE;cAClC3B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAE7BpK,OAAA,CAACrB,QAAQ;gBAACwL,SAAS,EAAC;cAAsC;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7DlL,OAAA;gBAAImK,SAAS,EAAC,yBAAyB;gBAACuB,KAAK,EAAE;kBAC7C3J,KAAK,EAAE,SAAS;kBAChBsK,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAlC,QAAA,EAAC;cAAgB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBlL,OAAA;gBAAGmK,SAAS,EAAC,SAAS;gBAACuB,KAAK,EAAE;kBAC5B3J,KAAK,EAAE,SAAS;kBAChBsK,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAlC,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC9K,EAAA,CA94CID,kBAAkB;EAAA,QACJ1B,WAAW;AAAA;AAAAkQ,EAAA,GADzBxO,kBAAkB;AAg5CxB,eAAeA,kBAAkB;AAAC,IAAAwO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}