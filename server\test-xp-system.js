const mongoose = require('mongoose');
const User = require('./models/userModel');
const Exam = require('./models/examModel');
const Report = require('./models/reportModel');
const enhancedXPService = require('./services/enhancedXPService');

// Database connection
const connectDB = async () => {
  try {
    const mongoUrl = 'mongodb://hvmgeeks:<EMAIL>:27017,ac-4ojvv6y-shard-00-01.cdg8fdn.mongodb.net:27017,ac-4ojvv6y-shard-00-02.cdg8fdn.mongodb.net:27017/stjoseph?ssl=true&replicaSet=atlas-fsgg6f-shard-0&authSource=admin&retryWrites=true&w=majority&appName=Cluster0';
    await mongoose.connect(mongoUrl);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const testXPSystem = async () => {
  try {
    await connectDB();
    
    console.log('🔍 Testing XP System...\n');
    
    // 1. Find a test user
    const testUser = await User.findOne({ isAdmin: { $ne: true } }).limit(1);
    if (!testUser) {
      console.log('❌ No test user found');
      return;
    }
    
    console.log(`👤 Test User: ${testUser.name} (${testUser.email})`);
    console.log(`📊 Current XP: ${testUser.totalXP || 0}`);
    console.log(`🎯 Current Level: ${testUser.currentLevel || 1}`);
    console.log(`🔥 Current Streak: ${testUser.currentStreak || 0}\n`);
    
    // 2. Find a test exam
    const testExam = await Exam.findOne().populate('questions').limit(1);
    if (!testExam) {
      console.log('❌ No test exam found');
      return;
    }
    
    console.log(`📝 Test Exam: ${testExam.name}`);
    console.log(`❓ Questions: ${testExam.questions?.length || 0}`);
    console.log(`📚 Subject: ${testExam.subject || 'N/A'}\n`);
    
    // 3. Simulate quiz completion
    const totalQuestions = testExam.questions?.length || 5;
    const correctAnswers = Math.floor(totalQuestions * 0.8); // 80% correct
    const wrongAnswers = totalQuestions - correctAnswers;
    const score = Math.round((correctAnswers / totalQuestions) * 100);
    const verdict = score >= (testExam.passingMarks || 70) ? 'Pass' : 'Fail';
    
    const quizResult = {
      correctAnswers: Array(correctAnswers).fill().map((_, i) => `answer_${i}`),
      wrongAnswers: Array(wrongAnswers).fill().map((_, i) => `wrong_${i}`),
      score: score,
      verdict: verdict,
      timeSpent: 1200, // 20 minutes
      totalTimeAllowed: 1800 // 30 minutes
    };
    
    console.log('🎯 Simulated Quiz Result:');
    console.log(`   Correct: ${correctAnswers}/${totalQuestions} (${score}%)`);
    console.log(`   Verdict: ${verdict}`);
    console.log(`   Time: ${quizResult.timeSpent}s / ${quizResult.totalTimeAllowed}s\n`);
    
    // 4. Calculate XP
    console.log('💰 Calculating XP...');
    const xpResult = await enhancedXPService.calculateQuizXP({
      userId: testUser._id,
      examData: {
        _id: testExam._id,
        subject: testExam.subject || 'General',
        difficulty: testExam.difficulty || 'medium',
        duration: testExam.duration || 30,
        name: testExam.name
      },
      result: quizResult,
      timeSpent: quizResult.timeSpent,
      isFirstAttempt: true,
      previousScore: null
    });
    
    console.log(`   XP Awarded: ${xpResult.xpAwarded}`);
    console.log(`   Breakdown:`, xpResult.breakdown);
    
    // 5. Award XP
    console.log('\n🏆 Awarding XP...');
    const xpAwardResult = await enhancedXPService.awardXP({
      userId: testUser._id,
      xpAmount: xpResult.xpAwarded,
      transactionType: 'quiz_completion',
      sourceId: testExam._id,
      sourceModel: 'exams',
      breakdown: xpResult.breakdown,
      quizData: {
        examId: testExam._id,
        subject: testExam.subject || 'General',
        difficulty: testExam.difficulty || 'medium',
        questionsTotal: totalQuestions,
        questionsCorrect: correctAnswers,
        timeSpent: quizResult.timeSpent,
        score: score,
        isFirstAttempt: true,
      },
      metadata: {
        testRun: true,
        verdict: verdict
      }
    });
    
    console.log(`   Success: ${xpAwardResult.success}`);
    console.log(`   New Total XP: ${xpAwardResult.newTotalXP}`);
    console.log(`   Level Up: ${xpAwardResult.levelUp}`);
    if (xpAwardResult.levelUp) {
      console.log(`   New Level: ${xpAwardResult.newLevel}`);
    }
    
    // 6. Check updated user
    const updatedUser = await User.findById(testUser._id);
    console.log('\n📈 Updated User Stats:');
    console.log(`   Total XP: ${updatedUser.totalXP || 0} (was ${testUser.totalXP || 0})`);
    console.log(`   Current Level: ${updatedUser.currentLevel || 1} (was ${testUser.currentLevel || 1})`);
    console.log(`   Current Streak: ${updatedUser.currentStreak || 0} (was ${testUser.currentStreak || 0})`);
    
    // 7. Create a test report to simulate full flow
    console.log('\n📋 Creating test report...');
    const testReport = new Report({
      exam: testExam._id,
      user: testUser._id,
      result: quizResult
    });
    
    await testReport.save();
    console.log(`   Report saved with ID: ${testReport._id}`);
    
    console.log('\n✅ XP System test completed successfully!');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ XP System test failed:', error);
    process.exit(1);
  }
};

testXPSystem();
