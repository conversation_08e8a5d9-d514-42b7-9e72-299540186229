const mongoose = require('mongoose');
const User = require('./models/userModel');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/brainwave', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const setupQuickXP = async () => {
  try {
    await connectDB();
    
    console.log('🚀 Setting up quick XP data for users...\n');
    
    // Find users without XP data
    const usersWithoutXP = await User.find({
      isAdmin: { $ne: true },
      $or: [
        { totalXP: { $exists: false } },
        { totalXP: 0 },
        { totalXP: null }
      ]
    });
    
    console.log(`👥 Found ${usersWithoutXP.length} users without XP data`);
    
    let updatedCount = 0;
    
    for (const user of usersWithoutXP) {
      try {
        // Calculate XP based on existing data
        let totalXP = 0;
        let currentLevel = 1;
        let xpToNextLevel = 100;
        
        // If user has legacy points, convert them
        if (user.totalPointsEarned && user.totalPointsEarned > 0) {
          // Convert points to XP with some bonuses
          const baseXP = user.totalPointsEarned;
          const quizBonus = (user.totalQuizzesTaken || 0) * 25;
          const scoreBonus = user.averageScore > 80 ? (user.totalQuizzesTaken || 0) * 15 : 0;
          const streakBonus = (user.bestStreak || 0) * 10;
          
          totalXP = Math.floor(baseXP + quizBonus + scoreBonus + streakBonus);
        } else if (user.totalQuizzesTaken && user.totalQuizzesTaken > 0) {
          // Calculate from quiz data
          const avgScore = user.averageScore || 70;
          totalXP = Math.floor(
            (avgScore * user.totalQuizzesTaken * 5) + // Base XP
            (user.totalQuizzesTaken * 30) + // Participation bonus
            ((user.bestStreak || 0) * 20) // Streak bonus
          );
        } else {
          // Give minimum XP for new users
          totalXP = 50;
        }
        
        // Calculate level based on XP
        let level = 1;
        let xpRequired = 100;
        let totalXPForLevels = 0;
        
        while (totalXPForLevels + xpRequired <= totalXP) {
          totalXPForLevels += xpRequired;
          level++;
          xpRequired = Math.round(100 * level * 0.5 + 50); // Progressive XP requirements
        }
        
        currentLevel = level;
        xpToNextLevel = xpRequired - (totalXP - totalXPForLevels);
        
        // Initialize activity tracking if not exists
        if (!user.activityTracking) {
          user.activityTracking = {
            dailyLoginStreak: Math.min(10, Math.floor(totalXP / 200)),
            bestLoginStreak: Math.min(20, Math.floor(totalXP / 150)),
            lastLoginDate: new Date(),
            totalLoginDays: Math.min(30, Math.floor(totalXP / 100)),
            quizCompletionStreak: user.totalQuizzesTaken || 0,
            bestQuizStreak: user.totalQuizzesTaken || 0,
            perfectScoreStreak: 0,
            bestPerfectStreak: Math.floor((user.averageScore || 0) / 25),
            totalStudyTimeMinutes: (user.totalQuizzesTaken || 0) * 15,
            quizzesPassed: Math.floor((user.totalQuizzesTaken || 0) * ((user.averageScore || 70) / 100)),
            quizzesFailed: Math.ceil((user.totalQuizzesTaken || 0) * (1 - (user.averageScore || 70) / 100)),
            totalQuestionsAnswered: (user.totalQuizzesTaken || 0) * 10,
            totalCorrectAnswers: Math.floor((user.totalQuizzesTaken || 0) * 10 * ((user.averageScore || 70) / 100)),
            subjectPerformance: [],
            weeklyStats: {
              currentWeek: getWeekString(new Date()),
              quizzesThisWeek: Math.min(5, user.totalQuizzesTaken || 0),
              xpThisWeek: Math.min(500, totalXP),
              studyTimeThisWeek: 0
            },
            monthlyStats: {
              currentMonth: getMonthString(new Date()),
              quizzesThisMonth: Math.min(20, user.totalQuizzesTaken || 0),
              xpThisMonth: Math.min(2000, totalXP),
              studyTimeThisMonth: 0
            }
          };
        }
        
        // Update XP stats
        if (!user.xpStats) {
          user.xpStats = {};
        }
        user.xpStats.averageXPPerQuiz = user.totalQuizzesTaken > 0 ? Math.round(totalXP / user.totalQuizzesTaken) : 50;
        user.xpStats.lastXPGain = new Date();
        user.xpStats.xpFromQuizzes = Math.floor(totalXP * 0.7);
        user.xpStats.xpFromStreaks = Math.floor(totalXP * 0.1);
        user.xpStats.xpFromConsistency = Math.floor(totalXP * 0.1);
        user.xpStats.xpFromAchievements = Math.floor(totalXP * 0.1);
        
        // Update user
        await User.findByIdAndUpdate(user._id, {
          totalXP: totalXP,
          lifetimeXP: totalXP,
          seasonXP: totalXP,
          currentLevel: currentLevel,
          xpToNextLevel: xpToNextLevel,
          currentSeason: '2024-S1',
          activityTracking: user.activityTracking,
          xpStats: user.xpStats
        });
        
        updatedCount++;
        console.log(`✅ Updated ${user.name}: ${totalXP} XP, Level ${currentLevel}`);
        
      } catch (error) {
        console.error(`❌ Error updating ${user.name}:`, error.message);
      }
    }
    
    console.log(`\n🎉 Successfully updated ${updatedCount} users with XP data!`);
    
    // Show sample of updated data
    const sampleUsers = await User.find({ isAdmin: { $ne: true } })
      .select('name totalXP currentLevel averageScore currentStreak')
      .sort({ totalXP: -1 })
      .limit(5);
    
    console.log('\n🏆 Top 5 users by XP:');
    sampleUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name}: ${user.totalXP} XP (Level ${user.currentLevel})`);
    });
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
};

// Helper functions
function getWeekString(date) {
  const year = date.getFullYear();
  const week = getWeekNumber(date);
  return `${year}-W${week.toString().padStart(2, '0')}`;
}

function getMonthString(date) {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  return `${year}-${month}`;
}

function getWeekNumber(date) {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay() || 7;
  d.setUTCDate(d.getUTCDate() + 4 - dayNum);
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
}

setupQuickXP();
