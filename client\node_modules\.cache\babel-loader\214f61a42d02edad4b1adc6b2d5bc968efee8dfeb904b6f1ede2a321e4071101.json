{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbTarget, TbBrain, TbSearch, TbFilter, TbRefresh, TbMedal, TbBolt, TbRocket, TbDiamond, TbHeart, TbEye, TbTrendingUp, TbAward, TbShield } from 'react-icons/tb';\nimport { getAllReportsForRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const user = userState.user || null;\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Performance tiers with SPECTACULAR visual themes and unique colors\n  const performanceTiers = {\n    legendary: {\n      min: 10000,\n      color: 'from-purple-400 via-pink-400 via-red-400 to-orange-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-pink-900/40 to-red-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FF69B4',\n      shadowColor: 'rgba(147, 51, 234, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery',\n      borderColor: '#8B5CF6',\n      effect: 'legendary-sparkle'\n    },\n    diamond: {\n      min: 7500,\n      color: 'from-cyan-200 via-blue-300 via-indigo-400 to-purple-500',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00E5FF',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 229, 255, 0.9)',\n      glow: 'shadow-cyan-300/80',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance',\n      borderColor: '#00E5FF',\n      effect: 'diamond-shine'\n    },\n    platinum: {\n      min: 5000,\n      color: 'from-slate-200 via-gray-300 via-zinc-400 to-stone-500',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#E8E8E8',\n      nameColor: '#C0C0C0',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-300/80',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam'\n    },\n    gold: {\n      min: 2500,\n      color: 'from-yellow-200 via-amber-300 via-orange-400 to-red-500',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-300/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent',\n      borderColor: '#FFD700',\n      effect: 'gold-glow'\n    },\n    silver: {\n      min: 1000,\n      color: 'from-gray-200 via-slate-300 via-zinc-400 to-gray-500',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#D3D3D3',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(211, 211, 211, 0.9)',\n      glow: 'shadow-gray-300/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress',\n      borderColor: '#D3D3D3',\n      effect: 'silver-shimmer'\n    },\n    bronze: {\n      min: 0,\n      color: 'from-orange-200 via-amber-300 via-yellow-400 to-orange-500',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-300/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = xp => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return {\n        tier,\n        ...config\n      };\n    }\n    return {\n      tier: 'bronze',\n      ...performanceTiers.bronze\n    };\n  };\n\n  // Fetch ranking data with multiple fallbacks and real user integration\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching amazing ranking data from database...');\n      let rankingResponse, usersResponse;\n\n      // Fetch both ranking reports and users data\n      try {\n        console.log('📊 Fetching ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n        console.log('✨ Ranking reports response:', rankingResponse);\n        console.log('👤 Users response:', usersResponse);\n      } catch (error) {\n        console.log('⚡ Error fetching data:', error);\n        // Try just users if reports fail\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      // Process real user data from database\n      let transformedData = [];\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing real user data from database...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            var _item$user;\n            const userId = ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n        transformedData = usersResponse.data.filter(userData => userData && userData._id) // Filter out invalid users\n        .map((userData, index) => {\n          // Get reports for this user\n          const userReports = userReportsMap[userData._id] || [];\n\n          // Calculate comprehensive stats\n          const totalQuizzes = userReports.length || 0;\n          const totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n          const averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : 0;\n\n          // Calculate XP based on performance (more realistic calculation)\n          let totalXP = 0;\n          if (totalQuizzes > 0) {\n            totalXP = Math.floor(averageScore * totalQuizzes * 10 +\n            // Base XP from scores\n            totalQuizzes * 50 + (\n            // Bonus for participation\n            averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n            );\n          }\n\n          // Calculate streaks from reports\n          let currentStreak = 0;\n          let bestStreak = 0;\n          let tempStreak = 0;\n          userReports.forEach(report => {\n            if (report.score >= 60) {\n              // Passing score\n              tempStreak++;\n              bestStreak = Math.max(bestStreak, tempStreak);\n            } else {\n              tempStreak = 0;\n            }\n          });\n          currentStreak = tempStreak;\n          return {\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: currentStreak,\n            bestStreak: bestStreak,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(totalXP),\n            isRealUser: true\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank\n        const userRank = user ? transformedData.findIndex(item => item._id === user._id) : -1;\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'champions');\n      } else {\n        // Fallback demo data to showcase the amazing design\n        console.log('🎭 Loading demo data to showcase the amazing design...');\n        const demoData = [{\n          _id: 'demo1',\n          name: 'Alex Champion',\n          email: '<EMAIL>',\n          class: '7',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 15000,\n          totalQuizzesTaken: 45,\n          averageScore: 92,\n          currentStreak: 12,\n          bestStreak: 18,\n          subscriptionStatus: 'premium',\n          rank: 1,\n          tier: getUserTier(15000)\n        }, {\n          _id: 'demo2',\n          name: 'Sarah Excellence',\n          email: '<EMAIL>',\n          class: '6',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 12500,\n          totalQuizzesTaken: 38,\n          averageScore: 88,\n          currentStreak: 8,\n          bestStreak: 15,\n          subscriptionStatus: 'premium',\n          rank: 2,\n          tier: getUserTier(12500)\n        }, {\n          _id: 'demo3',\n          name: 'Mike Achiever',\n          email: '<EMAIL>',\n          class: '7',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 9800,\n          totalQuizzesTaken: 32,\n          averageScore: 85,\n          currentStreak: 5,\n          bestStreak: 12,\n          subscriptionStatus: 'free',\n          rank: 3,\n          tier: getUserTier(9800)\n        }, {\n          _id: 'demo4',\n          name: 'Emma Rising',\n          email: '<EMAIL>',\n          class: '5',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 7200,\n          totalQuizzesTaken: 28,\n          averageScore: 82,\n          currentStreak: 3,\n          bestStreak: 9,\n          subscriptionStatus: 'free',\n          rank: 4,\n          tier: getUserTier(7200)\n        }, {\n          _id: 'demo5',\n          name: 'David Progress',\n          email: '<EMAIL>',\n          class: '6',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 5500,\n          totalQuizzesTaken: 22,\n          averageScore: 78,\n          currentStreak: 2,\n          bestStreak: 7,\n          subscriptionStatus: 'free',\n          rank: 5,\n          tier: getUserTier(5500)\n        }];\n        setRankingData(demoData);\n        setCurrentUserRank(null); // No current user in demo data\n        message.success('Welcome to the Hall of Champions! 🏆');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n    return () => clearInterval(animationTimer);\n  }, []);\n\n  // Filter and search functionality\n  const filteredData = rankingData.filter(rankingUser => {\n    const matchesSearch = rankingUser.name.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterType === 'all' || filterType === 'premium' && rankingUser.subscriptionStatus === 'premium' || filterType === 'free' && rankingUser.subscriptionStatus === 'free' || filterType === 'class' && user && rankingUser.class === user.class;\n    return matchesSearch && matchesFilter;\n  });\n\n  // Get top performers for special display\n  const topPerformers = filteredData.slice(0, 3);\n  const otherPerformers = filteredData.slice(3);\n\n  // Find Me functionality\n  const handleFindMe = () => {\n    if (currentUserRank && currentUserRef.current) {\n      setShowFindMe(true);\n      currentUserRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n\n      // Hide the find me highlight after 3 seconds\n      setTimeout(() => {\n        setShowFindMe(false);\n      }, 3000);\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading the Hall of Champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n      children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n        animate: {\n          y: [0, -100, 0],\n          x: [0, Math.random() * 100 - 50, 0],\n          opacity: [0.2, 0.8, 0.2]\n        },\n        transition: {\n          duration: 3 + Math.random() * 2,\n          repeat: Infinity,\n          delay: Math.random() * 2\n        },\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 442,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        ref: headerRef,\n        initial: {\n          opacity: 0,\n          y: -50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 1,\n          ease: \"easeOut\"\n        },\n        className: \"relative overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-purple-600 via-pink-500 via-red-500 via-orange-500 to-yellow-500 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10 px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-7xl mx-auto text-center\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  scale: [1, 1.02, 1],\n                  rotateY: [0, 5, 0]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                className: \"mb-8\",\n                children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-5xl sm:text-6xl lg:text-8xl font-black mb-4 tracking-tight\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    animate: {\n                      backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                    },\n                    transition: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"linear\"\n                    },\n                    className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                    style: {\n                      backgroundSize: '400% 400%',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      filter: 'drop-shadow(3px 3px 6px rgba(0,0,0,0.8))'\n                    },\n                    children: \"HALL OF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    animate: {\n                      textShadow: ['0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)', '0 0 50px rgba(255,215,0,1), 0 0 80px rgba(255,215,0,0.8)', '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)']\n                    },\n                    transition: {\n                      duration: 2.5,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    style: {\n                      color: '#FFD700',\n                      fontWeight: '900',\n                      textShadow: '4px 4px 8px rgba(0,0,0,0.9)'\n                    },\n                    children: \"CHAMPIONS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.5,\n                  duration: 0.8\n                },\n                className: \"text-xl sm:text-2xl lg:text-3xl font-semibold mb-8 max-w-4xl mx-auto leading-relaxed\",\n                style: {\n                  color: '#F3F4F6',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent'\n                },\n                children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  delay: 1,\n                  duration: 0.8\n                },\n                className: \"relative max-w-2xl mx-auto mb-8\",\n                style: {\n                  background: 'linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05))',\n                  backdropFilter: 'blur(20px)',\n                  borderRadius: '20px',\n                  padding: '24px',\n                  border: '2px solid rgba(255,255,255,0.2)',\n                  boxShadow: '0 8px 32px rgba(0,0,0,0.3)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-yellow-500/10 rounded-2xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 10\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"text-lg sm:text-xl font-semibold relative z-10\",\n                  style: {\n                    color: '#FBBF24',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    textAlign: 'center'\n                  },\n                  children: motivationalQuote\n                }, motivationalQuote, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 30\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 1.2,\n                  duration: 0.8\n                },\n                className: \"grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto\",\n                children: [{\n                  icon: TbTrophy,\n                  label: 'Total Champions',\n                  value: rankingData.length,\n                  iconColor: '#FFD700',\n                  bgGradient: 'from-yellow-500/20 to-amber-600/20',\n                  borderColor: '#FFD700'\n                }, {\n                  icon: TbFlame,\n                  label: 'Active Streaks',\n                  value: rankingData.filter(u => u.currentStreak > 0).length,\n                  iconColor: '#FF6B35',\n                  bgGradient: 'from-orange-500/20 to-red-600/20',\n                  borderColor: '#FF6B35'\n                }, {\n                  icon: TbBrain,\n                  label: 'Quizzes Taken',\n                  value: rankingData.reduce((sum, u) => sum + u.totalQuizzesTaken, 0),\n                  iconColor: '#3B82F6',\n                  bgGradient: 'from-blue-500/20 to-indigo-600/20',\n                  borderColor: '#3B82F6'\n                }, {\n                  icon: TbBolt,\n                  label: 'Total XP',\n                  value: rankingData.reduce((sum, u) => sum + u.totalXP, 0).toLocaleString(),\n                  iconColor: '#8B5CF6',\n                  bgGradient: 'from-purple-500/20 to-violet-600/20',\n                  borderColor: '#8B5CF6'\n                }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  whileHover: {\n                    scale: 1.08,\n                    y: -8\n                  },\n                  className: `bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                  style: {\n                    border: `2px solid ${stat.borderColor}40`,\n                    boxShadow: `0 8px 32px ${stat.borderColor}20`\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                    className: \"w-8 h-8 mx-auto mb-2 relative z-10\",\n                    style: {\n                      color: stat.iconColor,\n                      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl sm:text-3xl font-black mb-1 relative z-10\",\n                    style: {\n                      color: '#FFFFFF',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)'\n                    },\n                    children: stat.value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-semibold relative z-10\",\n                    style: {\n                      color: '#E5E7EB',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)'\n                    },\n                    children: stat.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 23\n                  }, this)]\n                }, stat.label, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 1.5,\n          duration: 0.8\n        },\n        className: \"px-4 sm:px-6 lg:px-8 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col lg:flex-row gap-6 items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative flex-1 max-w-md\",\n                children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n                  className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search champions...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  className: \"w-full pl-12 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-3\",\n                children: [{\n                  key: 'all',\n                  label: 'All Champions',\n                  icon: TbTrophy\n                }, {\n                  key: 'premium',\n                  label: 'Premium',\n                  icon: TbCrown\n                }, {\n                  key: 'free',\n                  label: 'Free',\n                  icon: TbStar\n                }, {\n                  key: 'class',\n                  label: 'My Class',\n                  icon: TbTarget\n                }].map(filter => /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setFilterType(filter.key),\n                  className: `flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${filterType === filter.key ? 'bg-purple-600 text-white shadow-lg shadow-purple-500/25' : 'bg-white/10 text-white/80 hover:bg-white/20'}`,\n                  children: [/*#__PURE__*/_jsxDEV(filter.icon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 712,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"hidden sm:inline\",\n                    children: filter.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 713,\n                    columnNumber: 23\n                  }, this)]\n                }, filter.key, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 17\n              }, this), currentUserRank && /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: handleFindMe,\n                className: \"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300\",\n                style: {\n                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                  color: '#000000',\n                  textShadow: 'none',\n                  fontWeight: '900'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Find Me #\", currentUserRank]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05,\n                  rotate: 180\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: fetchRankingData,\n                disabled: loading,\n                className: \"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50\",\n                children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                  className: `w-5 h-5 ${loading ? 'animate-spin' : ''}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Refresh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 671,\n        columnNumber: 9\n      }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"flex flex-col items-center justify-center py-20\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 760,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 765,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 755,\n        columnNumber: 11\n      }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3,\n          duration: 0.8\n        },\n        className: \"px-4 sm:px-6 lg:px-8 pb-20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              delay: 0.5,\n              duration: 0.8\n            },\n            className: \"mb-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl sm:text-4xl font-bold text-white text-center mb-8\",\n              children: \"\\uD83C\\uDFC6 Champions Podium \\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\",\n              children: topPerformers.map((champion, index) => {\n                const position = index + 1;\n                const isCurrentUser = user && champion._id === user._id;\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.7 + index * 0.2,\n                    duration: 0.8\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative ${position === 1 ? 'md:order-2 md:scale-110' : position === 2 ? 'md:order-1' : 'md:order-3'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-2xl ${champion.tier.glow} shadow-2xl`,\n                    style: {\n                      boxShadow: `0 20px 40px ${champion.tier.shadowColor}, 0 0 60px ${champion.tier.shadowColor}`\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${champion.tier.bgColor} backdrop-blur-lg rounded-2xl p-6 text-center relative overflow-hidden`,\n                      style: {\n                        border: `2px solid ${champion.tier.borderColor}60`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 821,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20`,\n                        style: {\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          border: `3px solid ${champion.tier.borderColor}`,\n                          boxShadow: `0 8px 16px ${champion.tier.shadowColor}`\n                        },\n                        children: position\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 824,\n                        columnNumber: 31\n                      }, this), position === 1 && /*#__PURE__*/_jsxDEV(motion.div, {\n                        animate: {\n                          rotate: [0, 10, -10, 0]\n                        },\n                        transition: {\n                          duration: 2,\n                          repeat: Infinity\n                        },\n                        className: \"absolute -top-8 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                          className: \"w-8 h-8 text-yellow-400\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 843,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 838,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-4 ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-20 h-20 rounded-full overflow-hidden mx-auto bg-gradient-to-br from-purple-500 to-pink-500 p-1\",\n                          children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: champion.profilePicture,\n                            alt: champion.name,\n                            className: \"w-full h-full object-cover rounded-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 851,\n                            columnNumber: 37\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-2xl\",\n                            children: champion.name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 857,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 849,\n                          columnNumber: 33\n                        }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-2 -right-2 bg-yellow-400 text-black rounded-full p-1\",\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-4 h-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 864,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 863,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 848,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-xl font-black mb-2 relative z-10\",\n                        style: {\n                          color: champion.tier.nameColor,\n                          textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                          fontSize: '1.5rem',\n                          filter: 'drop-shadow(0 0 10px currentColor)'\n                        },\n                        children: champion.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 870,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${champion.tier.color} rounded-full text-sm font-black mb-3 relative z-10`,\n                        style: {\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          border: `2px solid ${champion.tier.borderColor}`,\n                          boxShadow: `0 4px 12px ${champion.tier.shadowColor}`\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                          className: \"w-5 h-5\",\n                          style: {\n                            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 890,\n                          columnNumber: 33\n                        }, this), champion.tier.title]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 881,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#e5e7eb',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '600'\n                            },\n                            children: \"XP:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 897,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#ffffff',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '800'\n                            },\n                            children: champion.totalXP.toLocaleString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 902,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 896,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#e5e7eb',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '600'\n                            },\n                            children: \"Quizzes:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 909,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#ffffff',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '800'\n                            },\n                            children: champion.totalQuizzesTaken\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 914,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 908,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#e5e7eb',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '600'\n                            },\n                            children: \"Streak:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 921,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#ffffff',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '800'\n                            },\n                            className: \"flex items-center gap-1\",\n                            children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                              className: \"w-4 h-4 text-orange-400\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 931,\n                              columnNumber: 37\n                            }, this), champion.currentStreak]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 926,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 920,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 895,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 815,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 809,\n                    columnNumber: 27\n                  }, this)\n                }, champion._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 797,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 781,\n            columnNumber: 17\n          }, this), otherPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 1,\n              duration: 0.8\n            },\n            className: \"mt-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl sm:text-3xl font-bold text-white text-center mb-8\",\n              children: \"\\u26A1 Rising Champions \\u26A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 953,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: otherPerformers.map((champion, index) => {\n                const actualRank = index + 4; // Since top 3 are shown separately\n                const isCurrentUser = user && champion._id === user._id;\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: isCurrentUser ? currentUserRef : null,\n                  initial: {\n                    opacity: 0,\n                    x: -50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 1.2 + index * 0.1,\n                    duration: 0.6\n                  },\n                  whileHover: {\n                    scale: 1.02,\n                    x: 10\n                  },\n                  className: `relative ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow}`,\n                    style: {\n                      boxShadow: `0 8px 24px ${champion.tier.shadowColor}`\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 flex items-center gap-4 relative overflow-hidden`,\n                      style: {\n                        border: `1px solid ${champion.tier.borderColor}40`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 984,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `flex-shrink-0 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-10`,\n                        style: {\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          border: `2px solid ${champion.tier.borderColor}`,\n                          boxShadow: `0 6px 12px ${champion.tier.shadowColor}`\n                        },\n                        children: actualRank\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 987,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-14 h-14 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 to-pink-500 p-1\",\n                          children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: champion.profilePicture,\n                            alt: champion.name,\n                            className: \"w-full h-full object-cover rounded-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1003,\n                            columnNumber: 37\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-lg\",\n                            children: champion.name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1009,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1001,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1000,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 min-w-0 relative z-10\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-2 mb-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-lg font-black truncate\",\n                            style: {\n                              color: champion.tier.nameColor,\n                              textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                              fontSize: '1.25rem',\n                              filter: 'drop-shadow(0 0 8px currentColor)'\n                            },\n                            children: champion.name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1019,\n                            columnNumber: 35\n                          }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"px-3 py-1 rounded-full text-xs font-black animate-pulse\",\n                            style: {\n                              background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                              color: '#000000',\n                              textShadow: 'none',\n                              border: '2px solid #FFFFFF',\n                              boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                            },\n                            children: \"\\u2B50 YOU \\u2B50\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1031,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1018,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r ${champion.tier.color} rounded-full text-xs font-black`,\n                          style: {\n                            color: '#FFFFFF',\n                            textShadow: '1px 1px 2px rgba(0,0,0,0.9)',\n                            border: `1px solid ${champion.tier.borderColor}`,\n                            boxShadow: `0 2px 6px ${champion.tier.shadowColor}`\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                            className: \"w-4 h-4\",\n                            style: {\n                              filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.5))'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1054,\n                            columnNumber: 35\n                          }, this), champion.tier.title]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1045,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1017,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0 text-right\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-lg mb-1\",\n                          style: {\n                            color: '#ffffff',\n                            textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                            fontWeight: '800'\n                          },\n                          children: [champion.totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1061,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-4 text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"flex items-center gap-1\",\n                            style: {\n                              color: '#e5e7eb',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '600'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                              className: \"w-4 h-4\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1074,\n                              columnNumber: 37\n                            }, this), champion.totalQuizzesTaken]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1069,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"flex items-center gap-1\",\n                            style: {\n                              color: '#e5e7eb',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '600'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                              className: \"w-4 h-4 text-orange-400\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1082,\n                              columnNumber: 37\n                            }, this), champion.currentStreak]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1077,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1068,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1060,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 978,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 972,\n                    columnNumber: 27\n                  }, this)\n                }, champion._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 947,\n            columnNumber: 17\n          }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              delay: 1.5,\n              duration: 0.8\n            },\n            className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-2\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"Your Current Position\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1105,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-6xl font-black mb-2\",\n                style: {\n                  color: '#fbbf24',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  fontWeight: '900'\n                },\n                children: [\"#\", currentUserRank]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1110,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1115,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1104,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1098,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 2,\n              duration: 0.8\n            },\n            className: \"mt-16 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  scale: [1, 1.05, 1]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity\n                },\n                children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                  className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1138,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1134,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-3xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"Ready to Rise Higher?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl mb-6 max-w-2xl mx-auto\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1145,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                onClick: () => window.location.href = '/user/quiz',\n                children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1153,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1133,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1127,\n            columnNumber: 15\n          }, this), filteredData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            className: \"text-center py-20\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1171,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold mb-4\",\n              style: {\n                color: '#ffffff',\n                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                fontWeight: '800'\n              },\n              children: \"No Champions Found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1172,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg\",\n              style: {\n                color: '#e5e7eb',\n                textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                fontWeight: '600'\n              },\n              children: \"Try adjusting your search or filter criteria to find more champions!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1177,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1166,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 771,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 433,\n    columnNumber: 5\n  }, this);\n};\n_s(AmazingRankingPage, \"e+G3qu/FbuHarwyGfcH5LvXhb8c=\", false, function () {\n  return [useSelector];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbTarget", "TbBrain", "TbSearch", "Tb<PERSON><PERSON>er", "TbRefresh", "TbMedal", "TbBolt", "TbRocket", "TbDiamond", "TbHeart", "TbEye", "TbTrendingUp", "TbAward", "TbShield", "getAllReportsForRanking", "getAllUsers", "jsxDEV", "_jsxDEV", "AmazingRankingPage", "_s", "userState", "state", "users", "user", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "searchTerm", "setSearchTerm", "filterType", "setFilterType", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "showFindMe", "setShowFindMe", "headerRef", "currentUserRef", "motivationalQuotes", "performanceTiers", "legendary", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "effect", "diamond", "platinum", "gold", "silver", "bronze", "getUserTier", "xp", "tier", "config", "Object", "entries", "fetchRankingData", "console", "log", "rankingResponse", "usersResponse", "error", "userError", "transformedData", "success", "data", "userReportsMap", "for<PERSON>ach", "item", "_item$user", "userId", "_id", "reports", "filter", "userData", "map", "index", "userReports", "totalQuizzes", "length", "totalScore", "reduce", "sum", "report", "score", "averageScore", "Math", "round", "totalXP", "floor", "currentStreak", "bestStreak", "tempStreak", "max", "name", "email", "class", "level", "profilePicture", "totalQuizzesTaken", "subscriptionStatus", "rank", "isRealUser", "sort", "a", "b", "userRank", "findIndex", "demoData", "randomQuote", "random", "animationTimer", "setInterval", "prev", "clearInterval", "filteredData", "rankingUser", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "topPerformers", "slice", "otherPerformers", "handleFindMe", "current", "scrollIntoView", "behavior", "block", "setTimeout", "className", "children", "div", "initial", "opacity", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "_", "i", "y", "x", "delay", "style", "left", "top", "ref", "scale", "rotateY", "span", "backgroundPosition", "backgroundSize", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "fontWeight", "p", "background", "<PERSON><PERSON>ilter", "borderRadius", "padding", "border", "boxShadow", "textAlign", "label", "value", "iconColor", "bgGradient", "u", "toLocaleString", "stat", "whileHover", "type", "placeholder", "onChange", "e", "target", "key", "button", "whileTap", "onClick", "disabled", "champion", "position", "isCurrentUser", "src", "alt", "char<PERSON>t", "toUpperCase", "fontSize", "actualRank", "window", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbTarget,\n  TbBrain,\n  TbSearch,\n  TbFilter,\n  TbRefresh,\n  TbMedal,\n  TbBolt,\n  TbRocket,\n  TbDiamond,\n  TbHeart,\n  TbEye,\n  TbTrendingUp,\n  TbAward,\n  TbShield\n} from 'react-icons/tb';\nimport { getAllReportsForRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const user = userState.user || null;\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Performance tiers with SPECTACULAR visual themes and unique colors\n  const performanceTiers = {\n    legendary: {\n      min: 10000,\n      color: 'from-purple-400 via-pink-400 via-red-400 to-orange-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-pink-900/40 to-red-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FF69B4',\n      shadowColor: 'rgba(147, 51, 234, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery',\n      borderColor: '#8B5CF6',\n      effect: 'legendary-sparkle'\n    },\n    diamond: {\n      min: 7500,\n      color: 'from-cyan-200 via-blue-300 via-indigo-400 to-purple-500',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00E5FF',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 229, 255, 0.9)',\n      glow: 'shadow-cyan-300/80',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance',\n      borderColor: '#00E5FF',\n      effect: 'diamond-shine'\n    },\n    platinum: {\n      min: 5000,\n      color: 'from-slate-200 via-gray-300 via-zinc-400 to-stone-500',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#E8E8E8',\n      nameColor: '#C0C0C0',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-300/80',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam'\n    },\n    gold: {\n      min: 2500,\n      color: 'from-yellow-200 via-amber-300 via-orange-400 to-red-500',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-300/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent',\n      borderColor: '#FFD700',\n      effect: 'gold-glow'\n    },\n    silver: {\n      min: 1000,\n      color: 'from-gray-200 via-slate-300 via-zinc-400 to-gray-500',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#D3D3D3',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(211, 211, 211, 0.9)',\n      glow: 'shadow-gray-300/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress',\n      borderColor: '#D3D3D3',\n      effect: 'silver-shimmer'\n    },\n    bronze: {\n      min: 0,\n      color: 'from-orange-200 via-amber-300 via-yellow-400 to-orange-500',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-300/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = (xp) => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return { tier, ...config };\n    }\n    return { tier: 'bronze', ...performanceTiers.bronze };\n  };\n\n  // Fetch ranking data with multiple fallbacks and real user integration\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching amazing ranking data from database...');\n\n      let rankingResponse, usersResponse;\n\n      // Fetch both ranking reports and users data\n      try {\n        console.log('📊 Fetching ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n\n        console.log('✨ Ranking reports response:', rankingResponse);\n        console.log('👤 Users response:', usersResponse);\n      } catch (error) {\n        console.log('⚡ Error fetching data:', error);\n        // Try just users if reports fail\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      // Process real user data from database\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing real user data from database...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => userData && userData._id) // Filter out invalid users\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Calculate comprehensive stats\n            const totalQuizzes = userReports.length || 0;\n            const totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            const averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : 0;\n\n            // Calculate XP based on performance (more realistic calculation)\n            let totalXP = 0;\n            if (totalQuizzes > 0) {\n              totalXP = Math.floor(\n                (averageScore * totalQuizzes * 10) + // Base XP from scores\n                (totalQuizzes * 50) + // Bonus for participation\n                (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n              );\n            }\n\n            // Calculate streaks from reports\n            let currentStreak = 0;\n            let bestStreak = 0;\n            let tempStreak = 0;\n\n            userReports.forEach(report => {\n              if (report.score >= 60) { // Passing score\n                tempStreak++;\n                bestStreak = Math.max(bestStreak, tempStreak);\n              } else {\n                tempStreak = 0;\n              }\n            });\n            currentStreak = tempStreak;\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserTier(totalXP),\n              isRealUser: true\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank\n        const userRank = user ? transformedData.findIndex(item => item._id === user._id) : -1;\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'champions');\n      } else {\n        // Fallback demo data to showcase the amazing design\n        console.log('🎭 Loading demo data to showcase the amazing design...');\n        const demoData = [\n          {\n            _id: 'demo1',\n            name: 'Alex Champion',\n            email: '<EMAIL>',\n            class: '7',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 15000,\n            totalQuizzesTaken: 45,\n            averageScore: 92,\n            currentStreak: 12,\n            bestStreak: 18,\n            subscriptionStatus: 'premium',\n            rank: 1,\n            tier: getUserTier(15000)\n          },\n          {\n            _id: 'demo2',\n            name: 'Sarah Excellence',\n            email: '<EMAIL>',\n            class: '6',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 12500,\n            totalQuizzesTaken: 38,\n            averageScore: 88,\n            currentStreak: 8,\n            bestStreak: 15,\n            subscriptionStatus: 'premium',\n            rank: 2,\n            tier: getUserTier(12500)\n          },\n          {\n            _id: 'demo3',\n            name: 'Mike Achiever',\n            email: '<EMAIL>',\n            class: '7',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 9800,\n            totalQuizzesTaken: 32,\n            averageScore: 85,\n            currentStreak: 5,\n            bestStreak: 12,\n            subscriptionStatus: 'free',\n            rank: 3,\n            tier: getUserTier(9800)\n          },\n          {\n            _id: 'demo4',\n            name: 'Emma Rising',\n            email: '<EMAIL>',\n            class: '5',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 7200,\n            totalQuizzesTaken: 28,\n            averageScore: 82,\n            currentStreak: 3,\n            bestStreak: 9,\n            subscriptionStatus: 'free',\n            rank: 4,\n            tier: getUserTier(7200)\n          },\n          {\n            _id: 'demo5',\n            name: 'David Progress',\n            email: '<EMAIL>',\n            class: '6',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 5500,\n            totalQuizzesTaken: 22,\n            averageScore: 78,\n            currentStreak: 2,\n            bestStreak: 7,\n            subscriptionStatus: 'free',\n            rank: 5,\n            tier: getUserTier(5500)\n          }\n        ];\n\n        setRankingData(demoData);\n        setCurrentUserRank(null); // No current user in demo data\n        message.success('Welcome to the Hall of Champions! 🏆');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n    \n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    return () => clearInterval(animationTimer);\n  }, []);\n\n  // Filter and search functionality\n  const filteredData = rankingData.filter(rankingUser => {\n    const matchesSearch = rankingUser.name.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterType === 'all' ||\n                         (filterType === 'premium' && rankingUser.subscriptionStatus === 'premium') ||\n                         (filterType === 'free' && rankingUser.subscriptionStatus === 'free') ||\n                         (filterType === 'class' && user && rankingUser.class === user.class);\n    return matchesSearch && matchesFilter;\n  });\n\n  // Get top performers for special display\n  const topPerformers = filteredData.slice(0, 3);\n  const otherPerformers = filteredData.slice(3);\n\n  // Find Me functionality\n  const handleFindMe = () => {\n    if (currentUserRank && currentUserRef.current) {\n      setShowFindMe(true);\n      currentUserRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n\n      // Hide the find me highlight after 3 seconds\n      setTimeout(() => {\n        setShowFindMe(false);\n      }, 3000);\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center\"\n        >\n          <motion.div\n            animate={{ rotate: 360 }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n          />\n          <p className=\"text-white/80 text-lg font-medium\">Loading the Hall of Champions...</p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* SPECTACULAR HEADER */}\n        <motion.div\n          ref={headerRef}\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden\"\n        >\n          {/* Header Background with SPECTACULAR Gradient */}\n          <div className=\"bg-gradient-to-br from-purple-600 via-pink-500 via-red-500 via-orange-500 to-yellow-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n            \n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n                \n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-8\"\n                >\n                  <h1 className=\"text-5xl sm:text-6xl lg:text-8xl font-black mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(3px 3px 6px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)',\n                          '0 0 50px rgba(255,215,0,1), 0 0 80px rgba(255,215,0,0.8)',\n                          '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '4px 4px 8px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-xl sm:text-2xl lg:text-3xl font-semibold mb-8 max-w-4xl mx-auto leading-relaxed\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"relative max-w-2xl mx-auto mb-8\"\n                  style={{\n                    background: 'linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05))',\n                    backdropFilter: 'blur(20px)',\n                    borderRadius: '20px',\n                    padding: '24px',\n                    border: '2px solid rgba(255,255,255,0.2)',\n                    boxShadow: '0 8px 32px rgba(0,0,0,0.3)'\n                  }}\n                >\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-yellow-500/10 rounded-2xl\"></div>\n                  <motion.p\n                    key={motivationalQuote}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"text-lg sm:text-xl font-semibold relative z-10\"\n                    style={{\n                      color: '#FBBF24',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      textAlign: 'center'\n                    }}\n                  >\n                    {motivationalQuote}\n                  </motion.p>\n                </motion.div>\n\n                {/* Stats Overview */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.2, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbTrophy,\n                      label: 'Total Champions',\n                      value: rankingData.length,\n                      iconColor: '#FFD700',\n                      bgGradient: 'from-yellow-500/20 to-amber-600/20',\n                      borderColor: '#FFD700'\n                    },\n                    {\n                      icon: TbFlame,\n                      label: 'Active Streaks',\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      iconColor: '#FF6B35',\n                      bgGradient: 'from-orange-500/20 to-red-600/20',\n                      borderColor: '#FF6B35'\n                    },\n                    {\n                      icon: TbBrain,\n                      label: 'Quizzes Taken',\n                      value: rankingData.reduce((sum, u) => sum + u.totalQuizzesTaken, 0),\n                      iconColor: '#3B82F6',\n                      bgGradient: 'from-blue-500/20 to-indigo-600/20',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbBolt,\n                      label: 'Total XP',\n                      value: rankingData.reduce((sum, u) => sum + u.totalXP, 0).toLocaleString(),\n                      iconColor: '#8B5CF6',\n                      bgGradient: 'from-purple-500/20 to-violet-600/20',\n                      borderColor: '#8B5CF6'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={stat.label}\n                      whileHover={{ scale: 1.08, y: -8 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-8 h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-2xl sm:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-sm font-semibold relative z-10\"\n                        style={{\n                          color: '#E5E7EB',\n                          textShadow: '1px 1px 2px rgba(0,0,0,0.8)'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* INTERACTIVE CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1.5, duration: 0.8 }}\n          className=\"px-4 sm:px-6 lg:px-8 py-8\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10\">\n              <div className=\"flex flex-col lg:flex-row gap-6 items-center justify-between\">\n\n                {/* Search Bar */}\n                <div className=\"relative flex-1 max-w-md\">\n                  <TbSearch className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search champions...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"w-full pl-12 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300\"\n                  />\n                </div>\n\n                {/* Filter Controls */}\n                <div className=\"flex flex-wrap gap-3\">\n                  {[\n                    { key: 'all', label: 'All Champions', icon: TbTrophy },\n                    { key: 'premium', label: 'Premium', icon: TbCrown },\n                    { key: 'free', label: 'Free', icon: TbStar },\n                    { key: 'class', label: 'My Class', icon: TbTarget }\n                  ].map((filter) => (\n                    <motion.button\n                      key={filter.key}\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => setFilterType(filter.key)}\n                      className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${\n                        filterType === filter.key\n                          ? 'bg-purple-600 text-white shadow-lg shadow-purple-500/25'\n                          : 'bg-white/10 text-white/80 hover:bg-white/20'\n                      }`}\n                    >\n                      <filter.icon className=\"w-4 h-4\" />\n                      <span className=\"hidden sm:inline\">{filter.label}</span>\n                    </motion.button>\n                  ))}\n                </div>\n\n                {/* Find Me Button */}\n                {currentUserRank && (\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={handleFindMe}\n                    className=\"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300\"\n                    style={{\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                      color: '#000000',\n                      textShadow: 'none',\n                      fontWeight: '900'\n                    }}\n                  >\n                    <TbTarget className=\"w-5 h-5\" />\n                    <span>Find Me #{currentUserRank}</span>\n                  </motion.button>\n                )}\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50\"\n                >\n                  <TbRefresh className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 lg:px-8 pb-20\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-3xl sm:text-4xl font-bold text-white text-center mb-8\">\n                    🏆 Champions Podium 🏆\n                  </h2>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\">\n                    {topPerformers.map((champion, index) => {\n                      const position = index + 1;\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          initial={{ opacity: 0, y: 50 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: 0.7 + index * 0.2, duration: 0.8 }}\n                          whileHover={{ scale: 1.05, y: -10 }}\n                          className={`relative ${\n                            position === 1 ? 'md:order-2 md:scale-110' :\n                            position === 2 ? 'md:order-1' : 'md:order-3'\n                          }`}\n                        >\n                          {/* Podium Card */}\n                          <div\n                            className={`relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-2xl ${champion.tier.glow} shadow-2xl`}\n                            style={{\n                              boxShadow: `0 20px 40px ${champion.tier.shadowColor}, 0 0 60px ${champion.tier.shadowColor}`\n                            }}\n                          >\n                            <div\n                              className={`${champion.tier.bgColor} backdrop-blur-lg rounded-2xl p-6 text-center relative overflow-hidden`}\n                              style={{\n                                border: `2px solid ${champion.tier.borderColor}60`\n                              }}\n                            >\n                              <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent\"></div>\n\n                              {/* Position Badge */}\n                              <div\n                                className={`absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20`}\n                                style={{\n                                  color: '#FFFFFF',\n                                  textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                                  border: `3px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 8px 16px ${champion.tier.shadowColor}`\n                                }}\n                              >\n                                {position}\n                              </div>\n\n                              {/* Crown for #1 */}\n                              {position === 1 && (\n                                <motion.div\n                                  animate={{ rotate: [0, 10, -10, 0] }}\n                                  transition={{ duration: 2, repeat: Infinity }}\n                                  className=\"absolute -top-8 left-1/2 transform -translate-x-1/2\"\n                                >\n                                  <TbCrown className=\"w-8 h-8 text-yellow-400\" />\n                                </motion.div>\n                              )}\n\n                              {/* Profile Picture */}\n                              <div className={`relative mx-auto mb-4 ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''}`}>\n                                <div className=\"w-20 h-20 rounded-full overflow-hidden mx-auto bg-gradient-to-br from-purple-500 to-pink-500 p-1\">\n                                  {champion.profilePicture ? (\n                                    <img\n                                      src={champion.profilePicture}\n                                      alt={champion.name}\n                                      className=\"w-full h-full object-cover rounded-full\"\n                                    />\n                                  ) : (\n                                    <div className=\"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-2xl\">\n                                      {champion.name.charAt(0).toUpperCase()}\n                                    </div>\n                                  )}\n                                </div>\n                                {isCurrentUser && (\n                                  <div className=\"absolute -bottom-2 -right-2 bg-yellow-400 text-black rounded-full p-1\">\n                                    <TbStar className=\"w-4 h-4\" />\n                                  </div>\n                                )}\n                              </div>\n\n                              {/* Champion Info */}\n                              <h3\n                                className=\"text-xl font-black mb-2 relative z-10\"\n                                style={{\n                                  color: champion.tier.nameColor,\n                                  textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                                  fontSize: '1.5rem',\n                                  filter: 'drop-shadow(0 0 10px currentColor)'\n                                }}\n                              >\n                                {champion.name}\n                              </h3>\n                              <div\n                                className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${champion.tier.color} rounded-full text-sm font-black mb-3 relative z-10`}\n                                style={{\n                                  color: '#FFFFFF',\n                                  textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                                  border: `2px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 4px 12px ${champion.tier.shadowColor}`\n                                }}\n                              >\n                                <champion.tier.icon className=\"w-5 h-5\" style={{ filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }} />\n                                {champion.tier.title}\n                              </div>\n\n                              {/* Stats */}\n                              <div className=\"space-y-2\">\n                                <div className=\"flex justify-between text-sm\">\n                                  <span style={{\n                                    color: '#e5e7eb',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '600'\n                                  }}>XP:</span>\n                                  <span style={{\n                                    color: '#ffffff',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '800'\n                                  }}>{champion.totalXP.toLocaleString()}</span>\n                                </div>\n                                <div className=\"flex justify-between text-sm\">\n                                  <span style={{\n                                    color: '#e5e7eb',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '600'\n                                  }}>Quizzes:</span>\n                                  <span style={{\n                                    color: '#ffffff',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '800'\n                                  }}>{champion.totalQuizzesTaken}</span>\n                                </div>\n                                <div className=\"flex justify-between text-sm\">\n                                  <span style={{\n                                    color: '#e5e7eb',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '600'\n                                  }}>Streak:</span>\n                                  <span style={{\n                                    color: '#ffffff',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '800'\n                                  }} className=\"flex items-center gap-1\">\n                                    <TbFlame className=\"w-4 h-4 text-orange-400\" />\n                                    {champion.currentStreak}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* OTHER CHAMPIONS LIST */}\n              {otherPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"mt-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl font-bold text-white text-center mb-8\">\n                    ⚡ Rising Champions ⚡\n                  </h2>\n\n                  <div className=\"space-y-4\">\n                    {otherPerformers.map((champion, index) => {\n                      const actualRank = index + 4; // Since top 3 are shown separately\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          ref={isCurrentUser ? currentUserRef : null}\n                          initial={{ opacity: 0, x: -50 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                          whileHover={{ scale: 1.02, x: 10 }}\n                          className={`relative ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`}\n                        >\n                          <div\n                            className={`bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow}`}\n                            style={{\n                              boxShadow: `0 8px 24px ${champion.tier.shadowColor}`\n                            }}\n                          >\n                            <div\n                              className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 flex items-center gap-4 relative overflow-hidden`}\n                              style={{\n                                border: `1px solid ${champion.tier.borderColor}40`\n                              }}\n                            >\n                              <div className=\"absolute inset-0 bg-gradient-to-r from-white/5 to-transparent\"></div>\n\n                              {/* Rank */}\n                              <div\n                                className={`flex-shrink-0 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-10`}\n                                style={{\n                                  color: '#FFFFFF',\n                                  textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                                  border: `2px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 6px 12px ${champion.tier.shadowColor}`\n                                }}\n                              >\n                                {actualRank}\n                              </div>\n\n                              {/* Profile Picture */}\n                              <div className=\"flex-shrink-0\">\n                                <div className=\"w-14 h-14 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 to-pink-500 p-1\">\n                                  {champion.profilePicture ? (\n                                    <img\n                                      src={champion.profilePicture}\n                                      alt={champion.name}\n                                      className=\"w-full h-full object-cover rounded-full\"\n                                    />\n                                  ) : (\n                                    <div className=\"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-lg\">\n                                      {champion.name.charAt(0).toUpperCase()}\n                                    </div>\n                                  )}\n                                </div>\n                              </div>\n\n                              {/* Champion Info */}\n                              <div className=\"flex-1 min-w-0 relative z-10\">\n                                <div className=\"flex items-center gap-2 mb-1\">\n                                  <h3\n                                    className=\"text-lg font-black truncate\"\n                                    style={{\n                                      color: champion.tier.nameColor,\n                                      textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                      fontSize: '1.25rem',\n                                      filter: 'drop-shadow(0 0 8px currentColor)'\n                                    }}\n                                  >\n                                    {champion.name}\n                                  </h3>\n                                  {isCurrentUser && (\n                                    <div\n                                      className=\"px-3 py-1 rounded-full text-xs font-black animate-pulse\"\n                                      style={{\n                                        background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                        color: '#000000',\n                                        textShadow: 'none',\n                                        border: '2px solid #FFFFFF',\n                                        boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                                      }}\n                                    >\n                                      ⭐ YOU ⭐\n                                    </div>\n                                  )}\n                                </div>\n                                <div\n                                  className={`inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r ${champion.tier.color} rounded-full text-xs font-black`}\n                                  style={{\n                                    color: '#FFFFFF',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.9)',\n                                    border: `1px solid ${champion.tier.borderColor}`,\n                                    boxShadow: `0 2px 6px ${champion.tier.shadowColor}`\n                                  }}\n                                >\n                                  <champion.tier.icon className=\"w-4 h-4\" style={{ filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.5))' }} />\n                                  {champion.tier.title}\n                                </div>\n                              </div>\n\n                              {/* Stats */}\n                              <div className=\"flex-shrink-0 text-right\">\n                                <div className=\"text-lg mb-1\" style={{\n                                  color: '#ffffff',\n                                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                  fontWeight: '800'\n                                }}>\n                                  {champion.totalXP.toLocaleString()} XP\n                                </div>\n                                <div className=\"flex items-center gap-4 text-sm\">\n                                  <span className=\"flex items-center gap-1\" style={{\n                                    color: '#e5e7eb',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '600'\n                                  }}>\n                                    <TbBrain className=\"w-4 h-4\" />\n                                    {champion.totalQuizzesTaken}\n                                  </span>\n                                  <span className=\"flex items-center gap-1\" style={{\n                                    color: '#e5e7eb',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '600'\n                                  }}>\n                                    <TbFlame className=\"w-4 h-4 text-orange-400\" />\n                                    {champion.currentStreak}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold mb-2\" style={{\n                      color: '#ffffff',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>Your Current Position</h3>\n                    <div className=\"text-6xl font-black mb-2\" style={{\n                      color: '#fbbf24',\n                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                      fontWeight: '900'\n                    }}>#{currentUserRank}</div>\n                    <p className=\"text-lg\" style={{\n                      color: '#e5e7eb',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                      fontWeight: '600'\n                    }}>\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>Ready to Rise Higher?</h3>\n                  <p className=\"text-xl mb-6 max-w-2xl mx-auto\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {filteredData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbSearch className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Found</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Try adjusting your search or filter criteria to find more champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG1B,WAAW,CAAE2B,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,IAAI,GAAGH,SAAS,CAACG,IAAI,IAAI,IAAI;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACmD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMuD,SAAS,GAAGrD,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMsD,cAAc,GAAGtD,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMuD,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,gBAAgB,GAAG;IACvBC,SAAS,EAAE;MACTC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE3D,OAAO;MACb4D,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE;MACPZ,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEhD,SAAS;MACfiD,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,mBAAmB;MAChCC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDE,QAAQ,EAAE;MACRb,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE3C,QAAQ;MACd4C,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,aAAa;MAC1BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDG,IAAI,EAAE;MACJd,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE5D,QAAQ;MACd6D,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDI,MAAM,EAAE;MACNf,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEnD,OAAO;MACboD,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDK,MAAM,EAAE;MACNhB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE1D,MAAM;MACZ2D,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,aAAa;MAC1BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV;EACF,CAAC;;EAED;EACA,MAAMM,WAAW,GAAIC,EAAE,IAAK;IAC1B,KAAK,MAAM,CAACC,IAAI,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACxB,gBAAgB,CAAC,EAAE;MAC7D,IAAIoB,EAAE,IAAIE,MAAM,CAACpB,GAAG,EAAE,OAAO;QAAEmB,IAAI;QAAE,GAAGC;MAAO,CAAC;IAClD;IACA,OAAO;MAAED,IAAI,EAAE,QAAQ;MAAE,GAAGrB,gBAAgB,CAACkB;IAAO,CAAC;EACvD,CAAC;;EAED;EACA,MAAMO,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF7C,UAAU,CAAC,IAAI,CAAC;MAChB8C,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAEhE,IAAIC,eAAe,EAAEC,aAAa;;MAElC;MACA,IAAI;QACFH,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7CC,eAAe,GAAG,MAAM7D,uBAAuB,CAAC,CAAC;QACjD2D,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCE,aAAa,GAAG,MAAM7D,WAAW,CAAC,CAAC;QAEnC0D,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEC,eAAe,CAAC;QAC3DF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,aAAa,CAAC;MAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEG,KAAK,CAAC;QAC5C;QACA,IAAI;UACFD,aAAa,GAAG,MAAM7D,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC,OAAO+D,SAAS,EAAE;UAClBL,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEI,SAAS,CAAC;QACpD;MACF;;MAEA;MACA,IAAIC,eAAe,GAAG,EAAE;MAExB,IAAIH,aAAa,IAAIA,aAAa,CAACI,OAAO,IAAIJ,aAAa,CAACK,IAAI,EAAE;QAChER,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;;QAE5D;QACA,MAAMQ,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIP,eAAe,IAAIA,eAAe,CAACK,OAAO,IAAIL,eAAe,CAACM,IAAI,EAAE;UACtEN,eAAe,CAACM,IAAI,CAACE,OAAO,CAACC,IAAI,IAAI;YAAA,IAAAC,UAAA;YACnC,MAAMC,MAAM,GAAG,EAAAD,UAAA,GAAAD,IAAI,CAAC7D,IAAI,cAAA8D,UAAA,uBAATA,UAAA,CAAWE,GAAG,KAAIH,IAAI,CAACE,MAAM;YAC5C,IAAIA,MAAM,EAAE;cACVJ,cAAc,CAACI,MAAM,CAAC,GAAGF,IAAI,CAACI,OAAO,IAAI,EAAE;YAC7C;UACF,CAAC,CAAC;QACJ;QAEAT,eAAe,GAAGH,aAAa,CAACK,IAAI,CACjCQ,MAAM,CAACC,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAACH,GAAG,CAAC,CAAC;QAAA,CAC7CI,GAAG,CAAC,CAACD,QAAQ,EAAEE,KAAK,KAAK;UACxB;UACA,MAAMC,WAAW,GAAGX,cAAc,CAACQ,QAAQ,CAACH,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,MAAMO,YAAY,GAAGD,WAAW,CAACE,MAAM,IAAI,CAAC;UAC5C,MAAMC,UAAU,GAAGH,WAAW,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UACpF,MAAMC,YAAY,GAAGP,YAAY,GAAG,CAAC,GAAGQ,IAAI,CAACC,KAAK,CAACP,UAAU,GAAGF,YAAY,CAAC,GAAG,CAAC;;UAEjF;UACA,IAAIU,OAAO,GAAG,CAAC;UACf,IAAIV,YAAY,GAAG,CAAC,EAAE;YACpBU,OAAO,GAAGF,IAAI,CAACG,KAAK,CACjBJ,YAAY,GAAGP,YAAY,GAAG,EAAE;YAAI;YACpCA,YAAY,GAAG,EAAG;YAAG;YACrBO,YAAY,GAAG,EAAE,GAAGP,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAC9C,CAAC;UACH;;UAEA;UACA,IAAIY,aAAa,GAAG,CAAC;UACrB,IAAIC,UAAU,GAAG,CAAC;UAClB,IAAIC,UAAU,GAAG,CAAC;UAElBf,WAAW,CAACV,OAAO,CAACgB,MAAM,IAAI;YAC5B,IAAIA,MAAM,CAACC,KAAK,IAAI,EAAE,EAAE;cAAE;cACxBQ,UAAU,EAAE;cACZD,UAAU,GAAGL,IAAI,CAACO,GAAG,CAACF,UAAU,EAAEC,UAAU,CAAC;YAC/C,CAAC,MAAM;cACLA,UAAU,GAAG,CAAC;YAChB;UACF,CAAC,CAAC;UACFF,aAAa,GAAGE,UAAU;UAE1B,OAAO;YACLrB,GAAG,EAAEG,QAAQ,CAACH,GAAG;YACjBuB,IAAI,EAAEpB,QAAQ,CAACoB,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAErB,QAAQ,CAACqB,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEtB,QAAQ,CAACsB,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEvB,QAAQ,CAACuB,KAAK,IAAI,EAAE;YAC3BC,cAAc,EAAExB,QAAQ,CAACwB,cAAc,IAAI,EAAE;YAC7CV,OAAO,EAAEA,OAAO;YAChBW,iBAAiB,EAAErB,YAAY;YAC/BO,YAAY,EAAEA,YAAY;YAC1BK,aAAa,EAAEA,aAAa;YAC5BC,UAAU,EAAEA,UAAU;YACtBS,kBAAkB,EAAE1B,QAAQ,CAAC0B,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEzB,KAAK,GAAG,CAAC;YACfxB,IAAI,EAAEF,WAAW,CAACsC,OAAO,CAAC;YAC1Bc,UAAU,EAAE;UACd,CAAC;QACH,CAAC,CAAC;;QAEJ;QACAvC,eAAe,CAACwC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACjB,OAAO,GAAGgB,CAAC,CAAChB,OAAO,CAAC;;QAErD;QACAzB,eAAe,CAACI,OAAO,CAAC,CAAC5D,IAAI,EAAEqE,KAAK,KAAK;UACvCrE,IAAI,CAAC8F,IAAI,GAAGzB,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEFnE,cAAc,CAACsD,eAAe,CAAC;;QAE/B;QACA,MAAM2C,QAAQ,GAAGnG,IAAI,GAAGwD,eAAe,CAAC4C,SAAS,CAACvC,IAAI,IAAIA,IAAI,CAACG,GAAG,KAAKhE,IAAI,CAACgE,GAAG,CAAC,GAAG,CAAC,CAAC;QACrF1D,kBAAkB,CAAC6F,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;QAEvDjD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEK,eAAe,CAACgB,MAAM,EAAE,WAAW,CAAC;MACrF,CAAC,MAAM;QACL;QACAtB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrE,MAAMkD,QAAQ,GAAG,CACf;UACErC,GAAG,EAAE,OAAO;UACZuB,IAAI,EAAE,eAAe;UACrBC,KAAK,EAAE,kBAAkB;UACzBC,KAAK,EAAE,GAAG;UACVC,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBV,OAAO,EAAE,KAAK;UACdW,iBAAiB,EAAE,EAAE;UACrBd,YAAY,EAAE,EAAE;UAChBK,aAAa,EAAE,EAAE;UACjBC,UAAU,EAAE,EAAE;UACdS,kBAAkB,EAAE,SAAS;UAC7BC,IAAI,EAAE,CAAC;UACPjD,IAAI,EAAEF,WAAW,CAAC,KAAK;QACzB,CAAC,EACD;UACEqB,GAAG,EAAE,OAAO;UACZuB,IAAI,EAAE,kBAAkB;UACxBC,KAAK,EAAE,mBAAmB;UAC1BC,KAAK,EAAE,GAAG;UACVC,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBV,OAAO,EAAE,KAAK;UACdW,iBAAiB,EAAE,EAAE;UACrBd,YAAY,EAAE,EAAE;UAChBK,aAAa,EAAE,CAAC;UAChBC,UAAU,EAAE,EAAE;UACdS,kBAAkB,EAAE,SAAS;UAC7BC,IAAI,EAAE,CAAC;UACPjD,IAAI,EAAEF,WAAW,CAAC,KAAK;QACzB,CAAC,EACD;UACEqB,GAAG,EAAE,OAAO;UACZuB,IAAI,EAAE,eAAe;UACrBC,KAAK,EAAE,kBAAkB;UACzBC,KAAK,EAAE,GAAG;UACVC,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBV,OAAO,EAAE,IAAI;UACbW,iBAAiB,EAAE,EAAE;UACrBd,YAAY,EAAE,EAAE;UAChBK,aAAa,EAAE,CAAC;UAChBC,UAAU,EAAE,EAAE;UACdS,kBAAkB,EAAE,MAAM;UAC1BC,IAAI,EAAE,CAAC;UACPjD,IAAI,EAAEF,WAAW,CAAC,IAAI;QACxB,CAAC,EACD;UACEqB,GAAG,EAAE,OAAO;UACZuB,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,kBAAkB;UACzBC,KAAK,EAAE,GAAG;UACVC,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBV,OAAO,EAAE,IAAI;UACbW,iBAAiB,EAAE,EAAE;UACrBd,YAAY,EAAE,EAAE;UAChBK,aAAa,EAAE,CAAC;UAChBC,UAAU,EAAE,CAAC;UACbS,kBAAkB,EAAE,MAAM;UAC1BC,IAAI,EAAE,CAAC;UACPjD,IAAI,EAAEF,WAAW,CAAC,IAAI;QACxB,CAAC,EACD;UACEqB,GAAG,EAAE,OAAO;UACZuB,IAAI,EAAE,gBAAgB;UACtBC,KAAK,EAAE,mBAAmB;UAC1BC,KAAK,EAAE,GAAG;UACVC,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBV,OAAO,EAAE,IAAI;UACbW,iBAAiB,EAAE,EAAE;UACrBd,YAAY,EAAE,EAAE;UAChBK,aAAa,EAAE,CAAC;UAChBC,UAAU,EAAE,CAAC;UACbS,kBAAkB,EAAE,MAAM;UAC1BC,IAAI,EAAE,CAAC;UACPjD,IAAI,EAAEF,WAAW,CAAC,IAAI;QACxB,CAAC,CACF;QAEDzC,cAAc,CAACmG,QAAQ,CAAC;QACxB/F,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1BlC,OAAO,CAACqF,OAAO,CAAC,sCAAsC,CAAC;MACzD;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDlF,OAAO,CAACkF,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACRlD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACArC,SAAS,CAAC,MAAM;IACdkF,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMqD,WAAW,GAAG/E,kBAAkB,CAACwD,IAAI,CAACG,KAAK,CAACH,IAAI,CAACwB,MAAM,CAAC,CAAC,GAAGhF,kBAAkB,CAACiD,MAAM,CAAC,CAAC;IAC7FtD,oBAAoB,CAACoF,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvCzF,iBAAiB,CAAC0F,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,cAAc,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,YAAY,GAAG3G,WAAW,CAACiE,MAAM,CAAC2C,WAAW,IAAI;IACrD,MAAMC,aAAa,GAAGD,WAAW,CAACtB,IAAI,CAACwB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzG,UAAU,CAACwG,WAAW,CAAC,CAAC,CAAC;IACvF,MAAME,aAAa,GAAGxG,UAAU,KAAK,KAAK,IACpBA,UAAU,KAAK,SAAS,IAAIoG,WAAW,CAAChB,kBAAkB,KAAK,SAAU,IACzEpF,UAAU,KAAK,MAAM,IAAIoG,WAAW,CAAChB,kBAAkB,KAAK,MAAO,IACnEpF,UAAU,KAAK,OAAO,IAAIT,IAAI,IAAI6G,WAAW,CAACpB,KAAK,KAAKzF,IAAI,CAACyF,KAAM;IACzF,OAAOqB,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAGN,YAAY,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC9C,MAAMC,eAAe,GAAGR,YAAY,CAACO,KAAK,CAAC,CAAC,CAAC;;EAE7C;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIhH,eAAe,IAAIiB,cAAc,CAACgG,OAAO,EAAE;MAC7ClG,aAAa,CAAC,IAAI,CAAC;MACnBE,cAAc,CAACgG,OAAO,CAACC,cAAc,CAAC;QACpCC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACAC,UAAU,CAAC,MAAM;QACftG,aAAa,CAAC,KAAK,CAAC;MACtB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;;EAED;EACA,IAAIjB,OAAO,IAAIF,WAAW,CAACuE,MAAM,KAAK,CAAC,EAAE;IACvC,oBACE9E,OAAA;MAAKiI,SAAS,EAAC,4GAA4G;MAAAC,QAAA,eACzHlI,OAAA,CAACzB,MAAM,CAAC4J,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvBlI,OAAA,CAACzB,MAAM,CAAC4J,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DX,SAAS,EAAC;QAAqF;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACFhJ,OAAA;UAAGiI,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAgC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACEhJ,OAAA;IAAKiI,SAAS,EAAC,iHAAiH;IAAAC,QAAA,gBAE9HlI,OAAA;MAAKiI,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/ClI,OAAA;QAAKiI,SAAS,EAAC;MAA2H;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjJhJ,OAAA;QAAKiI,SAAS,EAAC;MAAkJ;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACxKhJ,OAAA;QAAKiI,SAAS,EAAC;MAA2I;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9J,CAAC,eAGNhJ,OAAA;MAAKiI,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClE,CAAC,GAAGe,KAAK,CAAC,EAAE,CAAC,CAAC,CAACvE,GAAG,CAAC,CAACwE,CAAC,EAAEC,CAAC,kBACvBnJ,OAAA,CAACzB,MAAM,CAAC4J,GAAG;QAETF,SAAS,EAAC,mDAAmD;QAC7DK,OAAO,EAAE;UACPc,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;UACfC,CAAC,EAAE,CAAC,CAAC,EAAEhE,IAAI,CAACwB,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;UACnCwB,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;QACzB,CAAE;QACFG,UAAU,EAAE;UACVC,QAAQ,EAAE,CAAC,GAAGpD,IAAI,CAACwB,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/B6B,MAAM,EAAEC,QAAQ;UAChBW,KAAK,EAAEjE,IAAI,CAACwB,MAAM,CAAC,CAAC,GAAG;QACzB,CAAE;QACF0C,KAAK,EAAE;UACLC,IAAI,EAAG,GAAEnE,IAAI,CAACwB,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;UAC/B4C,GAAG,EAAG,GAAEpE,IAAI,CAACwB,MAAM,CAAC,CAAC,GAAG,GAAI;QAC9B;MAAE,GAfGsC,CAAC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENhJ,OAAA;MAAKiI,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAE5BlI,OAAA,CAACzB,MAAM,CAAC4J,GAAG;QACTuB,GAAG,EAAE/H,SAAU;QACfyG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCd,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAE,CAAE;QAC9BZ,UAAU,EAAE;UAAEC,QAAQ,EAAE,CAAC;UAAEG,IAAI,EAAE;QAAU,CAAE;QAC7CX,SAAS,EAAC,0BAA0B;QAAAC,QAAA,eAGpClI,OAAA;UAAKiI,SAAS,EAAC,kGAAkG;UAAAC,QAAA,gBAC/GlI,OAAA;YAAKiI,SAAS,EAAC;UAA6E;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnGhJ,OAAA;YAAKiI,SAAS,EAAC;UAA+E;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGrGhJ,OAAA;YAAKiI,SAAS,EAAC,4DAA4D;YAAAC,QAAA,eACzElI,OAAA;cAAKiI,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAG5ClI,OAAA,CAACzB,MAAM,CAAC4J,GAAG;gBACTG,OAAO,EAAE;kBACPqB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;kBACnBC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnB,CAAE;gBACFpB,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXC,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR,CAAE;gBACFX,SAAS,EAAC,MAAM;gBAAAC,QAAA,eAEhBlI,OAAA;kBAAIiI,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,gBAC7ElI,OAAA,CAACzB,MAAM,CAACsL,IAAI;oBACVvB,OAAO,EAAE;sBACPwB,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;oBACrD,CAAE;oBACFtB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR,CAAE;oBACFX,SAAS,EAAC,+HAA+H;oBACzIsB,KAAK,EAAE;sBACLQ,cAAc,EAAE,WAAW;sBAC3BC,oBAAoB,EAAE,MAAM;sBAC5BC,mBAAmB,EAAE,aAAa;sBAClCzF,MAAM,EAAE;oBACV,CAAE;oBAAA0D,QAAA,EACH;kBAED;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eACdhJ,OAAA;oBAAA6I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNhJ,OAAA,CAACzB,MAAM,CAACsL,IAAI;oBACVvB,OAAO,EAAE;sBACP4B,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;oBAEhE,CAAE;oBACF1B,UAAU,EAAE;sBACVC,QAAQ,EAAE,GAAG;sBACbC,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR,CAAE;oBACFW,KAAK,EAAE;sBACLtH,KAAK,EAAE,SAAS;sBAChBkI,UAAU,EAAE,KAAK;sBACjBD,UAAU,EAAE;oBACd,CAAE;oBAAAhC,QAAA,EACH;kBAED;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAGbhJ,OAAA,CAACzB,MAAM,CAAC6L,CAAC;gBACPhC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAG,CAAE;gBAC/Bd,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAE,CAAE;gBAC9BZ,UAAU,EAAE;kBAAEc,KAAK,EAAE,GAAG;kBAAEb,QAAQ,EAAE;gBAAI,CAAE;gBAC1CR,SAAS,EAAC,sFAAsF;gBAChGsB,KAAK,EAAE;kBACLtH,KAAK,EAAE,SAAS;kBAChBiI,UAAU,EAAE,6BAA6B;kBACzCG,UAAU,EAAE,0CAA0C;kBACtDL,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE;gBACvB,CAAE;gBAAA/B,QAAA,EACH;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAGXhJ,OAAA,CAACzB,MAAM,CAAC4J,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEsB,KAAK,EAAE;gBAAI,CAAE;gBACpCrB,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEsB,KAAK,EAAE;gBAAE,CAAE;gBAClCnB,UAAU,EAAE;kBAAEc,KAAK,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAI,CAAE;gBACxCR,SAAS,EAAC,iCAAiC;gBAC3CsB,KAAK,EAAE;kBACLc,UAAU,EAAE,yEAAyE;kBACrFC,cAAc,EAAE,YAAY;kBAC5BC,YAAY,EAAE,MAAM;kBACpBC,OAAO,EAAE,MAAM;kBACfC,MAAM,EAAE,iCAAiC;kBACzCC,SAAS,EAAE;gBACb,CAAE;gBAAAxC,QAAA,gBAEFlI,OAAA;kBAAKiI,SAAS,EAAC;gBAAmG;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzHhJ,OAAA,CAACzB,MAAM,CAAC6L,CAAC;kBAEPhC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAG,CAAE;kBAC/Bd,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAE,CAAE;kBAC9BnB,SAAS,EAAC,gDAAgD;kBAC1DsB,KAAK,EAAE;oBACLtH,KAAK,EAAE,SAAS;oBAChBiI,UAAU,EAAE,6BAA6B;oBACzCS,SAAS,EAAE;kBACb,CAAE;kBAAAzC,QAAA,EAED3G;gBAAiB,GAVbA,iBAAiB;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGbhJ,OAAA,CAACzB,MAAM,CAAC4J,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAG,CAAE;gBAC/Bd,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAE,CAAE;gBAC9BZ,UAAU,EAAE;kBAAEc,KAAK,EAAE,GAAG;kBAAEb,QAAQ,EAAE;gBAAI,CAAE;gBAC1CR,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,EAE3E,CACC;kBACE3F,IAAI,EAAE5D,QAAQ;kBACdiM,KAAK,EAAE,iBAAiB;kBACxBC,KAAK,EAAEtK,WAAW,CAACuE,MAAM;kBACzBgG,SAAS,EAAE,SAAS;kBACpBC,UAAU,EAAE,oCAAoC;kBAChDrI,WAAW,EAAE;gBACf,CAAC,EACD;kBACEH,IAAI,EAAEzD,OAAO;kBACb8L,KAAK,EAAE,gBAAgB;kBACvBC,KAAK,EAAEtK,WAAW,CAACiE,MAAM,CAACwG,CAAC,IAAIA,CAAC,CAACvF,aAAa,GAAG,CAAC,CAAC,CAACX,MAAM;kBAC1DgG,SAAS,EAAE,SAAS;kBACpBC,UAAU,EAAE,kCAAkC;kBAC9CrI,WAAW,EAAE;gBACf,CAAC,EACD;kBACEH,IAAI,EAAEvD,OAAO;kBACb4L,KAAK,EAAE,eAAe;kBACtBC,KAAK,EAAEtK,WAAW,CAACyE,MAAM,CAAC,CAACC,GAAG,EAAE+F,CAAC,KAAK/F,GAAG,GAAG+F,CAAC,CAAC9E,iBAAiB,EAAE,CAAC,CAAC;kBACnE4E,SAAS,EAAE,SAAS;kBACpBC,UAAU,EAAE,mCAAmC;kBAC/CrI,WAAW,EAAE;gBACf,CAAC,EACD;kBACEH,IAAI,EAAElD,MAAM;kBACZuL,KAAK,EAAE,UAAU;kBACjBC,KAAK,EAAEtK,WAAW,CAACyE,MAAM,CAAC,CAACC,GAAG,EAAE+F,CAAC,KAAK/F,GAAG,GAAG+F,CAAC,CAACzF,OAAO,EAAE,CAAC,CAAC,CAAC0F,cAAc,CAAC,CAAC;kBAC1EH,SAAS,EAAE,SAAS;kBACpBC,UAAU,EAAE,qCAAqC;kBACjDrI,WAAW,EAAE;gBACf,CAAC,CACF,CAACgC,GAAG,CAAC,CAACwG,IAAI,EAAEvG,KAAK,kBAChB3E,OAAA,CAACzB,MAAM,CAAC4J,GAAG;kBAETgD,UAAU,EAAE;oBAAExB,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAE,CAAE;kBACnCnB,SAAS,EAAG,qBAAoBiD,IAAI,CAACH,UAAW,uEAAuE;kBACvHxB,KAAK,EAAE;oBACLkB,MAAM,EAAG,aAAYS,IAAI,CAACxI,WAAY,IAAG;oBACzCgI,SAAS,EAAG,cAAaQ,IAAI,CAACxI,WAAY;kBAC5C,CAAE;kBAAAwF,QAAA,gBAEFlI,OAAA;oBAAKiI,SAAS,EAAC;kBAAgE;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtFhJ,OAAA,CAACkL,IAAI,CAAC3I,IAAI;oBACR0F,SAAS,EAAC,oCAAoC;oBAC9CsB,KAAK,EAAE;sBAAEtH,KAAK,EAAEiJ,IAAI,CAACJ,SAAS;sBAAEtG,MAAM,EAAE;oBAAyC;kBAAE;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC,eACFhJ,OAAA;oBACEiI,SAAS,EAAC,oDAAoD;oBAC9DsB,KAAK,EAAE;sBACLtH,KAAK,EAAE,SAAS;sBAChBiI,UAAU,EAAE;oBACd,CAAE;oBAAAhC,QAAA,EAEDgD,IAAI,CAACL;kBAAK;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACNhJ,OAAA;oBACEiI,SAAS,EAAC,qCAAqC;oBAC/CsB,KAAK,EAAE;sBACLtH,KAAK,EAAE,SAAS;sBAChBiI,UAAU,EAAE;oBACd,CAAE;oBAAAhC,QAAA,EAEDgD,IAAI,CAACN;kBAAK;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC;gBAAA,GA9BDkC,IAAI,CAACN,KAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA+BL,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbhJ,OAAA,CAACzB,MAAM,CAAC4J,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAG,CAAE;QAC/Bd,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAE,CAAE;QAC9BZ,UAAU,EAAE;UAAEc,KAAK,EAAE,GAAG;UAAEb,QAAQ,EAAE;QAAI,CAAE;QAC1CR,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eAErClI,OAAA;UAAKiI,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChClI,OAAA;YAAKiI,SAAS,EAAC,oEAAoE;YAAAC,QAAA,eACjFlI,OAAA;cAAKiI,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAG3ElI,OAAA;gBAAKiI,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACvClI,OAAA,CAACf,QAAQ;kBAACgJ,SAAS,EAAC;gBAA0E;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjGhJ,OAAA;kBACEoL,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,qBAAqB;kBACjCR,KAAK,EAAEhK,UAAW;kBAClByK,QAAQ,EAAGC,CAAC,IAAKzK,aAAa,CAACyK,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;kBAC/C5C,SAAS,EAAC;gBAAiN;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5N,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNhJ,OAAA;gBAAKiI,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAClC,CACC;kBAAEuD,GAAG,EAAE,KAAK;kBAAEb,KAAK,EAAE,eAAe;kBAAErI,IAAI,EAAE5D;gBAAS,CAAC,EACtD;kBAAE8M,GAAG,EAAE,SAAS;kBAAEb,KAAK,EAAE,SAAS;kBAAErI,IAAI,EAAE3D;gBAAQ,CAAC,EACnD;kBAAE6M,GAAG,EAAE,MAAM;kBAAEb,KAAK,EAAE,MAAM;kBAAErI,IAAI,EAAE1D;gBAAO,CAAC,EAC5C;kBAAE4M,GAAG,EAAE,OAAO;kBAAEb,KAAK,EAAE,UAAU;kBAAErI,IAAI,EAAExD;gBAAS,CAAC,CACpD,CAAC2F,GAAG,CAAEF,MAAM,iBACXxE,OAAA,CAACzB,MAAM,CAACmN,MAAM;kBAEZP,UAAU,EAAE;oBAAExB,KAAK,EAAE;kBAAK,CAAE;kBAC5BgC,QAAQ,EAAE;oBAAEhC,KAAK,EAAE;kBAAK,CAAE;kBAC1BiC,OAAO,EAAEA,CAAA,KAAM5K,aAAa,CAACwD,MAAM,CAACiH,GAAG,CAAE;kBACzCxD,SAAS,EAAG,wFACVlH,UAAU,KAAKyD,MAAM,CAACiH,GAAG,GACrB,yDAAyD,GACzD,6CACL,EAAE;kBAAAvD,QAAA,gBAEHlI,OAAA,CAACwE,MAAM,CAACjC,IAAI;oBAAC0F,SAAS,EAAC;kBAAS;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnChJ,OAAA;oBAAMiI,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAE1D,MAAM,CAACoG;kBAAK;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAXnDxE,MAAM,CAACiH,GAAG;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYF,CAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGLrI,eAAe,iBACdX,OAAA,CAACzB,MAAM,CAACmN,MAAM;gBACZP,UAAU,EAAE;kBAAExB,KAAK,EAAE;gBAAK,CAAE;gBAC5BgC,QAAQ,EAAE;kBAAEhC,KAAK,EAAE;gBAAK,CAAE;gBAC1BiC,OAAO,EAAEjE,YAAa;gBACtBM,SAAS,EAAC,wKAAwK;gBAClLsB,KAAK,EAAE;kBACLc,UAAU,EAAE,0CAA0C;kBACtDpI,KAAK,EAAE,SAAS;kBAChBiI,UAAU,EAAE,MAAM;kBAClBC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,gBAEFlI,OAAA,CAACjB,QAAQ;kBAACkJ,SAAS,EAAC;gBAAS;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChChJ,OAAA;kBAAAkI,QAAA,GAAM,WAAS,EAACvH,eAAe;gBAAA;kBAAAkI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAChB,eAGDhJ,OAAA,CAACzB,MAAM,CAACmN,MAAM;gBACZP,UAAU,EAAE;kBAAExB,KAAK,EAAE,IAAI;kBAAEpB,MAAM,EAAE;gBAAI,CAAE;gBACzCoD,QAAQ,EAAE;kBAAEhC,KAAK,EAAE;gBAAK,CAAE;gBAC1BiC,OAAO,EAAErI,gBAAiB;gBAC1BsI,QAAQ,EAAEpL,OAAQ;gBAClBwH,SAAS,EAAC,4LAA4L;gBAAAC,QAAA,gBAEtMlI,OAAA,CAACb,SAAS;kBAAC8I,SAAS,EAAG,WAAUxH,OAAO,GAAG,cAAc,GAAG,EAAG;gBAAE;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpEhJ,OAAA;kBAAAkI,QAAA,EAAM;gBAAO;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGZvI,OAAO,iBACNT,OAAA,CAACzB,MAAM,CAAC4J,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBJ,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAE3DlI,OAAA,CAACzB,MAAM,CAAC4J,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DX,SAAS,EAAC;QAA6E;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,eACFhJ,OAAA;UAAGiI,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAoB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CACb,EAGA,CAACvI,OAAO,iBACPT,OAAA,CAACzB,MAAM,CAAC4J,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAG,CAAE;QAC/Bd,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAE,CAAE;QAC9BZ,UAAU,EAAE;UAAEc,KAAK,EAAE,GAAG;UAAEb,QAAQ,EAAE;QAAI,CAAE;QAC1CR,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eAEtClI,OAAA;UAAKiI,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAG/BV,aAAa,CAAC1C,MAAM,GAAG,CAAC,iBACvB9E,OAAA,CAACzB,MAAM,CAAC4J,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAI,CAAE;YACpCrB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAE,CAAE;YAClCnB,UAAU,EAAE;cAAEc,KAAK,EAAE,GAAG;cAAEb,QAAQ,EAAE;YAAI,CAAE;YAC1CR,SAAS,EAAC,OAAO;YAAAC,QAAA,gBAEjBlI,OAAA;cAAIiI,SAAS,EAAC,4DAA4D;cAAAC,QAAA,EAAC;YAE3E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELhJ,OAAA;cAAKiI,SAAS,EAAC,yDAAyD;cAAAC,QAAA,EACrEV,aAAa,CAAC9C,GAAG,CAAC,CAACoH,QAAQ,EAAEnH,KAAK,KAAK;gBACtC,MAAMoH,QAAQ,GAAGpH,KAAK,GAAG,CAAC;gBAC1B,MAAMqH,aAAa,GAAG1L,IAAI,IAAIwL,QAAQ,CAACxH,GAAG,KAAKhE,IAAI,CAACgE,GAAG;gBAEvD,oBACEtE,OAAA,CAACzB,MAAM,CAAC4J,GAAG;kBAETC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAG,CAAE;kBAC/Bd,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAE,CAAE;kBAC9BZ,UAAU,EAAE;oBAAEc,KAAK,EAAE,GAAG,GAAG3E,KAAK,GAAG,GAAG;oBAAE8D,QAAQ,EAAE;kBAAI,CAAE;kBACxD0C,UAAU,EAAE;oBAAExB,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCnB,SAAS,EAAG,YACV8D,QAAQ,KAAK,CAAC,GAAG,yBAAyB,GAC1CA,QAAQ,KAAK,CAAC,GAAG,YAAY,GAAG,YACjC,EAAE;kBAAA7D,QAAA,eAGHlI,OAAA;oBACEiI,SAAS,EAAG,8BAA6B6D,QAAQ,CAAC3I,IAAI,CAAClB,KAAM,oBAAmB6J,QAAQ,CAAC3I,IAAI,CAACb,IAAK,aAAa;oBAChHiH,KAAK,EAAE;sBACLmB,SAAS,EAAG,eAAcoB,QAAQ,CAAC3I,IAAI,CAACd,WAAY,cAAayJ,QAAQ,CAAC3I,IAAI,CAACd,WAAY;oBAC7F,CAAE;oBAAA6F,QAAA,eAEFlI,OAAA;sBACEiI,SAAS,EAAG,GAAE6D,QAAQ,CAAC3I,IAAI,CAACjB,OAAQ,wEAAwE;sBAC5GqH,KAAK,EAAE;wBACLkB,MAAM,EAAG,aAAYqB,QAAQ,CAAC3I,IAAI,CAACT,WAAY;sBACjD,CAAE;sBAAAwF,QAAA,gBAEFlI,OAAA;wBAAKiI,SAAS,EAAC;sBAAiE;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGvFhJ,OAAA;wBACEiI,SAAS,EAAG,mFAAkF6D,QAAQ,CAAC3I,IAAI,CAAClB,KAAM,2FAA2F;wBAC7MsH,KAAK,EAAE;0BACLtH,KAAK,EAAE,SAAS;0BAChBiI,UAAU,EAAE,6BAA6B;0BACzCO,MAAM,EAAG,aAAYqB,QAAQ,CAAC3I,IAAI,CAACT,WAAY,EAAC;0BAChDgI,SAAS,EAAG,cAAaoB,QAAQ,CAAC3I,IAAI,CAACd,WAAY;wBACrD,CAAE;wBAAA6F,QAAA,EAED6D;sBAAQ;wBAAAlD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,EAGL+C,QAAQ,KAAK,CAAC,iBACb/L,OAAA,CAACzB,MAAM,CAAC4J,GAAG;wBACTG,OAAO,EAAE;0BAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;wBAAE,CAAE;wBACrCC,UAAU,EAAE;0BAAEC,QAAQ,EAAE,CAAC;0BAAEC,MAAM,EAAEC;wBAAS,CAAE;wBAC9CV,SAAS,EAAC,qDAAqD;wBAAAC,QAAA,eAE/DlI,OAAA,CAACpB,OAAO;0BAACqJ,SAAS,EAAC;wBAAyB;0BAAAY,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CACb,eAGDhJ,OAAA;wBAAKiI,SAAS,EAAG,yBAAwB+D,aAAa,GAAG,wBAAwB,GAAG,EAAG,EAAE;wBAAA9D,QAAA,gBACvFlI,OAAA;0BAAKiI,SAAS,EAAC,kGAAkG;0BAAAC,QAAA,EAC9G4D,QAAQ,CAAC7F,cAAc,gBACtBjG,OAAA;4BACEiM,GAAG,EAAEH,QAAQ,CAAC7F,cAAe;4BAC7BiG,GAAG,EAAEJ,QAAQ,CAACjG,IAAK;4BACnBoC,SAAS,EAAC;0BAAyC;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFhJ,OAAA;4BAAKiI,SAAS,EAAC,yIAAyI;4BAAAC,QAAA,EACrJ4D,QAAQ,CAACjG,IAAI,CAACsG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAvD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnC;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,EACLgD,aAAa,iBACZhM,OAAA;0BAAKiI,SAAS,EAAC,uEAAuE;0BAAAC,QAAA,eACpFlI,OAAA,CAACnB,MAAM;4BAACoJ,SAAS,EAAC;0BAAS;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGNhJ,OAAA;wBACEiI,SAAS,EAAC,uCAAuC;wBACjDsB,KAAK,EAAE;0BACLtH,KAAK,EAAE6J,QAAQ,CAAC3I,IAAI,CAACf,SAAS;0BAC9B8H,UAAU,EAAG,eAAc4B,QAAQ,CAAC3I,IAAI,CAACd,WAAY,EAAC;0BACtDgK,QAAQ,EAAE,QAAQ;0BAClB7H,MAAM,EAAE;wBACV,CAAE;wBAAA0D,QAAA,EAED4D,QAAQ,CAACjG;sBAAI;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACLhJ,OAAA;wBACEiI,SAAS,EAAG,6DAA4D6D,QAAQ,CAAC3I,IAAI,CAAClB,KAAM,qDAAqD;wBACjJsH,KAAK,EAAE;0BACLtH,KAAK,EAAE,SAAS;0BAChBiI,UAAU,EAAE,6BAA6B;0BACzCO,MAAM,EAAG,aAAYqB,QAAQ,CAAC3I,IAAI,CAACT,WAAY,EAAC;0BAChDgI,SAAS,EAAG,cAAaoB,QAAQ,CAAC3I,IAAI,CAACd,WAAY;wBACrD,CAAE;wBAAA6F,QAAA,gBAEFlI,OAAA,CAAC8L,QAAQ,CAAC3I,IAAI,CAACZ,IAAI;0BAAC0F,SAAS,EAAC,SAAS;0BAACsB,KAAK,EAAE;4BAAE/E,MAAM,EAAE;0BAAyC;wBAAE;0BAAAqE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACtG8C,QAAQ,CAAC3I,IAAI,CAACX,KAAK;sBAAA;wBAAAqG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC,eAGNhJ,OAAA;wBAAKiI,SAAS,EAAC,WAAW;wBAAAC,QAAA,gBACxBlI,OAAA;0BAAKiI,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,gBAC3ClI,OAAA;4BAAMuJ,KAAK,EAAE;8BACXtH,KAAK,EAAE,SAAS;8BAChBiI,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,EAAC;0BAAG;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACbhJ,OAAA;4BAAMuJ,KAAK,EAAE;8BACXtH,KAAK,EAAE,SAAS;8BAChBiI,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,EAAE4D,QAAQ,CAACvG,OAAO,CAAC0F,cAAc,CAAC;0BAAC;4BAAApC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1C,CAAC,eACNhJ,OAAA;0BAAKiI,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,gBAC3ClI,OAAA;4BAAMuJ,KAAK,EAAE;8BACXtH,KAAK,EAAE,SAAS;8BAChBiI,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,EAAC;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAClBhJ,OAAA;4BAAMuJ,KAAK,EAAE;8BACXtH,KAAK,EAAE,SAAS;8BAChBiI,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,EAAE4D,QAAQ,CAAC5F;0BAAiB;4BAAA2C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnC,CAAC,eACNhJ,OAAA;0BAAKiI,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,gBAC3ClI,OAAA;4BAAMuJ,KAAK,EAAE;8BACXtH,KAAK,EAAE,SAAS;8BAChBiI,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,EAAC;0BAAO;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACjBhJ,OAAA;4BAAMuJ,KAAK,EAAE;8BACXtH,KAAK,EAAE,SAAS;8BAChBiI,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAClC,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,gBACpClI,OAAA,CAAClB,OAAO;8BAACmJ,SAAS,EAAC;4BAAyB;8BAAAY,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC9C8C,QAAQ,CAACrG,aAAa;0BAAA;4BAAAoD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GA3ID8C,QAAQ,CAACxH,GAAG;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4IP,CAAC;cAEjB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,EAGAtB,eAAe,CAAC5C,MAAM,GAAG,CAAC,iBACzB9E,OAAA,CAACzB,MAAM,CAAC4J,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAG,CAAE;YAC/Bd,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAE,CAAE;YAC9BZ,UAAU,EAAE;cAAEc,KAAK,EAAE,CAAC;cAAEb,QAAQ,EAAE;YAAI,CAAE;YACxCR,SAAS,EAAC,OAAO;YAAAC,QAAA,gBAEjBlI,OAAA;cAAIiI,SAAS,EAAC,4DAA4D;cAAAC,QAAA,EAAC;YAE3E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELhJ,OAAA;cAAKiI,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBR,eAAe,CAAChD,GAAG,CAAC,CAACoH,QAAQ,EAAEnH,KAAK,KAAK;gBACxC,MAAM2H,UAAU,GAAG3H,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC9B,MAAMqH,aAAa,GAAG1L,IAAI,IAAIwL,QAAQ,CAACxH,GAAG,KAAKhE,IAAI,CAACgE,GAAG;gBAEvD,oBACEtE,OAAA,CAACzB,MAAM,CAAC4J,GAAG;kBAETuB,GAAG,EAAEsC,aAAa,GAAGpK,cAAc,GAAG,IAAK;kBAC3CwG,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCf,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAE,CAAE;kBAC9Bb,UAAU,EAAE;oBAAEc,KAAK,EAAE,GAAG,GAAG3E,KAAK,GAAG,GAAG;oBAAE8D,QAAQ,EAAE;kBAAI,CAAE;kBACxD0C,UAAU,EAAE;oBAAExB,KAAK,EAAE,IAAI;oBAAEN,CAAC,EAAE;kBAAG,CAAE;kBACnCpB,SAAS,EAAG,YAAW+D,aAAa,GAAG,wBAAwB,GAAG,EAAG,IAAGvK,UAAU,IAAIuK,aAAa,GAAG,mBAAmB,GAAG,EAAG,EAAE;kBAAA9D,QAAA,eAEjIlI,OAAA;oBACEiI,SAAS,EAAG,oBAAmB6D,QAAQ,CAAC3I,IAAI,CAAClB,KAAM,mBAAkB6J,QAAQ,CAAC3I,IAAI,CAACb,IAAK,EAAE;oBAC1FiH,KAAK,EAAE;sBACLmB,SAAS,EAAG,cAAaoB,QAAQ,CAAC3I,IAAI,CAACd,WAAY;oBACrD,CAAE;oBAAA6F,QAAA,eAEFlI,OAAA;sBACEiI,SAAS,EAAG,GAAE6D,QAAQ,CAAC3I,IAAI,CAACjB,OAAQ,mFAAmF;sBACvHqH,KAAK,EAAE;wBACLkB,MAAM,EAAG,aAAYqB,QAAQ,CAAC3I,IAAI,CAACT,WAAY;sBACjD,CAAE;sBAAAwF,QAAA,gBAEFlI,OAAA;wBAAKiI,SAAS,EAAC;sBAA+D;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGrFhJ,OAAA;wBACEiI,SAAS,EAAG,6CAA4C6D,QAAQ,CAAC3I,IAAI,CAAClB,KAAM,2FAA2F;wBACvKsH,KAAK,EAAE;0BACLtH,KAAK,EAAE,SAAS;0BAChBiI,UAAU,EAAE,6BAA6B;0BACzCO,MAAM,EAAG,aAAYqB,QAAQ,CAAC3I,IAAI,CAACT,WAAY,EAAC;0BAChDgI,SAAS,EAAG,cAAaoB,QAAQ,CAAC3I,IAAI,CAACd,WAAY;wBACrD,CAAE;wBAAA6F,QAAA,EAEDoE;sBAAU;wBAAAzD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR,CAAC,eAGNhJ,OAAA;wBAAKiI,SAAS,EAAC,eAAe;wBAAAC,QAAA,eAC5BlI,OAAA;0BAAKiI,SAAS,EAAC,0FAA0F;0BAAAC,QAAA,EACtG4D,QAAQ,CAAC7F,cAAc,gBACtBjG,OAAA;4BACEiM,GAAG,EAAEH,QAAQ,CAAC7F,cAAe;4BAC7BiG,GAAG,EAAEJ,QAAQ,CAACjG,IAAK;4BACnBoC,SAAS,EAAC;0BAAyC;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFhJ,OAAA;4BAAKiI,SAAS,EAAC,wIAAwI;4BAAAC,QAAA,EACpJ4D,QAAQ,CAACjG,IAAI,CAACsG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAvD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnC;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNhJ,OAAA;wBAAKiI,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,gBAC3ClI,OAAA;0BAAKiI,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,gBAC3ClI,OAAA;4BACEiI,SAAS,EAAC,6BAA6B;4BACvCsB,KAAK,EAAE;8BACLtH,KAAK,EAAE6J,QAAQ,CAAC3I,IAAI,CAACf,SAAS;8BAC9B8H,UAAU,EAAG,eAAc4B,QAAQ,CAAC3I,IAAI,CAACd,WAAY,EAAC;8BACtDgK,QAAQ,EAAE,SAAS;8BACnB7H,MAAM,EAAE;4BACV,CAAE;4BAAA0D,QAAA,EAED4D,QAAQ,CAACjG;0BAAI;4BAAAgD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACZ,CAAC,EACJgD,aAAa,iBACZhM,OAAA;4BACEiI,SAAS,EAAC,yDAAyD;4BACnEsB,KAAK,EAAE;8BACLc,UAAU,EAAE,0CAA0C;8BACtDpI,KAAK,EAAE,SAAS;8BAChBiI,UAAU,EAAE,MAAM;8BAClBO,MAAM,EAAE,mBAAmB;8BAC3BC,SAAS,EAAE;4BACb,CAAE;4BAAAxC,QAAA,EACH;0BAED;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACNhJ,OAAA;0BACEiI,SAAS,EAAG,6DAA4D6D,QAAQ,CAAC3I,IAAI,CAAClB,KAAM,kCAAkC;0BAC9HsH,KAAK,EAAE;4BACLtH,KAAK,EAAE,SAAS;4BAChBiI,UAAU,EAAE,6BAA6B;4BACzCO,MAAM,EAAG,aAAYqB,QAAQ,CAAC3I,IAAI,CAACT,WAAY,EAAC;4BAChDgI,SAAS,EAAG,aAAYoB,QAAQ,CAAC3I,IAAI,CAACd,WAAY;0BACpD,CAAE;0BAAA6F,QAAA,gBAEFlI,OAAA,CAAC8L,QAAQ,CAAC3I,IAAI,CAACZ,IAAI;4BAAC0F,SAAS,EAAC,SAAS;4BAACsB,KAAK,EAAE;8BAAE/E,MAAM,EAAE;4BAAyC;0BAAE;4BAAAqE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACtG8C,QAAQ,CAAC3I,IAAI,CAACX,KAAK;wBAAA;0BAAAqG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNhJ,OAAA;wBAAKiI,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,gBACvClI,OAAA;0BAAKiI,SAAS,EAAC,cAAc;0BAACsB,KAAK,EAAE;4BACnCtH,KAAK,EAAE,SAAS;4BAChBiI,UAAU,EAAE,6BAA6B;4BACzCC,UAAU,EAAE;0BACd,CAAE;0BAAAjC,QAAA,GACC4D,QAAQ,CAACvG,OAAO,CAAC0F,cAAc,CAAC,CAAC,EAAC,KACrC;wBAAA;0BAAApC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACNhJ,OAAA;0BAAKiI,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,gBAC9ClI,OAAA;4BAAMiI,SAAS,EAAC,yBAAyB;4BAACsB,KAAK,EAAE;8BAC/CtH,KAAK,EAAE,SAAS;8BAChBiI,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,gBACAlI,OAAA,CAAChB,OAAO;8BAACiJ,SAAS,EAAC;4BAAS;8BAAAY,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC9B8C,QAAQ,CAAC5F,iBAAiB;0BAAA;4BAAA2C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB,CAAC,eACPhJ,OAAA;4BAAMiI,SAAS,EAAC,yBAAyB;4BAACsB,KAAK,EAAE;8BAC/CtH,KAAK,EAAE,SAAS;8BAChBiI,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,gBACAlI,OAAA,CAAClB,OAAO;8BAACmJ,SAAS,EAAC;4BAAyB;8BAAAY,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC9C8C,QAAQ,CAACrG,aAAa;0BAAA;4BAAAoD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GA5HD8C,QAAQ,CAACxH,GAAG;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6HP,CAAC;cAEjB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,EAGArI,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrCX,OAAA,CAACzB,MAAM,CAAC4J,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAI,CAAE;YACpCrB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAE,CAAE;YAClCnB,UAAU,EAAE;cAAEc,KAAK,EAAE,GAAG;cAAEb,QAAQ,EAAE;YAAI,CAAE;YAC1CR,SAAS,EAAC,wIAAwI;YAAAC,QAAA,eAElJlI,OAAA;cAAKiI,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlI,OAAA;gBAAIiI,SAAS,EAAC,yBAAyB;gBAACsB,KAAK,EAAE;kBAC7CtH,KAAK,EAAE,SAAS;kBAChBiI,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAAqB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BhJ,OAAA;gBAAKiI,SAAS,EAAC,0BAA0B;gBAACsB,KAAK,EAAE;kBAC/CtH,KAAK,EAAE,SAAS;kBAChBiI,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,GAAC,GAAC,EAACvH,eAAe;cAAA;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3BhJ,OAAA;gBAAGiI,SAAS,EAAC,SAAS;gBAACsB,KAAK,EAAE;kBAC5BtH,KAAK,EAAE,SAAS;kBAChBiI,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAGDhJ,OAAA,CAACzB,MAAM,CAAC4J,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAG,CAAE;YAC/Bd,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAE,CAAE;YAC9BZ,UAAU,EAAE;cAAEc,KAAK,EAAE,CAAC;cAAEb,QAAQ,EAAE;YAAI,CAAE;YACxCR,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAE7BlI,OAAA;cAAKiI,SAAS,EAAC,8HAA8H;cAAAC,QAAA,gBAC3IlI,OAAA,CAACzB,MAAM,CAAC4J,GAAG;gBACTG,OAAO,EAAE;kBAAEqB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;gBAAE,CAAE;gBACjCnB,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEC,MAAM,EAAEC;gBAAS,CAAE;gBAAAT,QAAA,eAE9ClI,OAAA,CAACV,QAAQ;kBAAC2I,SAAS,EAAC;gBAAwC;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACbhJ,OAAA;gBAAIiI,SAAS,EAAC,yBAAyB;gBAACsB,KAAK,EAAE;kBAC7CtH,KAAK,EAAE,SAAS;kBAChBiI,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAAqB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BhJ,OAAA;gBAAGiI,SAAS,EAAC,gCAAgC;gBAACsB,KAAK,EAAE;kBACnDtH,KAAK,EAAE,SAAS;kBAChBiI,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAGH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJhJ,OAAA,CAACzB,MAAM,CAACmN,MAAM;gBACZP,UAAU,EAAE;kBAAExB,KAAK,EAAE;gBAAK,CAAE;gBAC5BgC,QAAQ,EAAE;kBAAEhC,KAAK,EAAE;gBAAK,CAAE;gBAC1B1B,SAAS,EAAC,sJAAsJ;gBAChK2D,OAAO,EAAEA,CAAA,KAAMW,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAa;gBAAAvE,QAAA,EACpD;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EAGZ9B,YAAY,CAACpC,MAAM,KAAK,CAAC,IAAI,CAACrE,OAAO,iBACpCT,OAAA,CAACzB,MAAM,CAAC4J,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAI,CAAE;YACpCrB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAE,CAAE;YAClC1B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7BlI,OAAA,CAACf,QAAQ;cAACgJ,SAAS,EAAC;YAAsC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7DhJ,OAAA;cAAIiI,SAAS,EAAC,yBAAyB;cAACsB,KAAK,EAAE;gBAC7CtH,KAAK,EAAE,SAAS;gBAChBiI,UAAU,EAAE,6BAA6B;gBACzCC,UAAU,EAAE;cACd,CAAE;cAAAjC,QAAA,EAAC;YAAkB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BhJ,OAAA;cAAGiI,SAAS,EAAC,SAAS;cAACsB,KAAK,EAAE;gBAC5BtH,KAAK,EAAE,SAAS;gBAChBiI,UAAU,EAAE,6BAA6B;gBACzCC,UAAU,EAAE;cACd,CAAE;cAAAjC,QAAA,EAAC;YAEH;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9I,EAAA,CA5oCID,kBAAkB;EAAA,QACJxB,WAAW;AAAA;AAAAiO,EAAA,GADzBzM,kBAAkB;AA8oCxB,eAAeA,kBAAkB;AAAC,IAAAyM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}