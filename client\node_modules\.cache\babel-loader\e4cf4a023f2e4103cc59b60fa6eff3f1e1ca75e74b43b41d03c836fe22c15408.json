{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbTarget, TbBrain, TbRefresh, TbMedal, TbBolt, TbRocket, TbDiamond, TbHeart, TbEye, TbTrendingUp, TbAward, TbShield } from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const user = userState.user || null;\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Performance tiers with SPECTACULAR visual themes and unique colors\n  const performanceTiers = {\n    legendary: {\n      min: 10000,\n      color: 'from-purple-400 via-pink-400 via-red-400 to-orange-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-pink-900/40 to-red-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FF69B4',\n      shadowColor: 'rgba(147, 51, 234, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery',\n      borderColor: '#8B5CF6',\n      effect: 'legendary-sparkle'\n    },\n    diamond: {\n      min: 7500,\n      color: 'from-cyan-200 via-blue-300 via-indigo-400 to-purple-500',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00E5FF',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 229, 255, 0.9)',\n      glow: 'shadow-cyan-300/80',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance',\n      borderColor: '#00E5FF',\n      effect: 'diamond-shine'\n    },\n    platinum: {\n      min: 5000,\n      color: 'from-slate-200 via-gray-300 via-zinc-400 to-stone-500',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#E8E8E8',\n      nameColor: '#C0C0C0',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-300/80',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam'\n    },\n    gold: {\n      min: 2500,\n      color: 'from-yellow-200 via-amber-300 via-orange-400 to-red-500',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-300/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent',\n      borderColor: '#FFD700',\n      effect: 'gold-glow'\n    },\n    silver: {\n      min: 1000,\n      color: 'from-gray-200 via-slate-300 via-zinc-400 to-gray-500',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#D3D3D3',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(211, 211, 211, 0.9)',\n      glow: 'shadow-gray-300/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress',\n      borderColor: '#D3D3D3',\n      effect: 'silver-shimmer'\n    },\n    bronze: {\n      min: 0,\n      color: 'from-orange-200 via-amber-300 via-yellow-400 to-orange-500',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-300/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = xp => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return {\n        tier,\n        ...config\n      };\n    }\n    return {\n      tier: 'bronze',\n      ...performanceTiers.bronze\n    };\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: (user === null || user === void 0 ? void 0 : user.level) || 'all',\n          includeInactive: false\n        });\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n          const transformedData = xpLeaderboardResponse.data.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id));\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n      let rankingResponse, usersResponse;\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n      let transformedData = [];\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            var _item$user;\n            const userId = ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n        transformedData = usersResponse.data.filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n        .map((userData, index) => {\n          // Get reports for this user\n          const userReports = userReportsMap[userData._id] || [];\n\n          // Use existing user data or calculate from reports\n          let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n          let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n          let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n          // For existing users with old data, make intelligent assumptions\n          if (!userReports.length && userData.totalPoints) {\n            // Assume higher points = more exams and better performance\n            const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n            const estimatedAverage = Math.min(95, Math.max(60, 60 + userData.totalPoints / estimatedQuizzes / 10)); // Scale average based on points\n\n            totalQuizzes = estimatedQuizzes;\n            averageScore = Math.round(estimatedAverage);\n            totalScore = Math.round(averageScore * totalQuizzes);\n            console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n          }\n\n          // Calculate XP based on performance (enhanced calculation)\n          let totalXP = userData.totalXP || 0;\n          if (!totalXP) {\n            // Calculate XP from available data\n            if (userData.totalPoints) {\n              // Use existing points as base XP with bonuses\n              totalXP = Math.floor(userData.totalPoints +\n              // Base points\n              totalQuizzes * 25 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 15 : 0) + (\n              // Excellence bonus\n              averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n              );\n            } else if (totalQuizzes > 0) {\n              // Calculate from quiz performance\n              totalXP = Math.floor(averageScore * totalQuizzes * 8 +\n              // Base XP from scores\n              totalQuizzes * 40 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n              );\n            }\n          }\n\n          // Calculate streaks (enhanced logic)\n          let currentStreak = userData.currentStreak || 0;\n          let bestStreak = userData.bestStreak || 0;\n          if (userReports.length > 0) {\n            // Calculate from actual reports\n            let tempStreak = 0;\n            userReports.forEach(report => {\n              if (report.score >= 60) {\n                // Passing score\n                tempStreak++;\n                bestStreak = Math.max(bestStreak, tempStreak);\n              } else {\n                tempStreak = 0;\n              }\n            });\n            currentStreak = tempStreak;\n          } else if (userData.totalPoints && !currentStreak) {\n            // Estimate streaks from points (higher points = likely better streaks)\n            const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n            if (pointsPerQuiz > 80) {\n              currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n              bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n            }\n          }\n\n          return {\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: currentStreak,\n            bestStreak: bestStreak,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(totalXP),\n            isRealUser: true,\n            // Additional tracking fields for future updates\n            originalPoints: userData.totalPoints || 0,\n            hasReports: userReports.length > 0,\n            dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank\n        const userRank = user ? transformedData.findIndex(item => item._id === user._id) : -1;\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n    return () => clearInterval(animationTimer);\n  }, []);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Find Me functionality\n  const handleFindMe = () => {\n    if (currentUserRank && currentUserRef.current) {\n      setShowFindMe(true);\n      currentUserRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n\n      // Hide the find me highlight after 3 seconds\n      setTimeout(() => {\n        setShowFindMe(false);\n      }, 3000);\n    }\n  };\n\n  // Get subscription status badge based on actual active plan\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription (can log in and use the system)\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // Determine plan type from activePlanTitle or subscriptionPlan\n        const planTitle = (activePlanTitle === null || activePlanTitle === void 0 ? void 0 : activePlanTitle.toLowerCase()) || (subscriptionPlan === null || subscriptionPlan === void 0 ? void 0 : subscriptionPlan.toLowerCase()) || '';\n        console.log('Plan Title Check:', planTitle);\n\n        // Premium Activated - for premium/pro plans (6-month or yearly)\n        if (planTitle.includes('premium') || planTitle.includes('pro') || planTitle.includes('yearly') || planTitle.includes('6-month') || planTitle.includes('6 month') || subscriptionPlan === 'premium' || subscriptionPlan === 'pro') {\n          return {\n            text: 'PREMIUM ACTIVATED',\n            color: '#3B82F6',\n            // Blue\n            bgColor: 'rgba(59, 130, 246, 0.2)',\n            borderColor: '#3B82F6'\n          };\n        }\n\n        // Standard Activated - for standard/intermediate plans\n        else if (planTitle.includes('standard') || planTitle.includes('intermediate') || planTitle.includes('plus') || subscriptionPlan === 'standard') {\n          return {\n            text: 'STANDARD ACTIVATED',\n            color: '#F59E0B',\n            // Orange/Yellow\n            bgColor: 'rgba(245, 158, 11, 0.2)',\n            borderColor: '#F59E0B'\n          };\n        }\n\n        // Basic Activated - for basic plans (can log in and use system)\n        else {\n          return {\n            text: 'BASIC ACTIVATED',\n            color: '#10B981',\n            // Green\n            bgColor: 'rgba(16, 185, 129, 0.2)',\n            borderColor: '#10B981'\n          };\n        }\n      } else {\n        // Subscription status is active/premium but end date has passed\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444',\n          // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - user has no current plan\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444',\n        // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading the Hall of Champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n      children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n        animate: {\n          y: [0, -100, 0],\n          x: [0, Math.random() * 100 - 50, 0],\n          opacity: [0.2, 0.8, 0.2]\n        },\n        transition: {\n          duration: 3 + Math.random() * 2,\n          repeat: Infinity,\n          delay: Math.random() * 2\n        },\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        ref: headerRef,\n        initial: {\n          opacity: 0,\n          y: -50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 1,\n          ease: \"easeOut\"\n        },\n        className: \"relative overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-purple-600 via-pink-500 via-red-500 via-orange-500 to-yellow-500 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10 px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-7xl mx-auto text-center\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  scale: [1, 1.02, 1],\n                  rotateY: [0, 5, 0]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                className: \"mb-8\",\n                children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-5xl sm:text-6xl lg:text-8xl font-black mb-4 tracking-tight\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    animate: {\n                      backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                    },\n                    transition: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"linear\"\n                    },\n                    className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                    style: {\n                      backgroundSize: '400% 400%',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      filter: 'drop-shadow(3px 3px 6px rgba(0,0,0,0.8))'\n                    },\n                    children: \"HALL OF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    animate: {\n                      textShadow: ['0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)', '0 0 50px rgba(255,215,0,1), 0 0 80px rgba(255,215,0,0.8)', '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)']\n                    },\n                    transition: {\n                      duration: 2.5,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    style: {\n                      color: '#FFD700',\n                      fontWeight: '900',\n                      textShadow: '4px 4px 8px rgba(0,0,0,0.9)'\n                    },\n                    children: \"CHAMPIONS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.5,\n                  duration: 0.8\n                },\n                className: \"text-xl sm:text-2xl lg:text-3xl font-semibold mb-8 max-w-4xl mx-auto leading-relaxed\",\n                style: {\n                  color: '#F3F4F6',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent'\n                },\n                children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  delay: 1,\n                  duration: 0.8\n                },\n                className: \"relative max-w-2xl mx-auto mb-8\",\n                style: {\n                  background: 'linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05))',\n                  backdropFilter: 'blur(20px)',\n                  borderRadius: '20px',\n                  padding: '24px',\n                  border: '2px solid rgba(255,255,255,0.2)',\n                  boxShadow: '0 8px 32px rgba(0,0,0,0.3)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-yellow-500/10 rounded-2xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 10\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"text-lg sm:text-xl font-semibold relative z-10\",\n                  style: {\n                    color: '#FBBF24',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    textAlign: 'center'\n                  },\n                  children: motivationalQuote\n                }, motivationalQuote, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 30\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 1.2,\n                  duration: 0.8\n                },\n                className: \"grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto\",\n                children: [{\n                  icon: TbTrophy,\n                  label: 'Total Champions',\n                  value: rankingData.length,\n                  iconColor: '#FFD700',\n                  bgGradient: 'from-yellow-500/20 to-amber-600/20',\n                  borderColor: '#FFD700'\n                }, {\n                  icon: TbFlame,\n                  label: 'Active Streaks',\n                  value: rankingData.filter(u => u.currentStreak > 0).length,\n                  iconColor: '#FF6B35',\n                  bgGradient: 'from-orange-500/20 to-red-600/20',\n                  borderColor: '#FF6B35'\n                }, {\n                  icon: TbBrain,\n                  label: 'Quizzes Taken',\n                  value: rankingData.reduce((sum, u) => sum + u.totalQuizzesTaken, 0),\n                  iconColor: '#3B82F6',\n                  bgGradient: 'from-blue-500/20 to-indigo-600/20',\n                  borderColor: '#3B82F6'\n                }, {\n                  icon: TbBolt,\n                  label: 'Total XP',\n                  value: rankingData.reduce((sum, u) => sum + u.totalXP, 0).toLocaleString(),\n                  iconColor: '#8B5CF6',\n                  bgGradient: 'from-purple-500/20 to-violet-600/20',\n                  borderColor: '#8B5CF6'\n                }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  whileHover: {\n                    scale: 1.08,\n                    y: -8\n                  },\n                  className: `bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                  style: {\n                    border: `2px solid ${stat.borderColor}40`,\n                    boxShadow: `0 8px 32px ${stat.borderColor}20`\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                    className: \"w-8 h-8 mx-auto mb-2 relative z-10\",\n                    style: {\n                      color: stat.iconColor,\n                      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 732,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl sm:text-3xl font-black mb-1 relative z-10\",\n                    style: {\n                      color: stat.iconColor,\n                      textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                      filter: 'drop-shadow(0 0 10px currentColor)',\n                      fontSize: '2.5rem'\n                    },\n                    children: stat.value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold relative z-10\",\n                    style: {\n                      color: '#FFFFFF',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                      fontSize: '1rem'\n                    },\n                    children: stat.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 747,\n                    columnNumber: 23\n                  }, this)]\n                }, stat.label, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 1.5,\n          duration: 0.8\n        },\n        className: \"px-4 sm:px-6 lg:px-8 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4 items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: handleFindMe,\n                className: \"flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-black rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300\",\n                style: {\n                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                  color: '#000000',\n                  textShadow: 'none',\n                  fontWeight: '900',\n                  fontSize: '1.1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: currentUserRank ? `Find Me #${currentUserRank}` : 'Find Me'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05,\n                  rotate: 180\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: fetchRankingData,\n                disabled: loading,\n                className: \"flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50\",\n                style: {\n                  fontSize: '1.1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                  className: `w-6 h-6 ${loading ? 'animate-spin' : ''}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Refresh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 772,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 766,\n        columnNumber: 9\n      }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"flex flex-col items-center justify-center py-20\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 827,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 11\n      }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3,\n          duration: 0.8\n        },\n        className: \"px-4 sm:px-6 lg:px-8 pb-20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              delay: 0.5,\n              duration: 0.8\n            },\n            className: \"mb-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl sm:text-4xl font-black text-center mb-8\",\n              style: {\n                background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                filter: 'drop-shadow(0 0 15px #FFD700)'\n              },\n              children: \"\\uD83C\\uDFC6 CHAMPIONS PODIUM \\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\",\n              children: topPerformers.map((champion, index) => {\n                const position = index + 1;\n                const isCurrentUser = user && champion._id === user._id;\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.7 + index * 0.2,\n                    duration: 0.8\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative ${position === 1 ? 'md:order-2 md:scale-110' : position === 2 ? 'md:order-1' : 'md:order-3'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-2xl ${champion.tier.glow} ${champion.tier.effect} shadow-2xl`,\n                    style: {\n                      boxShadow: `0 20px 40px ${champion.tier.shadowColor}, 0 0 60px ${champion.tier.shadowColor}`\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${champion.tier.bgColor} backdrop-blur-lg rounded-2xl p-6 text-center relative overflow-hidden`,\n                      style: {\n                        border: `2px solid ${champion.tier.borderColor}60`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 889,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `absolute -top-6 left-1/2 transform -translate-x-1/2 w-16 h-16 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-2xl shadow-lg relative z-20`,\n                        style: {\n                          color: position === 1 ? '#FFD700' : position === 2 ? '#C0C0C0' : position === 3 ? '#CD7F32' : '#FFFFFF',\n                          textShadow: '3px 3px 6px rgba(0,0,0,0.9)',\n                          border: `4px solid ${champion.tier.borderColor}`,\n                          boxShadow: `0 12px 24px ${champion.tier.shadowColor}, 0 0 30px ${champion.tier.shadowColor}`,\n                          fontSize: '1.5rem',\n                          fontWeight: '900'\n                        },\n                        children: position === 1 ? '👑' : position === 2 ? '🥈' : position === 3 ? '🥉' : position\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 892,\n                        columnNumber: 31\n                      }, this), position === 1 && /*#__PURE__*/_jsxDEV(motion.div, {\n                        animate: {\n                          rotate: [0, 10, -10, 0]\n                        },\n                        transition: {\n                          duration: 2,\n                          repeat: Infinity\n                        },\n                        className: \"absolute -top-8 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                          className: \"w-8 h-8 text-yellow-400\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 913,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 908,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-4 ${isCurrentUser ? 'ring-4 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-24 h-24 rounded-full overflow-hidden mx-auto relative\",\n                          style: {\n                            background: `linear-gradient(45deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                            boxShadow: `0 8px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                            padding: '3px'\n                          },\n                          children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: champion.profilePicture,\n                            alt: champion.name,\n                            className: \"w-full h-full object-cover rounded-full\",\n                            style: {\n                              filter: 'brightness(1.1) contrast(1.1)',\n                              aspectRatio: '1/1'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 928,\n                            columnNumber: 37\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-full h-full rounded-full flex items-center justify-center font-black text-2xl\",\n                            style: {\n                              background: `linear-gradient(135deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                              color: '#FFFFFF',\n                              textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                              aspectRatio: '1/1'\n                            },\n                            children: champion.name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 938,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 919,\n                          columnNumber: 33\n                        }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                            boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-5 h-5 text-black\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 959,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 952,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 918,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-xl font-black mb-2 relative z-10\",\n                        style: {\n                          color: champion.tier.nameColor,\n                          textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                          fontSize: '1.5rem',\n                          filter: 'drop-shadow(0 0 10px currentColor)'\n                        },\n                        children: champion.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 965,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `inline-flex items-center gap-2 px-5 py-3 bg-gradient-to-r ${champion.tier.color} rounded-full text-sm font-black mb-3 relative z-10 animate-pulse`,\n                        style: {\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          border: `3px solid ${champion.tier.borderColor}`,\n                          boxShadow: `0 6px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                          fontSize: '0.9rem',\n                          letterSpacing: '0.5px'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                          className: \"w-6 h-6\",\n                          style: {\n                            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))',\n                            color: champion.tier.textColor\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 987,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: '#FFFFFF'\n                          },\n                          children: champion.tier.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 994,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 976,\n                        columnNumber: 31\n                      }, this), (() => {\n                        const badge = getSubscriptionBadge(champion.subscriptionStatus, champion.subscriptionEndDate, champion.subscriptionPlan, index);\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-bold mb-3 relative z-10\",\n                          style: {\n                            backgroundColor: badge.bgColor,\n                            color: badge.color,\n                            border: `2px solid ${badge.borderColor}`,\n                            textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                            fontSize: '0.75rem',\n                            letterSpacing: '0.5px'\n                          },\n                          children: badge.text\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1006,\n                          columnNumber: 35\n                        }, this);\n                      })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-3 relative z-10\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between text-base\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: champion.tier.textColor,\n                              textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                              fontWeight: '700',\n                              fontSize: '1rem'\n                            },\n                            children: \"\\uD83D\\uDC8E XP:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1025,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: champion.tier.nameColor,\n                              textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                              fontWeight: '900',\n                              fontSize: '1.1rem',\n                              filter: 'drop-shadow(0 0 8px currentColor)'\n                            },\n                            children: champion.totalXP.toLocaleString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1031,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1024,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between text-base\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: champion.tier.textColor,\n                              textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                              fontWeight: '700',\n                              fontSize: '1rem'\n                            },\n                            children: \"\\uD83E\\uDDE0 Quizzes:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1040,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: champion.tier.nameColor,\n                              textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                              fontWeight: '900',\n                              fontSize: '1.1rem',\n                              filter: 'drop-shadow(0 0 8px currentColor)'\n                            },\n                            children: champion.totalQuizzesTaken\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1046,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1039,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between text-base\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: champion.tier.textColor,\n                              textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                              fontWeight: '700',\n                              fontSize: '1rem'\n                            },\n                            children: \"\\uD83D\\uDD25 Streak:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1055,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: champion.tier.nameColor,\n                              textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                              fontWeight: '900',\n                              fontSize: '1.1rem',\n                              filter: 'drop-shadow(0 0 8px currentColor)'\n                            },\n                            className: \"flex items-center gap-1\",\n                            children: champion.currentStreak\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1061,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1054,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-center mt-2\",\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: champion.tier.textColor,\n                              textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                              fontWeight: '600',\n                              fontSize: '0.7rem',\n                              opacity: 0.8\n                            },\n                            children: champion.dataSource === 'reports' ? '📊 Live Data' : champion.dataSource === 'legacy_points' ? '📈 Legacy Points' : '🔮 Estimated'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1074,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1073,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1023,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 883,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 877,\n                    columnNumber: 27\n                  }, this)\n                }, champion._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 865,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 843,\n            columnNumber: 17\n          }, this), otherPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 1,\n              duration: 0.8\n            },\n            className: \"mt-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl sm:text-3xl font-black text-center mb-8\",\n              style: {\n                background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                filter: 'drop-shadow(0 0 12px #8B5CF6)'\n              },\n              children: \"\\u26A1 RISING CHAMPIONS \\u26A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1104,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: otherPerformers.map((champion, index) => {\n                const actualRank = index + 4; // Since top 3 are shown separately\n                const isCurrentUser = user && champion._id === user._id;\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: isCurrentUser ? currentUserRef : null,\n                  initial: {\n                    opacity: 0,\n                    x: -50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 1.2 + index * 0.1,\n                    duration: 0.6\n                  },\n                  whileHover: {\n                    scale: 1.02,\n                    x: 10\n                  },\n                  className: `relative ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow} ${champion.tier.effect}`,\n                    style: {\n                      boxShadow: `0 8px 24px ${champion.tier.shadowColor}`\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 flex items-center gap-4 relative overflow-hidden`,\n                      style: {\n                        border: `1px solid ${champion.tier.borderColor}40`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1141,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `flex-shrink-0 w-14 h-14 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-10`,\n                        style: {\n                          color: actualRank <= 10 ? champion.tier.textColor : '#FFFFFF',\n                          textShadow: '3px 3px 6px rgba(0,0,0,0.9)',\n                          border: `3px solid ${champion.tier.borderColor}`,\n                          boxShadow: `0 8px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                          fontSize: actualRank <= 10 ? '1.3rem' : '1.1rem',\n                          fontWeight: '900'\n                        },\n                        children: actualRank <= 10 ? `#${actualRank}` : actualRank\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1144,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0 relative\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-14 h-14 rounded-full overflow-hidden p-0.5 relative\",\n                          style: {\n                            background: `linear-gradient(45deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                            boxShadow: `0 6px 12px ${champion.tier.shadowColor}, 0 0 15px ${champion.tier.shadowColor}`\n                          },\n                          children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: champion.profilePicture,\n                            alt: champion.name,\n                            className: \"w-full h-full object-cover rounded-full\",\n                            style: {\n                              filter: 'brightness(1.1) contrast(1.1) saturate(1.2)',\n                              aspectRatio: '1/1'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1168,\n                            columnNumber: 37\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-full h-full rounded-full flex items-center justify-center font-black text-lg\",\n                            style: {\n                              background: `linear-gradient(135deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                              color: '#FFFFFF',\n                              textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                              aspectRatio: '1/1'\n                            },\n                            children: champion.name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1178,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1160,\n                          columnNumber: 33\n                        }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -top-1 -right-1 rounded-full p-1 animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                            boxShadow: '0 2px 8px rgba(255,215,0,0.6)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-3 h-3 text-black\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1199,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1192,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1159,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 min-w-0 relative z-10\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-2 mb-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-lg font-black truncate\",\n                            style: {\n                              color: champion.tier.nameColor,\n                              textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                              fontSize: '1.25rem',\n                              filter: 'drop-shadow(0 0 8px currentColor)'\n                            },\n                            children: champion.name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1207,\n                            columnNumber: 35\n                          }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"px-3 py-1 rounded-full text-xs font-black animate-pulse\",\n                            style: {\n                              background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                              color: '#000000',\n                              textShadow: 'none',\n                              border: '2px solid #FFFFFF',\n                              boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                            },\n                            children: \"\\u2B50 YOU \\u2B50\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1219,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1206,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${champion.tier.color} rounded-full text-xs font-black`,\n                          style: {\n                            color: '#FFFFFF',\n                            textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                            border: `2px solid ${champion.tier.borderColor}`,\n                            boxShadow: `0 4px 8px ${champion.tier.shadowColor}, 0 0 12px ${champion.tier.shadowColor}`,\n                            fontSize: '0.8rem',\n                            letterSpacing: '0.3px'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                            className: \"w-4 h-4\",\n                            style: {\n                              filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.5))',\n                              color: champion.tier.textColor\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1244,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#FFFFFF'\n                            },\n                            children: champion.tier.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1251,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1233,\n                          columnNumber: 33\n                        }, this), (() => {\n                          const badge = getSubscriptionBadge(champion.subscriptionStatus, champion.subscriptionEndDate, champion.subscriptionPlan, actualRank);\n                          return /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold mt-1\",\n                            style: {\n                              backgroundColor: badge.bgColor,\n                              color: badge.color,\n                              border: `1px solid ${badge.borderColor}`,\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                              fontSize: '0.7rem',\n                              letterSpacing: '0.3px'\n                            },\n                            children: badge.text\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1263,\n                            columnNumber: 37\n                          }, this);\n                        })()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1205,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0 text-right relative z-10\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xl mb-2\",\n                          style: {\n                            color: champion.tier.nameColor,\n                            textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                            fontWeight: '900',\n                            filter: 'drop-shadow(0 0 10px currentColor)',\n                            fontSize: '1.3rem'\n                          },\n                          children: [\"\\uD83D\\uDC8E \", champion.totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1282,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-3 text-sm justify-end\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"flex items-center gap-1\",\n                            style: {\n                              color: champion.tier.textColor,\n                              textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                              fontWeight: '700',\n                              fontSize: '0.9rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                              className: \"w-4 h-4\",\n                              style: {\n                                color: champion.tier.textColor\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1298,\n                              columnNumber: 37\n                            }, this), champion.totalQuizzesTaken]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1292,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"flex items-center gap-1\",\n                            style: {\n                              color: champion.tier.textColor,\n                              textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                              fontWeight: '700',\n                              fontSize: '0.9rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                              className: \"w-4 h-4\",\n                              style: {\n                                color: '#FF6B35'\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1307,\n                              columnNumber: 37\n                            }, this), champion.currentStreak]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1301,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1291,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1281,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1135,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1129,\n                    columnNumber: 27\n                  }, this)\n                }, champion._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1120,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1114,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1098,\n            columnNumber: 17\n          }, this), rankingData.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 1.8,\n              duration: 0.8\n            },\n            className: \"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold mb-4\",\n                style: {\n                  color: '#60A5FA',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"\\uD83D\\uDCCA Real User Data Integration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1330,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-500/20 rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-green-400 font-bold text-lg\",\n                    children: rankingData.filter(u => u.dataSource === 'reports').length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1337,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-white/80\",\n                    children: \"\\uD83D\\uDCCA Live Quiz Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1340,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1336,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-500/20 rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-blue-400 font-bold text-lg\",\n                    children: rankingData.filter(u => u.dataSource === 'legacy_points').length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1343,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-white/80\",\n                    children: \"\\uD83D\\uDCC8 Legacy Points\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1346,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1342,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-purple-500/20 rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-purple-400 font-bold text-lg\",\n                    children: rankingData.filter(u => u.dataSource === 'estimated').length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1349,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-white/80\",\n                    children: \"\\uD83D\\uDD2E Estimated Stats\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1352,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1348,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-white/70 text-sm mt-4\",\n                children: \"Using real database users (admins excluded) with intelligent data processing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1355,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1329,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1323,\n            columnNumber: 17\n          }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              delay: 1.5,\n              duration: 0.8\n            },\n            className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-2\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"Your Current Position\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1371,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-6xl font-black mb-2\",\n                style: {\n                  color: '#fbbf24',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  fontWeight: '900'\n                },\n                children: [\"#\", currentUserRank]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1376,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1381,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1370,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1364,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 2,\n              duration: 0.8\n            },\n            className: \"mt-16 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  scale: [1, 1.05, 1]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity\n                },\n                children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                  className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1404,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1400,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-3xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"Ready to Rise Higher?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1406,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl mb-6 max-w-2xl mx-auto\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1411,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                onClick: () => window.location.href = '/user/quiz',\n                children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1419,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1399,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1393,\n            columnNumber: 15\n          }, this), rankingData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            className: \"text-center py-20\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1437,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold mb-4\",\n              style: {\n                color: '#ffffff',\n                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                fontWeight: '800'\n              },\n              children: \"No Champions Yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1438,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg\",\n              style: {\n                color: '#e5e7eb',\n                textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                fontWeight: '600'\n              },\n              children: \"Be the first to take a quiz and claim your spot in the Hall of Champions!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1443,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1432,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 839,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 833,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 525,\n    columnNumber: 5\n  }, this);\n};\n_s(AmazingRankingPage, \"E8ll/bLiGD01L4wq4q4H2p2Oohc=\", false, function () {\n  return [useSelector];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbTarget", "TbBrain", "TbRefresh", "TbMedal", "TbBolt", "TbRocket", "TbDiamond", "TbHeart", "TbEye", "TbTrendingUp", "TbAward", "TbShield", "getAllReportsForRanking", "getXPLeaderboard", "getUserRanking", "getAllUsers", "jsxDEV", "_jsxDEV", "AmazingRankingPage", "_s", "userState", "state", "users", "user", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "showFindMe", "setShowFindMe", "headerRef", "currentUserRef", "motivationalQuotes", "performanceTiers", "legendary", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "effect", "diamond", "platinum", "gold", "silver", "bronze", "getUserTier", "xp", "tier", "config", "Object", "entries", "fetchRankingData", "console", "log", "xpLeaderboardResponse", "limit", "levelFilter", "level", "includeInactive", "success", "data", "transformedData", "map", "userData", "index", "_id", "name", "email", "class", "profilePicture", "profileImage", "totalXP", "totalQuizzesTaken", "averageScore", "currentStreak", "bestStreak", "subscriptionStatus", "rank", "isRealUser", "rankingScore", "currentLevel", "xpToNextLevel", "lifetimeXP", "seasonXP", "achievements", "dataSource", "userRankIndex", "findIndex", "item", "xpError", "rankingResponse", "usersResponse", "error", "userError", "userReportsMap", "for<PERSON>ach", "_item$user", "userId", "reports", "filter", "role", "userReports", "totalQuizzes", "length", "totalScore", "reduce", "sum", "report", "score", "Math", "round", "totalPoints", "estimatedQuizzes", "max", "floor", "estimatedAverage", "tempStreak", "pointsPerQuiz", "originalPoints", "hasReports", "sort", "a", "b", "userRank", "dataSources", "u", "legacy_points", "estimated", "slice", "quizzes", "avg", "source", "warning", "randomQuote", "random", "animationTimer", "setInterval", "prev", "clearInterval", "topPerformers", "otherPerformers", "handleFindMe", "current", "scrollIntoView", "behavior", "block", "setTimeout", "getSubscriptionBadge", "subscriptionEndDate", "subscriptionPlan", "activePlanTitle", "userIndex", "now", "Date", "endDate", "isActive", "planTitle", "toLowerCase", "includes", "text", "className", "children", "div", "initial", "opacity", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "_", "i", "y", "x", "delay", "style", "left", "top", "ref", "scale", "rotateY", "span", "backgroundPosition", "backgroundSize", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "fontWeight", "p", "background", "<PERSON><PERSON>ilter", "borderRadius", "padding", "border", "boxShadow", "textAlign", "label", "value", "iconColor", "bgGradient", "toLocaleString", "stat", "whileHover", "fontSize", "button", "whileTap", "onClick", "disabled", "champion", "position", "isCurrentUser", "src", "alt", "aspectRatio", "char<PERSON>t", "toUpperCase", "letterSpacing", "badge", "backgroundColor", "actualRank", "window", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbTarget,\n  TbBrain,\n\n  TbRefresh,\n  TbMedal,\n  TbBolt,\n  TbRocket,\n  TbDiamond,\n  TbHeart,\n  TbEye,\n  TbTrendingUp,\n  TbAward,\n  TbShield\n} from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const user = userState.user || null;\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Performance tiers with SPECTACULAR visual themes and unique colors\n  const performanceTiers = {\n    legendary: {\n      min: 10000,\n      color: 'from-purple-400 via-pink-400 via-red-400 to-orange-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-pink-900/40 to-red-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FF69B4',\n      shadowColor: 'rgba(147, 51, 234, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery',\n      borderColor: '#8B5CF6',\n      effect: 'legendary-sparkle'\n    },\n    diamond: {\n      min: 7500,\n      color: 'from-cyan-200 via-blue-300 via-indigo-400 to-purple-500',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00E5FF',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 229, 255, 0.9)',\n      glow: 'shadow-cyan-300/80',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance',\n      borderColor: '#00E5FF',\n      effect: 'diamond-shine'\n    },\n    platinum: {\n      min: 5000,\n      color: 'from-slate-200 via-gray-300 via-zinc-400 to-stone-500',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#E8E8E8',\n      nameColor: '#C0C0C0',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-300/80',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam'\n    },\n    gold: {\n      min: 2500,\n      color: 'from-yellow-200 via-amber-300 via-orange-400 to-red-500',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-300/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent',\n      borderColor: '#FFD700',\n      effect: 'gold-glow'\n    },\n    silver: {\n      min: 1000,\n      color: 'from-gray-200 via-slate-300 via-zinc-400 to-gray-500',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#D3D3D3',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(211, 211, 211, 0.9)',\n      glow: 'shadow-gray-300/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress',\n      borderColor: '#D3D3D3',\n      effect: 'silver-shimmer'\n    },\n    bronze: {\n      min: 0,\n      color: 'from-orange-200 via-amber-300 via-yellow-400 to-orange-500',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-300/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = (xp) => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return { tier, ...config };\n    }\n    return { tier: 'bronze', ...performanceTiers.bronze };\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: user?.level || 'all',\n          includeInactive: false\n        });\n\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          const transformedData = xpLeaderboardResponse.data.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === user?._id);\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n\n      let rankingResponse, usersResponse;\n\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Use existing user data or calculate from reports\n            let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n            let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n            // For existing users with old data, make intelligent assumptions\n            if (!userReports.length && userData.totalPoints) {\n              // Assume higher points = more exams and better performance\n              const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n              const estimatedAverage = Math.min(95, Math.max(60, 60 + (userData.totalPoints / estimatedQuizzes / 10))); // Scale average based on points\n\n              totalQuizzes = estimatedQuizzes;\n              averageScore = Math.round(estimatedAverage);\n              totalScore = Math.round(averageScore * totalQuizzes);\n\n              console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n            }\n\n            // Calculate XP based on performance (enhanced calculation)\n            let totalXP = userData.totalXP || 0;\n\n            if (!totalXP) {\n              // Calculate XP from available data\n              if (userData.totalPoints) {\n                // Use existing points as base XP with bonuses\n                totalXP = Math.floor(\n                  userData.totalPoints + // Base points\n                  (totalQuizzes * 25) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 15 : 0) + // Excellence bonus\n                  (averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n                );\n              } else if (totalQuizzes > 0) {\n                // Calculate from quiz performance\n                totalXP = Math.floor(\n                  (averageScore * totalQuizzes * 8) + // Base XP from scores\n                  (totalQuizzes * 40) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n                );\n              }\n            }\n\n            // Calculate streaks (enhanced logic)\n            let currentStreak = userData.currentStreak || 0;\n            let bestStreak = userData.bestStreak || 0;\n\n            if (userReports.length > 0) {\n              // Calculate from actual reports\n              let tempStreak = 0;\n              userReports.forEach(report => {\n                if (report.score >= 60) { // Passing score\n                  tempStreak++;\n                  bestStreak = Math.max(bestStreak, tempStreak);\n                } else {\n                  tempStreak = 0;\n                }\n              });\n              currentStreak = tempStreak;\n            } else if (userData.totalPoints && !currentStreak) {\n              // Estimate streaks from points (higher points = likely better streaks)\n              const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n              if (pointsPerQuiz > 80) {\n                currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n                bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n              }\n            }\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserTier(totalXP),\n              isRealUser: true,\n              // Additional tracking fields for future updates\n              originalPoints: userData.totalPoints || 0,\n              hasReports: userReports.length > 0,\n              dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank\n        const userRank = user ? transformedData.findIndex(item => item._id === user._id) : -1;\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n    \n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    return () => clearInterval(animationTimer);\n  }, []);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Find Me functionality\n  const handleFindMe = () => {\n    if (currentUserRank && currentUserRef.current) {\n      setShowFindMe(true);\n      currentUserRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n\n      // Hide the find me highlight after 3 seconds\n      setTimeout(() => {\n        setShowFindMe(false);\n      }, 3000);\n    }\n  };\n\n  // Get subscription status badge based on actual active plan\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription (can log in and use the system)\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n\n        // Determine plan type from activePlanTitle or subscriptionPlan\n        const planTitle = activePlanTitle?.toLowerCase() || subscriptionPlan?.toLowerCase() || '';\n\n        console.log('Plan Title Check:', planTitle);\n\n        // Premium Activated - for premium/pro plans (6-month or yearly)\n        if (planTitle.includes('premium') ||\n            planTitle.includes('pro') ||\n            planTitle.includes('yearly') ||\n            planTitle.includes('6-month') ||\n            planTitle.includes('6 month') ||\n            subscriptionPlan === 'premium' ||\n            subscriptionPlan === 'pro') {\n          return {\n            text: 'PREMIUM ACTIVATED',\n            color: '#3B82F6', // Blue\n            bgColor: 'rgba(59, 130, 246, 0.2)',\n            borderColor: '#3B82F6'\n          };\n        }\n\n        // Standard Activated - for standard/intermediate plans\n        else if (planTitle.includes('standard') ||\n                 planTitle.includes('intermediate') ||\n                 planTitle.includes('plus') ||\n                 subscriptionPlan === 'standard') {\n          return {\n            text: 'STANDARD ACTIVATED',\n            color: '#F59E0B', // Orange/Yellow\n            bgColor: 'rgba(245, 158, 11, 0.2)',\n            borderColor: '#F59E0B'\n          };\n        }\n\n        // Basic Activated - for basic plans (can log in and use system)\n        else {\n          return {\n            text: 'BASIC ACTIVATED',\n            color: '#10B981', // Green\n            bgColor: 'rgba(16, 185, 129, 0.2)',\n            borderColor: '#10B981'\n          };\n        }\n      } else {\n        // Subscription status is active/premium but end date has passed\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444', // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - user has no current plan\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444', // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center\"\n        >\n          <motion.div\n            animate={{ rotate: 360 }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n          />\n          <p className=\"text-white/80 text-lg font-medium\">Loading the Hall of Champions...</p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* SPECTACULAR HEADER */}\n        <motion.div\n          ref={headerRef}\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden\"\n        >\n          {/* Header Background with SPECTACULAR Gradient */}\n          <div className=\"bg-gradient-to-br from-purple-600 via-pink-500 via-red-500 via-orange-500 to-yellow-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n            \n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n                \n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-8\"\n                >\n                  <h1 className=\"text-5xl sm:text-6xl lg:text-8xl font-black mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(3px 3px 6px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)',\n                          '0 0 50px rgba(255,215,0,1), 0 0 80px rgba(255,215,0,0.8)',\n                          '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '4px 4px 8px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-xl sm:text-2xl lg:text-3xl font-semibold mb-8 max-w-4xl mx-auto leading-relaxed\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"relative max-w-2xl mx-auto mb-8\"\n                  style={{\n                    background: 'linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05))',\n                    backdropFilter: 'blur(20px)',\n                    borderRadius: '20px',\n                    padding: '24px',\n                    border: '2px solid rgba(255,255,255,0.2)',\n                    boxShadow: '0 8px 32px rgba(0,0,0,0.3)'\n                  }}\n                >\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-yellow-500/10 rounded-2xl\"></div>\n                  <motion.p\n                    key={motivationalQuote}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"text-lg sm:text-xl font-semibold relative z-10\"\n                    style={{\n                      color: '#FBBF24',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      textAlign: 'center'\n                    }}\n                  >\n                    {motivationalQuote}\n                  </motion.p>\n                </motion.div>\n\n                {/* Stats Overview */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.2, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbTrophy,\n                      label: 'Total Champions',\n                      value: rankingData.length,\n                      iconColor: '#FFD700',\n                      bgGradient: 'from-yellow-500/20 to-amber-600/20',\n                      borderColor: '#FFD700'\n                    },\n                    {\n                      icon: TbFlame,\n                      label: 'Active Streaks',\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      iconColor: '#FF6B35',\n                      bgGradient: 'from-orange-500/20 to-red-600/20',\n                      borderColor: '#FF6B35'\n                    },\n                    {\n                      icon: TbBrain,\n                      label: 'Quizzes Taken',\n                      value: rankingData.reduce((sum, u) => sum + u.totalQuizzesTaken, 0),\n                      iconColor: '#3B82F6',\n                      bgGradient: 'from-blue-500/20 to-indigo-600/20',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbBolt,\n                      label: 'Total XP',\n                      value: rankingData.reduce((sum, u) => sum + u.totalXP, 0).toLocaleString(),\n                      iconColor: '#8B5CF6',\n                      bgGradient: 'from-purple-500/20 to-violet-600/20',\n                      borderColor: '#8B5CF6'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={stat.label}\n                      whileHover={{ scale: 1.08, y: -8 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-8 h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-2xl sm:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: stat.iconColor,\n                          textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          fontSize: '2.5rem'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-sm font-bold relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          fontSize: '1rem'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* INTERACTIVE CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1.5, duration: 0.8 }}\n          className=\"px-4 sm:px-6 lg:px-8 py-8\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10\">\n              <div className=\"flex flex-col sm:flex-row gap-4 items-center justify-center\">\n\n                {/* Find Me Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={handleFindMe}\n                  className=\"flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-black rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300\"\n                  style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                    color: '#000000',\n                    textShadow: 'none',\n                    fontWeight: '900',\n                    fontSize: '1.1rem'\n                  }}\n                >\n                  <TbTarget className=\"w-6 h-6\" />\n                  <span>\n                    {currentUserRank ? `Find Me #${currentUserRank}` : 'Find Me'}\n                  </span>\n                </motion.button>\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50\"\n                  style={{\n                    fontSize: '1.1rem'\n                  }}\n                >\n                  <TbRefresh className={`w-6 h-6 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 lg:px-8 pb-20\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-3xl sm:text-4xl font-black text-center mb-8\" style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 15px #FFD700)'\n                  }}>\n                    🏆 CHAMPIONS PODIUM 🏆\n                  </h2>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\">\n                    {topPerformers.map((champion, index) => {\n                      const position = index + 1;\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          initial={{ opacity: 0, y: 50 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: 0.7 + index * 0.2, duration: 0.8 }}\n                          whileHover={{ scale: 1.05, y: -10 }}\n                          className={`relative ${\n                            position === 1 ? 'md:order-2 md:scale-110' :\n                            position === 2 ? 'md:order-1' : 'md:order-3'\n                          }`}\n                        >\n                          {/* Podium Card */}\n                          <div\n                            className={`relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-2xl ${champion.tier.glow} ${champion.tier.effect} shadow-2xl`}\n                            style={{\n                              boxShadow: `0 20px 40px ${champion.tier.shadowColor}, 0 0 60px ${champion.tier.shadowColor}`\n                            }}\n                          >\n                            <div\n                              className={`${champion.tier.bgColor} backdrop-blur-lg rounded-2xl p-6 text-center relative overflow-hidden`}\n                              style={{\n                                border: `2px solid ${champion.tier.borderColor}60`\n                              }}\n                            >\n                              <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent\"></div>\n\n                              {/* Enhanced Position Badge */}\n                              <div\n                                className={`absolute -top-6 left-1/2 transform -translate-x-1/2 w-16 h-16 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-2xl shadow-lg relative z-20`}\n                                style={{\n                                  color: position === 1 ? '#FFD700' : position === 2 ? '#C0C0C0' : position === 3 ? '#CD7F32' : '#FFFFFF',\n                                  textShadow: '3px 3px 6px rgba(0,0,0,0.9)',\n                                  border: `4px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 12px 24px ${champion.tier.shadowColor}, 0 0 30px ${champion.tier.shadowColor}`,\n                                  fontSize: '1.5rem',\n                                  fontWeight: '900'\n                                }}\n                              >\n                                {position === 1 ? '👑' : position === 2 ? '🥈' : position === 3 ? '🥉' : position}\n                              </div>\n\n                              {/* Crown for #1 */}\n                              {position === 1 && (\n                                <motion.div\n                                  animate={{ rotate: [0, 10, -10, 0] }}\n                                  transition={{ duration: 2, repeat: Infinity }}\n                                  className=\"absolute -top-8 left-1/2 transform -translate-x-1/2\"\n                                >\n                                  <TbCrown className=\"w-8 h-8 text-yellow-400\" />\n                                </motion.div>\n                              )}\n\n                              {/* Enhanced Profile Picture */}\n                              <div className={`relative mx-auto mb-4 ${isCurrentUser ? 'ring-4 ring-yellow-400 ring-opacity-80' : ''}`}>\n                                <div\n                                  className=\"w-24 h-24 rounded-full overflow-hidden mx-auto relative\"\n                                  style={{\n                                    background: `linear-gradient(45deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                    boxShadow: `0 8px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                                    padding: '3px'\n                                  }}\n                                >\n                                  {champion.profilePicture ? (\n                                    <img\n                                      src={champion.profilePicture}\n                                      alt={champion.name}\n                                      className=\"w-full h-full object-cover rounded-full\"\n                                      style={{\n                                        filter: 'brightness(1.1) contrast(1.1)',\n                                        aspectRatio: '1/1'\n                                      }}\n                                    />\n                                  ) : (\n                                    <div\n                                      className=\"w-full h-full rounded-full flex items-center justify-center font-black text-2xl\"\n                                      style={{\n                                        background: `linear-gradient(135deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                        color: '#FFFFFF',\n                                        textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                        aspectRatio: '1/1'\n                                      }}\n                                    >\n                                      {champion.name.charAt(0).toUpperCase()}\n                                    </div>\n                                  )}\n                                </div>\n                                {isCurrentUser && (\n                                  <div\n                                    className=\"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\"\n                                    style={{\n                                      background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                      boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                                    }}\n                                  >\n                                    <TbStar className=\"w-5 h-5 text-black\" />\n                                  </div>\n                                )}\n                              </div>\n\n                              {/* Champion Info */}\n                              <h3\n                                className=\"text-xl font-black mb-2 relative z-10\"\n                                style={{\n                                  color: champion.tier.nameColor,\n                                  textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                                  fontSize: '1.5rem',\n                                  filter: 'drop-shadow(0 0 10px currentColor)'\n                                }}\n                              >\n                                {champion.name}\n                              </h3>\n                              <div\n                                className={`inline-flex items-center gap-2 px-5 py-3 bg-gradient-to-r ${champion.tier.color} rounded-full text-sm font-black mb-3 relative z-10 animate-pulse`}\n                                style={{\n                                  color: '#FFFFFF',\n                                  textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                                  border: `3px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 6px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                                  fontSize: '0.9rem',\n                                  letterSpacing: '0.5px'\n                                }}\n                              >\n                                <champion.tier.icon\n                                  className=\"w-6 h-6\"\n                                  style={{\n                                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))',\n                                    color: champion.tier.textColor\n                                  }}\n                                />\n                                <span style={{ color: '#FFFFFF' }}>{champion.tier.title}</span>\n                              </div>\n\n                              {/* Subscription Status Badge */}\n                              {(() => {\n                                const badge = getSubscriptionBadge(\n                                  champion.subscriptionStatus,\n                                  champion.subscriptionEndDate,\n                                  champion.subscriptionPlan,\n                                  index\n                                );\n                                return (\n                                  <div\n                                    className=\"inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-bold mb-3 relative z-10\"\n                                    style={{\n                                      backgroundColor: badge.bgColor,\n                                      color: badge.color,\n                                      border: `2px solid ${badge.borderColor}`,\n                                      textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                                      fontSize: '0.75rem',\n                                      letterSpacing: '0.5px'\n                                    }}\n                                  >\n                                    {badge.text}\n                                  </div>\n                                );\n                              })()}\n\n                              {/* Enhanced Stats */}\n                              <div className=\"space-y-3 relative z-10\">\n                                <div className=\"flex justify-between text-base\">\n                                  <span style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '1rem'\n                                  }}>💎 XP:</span>\n                                  <span style={{\n                                    color: champion.tier.nameColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '900',\n                                    fontSize: '1.1rem',\n                                    filter: 'drop-shadow(0 0 8px currentColor)'\n                                  }}>{champion.totalXP.toLocaleString()}</span>\n                                </div>\n                                <div className=\"flex justify-between text-base\">\n                                  <span style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '1rem'\n                                  }}>🧠 Quizzes:</span>\n                                  <span style={{\n                                    color: champion.tier.nameColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '900',\n                                    fontSize: '1.1rem',\n                                    filter: 'drop-shadow(0 0 8px currentColor)'\n                                  }}>{champion.totalQuizzesTaken}</span>\n                                </div>\n                                <div className=\"flex justify-between text-base\">\n                                  <span style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '1rem'\n                                  }}>🔥 Streak:</span>\n                                  <span style={{\n                                    color: champion.tier.nameColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '900',\n                                    fontSize: '1.1rem',\n                                    filter: 'drop-shadow(0 0 8px currentColor)'\n                                  }} className=\"flex items-center gap-1\">\n                                    {champion.currentStreak}\n                                  </span>\n                                </div>\n\n                                {/* Data Source Indicator */}\n                                <div className=\"text-center mt-2\">\n                                  <span style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    fontWeight: '600',\n                                    fontSize: '0.7rem',\n                                    opacity: 0.8\n                                  }}>\n                                    {champion.dataSource === 'reports' ? '📊 Live Data' :\n                                     champion.dataSource === 'legacy_points' ? '📈 Legacy Points' :\n                                     '🔮 Estimated'}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* OTHER CHAMPIONS LIST */}\n              {otherPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"mt-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl font-black text-center mb-8\" style={{\n                    background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                  }}>\n                    ⚡ RISING CHAMPIONS ⚡\n                  </h2>\n\n                  <div className=\"space-y-4\">\n                    {otherPerformers.map((champion, index) => {\n                      const actualRank = index + 4; // Since top 3 are shown separately\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          ref={isCurrentUser ? currentUserRef : null}\n                          initial={{ opacity: 0, x: -50 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                          whileHover={{ scale: 1.02, x: 10 }}\n                          className={`relative ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`}\n                        >\n                          <div\n                            className={`bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow} ${champion.tier.effect}`}\n                            style={{\n                              boxShadow: `0 8px 24px ${champion.tier.shadowColor}`\n                            }}\n                          >\n                            <div\n                              className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 flex items-center gap-4 relative overflow-hidden`}\n                              style={{\n                                border: `1px solid ${champion.tier.borderColor}40`\n                              }}\n                            >\n                              <div className=\"absolute inset-0 bg-gradient-to-r from-white/5 to-transparent\"></div>\n\n                              {/* Enhanced Rank */}\n                              <div\n                                className={`flex-shrink-0 w-14 h-14 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-10`}\n                                style={{\n                                  color: actualRank <= 10 ? champion.tier.textColor : '#FFFFFF',\n                                  textShadow: '3px 3px 6px rgba(0,0,0,0.9)',\n                                  border: `3px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 8px 16px ${champion.tier.shadowColor}, 0 0 20px ${champion.tier.shadowColor}`,\n                                  fontSize: actualRank <= 10 ? '1.3rem' : '1.1rem',\n                                  fontWeight: '900'\n                                }}\n                              >\n                                {actualRank <= 10 ? `#${actualRank}` : actualRank}\n                              </div>\n\n                              {/* Enhanced Profile Picture */}\n                              <div className=\"flex-shrink-0 relative\">\n                                <div\n                                  className=\"w-14 h-14 rounded-full overflow-hidden p-0.5 relative\"\n                                  style={{\n                                    background: `linear-gradient(45deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                    boxShadow: `0 6px 12px ${champion.tier.shadowColor}, 0 0 15px ${champion.tier.shadowColor}`\n                                  }}\n                                >\n                                  {champion.profilePicture ? (\n                                    <img\n                                      src={champion.profilePicture}\n                                      alt={champion.name}\n                                      className=\"w-full h-full object-cover rounded-full\"\n                                      style={{\n                                        filter: 'brightness(1.1) contrast(1.1) saturate(1.2)',\n                                        aspectRatio: '1/1'\n                                      }}\n                                    />\n                                  ) : (\n                                    <div\n                                      className=\"w-full h-full rounded-full flex items-center justify-center font-black text-lg\"\n                                      style={{\n                                        background: `linear-gradient(135deg, ${champion.tier.borderColor}, ${champion.tier.textColor})`,\n                                        color: '#FFFFFF',\n                                        textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                        aspectRatio: '1/1'\n                                      }}\n                                    >\n                                      {champion.name.charAt(0).toUpperCase()}\n                                    </div>\n                                  )}\n                                </div>\n                                {isCurrentUser && (\n                                  <div\n                                    className=\"absolute -top-1 -right-1 rounded-full p-1 animate-pulse\"\n                                    style={{\n                                      background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                      boxShadow: '0 2px 8px rgba(255,215,0,0.6)'\n                                    }}\n                                  >\n                                    <TbStar className=\"w-3 h-3 text-black\" />\n                                  </div>\n                                )}\n                              </div>\n\n                              {/* Champion Info */}\n                              <div className=\"flex-1 min-w-0 relative z-10\">\n                                <div className=\"flex items-center gap-2 mb-1\">\n                                  <h3\n                                    className=\"text-lg font-black truncate\"\n                                    style={{\n                                      color: champion.tier.nameColor,\n                                      textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                      fontSize: '1.25rem',\n                                      filter: 'drop-shadow(0 0 8px currentColor)'\n                                    }}\n                                  >\n                                    {champion.name}\n                                  </h3>\n                                  {isCurrentUser && (\n                                    <div\n                                      className=\"px-3 py-1 rounded-full text-xs font-black animate-pulse\"\n                                      style={{\n                                        background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                        color: '#000000',\n                                        textShadow: 'none',\n                                        border: '2px solid #FFFFFF',\n                                        boxShadow: '0 4px 12px rgba(255,215,0,0.6)'\n                                      }}\n                                    >\n                                      ⭐ YOU ⭐\n                                    </div>\n                                  )}\n                                </div>\n                                <div\n                                  className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${champion.tier.color} rounded-full text-xs font-black`}\n                                  style={{\n                                    color: '#FFFFFF',\n                                    textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                                    border: `2px solid ${champion.tier.borderColor}`,\n                                    boxShadow: `0 4px 8px ${champion.tier.shadowColor}, 0 0 12px ${champion.tier.shadowColor}`,\n                                    fontSize: '0.8rem',\n                                    letterSpacing: '0.3px'\n                                  }}\n                                >\n                                  <champion.tier.icon\n                                    className=\"w-4 h-4\"\n                                    style={{\n                                      filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.5))',\n                                      color: champion.tier.textColor\n                                    }}\n                                  />\n                                  <span style={{ color: '#FFFFFF' }}>{champion.tier.title}</span>\n                                </div>\n\n                                {/* Subscription Status Badge */}\n                                {(() => {\n                                  const badge = getSubscriptionBadge(\n                                    champion.subscriptionStatus,\n                                    champion.subscriptionEndDate,\n                                    champion.subscriptionPlan,\n                                    actualRank\n                                  );\n                                  return (\n                                    <div\n                                      className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold mt-1\"\n                                      style={{\n                                        backgroundColor: badge.bgColor,\n                                        color: badge.color,\n                                        border: `1px solid ${badge.borderColor}`,\n                                        textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                                        fontSize: '0.7rem',\n                                        letterSpacing: '0.3px'\n                                      }}\n                                    >\n                                      {badge.text}\n                                    </div>\n                                  );\n                                })()}\n                              </div>\n\n                              {/* Enhanced Stats */}\n                              <div className=\"flex-shrink-0 text-right relative z-10\">\n                                <div className=\"text-xl mb-2\" style={{\n                                  color: champion.tier.nameColor,\n                                  textShadow: `3px 3px 6px ${champion.tier.shadowColor}`,\n                                  fontWeight: '900',\n                                  filter: 'drop-shadow(0 0 10px currentColor)',\n                                  fontSize: '1.3rem'\n                                }}>\n                                  💎 {champion.totalXP.toLocaleString()} XP\n                                </div>\n                                <div className=\"flex items-center gap-3 text-sm justify-end\">\n                                  <span className=\"flex items-center gap-1\" style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '0.9rem'\n                                  }}>\n                                    <TbBrain className=\"w-4 h-4\" style={{ color: champion.tier.textColor }} />\n                                    {champion.totalQuizzesTaken}\n                                  </span>\n                                  <span className=\"flex items-center gap-1\" style={{\n                                    color: champion.tier.textColor,\n                                    textShadow: `2px 2px 4px ${champion.tier.shadowColor}`,\n                                    fontWeight: '700',\n                                    fontSize: '0.9rem'\n                                  }}>\n                                    <TbFlame className=\"w-4 h-4\" style={{ color: '#FF6B35' }} />\n                                    {champion.currentStreak}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* DATA INTEGRATION STATUS */}\n              {rankingData.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.8, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-xl font-bold mb-4\" style={{\n                      color: '#60A5FA',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>📊 Real User Data Integration</h3>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\">\n                      <div className=\"bg-green-500/20 rounded-lg p-3\">\n                        <div className=\"text-green-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'reports').length}\n                        </div>\n                        <div className=\"text-white/80\">📊 Live Quiz Data</div>\n                      </div>\n                      <div className=\"bg-blue-500/20 rounded-lg p-3\">\n                        <div className=\"text-blue-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'legacy_points').length}\n                        </div>\n                        <div className=\"text-white/80\">📈 Legacy Points</div>\n                      </div>\n                      <div className=\"bg-purple-500/20 rounded-lg p-3\">\n                        <div className=\"text-purple-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'estimated').length}\n                        </div>\n                        <div className=\"text-white/80\">🔮 Estimated Stats</div>\n                      </div>\n                    </div>\n                    <p className=\"text-white/70 text-sm mt-4\">\n                      Using real database users (admins excluded) with intelligent data processing\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold mb-2\" style={{\n                      color: '#ffffff',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>Your Current Position</h3>\n                    <div className=\"text-6xl font-black mb-2\" style={{\n                      color: '#fbbf24',\n                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                      fontWeight: '900'\n                    }}>#{currentUserRank}</div>\n                    <p className=\"text-lg\" style={{\n                      color: '#e5e7eb',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                      fontWeight: '600'\n                    }}>\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>Ready to Rise Higher?</h3>\n                  <p className=\"text-xl mb-6 max-w-2xl mx-auto\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {rankingData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbTrophy className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Yet</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Be the first to take a quiz and claim your spot in the Hall of Champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,OAAO,EAEPC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,SAASC,uBAAuB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,2BAA2B;AACrG,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG1B,WAAW,CAAE2B,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,IAAI,GAAGH,SAAS,CAACG,IAAI,IAAI,IAAI;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6C,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC+C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACiD,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMmD,SAAS,GAAGjD,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMkD,cAAc,GAAGlD,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMmD,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,gBAAgB,GAAG;IACvBC,SAAS,EAAE;MACTC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEvD,OAAO;MACbwD,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE;MACPZ,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAE9C,SAAS;MACf+C,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,mBAAmB;MAChCC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDE,QAAQ,EAAE;MACRb,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAEzC,QAAQ;MACd0C,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,aAAa;MAC1BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDG,IAAI,EAAE;MACJd,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAExD,QAAQ;MACdyD,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDI,MAAM,EAAE;MACNf,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEjD,OAAO;MACbkD,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV,CAAC;IACDK,MAAM,EAAE;MACNhB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEtD,MAAM;MACZuD,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,aAAa;MAC1BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE;IACV;EACF,CAAC;;EAED;EACA,MAAMM,WAAW,GAAIC,EAAE,IAAK;IAC1B,KAAK,MAAM,CAACC,IAAI,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACxB,gBAAgB,CAAC,EAAE;MAC7D,IAAIoB,EAAE,IAAIE,MAAM,CAACpB,GAAG,EAAE,OAAO;QAAEmB,IAAI;QAAE,GAAGC;MAAO,CAAC;IAClD;IACA,OAAO;MAAED,IAAI,EAAE,QAAQ;MAAE,GAAGrB,gBAAgB,CAACkB;IAAO,CAAC;EACvD,CAAC;;EAED;EACA,MAAMO,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFzC,UAAU,CAAC,IAAI,CAAC;MAChB0C,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;MAEtD;MACA,IAAI;QACFD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMC,qBAAqB,GAAG,MAAM1D,gBAAgB,CAAC;UACnD2D,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,KAAK,KAAI,KAAK;UACjCC,eAAe,EAAE;QACnB,CAAC,CAAC;QAEFN,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,qBAAqB,CAAC;QAEhE,IAAIA,qBAAqB,IAAIA,qBAAqB,CAACK,OAAO,IAAIL,qBAAqB,CAACM,IAAI,EAAE;UACxFR,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;UAEhD,MAAMQ,eAAe,GAAGP,qBAAqB,CAACM,IAAI,CAACE,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,MAAM;YAC3EC,GAAG,EAAEF,QAAQ,CAACE,GAAG;YACjBC,IAAI,EAAEH,QAAQ,CAACG,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAEJ,QAAQ,CAACI,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEL,QAAQ,CAACK,KAAK,IAAI,EAAE;YAC3BX,KAAK,EAAEM,QAAQ,CAACN,KAAK,IAAI,EAAE;YAC3BY,cAAc,EAAEN,QAAQ,CAACO,YAAY,IAAI,EAAE;YAC3CC,OAAO,EAAER,QAAQ,CAACQ,OAAO,IAAI,CAAC;YAC9BC,iBAAiB,EAAET,QAAQ,CAACS,iBAAiB,IAAI,CAAC;YAClDC,YAAY,EAAEV,QAAQ,CAACU,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAEX,QAAQ,CAACW,aAAa,IAAI,CAAC;YAC1CC,UAAU,EAAEZ,QAAQ,CAACY,UAAU,IAAI,CAAC;YACpCC,kBAAkB,EAAEb,QAAQ,CAACa,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEb,KAAK,GAAG,CAAC;YACfjB,IAAI,EAAEF,WAAW,CAACkB,QAAQ,CAACQ,OAAO,IAAI,CAAC,CAAC;YACxCO,UAAU,EAAE,IAAI;YAChBC,YAAY,EAAEhB,QAAQ,CAACgB,YAAY,IAAI,CAAC;YACxC;YACAC,YAAY,EAAEjB,QAAQ,CAACiB,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAElB,QAAQ,CAACkB,aAAa,IAAI,GAAG;YAC5CC,UAAU,EAAEnB,QAAQ,CAACmB,UAAU,IAAI,CAAC;YACpCC,QAAQ,EAAEpB,QAAQ,CAACoB,QAAQ,IAAI,CAAC;YAChCC,YAAY,EAAErB,QAAQ,CAACqB,YAAY,IAAI,EAAE;YACzCC,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;UAEH7E,cAAc,CAACqD,eAAe,CAAC;;UAE/B;UACA,MAAMyB,aAAa,GAAGzB,eAAe,CAAC0B,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACvB,GAAG,MAAK3D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,GAAG,EAAC;UAC/ErD,kBAAkB,CAAC0E,aAAa,IAAI,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC;UAEjE5E,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAO+E,OAAO,EAAE;QAChBrC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEoC,OAAO,CAAC;MACpE;;MAEA;MACArC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D,IAAIqC,eAAe,EAAEC,aAAa;MAElC,IAAI;QACFvC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDqC,eAAe,GAAG,MAAM/F,uBAAuB,CAAC,CAAC;QACjDyD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCsC,aAAa,GAAG,MAAM7F,WAAW,CAAC,CAAC;MACrC,CAAC,CAAC,OAAO8F,KAAK,EAAE;QACdxC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuC,KAAK,CAAC;QACnD,IAAI;UACFD,aAAa,GAAG,MAAM7F,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC,OAAO+F,SAAS,EAAE;UAClBzC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEwC,SAAS,CAAC;QACpD;MACF;MAEA,IAAIhC,eAAe,GAAG,EAAE;MAExB,IAAI8B,aAAa,IAAIA,aAAa,CAAChC,OAAO,IAAIgC,aAAa,CAAC/B,IAAI,EAAE;QAChER,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;QAEhD;QACA,MAAMyC,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIJ,eAAe,IAAIA,eAAe,CAAC/B,OAAO,IAAI+B,eAAe,CAAC9B,IAAI,EAAE;UACtE8B,eAAe,CAAC9B,IAAI,CAACmC,OAAO,CAACP,IAAI,IAAI;YAAA,IAAAQ,UAAA;YACnC,MAAMC,MAAM,GAAG,EAAAD,UAAA,GAAAR,IAAI,CAAClF,IAAI,cAAA0F,UAAA,uBAATA,UAAA,CAAW/B,GAAG,KAAIuB,IAAI,CAACS,MAAM;YAC5C,IAAIA,MAAM,EAAE;cACVH,cAAc,CAACG,MAAM,CAAC,GAAGT,IAAI,CAACU,OAAO,IAAI,EAAE;YAC7C;UACF,CAAC,CAAC;QACJ;QAEArC,eAAe,GAAG8B,aAAa,CAAC/B,IAAI,CACjCuC,MAAM,CAACpC,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAACE,GAAG,IAAIF,QAAQ,CAACqC,IAAI,KAAK,OAAO,CAAC,CAAC;QAAA,CAC1EtC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;UACxB;UACA,MAAMqC,WAAW,GAAGP,cAAc,CAAC/B,QAAQ,CAACE,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,IAAIqC,YAAY,GAAGD,WAAW,CAACE,MAAM,IAAIxC,QAAQ,CAACS,iBAAiB,IAAI,CAAC;UACxE,IAAIgC,UAAU,GAAGH,WAAW,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAClF,IAAInC,YAAY,GAAG6B,YAAY,GAAG,CAAC,GAAGO,IAAI,CAACC,KAAK,CAACN,UAAU,GAAGF,YAAY,CAAC,GAAGvC,QAAQ,CAACU,YAAY,IAAI,CAAC;;UAExG;UACA,IAAI,CAAC4B,WAAW,CAACE,MAAM,IAAIxC,QAAQ,CAACgD,WAAW,EAAE;YAC/C;YACA,MAAMC,gBAAgB,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACK,KAAK,CAACnD,QAAQ,CAACgD,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAMI,gBAAgB,GAAGN,IAAI,CAACjF,GAAG,CAAC,EAAE,EAAEiF,IAAI,CAACI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAIlD,QAAQ,CAACgD,WAAW,GAAGC,gBAAgB,GAAG,EAAG,CAAC,CAAC,CAAC,CAAC;;YAE1GV,YAAY,GAAGU,gBAAgB;YAC/BvC,YAAY,GAAGoC,IAAI,CAACC,KAAK,CAACK,gBAAgB,CAAC;YAC3CX,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACrC,YAAY,GAAG6B,YAAY,CAAC;YAEpDlD,OAAO,CAACC,GAAG,CAAE,0BAAyBU,QAAQ,CAACG,IAAK,KAAI8C,gBAAiB,aAAYG,gBAAiB,cAAapD,QAAQ,CAACgD,WAAY,SAAQ,CAAC;UACnJ;;UAEA;UACA,IAAIxC,OAAO,GAAGR,QAAQ,CAACQ,OAAO,IAAI,CAAC;UAEnC,IAAI,CAACA,OAAO,EAAE;YACZ;YACA,IAAIR,QAAQ,CAACgD,WAAW,EAAE;cACxB;cACAxC,OAAO,GAAGsC,IAAI,CAACK,KAAK,CAClBnD,QAAQ,CAACgD,WAAW;cAAG;cACtBT,YAAY,GAAG,EAAG;cAAG;cACrB7B,YAAY,GAAG,EAAE,GAAG6B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;cAAG;cAC7C7B,YAAY,GAAG,EAAE,GAAG6B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAAC,EAAE;cAC3B;cACA/B,OAAO,GAAGsC,IAAI,CAACK,KAAK,CACjBzC,YAAY,GAAG6B,YAAY,GAAG,CAAC;cAAI;cACnCA,YAAY,GAAG,EAAG;cAAG;cACrB7B,YAAY,GAAG,EAAE,GAAG6B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH;UACF;;UAEA;UACA,IAAI5B,aAAa,GAAGX,QAAQ,CAACW,aAAa,IAAI,CAAC;UAC/C,IAAIC,UAAU,GAAGZ,QAAQ,CAACY,UAAU,IAAI,CAAC;UAEzC,IAAI0B,WAAW,CAACE,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAIa,UAAU,GAAG,CAAC;YAClBf,WAAW,CAACN,OAAO,CAACY,MAAM,IAAI;cAC5B,IAAIA,MAAM,CAACC,KAAK,IAAI,EAAE,EAAE;gBAAE;gBACxBQ,UAAU,EAAE;gBACZzC,UAAU,GAAGkC,IAAI,CAACI,GAAG,CAACtC,UAAU,EAAEyC,UAAU,CAAC;cAC/C,CAAC,MAAM;gBACLA,UAAU,GAAG,CAAC;cAChB;YACF,CAAC,CAAC;YACF1C,aAAa,GAAG0C,UAAU;UAC5B,CAAC,MAAM,IAAIrD,QAAQ,CAACgD,WAAW,IAAI,CAACrC,aAAa,EAAE;YACjD;YACA,MAAM2C,aAAa,GAAGf,YAAY,GAAG,CAAC,GAAGvC,QAAQ,CAACgD,WAAW,GAAGT,YAAY,GAAG,CAAC;YAChF,IAAIe,aAAa,GAAG,EAAE,EAAE;cACtB3C,aAAa,GAAGmC,IAAI,CAACjF,GAAG,CAAC0E,YAAY,EAAEO,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;cACxE1C,UAAU,GAAGkC,IAAI,CAACI,GAAG,CAACvC,aAAa,EAAEmC,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACxE;UACF;;UAEA,OAAO;YACLpD,GAAG,EAAEF,QAAQ,CAACE,GAAG;YACjBC,IAAI,EAAEH,QAAQ,CAACG,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAEJ,QAAQ,CAACI,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEL,QAAQ,CAACK,KAAK,IAAI,EAAE;YAC3BX,KAAK,EAAEM,QAAQ,CAACN,KAAK,IAAI,EAAE;YAC3BY,cAAc,EAAEN,QAAQ,CAACM,cAAc,IAAI,EAAE;YAC7CE,OAAO,EAAEA,OAAO;YAChBC,iBAAiB,EAAE8B,YAAY;YAC/B7B,YAAY,EAAEA,YAAY;YAC1BC,aAAa,EAAEA,aAAa;YAC5BC,UAAU,EAAEA,UAAU;YACtBC,kBAAkB,EAAEb,QAAQ,CAACa,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEb,KAAK,GAAG,CAAC;YACfjB,IAAI,EAAEF,WAAW,CAAC0B,OAAO,CAAC;YAC1BO,UAAU,EAAE,IAAI;YAChB;YACAwC,cAAc,EAAEvD,QAAQ,CAACgD,WAAW,IAAI,CAAC;YACzCQ,UAAU,EAAElB,WAAW,CAACE,MAAM,GAAG,CAAC;YAClClB,UAAU,EAAEgB,WAAW,CAACE,MAAM,GAAG,CAAC,GAAG,SAAS,GAAGxC,QAAQ,CAACgD,WAAW,GAAG,eAAe,GAAG;UAC5F,CAAC;QACH,CAAC,CAAC;;QAEJ;QACAlD,eAAe,CAAC2D,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACnD,OAAO,GAAGkD,CAAC,CAAClD,OAAO,CAAC;;QAErD;QACAV,eAAe,CAACkC,OAAO,CAAC,CAACzF,IAAI,EAAE0D,KAAK,KAAK;UACvC1D,IAAI,CAACuE,IAAI,GAAGb,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEFxD,cAAc,CAACqD,eAAe,CAAC;;QAE/B;QACA,MAAM8D,QAAQ,GAAGrH,IAAI,GAAGuD,eAAe,CAAC0B,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACvB,GAAG,KAAK3D,IAAI,CAAC2D,GAAG,CAAC,GAAG,CAAC,CAAC;QACrFrD,kBAAkB,CAAC+G,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;;QAEvD;QACA,MAAMC,WAAW,GAAG;UAClB1B,OAAO,EAAErC,eAAe,CAACsC,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACxC,UAAU,KAAK,SAAS,CAAC,CAACkB,MAAM;UACvEuB,aAAa,EAAEjE,eAAe,CAACsC,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACxC,UAAU,KAAK,eAAe,CAAC,CAACkB,MAAM;UACnFwB,SAAS,EAAElE,eAAe,CAACsC,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACxC,UAAU,KAAK,WAAW,CAAC,CAACkB;QACvE,CAAC;QAEDnD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEQ,eAAe,CAAC0C,MAAM,EAAE,gBAAgB,CAAC;QACxFnD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEuE,WAAW,CAAC;QAC5CxE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEQ,eAAe,CAACmE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClE,GAAG,CAAC+D,CAAC,KAAK;UACvE3D,IAAI,EAAE2D,CAAC,CAAC3D,IAAI;UACZpB,EAAE,EAAE+E,CAAC,CAACtD,OAAO;UACb0D,OAAO,EAAEJ,CAAC,CAACrD,iBAAiB;UAC5B0D,GAAG,EAAEL,CAAC,CAACpD,YAAY;UACnB0D,MAAM,EAAEN,CAAC,CAACxC;QACZ,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,MAAM;QACLjC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxC7C,cAAc,CAAC,EAAE,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;QACxBlC,OAAO,CAAC0J,OAAO,CAAC,0DAA0D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdxC,OAAO,CAACwC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDlH,OAAO,CAACkH,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACRlF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACArC,SAAS,CAAC,MAAM;IACd8E,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMkF,WAAW,GAAG5G,kBAAkB,CAACoF,IAAI,CAACK,KAAK,CAACL,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAG7G,kBAAkB,CAAC8E,MAAM,CAAC,CAAC;IAC7FnF,oBAAoB,CAACiH,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvCtH,iBAAiB,CAACuH,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,cAAc,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,aAAa,GAAGpI,WAAW,CAACyH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAMY,eAAe,GAAGrI,WAAW,CAACyH,KAAK,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIlI,eAAe,IAAIa,cAAc,CAACsH,OAAO,EAAE;MAC7CxH,aAAa,CAAC,IAAI,CAAC;MACnBE,cAAc,CAACsH,OAAO,CAACC,cAAc,CAAC;QACpCC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACAC,UAAU,CAAC,MAAM;QACf5H,aAAa,CAAC,KAAK,CAAC;MACtB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;;EAED;EACA,MAAM6H,oBAAoB,GAAGA,CAACvE,kBAAkB,EAAEwE,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,GAAG,CAAC,KAAK;IAC1H,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,OAAO,GAAGN,mBAAmB,GAAG,IAAIK,IAAI,CAACL,mBAAmB,CAAC,GAAG,IAAI;IAE1EhG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCuB,kBAAkB;MAClBwE,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfI,OAAO;MACPF,GAAG;MACHG,QAAQ,EAAED,OAAO,IAAIA,OAAO,GAAGF,GAAG;MAClCD;IACF,CAAC,CAAC;;IAEF;IACA,IAAI3E,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,KAAK,SAAS,EAAE;MACvE;MACA,IAAI,CAAC8E,OAAO,IAAIA,OAAO,GAAGF,GAAG,EAAE;QAE7B;QACA,MAAMI,SAAS,GAAG,CAAAN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEO,WAAW,CAAC,CAAC,MAAIR,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEQ,WAAW,CAAC,CAAC,KAAI,EAAE;QAEzFzG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEuG,SAAS,CAAC;;QAE3C;QACA,IAAIA,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,IAC7BF,SAAS,CAACE,QAAQ,CAAC,KAAK,CAAC,IACzBF,SAAS,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAC5BF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,IAC7BF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,IAC7BT,gBAAgB,KAAK,SAAS,IAC9BA,gBAAgB,KAAK,KAAK,EAAE;UAC9B,OAAO;YACLU,IAAI,EAAE,mBAAmB;YACzBlI,KAAK,EAAE,SAAS;YAAE;YAClBC,OAAO,EAAE,yBAAyB;YAClCQ,WAAW,EAAE;UACf,CAAC;QACH;;QAEA;QAAA,KACK,IAAIsH,SAAS,CAACE,QAAQ,CAAC,UAAU,CAAC,IAC9BF,SAAS,CAACE,QAAQ,CAAC,cAAc,CAAC,IAClCF,SAAS,CAACE,QAAQ,CAAC,MAAM,CAAC,IAC1BT,gBAAgB,KAAK,UAAU,EAAE;UACxC,OAAO;YACLU,IAAI,EAAE,oBAAoB;YAC1BlI,KAAK,EAAE,SAAS;YAAE;YAClBC,OAAO,EAAE,yBAAyB;YAClCQ,WAAW,EAAE;UACf,CAAC;QACH;;QAEA;QAAA,KACK;UACH,OAAO;YACLyH,IAAI,EAAE,iBAAiB;YACvBlI,KAAK,EAAE,SAAS;YAAE;YAClBC,OAAO,EAAE,yBAAyB;YAClCQ,WAAW,EAAE;UACf,CAAC;QACH;MACF,CAAC,MAAM;QACL;QACA,OAAO;UACLyH,IAAI,EAAE,SAAS;UACflI,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,wBAAwB;UACjCQ,WAAW,EAAE;QACf,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA,OAAO;QACLyH,IAAI,EAAE,SAAS;QACflI,KAAK,EAAE,SAAS;QAAE;QAClBC,OAAO,EAAE,wBAAwB;QACjCQ,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,IAAI7B,OAAO,IAAIF,WAAW,CAACgG,MAAM,KAAK,CAAC,EAAE;IACvC,oBACEvG,OAAA;MAAKgK,SAAS,EAAC,4GAA4G;MAAAC,QAAA,eACzHjK,OAAA,CAACzB,MAAM,CAAC2L,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvBjK,OAAA,CAACzB,MAAM,CAAC2L,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DX,SAAS,EAAC;QAAqF;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACF/K,OAAA;UAAGgK,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAgC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACE/K,OAAA;IAAKgK,SAAS,EAAC,iHAAiH;IAAAC,QAAA,gBAE9HjK,OAAA;MAAKgK,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/CjK,OAAA;QAAKgK,SAAS,EAAC;MAA2H;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjJ/K,OAAA;QAAKgK,SAAS,EAAC;MAAkJ;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACxK/K,OAAA;QAAKgK,SAAS,EAAC;MAA2I;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9J,CAAC,eAGN/K,OAAA;MAAKgK,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClE,CAAC,GAAGe,KAAK,CAAC,EAAE,CAAC,CAAC,CAAClH,GAAG,CAAC,CAACmH,CAAC,EAAEC,CAAC,kBACvBlL,OAAA,CAACzB,MAAM,CAAC2L,GAAG;QAETF,SAAS,EAAC,mDAAmD;QAC7DK,OAAO,EAAE;UACPc,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;UACfC,CAAC,EAAE,CAAC,CAAC,EAAEvE,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;UACnC8B,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;QACzB,CAAE;QACFG,UAAU,EAAE;UACVC,QAAQ,EAAE,CAAC,GAAG3D,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/BmC,MAAM,EAAEC,QAAQ;UAChBW,KAAK,EAAExE,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAG;QACzB,CAAE;QACFgD,KAAK,EAAE;UACLC,IAAI,EAAG,GAAE1E,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;UAC/BkD,GAAG,EAAG,GAAE3E,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAG,GAAI;QAC9B;MAAE,GAfG4C,CAAC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN/K,OAAA;MAAKgK,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAE5BjK,OAAA,CAACzB,MAAM,CAAC2L,GAAG;QACTuB,GAAG,EAAElK,SAAU;QACf4I,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCd,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAE,CAAE;QAC9BZ,UAAU,EAAE;UAAEC,QAAQ,EAAE,CAAC;UAAEG,IAAI,EAAE;QAAU,CAAE;QAC7CX,SAAS,EAAC,0BAA0B;QAAAC,QAAA,eAGpCjK,OAAA;UAAKgK,SAAS,EAAC,kGAAkG;UAAAC,QAAA,gBAC/GjK,OAAA;YAAKgK,SAAS,EAAC;UAA6E;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnG/K,OAAA;YAAKgK,SAAS,EAAC;UAA+E;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGrG/K,OAAA;YAAKgK,SAAS,EAAC,4DAA4D;YAAAC,QAAA,eACzEjK,OAAA;cAAKgK,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAG5CjK,OAAA,CAACzB,MAAM,CAAC2L,GAAG;gBACTG,OAAO,EAAE;kBACPqB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;kBACnBC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnB,CAAE;gBACFpB,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXC,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR,CAAE;gBACFX,SAAS,EAAC,MAAM;gBAAAC,QAAA,eAEhBjK,OAAA;kBAAIgK,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,gBAC7EjK,OAAA,CAACzB,MAAM,CAACqN,IAAI;oBACVvB,OAAO,EAAE;sBACPwB,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;oBACrD,CAAE;oBACFtB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR,CAAE;oBACFX,SAAS,EAAC,+HAA+H;oBACzIsB,KAAK,EAAE;sBACLQ,cAAc,EAAE,WAAW;sBAC3BC,oBAAoB,EAAE,MAAM;sBAC5BC,mBAAmB,EAAE,aAAa;sBAClC7F,MAAM,EAAE;oBACV,CAAE;oBAAA8D,QAAA,EACH;kBAED;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eACd/K,OAAA;oBAAA4K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN/K,OAAA,CAACzB,MAAM,CAACqN,IAAI;oBACVvB,OAAO,EAAE;sBACP4B,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;oBAEhE,CAAE;oBACF1B,UAAU,EAAE;sBACVC,QAAQ,EAAE,GAAG;sBACbC,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR,CAAE;oBACFW,KAAK,EAAE;sBACLzJ,KAAK,EAAE,SAAS;sBAChBqK,UAAU,EAAE,KAAK;sBACjBD,UAAU,EAAE;oBACd,CAAE;oBAAAhC,QAAA,EACH;kBAED;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAGb/K,OAAA,CAACzB,MAAM,CAAC4N,CAAC;gBACPhC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAG,CAAE;gBAC/Bd,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAE,CAAE;gBAC9BZ,UAAU,EAAE;kBAAEc,KAAK,EAAE,GAAG;kBAAEb,QAAQ,EAAE;gBAAI,CAAE;gBAC1CR,SAAS,EAAC,sFAAsF;gBAChGsB,KAAK,EAAE;kBACLzJ,KAAK,EAAE,SAAS;kBAChBoK,UAAU,EAAE,6BAA6B;kBACzCG,UAAU,EAAE,0CAA0C;kBACtDL,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE;gBACvB,CAAE;gBAAA/B,QAAA,EACH;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAGX/K,OAAA,CAACzB,MAAM,CAAC2L,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEsB,KAAK,EAAE;gBAAI,CAAE;gBACpCrB,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEsB,KAAK,EAAE;gBAAE,CAAE;gBAClCnB,UAAU,EAAE;kBAAEc,KAAK,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAI,CAAE;gBACxCR,SAAS,EAAC,iCAAiC;gBAC3CsB,KAAK,EAAE;kBACLc,UAAU,EAAE,yEAAyE;kBACrFC,cAAc,EAAE,YAAY;kBAC5BC,YAAY,EAAE,MAAM;kBACpBC,OAAO,EAAE,MAAM;kBACfC,MAAM,EAAE,iCAAiC;kBACzCC,SAAS,EAAE;gBACb,CAAE;gBAAAxC,QAAA,gBAEFjK,OAAA;kBAAKgK,SAAS,EAAC;gBAAmG;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzH/K,OAAA,CAACzB,MAAM,CAAC4N,CAAC;kBAEPhC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAG,CAAE;kBAC/Bd,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAE,CAAE;kBAC9BnB,SAAS,EAAC,gDAAgD;kBAC1DsB,KAAK,EAAE;oBACLzJ,KAAK,EAAE,SAAS;oBAChBoK,UAAU,EAAE,6BAA6B;oBACzCS,SAAS,EAAE;kBACb,CAAE;kBAAAzC,QAAA,EAED9I;gBAAiB,GAVbA,iBAAiB;kBAAAyJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGb/K,OAAA,CAACzB,MAAM,CAAC2L,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAG,CAAE;gBAC/Bd,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAE,CAAE;gBAC9BZ,UAAU,EAAE;kBAAEc,KAAK,EAAE,GAAG;kBAAEb,QAAQ,EAAE;gBAAI,CAAE;gBAC1CR,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,EAE3E,CACC;kBACE9H,IAAI,EAAExD,QAAQ;kBACdgO,KAAK,EAAE,iBAAiB;kBACxBC,KAAK,EAAErM,WAAW,CAACgG,MAAM;kBACzBsG,SAAS,EAAE,SAAS;kBACpBC,UAAU,EAAE,oCAAoC;kBAChDxK,WAAW,EAAE;gBACf,CAAC,EACD;kBACEH,IAAI,EAAErD,OAAO;kBACb6N,KAAK,EAAE,gBAAgB;kBACvBC,KAAK,EAAErM,WAAW,CAAC4F,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACnD,aAAa,GAAG,CAAC,CAAC,CAAC6B,MAAM;kBAC1DsG,SAAS,EAAE,SAAS;kBACpBC,UAAU,EAAE,kCAAkC;kBAC9CxK,WAAW,EAAE;gBACf,CAAC,EACD;kBACEH,IAAI,EAAEnD,OAAO;kBACb2N,KAAK,EAAE,eAAe;kBACtBC,KAAK,EAAErM,WAAW,CAACkG,MAAM,CAAC,CAACC,GAAG,EAAEmB,CAAC,KAAKnB,GAAG,GAAGmB,CAAC,CAACrD,iBAAiB,EAAE,CAAC,CAAC;kBACnEqI,SAAS,EAAE,SAAS;kBACpBC,UAAU,EAAE,mCAAmC;kBAC/CxK,WAAW,EAAE;gBACf,CAAC,EACD;kBACEH,IAAI,EAAEhD,MAAM;kBACZwN,KAAK,EAAE,UAAU;kBACjBC,KAAK,EAAErM,WAAW,CAACkG,MAAM,CAAC,CAACC,GAAG,EAAEmB,CAAC,KAAKnB,GAAG,GAAGmB,CAAC,CAACtD,OAAO,EAAE,CAAC,CAAC,CAACwI,cAAc,CAAC,CAAC;kBAC1EF,SAAS,EAAE,SAAS;kBACpBC,UAAU,EAAE,qCAAqC;kBACjDxK,WAAW,EAAE;gBACf,CAAC,CACF,CAACwB,GAAG,CAAC,CAACkJ,IAAI,EAAEhJ,KAAK,kBAChBhE,OAAA,CAACzB,MAAM,CAAC2L,GAAG;kBAET+C,UAAU,EAAE;oBAAEvB,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAE,CAAE;kBACnCnB,SAAS,EAAG,qBAAoBgD,IAAI,CAACF,UAAW,uEAAuE;kBACvHxB,KAAK,EAAE;oBACLkB,MAAM,EAAG,aAAYQ,IAAI,CAAC1K,WAAY,IAAG;oBACzCmK,SAAS,EAAG,cAAaO,IAAI,CAAC1K,WAAY;kBAC5C,CAAE;kBAAA2H,QAAA,gBAEFjK,OAAA;oBAAKgK,SAAS,EAAC;kBAAgE;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtF/K,OAAA,CAACgN,IAAI,CAAC7K,IAAI;oBACR6H,SAAS,EAAC,oCAAoC;oBAC9CsB,KAAK,EAAE;sBAAEzJ,KAAK,EAAEmL,IAAI,CAACH,SAAS;sBAAE1G,MAAM,EAAE;oBAAyC;kBAAE;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC,eACF/K,OAAA;oBACEgK,SAAS,EAAC,oDAAoD;oBAC9DsB,KAAK,EAAE;sBACLzJ,KAAK,EAAEmL,IAAI,CAACH,SAAS;sBACrBZ,UAAU,EAAG,6BAA4B;sBACzC9F,MAAM,EAAE,oCAAoC;sBAC5C+G,QAAQ,EAAE;oBACZ,CAAE;oBAAAjD,QAAA,EAED+C,IAAI,CAACJ;kBAAK;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACN/K,OAAA;oBACEgK,SAAS,EAAC,iCAAiC;oBAC3CsB,KAAK,EAAE;sBACLzJ,KAAK,EAAE,SAAS;sBAChBoK,UAAU,EAAE,6BAA6B;sBACzCiB,QAAQ,EAAE;oBACZ,CAAE;oBAAAjD,QAAA,EAED+C,IAAI,CAACL;kBAAK;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC;gBAAA,GAjCDiC,IAAI,CAACL,KAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkCL,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb/K,OAAA,CAACzB,MAAM,CAAC2L,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAG,CAAE;QAC/Bd,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAE,CAAE;QAC9BZ,UAAU,EAAE;UAAEc,KAAK,EAAE,GAAG;UAAEb,QAAQ,EAAE;QAAI,CAAE;QAC1CR,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eAErCjK,OAAA;UAAKgK,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCjK,OAAA;YAAKgK,SAAS,EAAC,oEAAoE;YAAAC,QAAA,eACjFjK,OAAA;cAAKgK,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBAG1EjK,OAAA,CAACzB,MAAM,CAAC4O,MAAM;gBACZF,UAAU,EAAE;kBAAEvB,KAAK,EAAE;gBAAK,CAAE;gBAC5B0B,QAAQ,EAAE;kBAAE1B,KAAK,EAAE;gBAAK,CAAE;gBAC1B2B,OAAO,EAAExE,YAAa;gBACtBmB,SAAS,EAAC,wKAAwK;gBAClLsB,KAAK,EAAE;kBACLc,UAAU,EAAE,0CAA0C;kBACtDvK,KAAK,EAAE,SAAS;kBAChBoK,UAAU,EAAE,MAAM;kBAClBC,UAAU,EAAE,KAAK;kBACjBgB,QAAQ,EAAE;gBACZ,CAAE;gBAAAjD,QAAA,gBAEFjK,OAAA,CAACjB,QAAQ;kBAACiL,SAAS,EAAC;gBAAS;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChC/K,OAAA;kBAAAiK,QAAA,EACGtJ,eAAe,GAAI,YAAWA,eAAgB,EAAC,GAAG;gBAAS;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eAGhB/K,OAAA,CAACzB,MAAM,CAAC4O,MAAM;gBACZF,UAAU,EAAE;kBAAEvB,KAAK,EAAE,IAAI;kBAAEpB,MAAM,EAAE;gBAAI,CAAE;gBACzC8C,QAAQ,EAAE;kBAAE1B,KAAK,EAAE;gBAAK,CAAE;gBAC1B2B,OAAO,EAAElK,gBAAiB;gBAC1BmK,QAAQ,EAAE7M,OAAQ;gBAClBuJ,SAAS,EAAC,4LAA4L;gBACtMsB,KAAK,EAAE;kBACL4B,QAAQ,EAAE;gBACZ,CAAE;gBAAAjD,QAAA,gBAEFjK,OAAA,CAACf,SAAS;kBAAC+K,SAAS,EAAG,WAAUvJ,OAAO,GAAG,cAAc,GAAG,EAAG;gBAAE;kBAAAmK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpE/K,OAAA;kBAAAiK,QAAA,EAAM;gBAAO;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGZtK,OAAO,iBACNT,OAAA,CAACzB,MAAM,CAAC2L,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBJ,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAE3DjK,OAAA,CAACzB,MAAM,CAAC2L,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DX,SAAS,EAAC;QAA6E;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,eACF/K,OAAA;UAAGgK,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAoB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CACb,EAGA,CAACtK,OAAO,iBACPT,OAAA,CAACzB,MAAM,CAAC2L,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAG,CAAE;QAC/Bd,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAE,CAAE;QAC9BZ,UAAU,EAAE;UAAEc,KAAK,EAAE,GAAG;UAAEb,QAAQ,EAAE;QAAI,CAAE;QAC1CR,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eAEtCjK,OAAA;UAAKgK,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAG/BtB,aAAa,CAACpC,MAAM,GAAG,CAAC,iBACvBvG,OAAA,CAACzB,MAAM,CAAC2L,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAI,CAAE;YACpCrB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAE,CAAE;YAClCnB,UAAU,EAAE;cAAEc,KAAK,EAAE,GAAG;cAAEb,QAAQ,EAAE;YAAI,CAAE;YAC1CR,SAAS,EAAC,OAAO;YAAAC,QAAA,gBAEjBjK,OAAA;cAAIgK,SAAS,EAAC,kDAAkD;cAACsB,KAAK,EAAE;gBACtEc,UAAU,EAAE,mDAAmD;gBAC/DL,oBAAoB,EAAE,MAAM;gBAC5BC,mBAAmB,EAAE,aAAa;gBAClCC,UAAU,EAAE,6BAA6B;gBACzC9F,MAAM,EAAE;cACV,CAAE;cAAA8D,QAAA,EAAC;YAEH;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL/K,OAAA;cAAKgK,SAAS,EAAC,yDAAyD;cAAAC,QAAA,EACrEtB,aAAa,CAAC7E,GAAG,CAAC,CAACyJ,QAAQ,EAAEvJ,KAAK,KAAK;gBACtC,MAAMwJ,QAAQ,GAAGxJ,KAAK,GAAG,CAAC;gBAC1B,MAAMyJ,aAAa,GAAGnN,IAAI,IAAIiN,QAAQ,CAACtJ,GAAG,KAAK3D,IAAI,CAAC2D,GAAG;gBAEvD,oBACEjE,OAAA,CAACzB,MAAM,CAAC2L,GAAG;kBAETC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAG,CAAE;kBAC/Bd,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAE,CAAE;kBAC9BZ,UAAU,EAAE;oBAAEc,KAAK,EAAE,GAAG,GAAGrH,KAAK,GAAG,GAAG;oBAAEwG,QAAQ,EAAE;kBAAI,CAAE;kBACxDyC,UAAU,EAAE;oBAAEvB,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCnB,SAAS,EAAG,YACVwD,QAAQ,KAAK,CAAC,GAAG,yBAAyB,GAC1CA,QAAQ,KAAK,CAAC,GAAG,YAAY,GAAG,YACjC,EAAE;kBAAAvD,QAAA,eAGHjK,OAAA;oBACEgK,SAAS,EAAG,8BAA6BuD,QAAQ,CAACxK,IAAI,CAAClB,KAAM,oBAAmB0L,QAAQ,CAACxK,IAAI,CAACb,IAAK,IAAGqL,QAAQ,CAACxK,IAAI,CAACR,MAAO,aAAa;oBACxI+I,KAAK,EAAE;sBACLmB,SAAS,EAAG,eAAcc,QAAQ,CAACxK,IAAI,CAACd,WAAY,cAAasL,QAAQ,CAACxK,IAAI,CAACd,WAAY;oBAC7F,CAAE;oBAAAgI,QAAA,eAEFjK,OAAA;sBACEgK,SAAS,EAAG,GAAEuD,QAAQ,CAACxK,IAAI,CAACjB,OAAQ,wEAAwE;sBAC5GwJ,KAAK,EAAE;wBACLkB,MAAM,EAAG,aAAYe,QAAQ,CAACxK,IAAI,CAACT,WAAY;sBACjD,CAAE;sBAAA2H,QAAA,gBAEFjK,OAAA;wBAAKgK,SAAS,EAAC;sBAAiE;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGvF/K,OAAA;wBACEgK,SAAS,EAAG,mFAAkFuD,QAAQ,CAACxK,IAAI,CAAClB,KAAM,4FAA4F;wBAC9MyJ,KAAK,EAAE;0BACLzJ,KAAK,EAAE2L,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAGA,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAGA,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;0BACvGvB,UAAU,EAAE,6BAA6B;0BACzCO,MAAM,EAAG,aAAYe,QAAQ,CAACxK,IAAI,CAACT,WAAY,EAAC;0BAChDmK,SAAS,EAAG,eAAcc,QAAQ,CAACxK,IAAI,CAACd,WAAY,cAAasL,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;0BAC5FiL,QAAQ,EAAE,QAAQ;0BAClBhB,UAAU,EAAE;wBACd,CAAE;wBAAAjC,QAAA,EAEDuD,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAGA,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAGA,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAGA;sBAAQ;wBAAA5C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E,CAAC,EAGLyC,QAAQ,KAAK,CAAC,iBACbxN,OAAA,CAACzB,MAAM,CAAC2L,GAAG;wBACTG,OAAO,EAAE;0BAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;wBAAE,CAAE;wBACrCC,UAAU,EAAE;0BAAEC,QAAQ,EAAE,CAAC;0BAAEC,MAAM,EAAEC;wBAAS,CAAE;wBAC9CV,SAAS,EAAC,qDAAqD;wBAAAC,QAAA,eAE/DjK,OAAA,CAACpB,OAAO;0BAACoL,SAAS,EAAC;wBAAyB;0BAAAY,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CACb,eAGD/K,OAAA;wBAAKgK,SAAS,EAAG,yBAAwByD,aAAa,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAxD,QAAA,gBACvGjK,OAAA;0BACEgK,SAAS,EAAC,yDAAyD;0BACnEsB,KAAK,EAAE;4BACLc,UAAU,EAAG,0BAAyBmB,QAAQ,CAACxK,IAAI,CAACT,WAAY,KAAIiL,QAAQ,CAACxK,IAAI,CAAChB,SAAU,GAAE;4BAC9F0K,SAAS,EAAG,cAAac,QAAQ,CAACxK,IAAI,CAACd,WAAY,cAAasL,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;4BAC3FsK,OAAO,EAAE;0BACX,CAAE;0BAAAtC,QAAA,EAEDsD,QAAQ,CAAClJ,cAAc,gBACtBrE,OAAA;4BACE0N,GAAG,EAAEH,QAAQ,CAAClJ,cAAe;4BAC7BsJ,GAAG,EAAEJ,QAAQ,CAACrJ,IAAK;4BACnB8F,SAAS,EAAC,yCAAyC;4BACnDsB,KAAK,EAAE;8BACLnF,MAAM,EAAE,+BAA+B;8BACvCyH,WAAW,EAAE;4BACf;0BAAE;4BAAAhD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,gBAEF/K,OAAA;4BACEgK,SAAS,EAAC,iFAAiF;4BAC3FsB,KAAK,EAAE;8BACLc,UAAU,EAAG,2BAA0BmB,QAAQ,CAACxK,IAAI,CAACT,WAAY,KAAIiL,QAAQ,CAACxK,IAAI,CAAChB,SAAU,GAAE;8BAC/FF,KAAK,EAAE,SAAS;8BAChBoK,UAAU,EAAE,6BAA6B;8BACzC2B,WAAW,EAAE;4BACf,CAAE;4BAAA3D,QAAA,EAEDsD,QAAQ,CAACrJ,IAAI,CAAC2J,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAlD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnC;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,EACL0C,aAAa,iBACZzN,OAAA;0BACEgK,SAAS,EAAC,4DAA4D;0BACtEsB,KAAK,EAAE;4BACLc,UAAU,EAAE,0CAA0C;4BACtDK,SAAS,EAAE;0BACb,CAAE;0BAAAxC,QAAA,eAEFjK,OAAA,CAACnB,MAAM;4BAACmL,SAAS,EAAC;0BAAoB;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGN/K,OAAA;wBACEgK,SAAS,EAAC,uCAAuC;wBACjDsB,KAAK,EAAE;0BACLzJ,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAACf,SAAS;0BAC9BiK,UAAU,EAAG,eAAcsB,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;0BACtDiL,QAAQ,EAAE,QAAQ;0BAClB/G,MAAM,EAAE;wBACV,CAAE;wBAAA8D,QAAA,EAEDsD,QAAQ,CAACrJ;sBAAI;wBAAA0G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACL/K,OAAA;wBACEgK,SAAS,EAAG,6DAA4DuD,QAAQ,CAACxK,IAAI,CAAClB,KAAM,mEAAmE;wBAC/JyJ,KAAK,EAAE;0BACLzJ,KAAK,EAAE,SAAS;0BAChBoK,UAAU,EAAE,6BAA6B;0BACzCO,MAAM,EAAG,aAAYe,QAAQ,CAACxK,IAAI,CAACT,WAAY,EAAC;0BAChDmK,SAAS,EAAG,cAAac,QAAQ,CAACxK,IAAI,CAACd,WAAY,cAAasL,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;0BAC3FiL,QAAQ,EAAE,QAAQ;0BAClBa,aAAa,EAAE;wBACjB,CAAE;wBAAA9D,QAAA,gBAEFjK,OAAA,CAACuN,QAAQ,CAACxK,IAAI,CAACZ,IAAI;0BACjB6H,SAAS,EAAC,SAAS;0BACnBsB,KAAK,EAAE;4BACLnF,MAAM,EAAE,wCAAwC;4BAChDtE,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAAChB;0BACvB;wBAAE;0BAAA6I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACF/K,OAAA;0BAAMsL,KAAK,EAAE;4BAAEzJ,KAAK,EAAE;0BAAU,CAAE;0BAAAoI,QAAA,EAAEsD,QAAQ,CAACxK,IAAI,CAACX;wBAAK;0BAAAwI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5D,CAAC,EAGL,CAAC,MAAM;wBACN,MAAMiD,KAAK,GAAG7E,oBAAoB,CAChCoE,QAAQ,CAAC3I,kBAAkB,EAC3B2I,QAAQ,CAACnE,mBAAmB,EAC5BmE,QAAQ,CAAClE,gBAAgB,EACzBrF,KACF,CAAC;wBACD,oBACEhE,OAAA;0BACEgK,SAAS,EAAC,4FAA4F;0BACtGsB,KAAK,EAAE;4BACL2C,eAAe,EAAED,KAAK,CAAClM,OAAO;4BAC9BD,KAAK,EAAEmM,KAAK,CAACnM,KAAK;4BAClB2K,MAAM,EAAG,aAAYwB,KAAK,CAAC1L,WAAY,EAAC;4BACxC2J,UAAU,EAAE,6BAA6B;4BACzCiB,QAAQ,EAAE,SAAS;4BACnBa,aAAa,EAAE;0BACjB,CAAE;0BAAA9D,QAAA,EAED+D,KAAK,CAACjE;wBAAI;0BAAAa,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACR,CAAC;sBAEV,CAAC,EAAE,CAAC,eAGJ/K,OAAA;wBAAKgK,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtCjK,OAAA;0BAAKgK,SAAS,EAAC,gCAAgC;0BAAAC,QAAA,gBAC7CjK,OAAA;4BAAMsL,KAAK,EAAE;8BACXzJ,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAAChB,SAAS;8BAC9BkK,UAAU,EAAG,eAAcsB,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;8BACtDiK,UAAU,EAAE,KAAK;8BACjBgB,QAAQ,EAAE;4BACZ,CAAE;4BAAAjD,QAAA,EAAC;0BAAM;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAChB/K,OAAA;4BAAMsL,KAAK,EAAE;8BACXzJ,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAACf,SAAS;8BAC9BiK,UAAU,EAAG,eAAcsB,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;8BACtDiK,UAAU,EAAE,KAAK;8BACjBgB,QAAQ,EAAE,QAAQ;8BAClB/G,MAAM,EAAE;4BACV,CAAE;4BAAA8D,QAAA,EAAEsD,QAAQ,CAAChJ,OAAO,CAACwI,cAAc,CAAC;0BAAC;4BAAAnC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1C,CAAC,eACN/K,OAAA;0BAAKgK,SAAS,EAAC,gCAAgC;0BAAAC,QAAA,gBAC7CjK,OAAA;4BAAMsL,KAAK,EAAE;8BACXzJ,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAAChB,SAAS;8BAC9BkK,UAAU,EAAG,eAAcsB,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;8BACtDiK,UAAU,EAAE,KAAK;8BACjBgB,QAAQ,EAAE;4BACZ,CAAE;4BAAAjD,QAAA,EAAC;0BAAW;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACrB/K,OAAA;4BAAMsL,KAAK,EAAE;8BACXzJ,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAACf,SAAS;8BAC9BiK,UAAU,EAAG,eAAcsB,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;8BACtDiK,UAAU,EAAE,KAAK;8BACjBgB,QAAQ,EAAE,QAAQ;8BAClB/G,MAAM,EAAE;4BACV,CAAE;4BAAA8D,QAAA,EAAEsD,QAAQ,CAAC/I;0BAAiB;4BAAAoG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnC,CAAC,eACN/K,OAAA;0BAAKgK,SAAS,EAAC,gCAAgC;0BAAAC,QAAA,gBAC7CjK,OAAA;4BAAMsL,KAAK,EAAE;8BACXzJ,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAAChB,SAAS;8BAC9BkK,UAAU,EAAG,eAAcsB,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;8BACtDiK,UAAU,EAAE,KAAK;8BACjBgB,QAAQ,EAAE;4BACZ,CAAE;4BAAAjD,QAAA,EAAC;0BAAU;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACpB/K,OAAA;4BAAMsL,KAAK,EAAE;8BACXzJ,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAACf,SAAS;8BAC9BiK,UAAU,EAAG,eAAcsB,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;8BACtDiK,UAAU,EAAE,KAAK;8BACjBgB,QAAQ,EAAE,QAAQ;8BAClB/G,MAAM,EAAE;4BACV,CAAE;4BAAC6D,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,EACnCsD,QAAQ,CAAC7I;0BAAa;4BAAAkG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eAGN/K,OAAA;0BAAKgK,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,eAC/BjK,OAAA;4BAAMsL,KAAK,EAAE;8BACXzJ,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAAChB,SAAS;8BAC9BkK,UAAU,EAAG,eAAcsB,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;8BACtDiK,UAAU,EAAE,KAAK;8BACjBgB,QAAQ,EAAE,QAAQ;8BAClB9C,OAAO,EAAE;4BACX,CAAE;4BAAAH,QAAA,EACCsD,QAAQ,CAAClI,UAAU,KAAK,SAAS,GAAG,cAAc,GAClDkI,QAAQ,CAAClI,UAAU,KAAK,eAAe,GAAG,kBAAkB,GAC5D;0BAAc;4BAAAuF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACX;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GA9NDwC,QAAQ,CAACtJ,GAAG;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA+NP,CAAC;cAEjB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,EAGAnC,eAAe,CAACrC,MAAM,GAAG,CAAC,iBACzBvG,OAAA,CAACzB,MAAM,CAAC2L,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAG,CAAE;YAC/Bd,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAE,CAAE;YAC9BZ,UAAU,EAAE;cAAEc,KAAK,EAAE,CAAC;cAAEb,QAAQ,EAAE;YAAI,CAAE;YACxCR,SAAS,EAAC,OAAO;YAAAC,QAAA,gBAEjBjK,OAAA;cAAIgK,SAAS,EAAC,kDAAkD;cAACsB,KAAK,EAAE;gBACtEc,UAAU,EAAE,mDAAmD;gBAC/DL,oBAAoB,EAAE,MAAM;gBAC5BC,mBAAmB,EAAE,aAAa;gBAClCC,UAAU,EAAE,6BAA6B;gBACzC9F,MAAM,EAAE;cACV,CAAE;cAAA8D,QAAA,EAAC;YAEH;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL/K,OAAA;cAAKgK,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBrB,eAAe,CAAC9E,GAAG,CAAC,CAACyJ,QAAQ,EAAEvJ,KAAK,KAAK;gBACxC,MAAMkK,UAAU,GAAGlK,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC9B,MAAMyJ,aAAa,GAAGnN,IAAI,IAAIiN,QAAQ,CAACtJ,GAAG,KAAK3D,IAAI,CAAC2D,GAAG;gBAEvD,oBACEjE,OAAA,CAACzB,MAAM,CAAC2L,GAAG;kBAETuB,GAAG,EAAEgC,aAAa,GAAGjM,cAAc,GAAG,IAAK;kBAC3C2I,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCf,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAE,CAAE;kBAC9Bb,UAAU,EAAE;oBAAEc,KAAK,EAAE,GAAG,GAAGrH,KAAK,GAAG,GAAG;oBAAEwG,QAAQ,EAAE;kBAAI,CAAE;kBACxDyC,UAAU,EAAE;oBAAEvB,KAAK,EAAE,IAAI;oBAAEN,CAAC,EAAE;kBAAG,CAAE;kBACnCpB,SAAS,EAAG,YAAWyD,aAAa,GAAG,wBAAwB,GAAG,EAAG,IAAGpM,UAAU,IAAIoM,aAAa,GAAG,mBAAmB,GAAG,EAAG,EAAE;kBAAAxD,QAAA,eAEjIjK,OAAA;oBACEgK,SAAS,EAAG,oBAAmBuD,QAAQ,CAACxK,IAAI,CAAClB,KAAM,mBAAkB0L,QAAQ,CAACxK,IAAI,CAACb,IAAK,IAAGqL,QAAQ,CAACxK,IAAI,CAACR,MAAO,EAAE;oBAClH+I,KAAK,EAAE;sBACLmB,SAAS,EAAG,cAAac,QAAQ,CAACxK,IAAI,CAACd,WAAY;oBACrD,CAAE;oBAAAgI,QAAA,eAEFjK,OAAA;sBACEgK,SAAS,EAAG,GAAEuD,QAAQ,CAACxK,IAAI,CAACjB,OAAQ,mFAAmF;sBACvHwJ,KAAK,EAAE;wBACLkB,MAAM,EAAG,aAAYe,QAAQ,CAACxK,IAAI,CAACT,WAAY;sBACjD,CAAE;sBAAA2H,QAAA,gBAEFjK,OAAA;wBAAKgK,SAAS,EAAC;sBAA+D;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGrF/K,OAAA;wBACEgK,SAAS,EAAG,6CAA4CuD,QAAQ,CAACxK,IAAI,CAAClB,KAAM,2FAA2F;wBACvKyJ,KAAK,EAAE;0BACLzJ,KAAK,EAAEqM,UAAU,IAAI,EAAE,GAAGX,QAAQ,CAACxK,IAAI,CAAChB,SAAS,GAAG,SAAS;0BAC7DkK,UAAU,EAAE,6BAA6B;0BACzCO,MAAM,EAAG,aAAYe,QAAQ,CAACxK,IAAI,CAACT,WAAY,EAAC;0BAChDmK,SAAS,EAAG,cAAac,QAAQ,CAACxK,IAAI,CAACd,WAAY,cAAasL,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;0BAC3FiL,QAAQ,EAAEgB,UAAU,IAAI,EAAE,GAAG,QAAQ,GAAG,QAAQ;0BAChDhC,UAAU,EAAE;wBACd,CAAE;wBAAAjC,QAAA,EAEDiE,UAAU,IAAI,EAAE,GAAI,IAAGA,UAAW,EAAC,GAAGA;sBAAU;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC,eAGN/K,OAAA;wBAAKgK,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,gBACrCjK,OAAA;0BACEgK,SAAS,EAAC,uDAAuD;0BACjEsB,KAAK,EAAE;4BACLc,UAAU,EAAG,0BAAyBmB,QAAQ,CAACxK,IAAI,CAACT,WAAY,KAAIiL,QAAQ,CAACxK,IAAI,CAAChB,SAAU,GAAE;4BAC9F0K,SAAS,EAAG,cAAac,QAAQ,CAACxK,IAAI,CAACd,WAAY,cAAasL,QAAQ,CAACxK,IAAI,CAACd,WAAY;0BAC5F,CAAE;0BAAAgI,QAAA,EAEDsD,QAAQ,CAAClJ,cAAc,gBACtBrE,OAAA;4BACE0N,GAAG,EAAEH,QAAQ,CAAClJ,cAAe;4BAC7BsJ,GAAG,EAAEJ,QAAQ,CAACrJ,IAAK;4BACnB8F,SAAS,EAAC,yCAAyC;4BACnDsB,KAAK,EAAE;8BACLnF,MAAM,EAAE,6CAA6C;8BACrDyH,WAAW,EAAE;4BACf;0BAAE;4BAAAhD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,gBAEF/K,OAAA;4BACEgK,SAAS,EAAC,gFAAgF;4BAC1FsB,KAAK,EAAE;8BACLc,UAAU,EAAG,2BAA0BmB,QAAQ,CAACxK,IAAI,CAACT,WAAY,KAAIiL,QAAQ,CAACxK,IAAI,CAAChB,SAAU,GAAE;8BAC/FF,KAAK,EAAE,SAAS;8BAChBoK,UAAU,EAAE,6BAA6B;8BACzC2B,WAAW,EAAE;4BACf,CAAE;4BAAA3D,QAAA,EAEDsD,QAAQ,CAACrJ,IAAI,CAAC2J,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAlD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnC;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,EACL0C,aAAa,iBACZzN,OAAA;0BACEgK,SAAS,EAAC,yDAAyD;0BACnEsB,KAAK,EAAE;4BACLc,UAAU,EAAE,0CAA0C;4BACtDK,SAAS,EAAE;0BACb,CAAE;0BAAAxC,QAAA,eAEFjK,OAAA,CAACnB,MAAM;4BAACmL,SAAS,EAAC;0BAAoB;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGN/K,OAAA;wBAAKgK,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,gBAC3CjK,OAAA;0BAAKgK,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,gBAC3CjK,OAAA;4BACEgK,SAAS,EAAC,6BAA6B;4BACvCsB,KAAK,EAAE;8BACLzJ,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAACf,SAAS;8BAC9BiK,UAAU,EAAG,eAAcsB,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;8BACtDiL,QAAQ,EAAE,SAAS;8BACnB/G,MAAM,EAAE;4BACV,CAAE;4BAAA8D,QAAA,EAEDsD,QAAQ,CAACrJ;0BAAI;4BAAA0G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACZ,CAAC,EACJ0C,aAAa,iBACZzN,OAAA;4BACEgK,SAAS,EAAC,yDAAyD;4BACnEsB,KAAK,EAAE;8BACLc,UAAU,EAAE,0CAA0C;8BACtDvK,KAAK,EAAE,SAAS;8BAChBoK,UAAU,EAAE,MAAM;8BAClBO,MAAM,EAAE,mBAAmB;8BAC3BC,SAAS,EAAE;4BACb,CAAE;4BAAAxC,QAAA,EACH;0BAED;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACN/K,OAAA;0BACEgK,SAAS,EAAG,6DAA4DuD,QAAQ,CAACxK,IAAI,CAAClB,KAAM,kCAAkC;0BAC9HyJ,KAAK,EAAE;4BACLzJ,KAAK,EAAE,SAAS;4BAChBoK,UAAU,EAAE,6BAA6B;4BACzCO,MAAM,EAAG,aAAYe,QAAQ,CAACxK,IAAI,CAACT,WAAY,EAAC;4BAChDmK,SAAS,EAAG,aAAYc,QAAQ,CAACxK,IAAI,CAACd,WAAY,cAAasL,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;4BAC1FiL,QAAQ,EAAE,QAAQ;4BAClBa,aAAa,EAAE;0BACjB,CAAE;0BAAA9D,QAAA,gBAEFjK,OAAA,CAACuN,QAAQ,CAACxK,IAAI,CAACZ,IAAI;4BACjB6H,SAAS,EAAC,SAAS;4BACnBsB,KAAK,EAAE;8BACLnF,MAAM,EAAE,wCAAwC;8BAChDtE,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAAChB;4BACvB;0BAAE;4BAAA6I,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eACF/K,OAAA;4BAAMsL,KAAK,EAAE;8BAAEzJ,KAAK,EAAE;4BAAU,CAAE;4BAAAoI,QAAA,EAAEsD,QAAQ,CAACxK,IAAI,CAACX;0BAAK;4BAAAwI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5D,CAAC,EAGL,CAAC,MAAM;0BACN,MAAMiD,KAAK,GAAG7E,oBAAoB,CAChCoE,QAAQ,CAAC3I,kBAAkB,EAC3B2I,QAAQ,CAACnE,mBAAmB,EAC5BmE,QAAQ,CAAClE,gBAAgB,EACzB6E,UACF,CAAC;0BACD,oBACElO,OAAA;4BACEgK,SAAS,EAAC,8EAA8E;4BACxFsB,KAAK,EAAE;8BACL2C,eAAe,EAAED,KAAK,CAAClM,OAAO;8BAC9BD,KAAK,EAAEmM,KAAK,CAACnM,KAAK;8BAClB2K,MAAM,EAAG,aAAYwB,KAAK,CAAC1L,WAAY,EAAC;8BACxC2J,UAAU,EAAE,6BAA6B;8BACzCiB,QAAQ,EAAE,QAAQ;8BAClBa,aAAa,EAAE;4BACjB,CAAE;4BAAA9D,QAAA,EAED+D,KAAK,CAACjE;0BAAI;4BAAAa,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACR,CAAC;wBAEV,CAAC,EAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eAGN/K,OAAA;wBAAKgK,SAAS,EAAC,wCAAwC;wBAAAC,QAAA,gBACrDjK,OAAA;0BAAKgK,SAAS,EAAC,cAAc;0BAACsB,KAAK,EAAE;4BACnCzJ,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAACf,SAAS;4BAC9BiK,UAAU,EAAG,eAAcsB,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;4BACtDiK,UAAU,EAAE,KAAK;4BACjB/F,MAAM,EAAE,oCAAoC;4BAC5C+G,QAAQ,EAAE;0BACZ,CAAE;0BAAAjD,QAAA,GAAC,eACE,EAACsD,QAAQ,CAAChJ,OAAO,CAACwI,cAAc,CAAC,CAAC,EAAC,KACxC;wBAAA;0BAAAnC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACN/K,OAAA;0BAAKgK,SAAS,EAAC,6CAA6C;0BAAAC,QAAA,gBAC1DjK,OAAA;4BAAMgK,SAAS,EAAC,yBAAyB;4BAACsB,KAAK,EAAE;8BAC/CzJ,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAAChB,SAAS;8BAC9BkK,UAAU,EAAG,eAAcsB,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;8BACtDiK,UAAU,EAAE,KAAK;8BACjBgB,QAAQ,EAAE;4BACZ,CAAE;4BAAAjD,QAAA,gBACAjK,OAAA,CAAChB,OAAO;8BAACgL,SAAS,EAAC,SAAS;8BAACsB,KAAK,EAAE;gCAAEzJ,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAAChB;8BAAU;4BAAE;8BAAA6I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACzEwC,QAAQ,CAAC/I,iBAAiB;0BAAA;4BAAAoG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB,CAAC,eACP/K,OAAA;4BAAMgK,SAAS,EAAC,yBAAyB;4BAACsB,KAAK,EAAE;8BAC/CzJ,KAAK,EAAE0L,QAAQ,CAACxK,IAAI,CAAChB,SAAS;8BAC9BkK,UAAU,EAAG,eAAcsB,QAAQ,CAACxK,IAAI,CAACd,WAAY,EAAC;8BACtDiK,UAAU,EAAE,KAAK;8BACjBgB,QAAQ,EAAE;4BACZ,CAAE;4BAAAjD,QAAA,gBACAjK,OAAA,CAAClB,OAAO;8BAACkL,SAAS,EAAC,SAAS;8BAACsB,KAAK,EAAE;gCAAEzJ,KAAK,EAAE;8BAAU;4BAAE;8BAAA+I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC3DwC,QAAQ,CAAC7I,aAAa;0BAAA;4BAAAkG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAhMDwC,QAAQ,CAACtJ,GAAG;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiMP,CAAC;cAEjB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,EAGAxK,WAAW,CAACgG,MAAM,GAAG,CAAC,iBACrBvG,OAAA,CAACzB,MAAM,CAAC2L,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAG,CAAE;YAC/Bd,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAE,CAAE;YAC9BZ,UAAU,EAAE;cAAEc,KAAK,EAAE,GAAG;cAAEb,QAAQ,EAAE;YAAI,CAAE;YAC1CR,SAAS,EAAC,sIAAsI;YAAAC,QAAA,eAEhJjK,OAAA;cAAKgK,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjK,OAAA;gBAAIgK,SAAS,EAAC,wBAAwB;gBAACsB,KAAK,EAAE;kBAC5CzJ,KAAK,EAAE,SAAS;kBAChBoK,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAA6B;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrC/K,OAAA;gBAAKgK,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,gBAC5DjK,OAAA;kBAAKgK,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CjK,OAAA;oBAAKgK,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C1J,WAAW,CAAC4F,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACxC,UAAU,KAAK,SAAS,CAAC,CAACkB;kBAAM;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACN/K,OAAA;oBAAKgK,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACN/K,OAAA;kBAAKgK,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,gBAC5CjK,OAAA;oBAAKgK,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC7C1J,WAAW,CAAC4F,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACxC,UAAU,KAAK,eAAe,CAAC,CAACkB;kBAAM;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACN/K,OAAA;oBAAKgK,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACN/K,OAAA;kBAAKgK,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC9CjK,OAAA;oBAAKgK,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC/C1J,WAAW,CAAC4F,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACxC,UAAU,KAAK,WAAW,CAAC,CAACkB;kBAAM;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACN/K,OAAA;oBAAKgK,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/K,OAAA;gBAAGgK,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,EAGApK,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrCX,OAAA,CAACzB,MAAM,CAAC2L,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAI,CAAE;YACpCrB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAE,CAAE;YAClCnB,UAAU,EAAE;cAAEc,KAAK,EAAE,GAAG;cAAEb,QAAQ,EAAE;YAAI,CAAE;YAC1CR,SAAS,EAAC,wIAAwI;YAAAC,QAAA,eAElJjK,OAAA;cAAKgK,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjK,OAAA;gBAAIgK,SAAS,EAAC,yBAAyB;gBAACsB,KAAK,EAAE;kBAC7CzJ,KAAK,EAAE,SAAS;kBAChBoK,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAAqB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7B/K,OAAA;gBAAKgK,SAAS,EAAC,0BAA0B;gBAACsB,KAAK,EAAE;kBAC/CzJ,KAAK,EAAE,SAAS;kBAChBoK,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,GAAC,GAAC,EAACtJ,eAAe;cAAA;gBAAAiK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3B/K,OAAA;gBAAGgK,SAAS,EAAC,SAAS;gBAACsB,KAAK,EAAE;kBAC5BzJ,KAAK,EAAE,SAAS;kBAChBoK,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAGD/K,OAAA,CAACzB,MAAM,CAAC2L,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAG,CAAE;YAC/Bd,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAE,CAAE;YAC9BZ,UAAU,EAAE;cAAEc,KAAK,EAAE,CAAC;cAAEb,QAAQ,EAAE;YAAI,CAAE;YACxCR,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAE7BjK,OAAA;cAAKgK,SAAS,EAAC,8HAA8H;cAAAC,QAAA,gBAC3IjK,OAAA,CAACzB,MAAM,CAAC2L,GAAG;gBACTG,OAAO,EAAE;kBAAEqB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;gBAAE,CAAE;gBACjCnB,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEC,MAAM,EAAEC;gBAAS,CAAE;gBAAAT,QAAA,eAE9CjK,OAAA,CAACZ,QAAQ;kBAAC4K,SAAS,EAAC;gBAAwC;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACb/K,OAAA;gBAAIgK,SAAS,EAAC,yBAAyB;gBAACsB,KAAK,EAAE;kBAC7CzJ,KAAK,EAAE,SAAS;kBAChBoK,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAAqB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7B/K,OAAA;gBAAGgK,SAAS,EAAC,gCAAgC;gBAACsB,KAAK,EAAE;kBACnDzJ,KAAK,EAAE,SAAS;kBAChBoK,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAGH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ/K,OAAA,CAACzB,MAAM,CAAC4O,MAAM;gBACZF,UAAU,EAAE;kBAAEvB,KAAK,EAAE;gBAAK,CAAE;gBAC5B0B,QAAQ,EAAE;kBAAE1B,KAAK,EAAE;gBAAK,CAAE;gBAC1B1B,SAAS,EAAC,sJAAsJ;gBAChKqD,OAAO,EAAEA,CAAA,KAAMc,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAa;gBAAApE,QAAA,EACpD;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EAGZxK,WAAW,CAACgG,MAAM,KAAK,CAAC,IAAI,CAAC9F,OAAO,iBACnCT,OAAA,CAACzB,MAAM,CAAC2L,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAI,CAAE;YACpCrB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAE,CAAE;YAClC1B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7BjK,OAAA,CAACrB,QAAQ;cAACqL,SAAS,EAAC;YAAsC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7D/K,OAAA;cAAIgK,SAAS,EAAC,yBAAyB;cAACsB,KAAK,EAAE;gBAC7CzJ,KAAK,EAAE,SAAS;gBAChBoK,UAAU,EAAE,6BAA6B;gBACzCC,UAAU,EAAE;cACd,CAAE;cAAAjC,QAAA,EAAC;YAAgB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxB/K,OAAA;cAAGgK,SAAS,EAAC,SAAS;cAACsB,KAAK,EAAE;gBAC5BzJ,KAAK,EAAE,SAAS;gBAChBoK,UAAU,EAAE,6BAA6B;gBACzCC,UAAU,EAAE;cACd,CAAE;cAAAjC,QAAA,EAAC;YAEH;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7K,EAAA,CAv5CID,kBAAkB;EAAA,QACJxB,WAAW;AAAA;AAAA6P,EAAA,GADzBrO,kBAAkB;AAy5CxB,eAAeA,kBAAkB;AAAC,IAAAqO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}