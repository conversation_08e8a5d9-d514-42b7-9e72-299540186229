require("dotenv").config();
const mongoose = require("mongoose");
const Exam = require("./models/examModel");
const Question = require("./models/questionModel");
const { AIQuestionGeneration } = require("./models/aiQuestionGenerationModel");

// Connect to MongoDB with proper configuration
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
    });
    console.log("✅ MongoDB Connected Successfully");
  } catch (error) {
    console.error("❌ MongoDB connection failed:", error);
    process.exit(1);
  }
};

async function debugExamQuestions() {
  await connectDB();

  try {
    const examId = "685f80c63916d9527839d161"; // The exam ID from your debug output
    
    console.log("🔍 Debugging Exam Questions...");
    console.log("Exam ID:", examId);
    console.log("=" * 50);
    
    // 1. Check the exam
    const exam = await Exam.findById(examId);
    console.log("\n1️⃣ EXAM DETAILS:");
    console.log("Exam found:", !!exam);
    if (exam) {
      console.log("Exam name:", exam.name);
      console.log("Questions in exam:", exam.questions.length);
      console.log("Question IDs:", exam.questions);
    }
    
    // 2. Check all questions for this exam
    const questionsForExam = await Question.find({ exam: examId });
    console.log("\n2️⃣ QUESTIONS IN DATABASE FOR THIS EXAM:");
    console.log("Questions found:", questionsForExam.length);
    questionsForExam.forEach((q, index) => {
      console.log(`Question ${index + 1}:`);
      console.log("  ID:", q._id);
      console.log("  Name:", q.name);
      console.log("  Answer Type:", q.answerType);
      console.log("  Is AI Generated:", q.isAIGenerated);
      console.log("  Generation Source:", q.generationSource);
      console.log("  Has Options:", !!q.options);
      console.log("  Correct Option:", q.correctOption);
      console.log("  Correct Answer:", q.correctAnswer);
      console.log("  ---");
    });
    
    // 3. Check AI generations for this exam
    const aiGenerations = await AIQuestionGeneration.find({ examId: examId });
    console.log("\n3️⃣ AI GENERATIONS FOR THIS EXAM:");
    console.log("AI generations found:", aiGenerations.length);
    aiGenerations.forEach((gen, index) => {
      console.log(`Generation ${index + 1}:`);
      console.log("  ID:", gen._id);
      console.log("  Status:", gen.generationStatus);
      console.log("  Generated questions:", gen.generatedQuestions.length);
      console.log("  Approved questions:", gen.generatedQuestions.filter(q => q.approved).length);
      console.log("  ---");
      
      gen.generatedQuestions.forEach((gq, qIndex) => {
        console.log(`    Question ${qIndex + 1}:`);
        console.log("      Approved:", gq.approved);
        console.log("      Question ID:", gq.questionId);
        console.log("      Name:", gq.generatedContent?.name);
        console.log("      Answer Type:", gq.generatedContent?.answerType);
      });
    });
    
    // 4. Check if there are orphaned questions (questions that should be in exam but aren't)
    const orphanedQuestions = await Question.find({ 
      exam: examId,
      _id: { $nin: exam?.questions || [] }
    });
    console.log("\n4️⃣ ORPHANED QUESTIONS (exist but not in exam.questions array):");
    console.log("Orphaned questions found:", orphanedQuestions.length);
    orphanedQuestions.forEach((q, index) => {
      console.log(`Orphaned Question ${index + 1}:`);
      console.log("  ID:", q._id);
      console.log("  Name:", q.name);
      console.log("  Answer Type:", q.answerType);
      console.log("  Is AI Generated:", q.isAIGenerated);
    });
    
    // 5. Suggest fix if orphaned questions exist
    if (orphanedQuestions.length > 0) {
      console.log("\n🔧 SUGGESTED FIX:");
      console.log("There are questions in the database for this exam that aren't linked to the exam.");
      console.log("You can fix this by running:");
      console.log(`exam.questions = [${orphanedQuestions.map(q => `"${q._id}"`).join(", ")}];`);
      console.log("exam.save();");
    }
    
    console.log("\n✅ Debug complete!");
    
  } catch (error) {
    console.error("❌ Debug error:", error);
  } finally {
    mongoose.connection.close();
  }
}

debugExamQuestions();
