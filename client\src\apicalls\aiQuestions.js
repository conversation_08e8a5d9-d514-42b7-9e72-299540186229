import axiosInstance from "./index";

// Generate questions using AI
export const generateQuestions = async (payload) => {
  try {
    console.log("🔗 Making API call to generate questions...");
    const response = await axiosInstance.post("/api/ai-questions/generate-questions", payload, {
      timeout: 600000, // 10 minutes timeout specifically for AI generation
    });
    console.log("✅ API call successful:", response.data);
    return response.data;
  } catch (error) {
    console.error("❌ API call failed:", error);

    if (error.response) {
      console.error("📊 Error response:", error.response.data);
      return error.response.data;
    } else if (error.request) {
      console.error("📡 Network error:", error.request);
      return { success: false, message: "Network error - please check your connection" };
    } else {
      console.error("⚠️ Request setup error:", error.message);
      return { success: false, message: error.message };
    }
  }
};

// Get generation history
export const getGenerationHistory = async (params = {}) => {
  try {
    const response = await axiosInstance.get("/api/ai-questions/generation-history", { params });
    return response.data;
  } catch (error) {
    return error.response.data;
  }
};

// Get specific generation details
export const getGenerationDetails = async (generationId) => {
  try {
    const response = await axiosInstance.get(`/api/ai-questions/generation/${generationId}`);
    return response.data;
  } catch (error) {
    return error.response.data;
  }
};

// Approve generated questions
export const approveQuestions = async (payload) => {
  try {
    const response = await axiosInstance.post("/api/ai-questions/approve-questions", payload);
    return response.data;
  } catch (error) {
    return error.response.data;
  }
};

// Preview generated questions
export const previewQuestions = async (generationId) => {
  try {
    const response = await axiosInstance.get(`/api/ai-questions/preview/${generationId}`);
    return response.data;
  } catch (error) {
    return error.response.data;
  }
};

// Get available subjects for a level
export const getSubjectsForLevel = async (level) => {
  try {
    const response = await axiosInstance.get(`/api/ai-questions/subjects/${level}`);
    return response.data;
  } catch (error) {
    return error.response.data;
  }
};

// Get Tanzania syllabus topics for level, class, and subject
export const getSyllabusTopics = async (level, className, subject) => {
  try {
    const response = await axiosInstance.get(`/api/ai-questions/syllabus-topics/${level}/${className}/${subject}`);
    return response.data;
  } catch (error) {
    return error.response.data;
  }
};

// Generate exam name
export const generateExamName = async (level, className, subjects) => {
  try {
    const response = await axiosInstance.post("/api/ai-questions/generate-exam-name", {
      level,
      className,
      subjects
    });
    return response.data;
  } catch (error) {
    return error.response.data;
  }
};
