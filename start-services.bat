@echo off
echo Starting BrainWave Application Services...
echo.

echo Starting Server (Backend)...
cd /d "C:\Users\<USER>\Desktop\BrainWave\server"
start "BrainWave Server" cmd /k "npm start"

echo Waiting 5 seconds for server to start...
timeout /t 5 /nobreak >nul

echo Starting Client (Frontend)...
cd /d "C:\Users\<USER>\Desktop\BrainWave\client"
start "BrainWave Client" cmd /k "npm start"

echo.
echo Services are starting...
echo Server will be available at: http://localhost:5000
echo Client will be available at: http://localhost:3000
echo.
echo Press any key to exit this window...
pause >nul
