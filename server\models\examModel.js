const mongoose = require("mongoose");

const examSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    duration: {
      type: Number,
      required: true,
    },
    category: {
      type: String,
      required: true,
    },
    subject: {
      type: String,
      required: false,
      default: "General",
    },
    level: {
      type: String,
      enum: ["primary", "secondary", "advance", "Primary", "Secondary", "Advance"],
      default: "Primary",
      required: false,
    },
    class: {
      type: String,
      required: true,
      default: "default"
    },
    totalMarks: {
      type: Number,
      required: true,
    },
    passingMarks: {
      type: Number,
      required: true,
    },
    // Add passingPercentage for consistency
    passingPercentage: {
      type: Number,
      default: function() {
        return this.passingMarks || 70;
      }
    },
    // Add difficulty field for XP calculations
    difficulty: {
      type: String,
      enum: ["easy", "medium", "hard"],
      default: "medium",
    },
    difficultyLevel: {
      type: String,
      enum: ["easy", "medium", "hard"],
      default: "medium",
    },
    questions: {
      type: [mongoose.Schema.Types.ObjectId],
      ref: "questions",
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Pre-save middleware to ensure consistency
examSchema.pre('save', function(next) {
  // Ensure passingPercentage is set if not provided
  if (!this.passingPercentage && this.passingMarks) {
    this.passingPercentage = this.passingMarks;
  }

  // Ensure subject is set if not provided
  if (!this.subject && this.category) {
    this.subject = this.category;
  }

  // Ensure difficulty is set
  if (!this.difficulty) {
    this.difficulty = this.difficultyLevel || 'medium';
  }

  // Ensure difficultyLevel matches difficulty
  if (!this.difficultyLevel) {
    this.difficultyLevel = this.difficulty || 'medium';
  }

  next();
});

const Exam = mongoose.model("exams", examSchema);
module.exports = Exam;