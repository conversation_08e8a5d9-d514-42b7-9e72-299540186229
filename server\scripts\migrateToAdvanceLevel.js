const mongoose = require("mongoose");
const User = require("../models/userModel");
const Videos = require("../models/studyVideos");
const Notes = require("../models/studyNotes");
const Books = require("../models/studyBooks");
const PastPapers = require("../models/studyPastPapers");
const Exam = require("../models/examModel");
const ForumQuestion = require("../models/forumQuestionModel");

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL || "mongodb://localhost:27017/your-database-name");
    console.log("✅ Connected to MongoDB");
  } catch (error) {
    console.error("❌ MongoDB connection error:", error);
    process.exit(1);
  }
};

// Function to migrate data from secondary to advance level
const migrateToAdvanceLevel = async () => {
  try {
    console.log("🚀 Starting migration to Advance level...");
    console.log("📋 This will move Form-5 and Form-6 data from secondary to advance level");
    
    // Define the classes that should be moved to advance level
    const advanceClasses = ["Form-5", "Form-6", "5", "6"];
    
    let totalUpdated = 0;

    // 1. Migrate Users
    console.log("\n👥 Migrating Users...");
    const usersToUpdate = await User.find({
      level: "secondary",
      class: { $in: advanceClasses }
    });
    
    console.log(`   Found ${usersToUpdate.length} users to migrate`);
    
    for (const user of usersToUpdate) {
      await User.findByIdAndUpdate(user._id, { level: "advance" });
      console.log(`   ✓ Updated user: ${user.name} (Class: ${user.class})`);
      totalUpdated++;
    }

    // 2. Migrate Videos
    console.log("\n🎥 Migrating Videos...");
    const videosToUpdate = await Videos.find({
      level: "secondary",
      className: { $in: advanceClasses }
    });
    
    console.log(`   Found ${videosToUpdate.length} videos to migrate`);
    
    for (const video of videosToUpdate) {
      await Videos.findByIdAndUpdate(video._id, { level: "advance" });
      console.log(`   ✓ Updated video: ${video.title} (Class: ${video.className})`);
      totalUpdated++;
    }

    // 3. Migrate Notes
    console.log("\n📝 Migrating Notes...");
    const notesToUpdate = await Notes.find({
      level: "secondary",
      className: { $in: advanceClasses }
    });
    
    console.log(`   Found ${notesToUpdate.length} notes to migrate`);
    
    for (const note of notesToUpdate) {
      await Notes.findByIdAndUpdate(note._id, { level: "advance" });
      console.log(`   ✓ Updated note: ${note.title} (Class: ${note.className})`);
      totalUpdated++;
    }

    // 4. Migrate Books
    console.log("\n📚 Migrating Books...");
    const booksToUpdate = await Books.find({
      level: "secondary",
      className: { $in: advanceClasses }
    });
    
    console.log(`   Found ${booksToUpdate.length} books to migrate`);
    
    for (const book of booksToUpdate) {
      await Books.findByIdAndUpdate(book._id, { level: "advance" });
      console.log(`   ✓ Updated book: ${book.title} (Class: ${book.className})`);
      totalUpdated++;
    }

    // 5. Migrate Past Papers
    console.log("\n📄 Migrating Past Papers...");
    const papersToUpdate = await PastPapers.find({
      level: "secondary",
      className: { $in: advanceClasses }
    });
    
    console.log(`   Found ${papersToUpdate.length} past papers to migrate`);
    
    for (const paper of papersToUpdate) {
      await PastPapers.findByIdAndUpdate(paper._id, { level: "advance" });
      console.log(`   ✓ Updated past paper: ${paper.title} (Class: ${paper.className})`);
      totalUpdated++;
    }

    // 6. Migrate Exams
    console.log("\n📝 Migrating Exams...");
    const examsToUpdate = await Exam.find({
      level: "secondary",
      class: { $in: advanceClasses }
    });
    
    console.log(`   Found ${examsToUpdate.length} exams to migrate`);
    
    for (const exam of examsToUpdate) {
      await Exam.findByIdAndUpdate(exam._id, { level: "advance" });
      console.log(`   ✓ Updated exam: ${exam.name} (Class: ${exam.class})`);
      totalUpdated++;
    }

    // 7. Migrate Forum Questions (if any users were migrated)
    console.log("\n💬 Migrating Forum Questions...");
    const migratedUserIds = usersToUpdate.map(user => user._id);
    
    if (migratedUserIds.length > 0) {
      const forumQuestionsToUpdate = await ForumQuestion.find({
        level: "secondary",
        user: { $in: migratedUserIds }
      });
      
      console.log(`   Found ${forumQuestionsToUpdate.length} forum questions to migrate`);
      
      for (const question of forumQuestionsToUpdate) {
        await ForumQuestion.findByIdAndUpdate(question._id, { level: "advance" });
        console.log(`   ✓ Updated forum question: ${question.title}`);
        totalUpdated++;
      }
    }

    console.log("\n🎉 Migration completed successfully!");
    console.log(`📊 Total records updated: ${totalUpdated}`);
    
    // Verification
    console.log("\n🔍 Verification - Current level distribution:");
    
    const userStats = await User.aggregate([
      { $group: { _id: "$level", count: { $sum: 1 } } }
    ]);
    
    const videoStats = await Videos.aggregate([
      { $group: { _id: "$level", count: { $sum: 1 } } }
    ]);
    
    console.log("   Users by level:");
    userStats.forEach(stat => {
      console.log(`     ${stat._id}: ${stat.count} users`);
    });
    
    console.log("   Videos by level:");
    videoStats.forEach(stat => {
      console.log(`     ${stat._id}: ${stat.count} videos`);
    });

  } catch (error) {
    console.error("❌ Migration failed:", error);
    throw error;
  }
};

// Main execution function
const runMigration = async () => {
  try {
    await connectDB();
    await migrateToAdvanceLevel();
    console.log("\n✅ Migration script completed successfully!");
  } catch (error) {
    console.error("❌ Migration script failed:", error);
  } finally {
    await mongoose.connection.close();
    console.log("🔌 Database connection closed");
    process.exit(0);
  }
};

// Run the migration if this file is executed directly
if (require.main === module) {
  runMigration();
}

module.exports = { migrateToAdvanceLevel, connectDB };
