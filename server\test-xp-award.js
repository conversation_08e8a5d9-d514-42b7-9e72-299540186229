const mongoose = require('mongoose');
require('dotenv').config();
const enhancedXPService = require('./services/enhancedXPService');
const User = require('./models/userModel');

async function testXPAward() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('Connected to MongoDB');
    
    // Find a non-admin user to test with
    const user = await User.findById('6793d2ac3213409e03cd5a83');
    if (!user) {
      console.log('Test user not found');
      process.exit(1);
    }
    
    console.log('Testing XP award for user:', user.name);
    console.log('Current XP before:', user.totalXP);
    
    // Test XP awarding
    const awardResult = await enhancedXPService.awardXP({
      userId: user._id,
      xpAmount: 50,
      transactionType: 'quiz_completion',
      sourceId: '651d4f8d254a5e08817a4546', // Real exam ID
      sourceModel: 'exams',
      breakdown: {
        baseCompletion: 10,
        correctAnswers: 20,
        firstAttemptBonus: 15,
        consistencyBonus: 5
      },
      quizData: {
        examId: '651d4f8d254a5e08817a4546', // Real exam ID
        subject: 'Math',
        difficulty: 'medium',
        questionsTotal: 5,
        questionsCorrect: 4,
        timeSpent: 1200,
        score: 80,
        isFirstAttempt: true
      },
      metadata: {
        testAward: true
      }
    });
    
    console.log('XP award result:', awardResult);
    
    // Check user XP after
    const updatedUser = await User.findById(user._id);
    console.log('Current XP after:', updatedUser.totalXP);
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

testXPAward();
