const jwt = require("jsonwebtoken");

module.exports = (req, res, next) => {
  try {
    // Check if authorization header exists
    if (!req.headers.authorization) {
      console.log("❌ Auth Error: No authorization header");
      return res.status(401).send({
        message: "No authorization header provided",
        success: false,
      });
    }

    // Check if authorization header has the correct format
    const authParts = req.headers.authorization.split(" ");
    if (authParts.length !== 2 || authParts[0] !== "Bearer") {
      console.log("❌ Auth Error: Invalid authorization header format");
      return res.status(401).send({
        message: "Invalid authorization header format",
        success: false,
      });
    }

    const token = authParts[1];

    // Verify the token
    const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decodedToken.userId;

    // Add userId to request body for backward compatibility
    req.body.userId = userId;
    // Also add to req.user for better practice
    req.user = { userId };

    console.log(`✅ Auth Success: User ${userId} authenticated`);
    next();
  } catch (error) {
    console.log("❌ Auth Error:", error.message);

    let message = "You are not authenticated";
    if (error.name === "TokenExpiredError") {
      message = "Your session has expired. Please login again.";
    } else if (error.name === "JsonWebTokenError") {
      message = "Invalid token. Please login again.";
    }

    res.status(401).send({
      message,
      success: false,
    });
  }
};