const axios = require("axios");
const AWS = require("aws-sdk");
const sharp = require("sharp");
const { v4: uuidv4 } = require("uuid");

// Configure AWS S3
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
});

class WebImageService {
  constructor() {
    this.unsplashAccessKey = process.env.UNSPLASH_ACCESS_KEY;
    this.pixabayApiKey = process.env.PIXABAY_API_KEY;
  }

  // Search for educational images using Unsplash API
  async searchUnsplashImages(query, count = 5) {
    try {
      if (!this.unsplashAccessKey) {
        console.warn("Unsplash API key not configured");
        return [];
      }

      const response = await axios.get("https://api.unsplash.com/search/photos", {
        params: {
          query: `${query} education school learning`,
          per_page: count,
          orientation: "landscape",
          content_filter: "high",
        },
        headers: {
          Authorization: `Client-ID ${this.unsplashAccessKey}`,
        },
      });

      return response.data.results.map(image => ({
        id: image.id,
        url: image.urls.regular,
        thumbnail: image.urls.thumb,
        description: image.alt_description || image.description,
        attribution: {
          photographer: image.user.name,
          photographerUrl: image.user.links.html,
          source: "Unsplash",
          sourceUrl: image.links.html,
        },
        license: "Unsplash License",
        tags: image.tags?.map(tag => tag.title) || [],
      }));
    } catch (error) {
      console.error("Unsplash search error:", error);
      return [];
    }
  }

  // Search for educational images using Pixabay API
  async searchPixabayImages(query, count = 5) {
    try {
      if (!this.pixabayApiKey) {
        console.warn("Pixabay API key not configured");
        return [];
      }

      const response = await axios.get("https://pixabay.com/api/", {
        params: {
          key: this.pixabayApiKey,
          q: `${query} education school`,
          image_type: "photo",
          orientation: "horizontal",
          category: "education,science,people",
          safesearch: "true",
          per_page: count,
        },
      });

      return response.data.hits.map(image => ({
        id: image.id.toString(),
        url: image.webformatURL,
        thumbnail: image.previewURL,
        description: image.tags,
        attribution: {
          photographer: image.user,
          source: "Pixabay",
          sourceUrl: image.pageURL,
        },
        license: "Pixabay License",
        tags: image.tags.split(", "),
      }));
    } catch (error) {
      console.error("Pixabay search error:", error);
      return [];
    }
  }

  // Generate educational placeholder images for common subjects
  async generatePlaceholderImage(subject, topic, difficulty = "medium") {
    try {
      const placeholderPrompts = {
        Mathematics: {
          easy: "simple numbers and basic shapes",
          medium: "geometric shapes and equations",
          hard: "complex mathematical formulas and graphs",
        },
        Science: {
          easy: "colorful science equipment and simple experiments",
          medium: "laboratory equipment and scientific diagrams",
          hard: "complex scientific apparatus and molecular structures",
        },
        Geography: {
          easy: "world map and basic geographical features",
          medium: "detailed maps and geographical formations",
          hard: "topographical maps and complex geographical data",
        },
        History: {
          easy: "historical artifacts and simple timelines",
          medium: "historical documents and important figures",
          hard: "complex historical events and detailed timelines",
        },
      };

      const prompt = placeholderPrompts[subject]?.[difficulty] || "educational content";
      
      // For now, return a placeholder URL that can be replaced with actual image generation
      return {
        id: uuidv4(),
        url: `https://via.placeholder.com/800x600/4A90E2/FFFFFF?text=${encodeURIComponent(subject + " - " + topic)}`,
        thumbnail: `https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=${encodeURIComponent(subject)}`,
        description: `Educational image for ${subject} - ${topic}`,
        attribution: {
          source: "Generated Placeholder",
        },
        license: "Educational Use",
        tags: [subject.toLowerCase(), topic.toLowerCase(), "education"],
      };
    } catch (error) {
      console.error("Placeholder generation error:", error);
      return null;
    }
  }

  // Search for appropriate images for a question
  async searchImagesForQuestion(subject, topic, questionType, difficulty = "medium") {
    try {
      const searchQuery = `${subject} ${topic} ${questionType === "picture_based" ? "diagram illustration" : ""}`;
      
      // Try multiple sources
      const [unsplashImages, pixabayImages] = await Promise.all([
        this.searchUnsplashImages(searchQuery, 3),
        this.searchPixabayImages(searchQuery, 3),
      ]);

      let allImages = [...unsplashImages, ...pixabayImages];

      // If no images found, generate a placeholder
      if (allImages.length === 0) {
        const placeholder = await this.generatePlaceholderImage(subject, topic, difficulty);
        if (placeholder) {
          allImages = [placeholder];
        }
      }

      // Filter and rank images based on educational relevance
      return this.rankImagesByRelevance(allImages, subject, topic);
    } catch (error) {
      console.error("Image search error:", error);
      return [];
    }
  }

  // Rank images by educational relevance
  rankImagesByRelevance(images, subject, topic) {
    return images
      .map(image => {
        let score = 0;
        const searchTerms = [subject.toLowerCase(), topic.toLowerCase(), "education", "school", "learning"];
        
        // Score based on description and tags
        const imageText = `${image.description || ""} ${image.tags?.join(" ") || ""}`.toLowerCase();
        
        searchTerms.forEach(term => {
          if (imageText.includes(term)) {
            score += 1;
          }
        });

        // Prefer images with educational context
        if (imageText.includes("education") || imageText.includes("school") || imageText.includes("learning")) {
          score += 2;
        }

        return { ...image, relevanceScore: score };
      })
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 5); // Return top 5 most relevant images
  }

  // Download and upload image to S3
  async downloadAndUploadImage(imageUrl, questionId) {
    try {
      // Download image
      const response = await axios.get(imageUrl, {
        responseType: "arraybuffer",
        timeout: 30000,
      });

      const imageBuffer = Buffer.from(response.data);

      // Process image with Sharp (resize, optimize)
      const processedImage = await sharp(imageBuffer)
        .resize(800, 600, { 
          fit: "inside", 
          withoutEnlargement: true 
        })
        .jpeg({ 
          quality: 85,
          progressive: true 
        })
        .toBuffer();

      // Generate unique filename
      const filename = `Questions/AI-Generated/${questionId}-${uuidv4()}.jpg`;

      // Upload to S3
      const uploadParams = {
        Bucket: process.env.AWS_S3_BUCKET_NAME,
        Key: filename,
        Body: processedImage,
        ContentType: "image/jpeg",
        ACL: "public-read",
      };

      const uploadResult = await s3.upload(uploadParams).promise();

      return {
        success: true,
        imageUrl: uploadResult.Location,
        key: uploadResult.Key,
      };
    } catch (error) {
      console.error("Image download/upload error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Validate image for educational content
  async validateImageContent(imageUrl) {
    try {
      // Basic validation - check if image is accessible
      const response = await axios.head(imageUrl, { timeout: 10000 });
      
      const contentType = response.headers["content-type"];
      const contentLength = parseInt(response.headers["content-length"] || "0");

      // Check if it's an image and reasonable size
      const isValidImage = contentType && contentType.startsWith("image/");
      const isReasonableSize = contentLength > 1000 && contentLength < 10 * 1024 * 1024; // 1KB to 10MB

      return {
        isValid: isValidImage && isReasonableSize,
        contentType,
        contentLength,
        accessible: response.status === 200,
      };
    } catch (error) {
      return {
        isValid: false,
        error: error.message,
        accessible: false,
      };
    }
  }

  // Get copyright-safe educational images
  async getEducationalImages(subject, topic, count = 3) {
    try {
      const images = await this.searchImagesForQuestion(subject, topic, "picture_based");
      
      // Validate each image
      const validatedImages = await Promise.all(
        images.slice(0, count).map(async (image) => {
          const validation = await this.validateImageContent(image.url);
          return {
            ...image,
            isValid: validation.isValid,
            validation,
          };
        })
      );

      return validatedImages.filter(image => image.isValid);
    } catch (error) {
      console.error("Educational images error:", error);
      return [];
    }
  }
}

module.exports = WebImageService;
