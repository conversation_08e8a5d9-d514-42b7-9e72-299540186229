import { message } from "antd";
import React, { useEffect, useState, useRef } from "react";
import { motion } from "framer-motion";
import { getUserInfo } from "../apicalls/users";
import { useDispatch, useSelector } from "react-redux";
import { SetUser } from "../redux/usersSlice.js";
import { useNavigate, useLocation } from "react-router-dom";
import { HideLoading, ShowLoading } from "../redux/loaderSlice";
import { checkPaymentStatus } from "../apicalls/payment.js";
import "./ProtectedRoute.css";
import { SetSubscription } from "../redux/subscriptionSlice.js";
import { setPaymentVerificationNeeded } from "../redux/paymentSlice.js";


function ProtectedRoute({ children }) {
  const { user } = useSelector((state) => state.user);
  const [isPaymentPending, setIsPaymentPending] = useState(false);
  const intervalRef = useRef(null);
  const { subscriptionData } = useSelector((state) => state.subscription);
  const { paymentVerificationNeeded } = useSelector((state) => state.payment);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const activeRoute = location.pathname;





  const getUserData = async () => {
    try {
      const response = await getUserInfo();
      if (response.success) {
        dispatch(SetUser(response.data));
      } else {
        message.error(response.message);
        navigate("/login");
      }
    } catch (error) {
      navigate("/login");
      message.error(error.message);
    }
  };

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token) {
      getUserData();
    } else {
      navigate("/login");
    }
  }, []);



  useEffect(() => {
    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {
      navigate('/user/plans');
    }
  }, [isPaymentPending, activeRoute, navigate]);

  const verifyPaymentStatus = async () => {
    try {
      const data = await checkPaymentStatus();
      console.log("Payment Status:", data);
      if (data?.error || data?.paymentStatus !== 'paid') {
        if (subscriptionData !== null) {
          dispatch(SetSubscription(null));
        }
        setIsPaymentPending(true);
      }
      else {
        setIsPaymentPending(false);
        dispatch(SetSubscription(data));
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      }
    } catch (error) {
      console.log("Error checking payment status:", error);
      dispatch(SetSubscription(null));
      setIsPaymentPending(true);
    }
  };

  useEffect(() => {
    if (user?.paymentRequired && !user?.isAdmin) {
      console.log("Effect Runing 2222222...");

      if (paymentVerificationNeeded) {
        console.log('Inside timer in effect 2....');
        intervalRef.current = setInterval(() => {
          console.log('Timer in action...');
          verifyPaymentStatus();
        }, 15000);
        dispatch(setPaymentVerificationNeeded(false));
      }
    }
  }, [paymentVerificationNeeded]);

  useEffect(() => {
    if (user?.paymentRequired && !user?.isAdmin) {
      console.log("Effect Runing...");
      verifyPaymentStatus();
    }
  }, [user, activeRoute]);


  const getButtonClass = (title) => {
    // Exclude "Plans" and "Profile" buttons from the "button-disabled" class
    if (!user.paymentRequired || title === "Plans" || title === "Profile" || title === "Logout") {
      return ""; // No class applied
    }

    return subscriptionData?.paymentStatus !== "paid" && user?.paymentRequired
      ? "button-disabled"
      : "";
  };




  return (
    <div className="layout-modern min-h-screen flex flex-col">
      {/* No sidebar - users will use hub for navigation */}


      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Modern Responsive Header */}
        <motion.header
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className={`nav-modern ${
            location.pathname.includes('/quiz') || location.pathname.includes('/write-exam')
              ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98'
              : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'
          } backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`}
        >
          <div className="px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10">
            <div className="flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20">
              {/* Empty left section for balance */}
              <div className="w-16"></div>

              {/* Center Section - Brainwave Title with Logo */}
              <div className="flex-1 flex justify-center">
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="relative group flex items-center space-x-2 sm:space-x-3"
                >
                  {/* Brain Logo */}
                  <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center shadow-lg text-white text-sm sm:text-base md:text-lg">
                    🧠
                  </div>

                  {/* Brainwave Text */}
                  <h1 className="text-2xl sm:text-3xl md:text-4xl font-black tracking-tight relative z-10">
                    <span className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
                      Brainwave
                    </span>
                  </h1>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-indigo-600/20 to-purple-600/20 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110"></div>
                </motion.div>
              </div>

              {/* Right Section - User Profile */}
              <div className="flex items-center justify-end">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="group"
                >
                  <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-blue-200 group-hover:border-blue-400 transition-all duration-300 transform group-hover:scale-110 shadow-md group-hover:shadow-lg cursor-pointer relative bg-white">
                    {user?.profileImage ? (
                      <img
                        src={user.profileImage}
                        alt="Profile"
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          if (e.target.nextSibling) {
                            e.target.nextSibling.style.display = 'flex';
                          }
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 text-white font-bold text-xs sm:text-sm md:text-base relative overflow-hidden">
                        <span className="relative z-10">{user?.name?.charAt(0)?.toUpperCase() || 'U'}</span>
                        <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                    )}
                    <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-30 blur transition-opacity duration-300"></div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50 pb-20 sm:pb-0">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="h-full"
          >
            {children}
          </motion.div>
        </main>


      </div>
    </div>
  );
}

export default ProtectedRoute;
