# AI Question Generation Feature Documentation

## Overview

The AI Question Generation feature allows administrators to automatically generate high-quality educational questions using artificial intelligence. The system supports multiple question types, follows Tanzania National Curriculum standards, and includes comprehensive validation and quality control mechanisms.

## Features

### Question Types Supported
1. **Multiple Choice Questions** - Traditional 4-option (A, B, C, D) questions
2. **Fill in the Blank** - Questions with blank spaces for students to complete
3. **Picture-based Questions** - Questions that include relevant educational images

### Key Capabilities
- **Tanzania Syllabus Compliance** - All questions align with official curriculum standards
- **Multi-level Support** - Primary, Secondary, and Advance education levels
- **Subject Integration** - Supports all subjects in the curriculum
- **Quality Validation** - Comprehensive validation system ensures question quality
- **Image Integration** - Automatic sourcing and integration of educational images
- **Batch Generation** - Generate multiple questions simultaneously
- **Preview & Approval** - Review and approve questions before adding to exams

## Getting Started

### Prerequisites
- Admin access to the system
- OpenAI API key configured in environment variables
- AWS S3 bucket for image storage (optional)

### Basic Usage

1. **Navigate to AI Questions**
   - Go to Admin Panel → AI Questions
   - Click "Generate New Questions"

2. **Configure Generation Parameters**
   - Select target exam
   - Choose education level and class
   - Select subjects
   - Choose question types and difficulty levels
   - Set question distribution

3. **Generate Questions**
   - Click "Generate Questions"
   - Wait for AI processing (typically 30-60 seconds)

4. **Review and Approve**
   - Preview generated questions
   - Select questions to approve
   - Reject questions with quality issues
   - Add approved questions to exam

## API Documentation

### Generate Questions
```
POST /api/ai-questions/generate-questions
```

**Request Body:**
```json
{
  "examId": "exam_id_here",
  "questionTypes": ["multiple_choice", "fill_blank", "picture_based"],
  "subjects": ["Mathematics", "Science"],
  "level": "primary",
  "class": "5",
  "difficultyLevels": ["easy", "medium"],
  "totalQuestions": 10,
  "questionDistribution": {
    "multiple_choice": 6,
    "fill_blank": 2,
    "picture_based": 2
  },
  "syllabusTopics": ["Numbers", "Geometry"],
  "userId": "admin_user_id"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Questions generated successfully",
  "data": {
    "generationId": "generation_id",
    "questions": [...],
    "generationTime": 45000
  }
}
```

### Preview Questions
```
GET /api/ai-questions/preview/{generationId}
```

### Approve Questions
```
POST /api/ai-questions/approve-questions
```

**Request Body:**
```json
{
  "generationId": "generation_id",
  "approvedQuestionIds": [0, 1, 3, 5],
  "rejectedQuestions": [
    {
      "questionIndex": 2,
      "reason": "Question too complex for target level"
    }
  ]
}
```

## Configuration

### Environment Variables

Add these to your `.env` file:

```env
# OpenAI Configuration (Required)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o

# Image Integration (Optional)
UNSPLASH_ACCESS_KEY=your_unsplash_key
PIXABAY_API_KEY=your_pixabay_key

# AWS S3 for Image Storage (Required for picture-based questions)
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_S3_BUCKET_NAME=your_bucket_name
AWS_REGION=us-east-1
```

## Quality Control

### Validation Rules

The system automatically validates generated questions against:

1. **Structure Validation**
   - Question length (10-500 characters)
   - Required fields presence
   - Answer type consistency

2. **Content Validation**
   - Inappropriate content filtering
   - Educational language patterns
   - Clear question structure

3. **Answer Type Validation**
   - Multiple choice: 4 options (A-D), correct option specified
   - Fill in the blank: Blank indicators present, reasonable answer length
   - Free text: Non-empty correct answer

4. **Syllabus Compliance**
   - Subject alignment
   - Topic relevance
   - Level appropriateness

5. **Educational Quality**
   - Language complexity for target level
   - Tanzanian context integration
   - Difficulty alignment

### Quality Scoring

Questions receive a quality score (0-100) based on:
- **Excellent (90-100)**: Ready for immediate use
- **Good (75-89)**: Minor improvements recommended
- **Fair (60-74)**: Requires review and editing
- **Poor (0-59)**: Significant issues, consider regenerating

## Best Practices

### For Optimal Results

1. **Be Specific with Topics**
   - Include specific syllabus topics when possible
   - Use relevant Tanzanian context keywords

2. **Balance Question Types**
   - Mix different question types for variety
   - Consider student level when choosing types

3. **Review Before Approval**
   - Always preview generated questions
   - Check for cultural appropriateness
   - Verify answer accuracy

4. **Quality Over Quantity**
   - Generate smaller batches for better quality
   - Focus on specific topics rather than broad subjects

### Common Issues and Solutions

**Issue: Questions too complex for level**
- Solution: Specify easier difficulty levels, review language complexity

**Issue: Poor image quality**
- Solution: Use more specific image descriptions, check internet connection

**Issue: Low syllabus compliance**
- Solution: Include specific syllabus topics, verify subject selection

## Troubleshooting

### Common Error Messages

1. **"OpenAI API key not configured"**
   - Check OPENAI_API_KEY in environment variables
   - Verify API key is valid and has sufficient credits

2. **"Failed to generate questions"**
   - Check internet connection
   - Verify OpenAI service status
   - Try reducing question count

3. **"Image integration failed"**
   - Check AWS S3 configuration
   - Verify image service API keys
   - Continue without images if needed

### Performance Optimization

- **Batch Size**: Limit to 20 questions per generation for optimal performance
- **Concurrent Generations**: Avoid multiple simultaneous generations
- **Image Processing**: Allow extra time for picture-based questions

## Support and Maintenance

### Regular Maintenance Tasks

1. **Monitor Generation History**
   - Review quality scores regularly
   - Identify patterns in rejected questions

2. **Update Syllabus Data**
   - Keep Tanzania syllabus data current
   - Add new topics as curriculum evolves

3. **API Key Management**
   - Monitor OpenAI usage and costs
   - Rotate API keys periodically

### Getting Help

For technical support or feature requests:
1. Check this documentation first
2. Review error logs in admin panel
3. Contact system administrator
4. Submit feature requests through proper channels

## Version History

- **v1.0.0** - Initial release with basic question generation
- **v1.1.0** - Added image integration and validation
- **v1.2.0** - Enhanced Tanzania syllabus compliance

---

*This documentation is maintained by the development team and updated with each release.*
