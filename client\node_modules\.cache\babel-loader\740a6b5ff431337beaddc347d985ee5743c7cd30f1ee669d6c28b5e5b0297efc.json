{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport Confetti from 'react-confetti';\nimport useWindowSize from 'react-use/lib/useWindowSize';\nimport { TbCheck, TbX, TbTrophy, TbBrain, TbTarget, TbRefresh, TbEye, TbBulb } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport ContentRenderer from '../../../components/ContentRenderer';\nimport XPResultDisplay from '../../../components/modern/XPResultDisplay';\nimport './responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizResult = () => {\n  _s();\n  var _location$state, _result$correctAnswer2, _result$wrongAnswers;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [showReview, setShowReview] = useState(false);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const {\n    width,\n    height\n  } = useWindowSize();\n  const result = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.result;\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({\n          examId: id\n        });\n        dispatch(HideLoading());\n        if (response.success) {\n          var _response$data;\n          setExamData(response.data);\n          setQuestions(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.questions) || []);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n\n  // Play sound effect based on performance\n  useEffect(() => {\n    if (result) {\n      console.log(`Quiz ${result.verdict === \"Pass\" ? \"passed\" : \"failed\"}!`);\n\n      // Play performance-based sound\n      const playSound = () => {\n        try {\n          const score = result.score || 0;\n\n          // Create enhanced sound effects using Web Audio API\n          const createEnhancedSound = (frequencies, durations, volumes = [0.3], types = ['sine']) => {\n            try {\n              const audioContext = new window.AudioContext();\n              frequencies.forEach((frequency, index) => {\n                const oscillator = audioContext.createOscillator();\n                const gainNode = audioContext.createGain();\n                const delay = index * 0.15; // Stagger notes\n\n                oscillator.connect(gainNode);\n                gainNode.connect(audioContext.destination);\n                oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + delay);\n                oscillator.type = types[index] || types[0] || 'sine';\n                const volume = volumes[index] || volumes[0] || 0.3;\n                const duration = durations[index] || durations[0] || 0.5;\n                gainNode.gain.setValueAtTime(0, audioContext.currentTime + delay);\n                gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + delay + 0.02);\n                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + delay + duration);\n                oscillator.start(audioContext.currentTime + delay);\n                oscillator.stop(audioContext.currentTime + delay + duration);\n              });\n              return true;\n            } catch (error) {\n              console.log('Enhanced Audio failed:', error);\n              return false;\n            }\n          };\n\n          // Create celebration sound with multiple harmonies\n          const createCelebrationSound = () => {\n            const frequencies = [523, 659, 784, 1047]; // C5, E5, G5, C6\n            const durations = [0.3, 0.3, 0.3, 0.6];\n            const volumes = [0.4, 0.4, 0.4, 0.5];\n            const types = ['sine', 'triangle', 'sine', 'triangle'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create excellent sound with rich harmonies\n          const createExcellentSound = () => {\n            const frequencies = [440, 554, 659, 880]; // A4, C#5, E5, A5\n            const durations = [0.4, 0.4, 0.4, 0.7];\n            const volumes = [0.35, 0.35, 0.35, 0.4];\n            const types = ['sine', 'triangle', 'sine', 'sawtooth'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create encouraging pass sound\n          const createPassSound = () => {\n            const frequencies = [349, 440, 523]; // F4, A4, C5\n            const durations = [0.3, 0.3, 0.5];\n            const volumes = [0.3, 0.3, 0.35];\n            const types = ['sine', 'triangle', 'sine'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create gentle fail sound\n          const createFailSound = () => {\n            const frequencies = [220, 196]; // A3, G3\n            const durations = [0.4, 0.6];\n            const volumes = [0.25, 0.2];\n            const types = ['sine', 'triangle'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Play different enhanced sounds based on performance\n          if (score === 100) {\n            // Perfect score - triumphant celebration\n            createCelebrationSound();\n            console.log('🏆 PERFECT SCORE! 🎉');\n          } else if (score >= 80) {\n            // Excellent - rich harmonies\n            createExcellentSound();\n            console.log('🎉 EXCELLENT! ⭐');\n          } else if (result.verdict === \"Pass\") {\n            // Pass - encouraging melody\n            createPassSound();\n            console.log('✅ Well Done! 🚀');\n          } else {\n            // Fail - gentle, encouraging tone\n            createFailSound();\n            console.log('💪 Keep Trying! 🌱');\n          }\n        } catch (error) {\n          console.log('Audio not supported:', error);\n          // Visual feedback as fallback\n          if (result.verdict === \"Pass\") {\n            console.log('🎉 Quiz Passed!');\n          } else {\n            console.log('💪 Keep trying!');\n          }\n        }\n      };\n\n      // Delay sound to sync with animation\n      setTimeout(playSound, 500);\n    }\n  }, [result]);\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({\n        question,\n        expectedAnswer,\n        userAnswer,\n        imageUrl\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [question]: response.explanation\n        }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n\n  // Handle missing result data\n  if (!result) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-700 mb-2\",\n          children: \"No Result Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 mb-4\",\n          children: \"Unable to load quiz results.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/user/quiz'),\n          className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Calculate performance level for animations and sounds\n  const getPerformanceLevel = () => {\n    const score = result.score || 0;\n    if (score === 100) return 'perfect';\n    if (score >= 80) return 'excellent';\n    if (score >= 60) return 'good';\n    if (result.verdict === \"Pass\") return 'pass';\n    return 'fail';\n  };\n  const performanceLevel = getPerformanceLevel();\n\n  // Performance-based styling and content\n  const getPerformanceConfig = () => {\n    switch (performanceLevel) {\n      case 'perfect':\n        return {\n          bgGradient: 'from-yellow-400 via-orange-500 to-red-500',\n          iconBg: 'from-yellow-400 to-orange-500',\n          icon: TbTrophy,\n          title: '🏆 PERFECT SCORE!',\n          subtitle: 'Outstanding! You\\'re a quiz master! 🌟',\n          confetti: true,\n          soundFile: '/sounds/perfect.mp3'\n        };\n      case 'excellent':\n        return {\n          bgGradient: 'from-green-400 via-emerald-500 to-teal-600',\n          iconBg: 'from-green-400 to-emerald-500',\n          icon: TbTrophy,\n          title: '🎉 EXCELLENT!',\n          subtitle: 'Amazing work! You\\'re doing great! ✨',\n          confetti: true,\n          soundFile: '/sounds/excellent.mp3'\n        };\n      case 'good':\n      case 'pass':\n        return {\n          bgGradient: 'from-blue-400 via-indigo-500 to-purple-600',\n          iconBg: 'from-blue-400 to-indigo-500',\n          icon: TbCheck,\n          title: '✅ Well Done!',\n          subtitle: 'Good job! Keep up the great work! 🚀',\n          confetti: result.verdict === \"Pass\",\n          soundFile: '/sounds/pass.mp3'\n        };\n      default:\n        return {\n          bgGradient: 'from-red-400 via-pink-500 to-rose-600',\n          iconBg: 'from-red-400 to-pink-500',\n          icon: TbX,\n          title: '💪 Keep Trying!',\n          subtitle: 'Don\\'t give up! Practice makes perfect! 🌱',\n          confetti: false,\n          soundFile: '/sounds/fail.mp3'\n        };\n    }\n  };\n  const config = getPerformanceConfig();\n\n  // Review Section Component\n  if (showReview) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gray-50 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 p-4 sm:p-6 lg:p-8 text-center flex-shrink-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-white/20 rounded-full mb-3 sm:mb-4\",\n          children: /*#__PURE__*/_jsxDEV(TbEye, {\n            className: \"w-6 h-6 sm:w-8 sm:h-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl sm:text-2xl lg:text-3xl font-black text-white mb-2 sm:mb-3\",\n          children: \"Review Your Answers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/90 text-sm sm:text-base lg:text-lg font-medium\",\n          children: \"Detailed breakdown of your quiz performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 sm:space-y-6\",\n            children: questions.map((question, index) => {\n              var _result$correctAnswer, _result$wrongAnswers$;\n              const userAnswer = ((_result$correctAnswer = result.correctAnswers.find(q => q._id === question._id)) === null || _result$correctAnswer === void 0 ? void 0 : _result$correctAnswer.userAnswer) || ((_result$wrongAnswers$ = result.wrongAnswers.find(q => q._id === question._id)) === null || _result$wrongAnswers$ === void 0 ? void 0 : _result$wrongAnswers$.userAnswer) || \"\";\n              const isCorrect = result.correctAnswers.some(q => q._id === question._id);\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `px-4 sm:px-6 py-3 sm:py-4 border-b-2 ${isCorrect ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-300' : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-base sm:text-lg font-bold text-gray-900 flex items-center flex-1 min-w-0\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `inline-flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 rounded-full mr-2 sm:mr-3 flex-shrink-0 ${isCorrect ? 'bg-green-100' : 'bg-red-100'}`,\n                        children: isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-3 h-3 sm:w-5 sm:h-5 text-green-600\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 323,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                          className: \"w-3 h-3 sm:w-5 sm:h-5 text-red-600\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 325,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 319,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"truncate\",\n                        children: [\"Question \", index + 1]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 328,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-semibold ${isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                      children: isCorrect ? 'Correct' : 'Wrong'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-4 sm:p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-bold text-gray-900 mb-3 text-base sm:text-lg\",\n                      children: \"Question:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-4 bg-gray-50 rounded-lg border border-gray-200\",\n                      children: /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                        content: question.name,\n                        className: \"text-gray-800 text-sm sm:text-base leading-relaxed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 345,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 23\n                  }, this), question.image && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-6 text-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"inline-block p-3 bg-gray-50 rounded-lg border border-gray-200\",\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: question.image,\n                        alt: \"Question\",\n                        className: \"max-w-full max-h-48 sm:max-h-64 rounded-lg shadow-md\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 352,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-bold text-gray-900 mb-3 text-sm sm:text-base flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `inline-flex items-center justify-center w-6 h-6 rounded-full mr-2 ${isCorrect ? 'bg-green-100' : 'bg-red-100'}`,\n                          children: isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                            className: \"w-4 h-4 text-green-600\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 370,\n                            columnNumber: 33\n                          }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                            className: \"w-4 h-4 text-red-600\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 372,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 366,\n                          columnNumber: 29\n                        }, this), \"Your Answer:\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 365,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `p-4 rounded-lg text-sm sm:text-base font-medium border-2 ${isCorrect ? 'bg-green-50 text-green-900 border-green-300' : 'bg-red-50 text-red-900 border-red-300'}`,\n                        children: userAnswer || 'No answer provided'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-bold text-gray-900 mb-3 text-sm sm:text-base flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"inline-flex items-center justify-center w-6 h-6 rounded-full mr-2 bg-green-100\",\n                          children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                            className: \"w-4 h-4 text-green-600\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 390,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 389,\n                          columnNumber: 29\n                        }, this), \"Correct Answer:\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 388,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"p-4 bg-green-50 text-green-900 rounded-lg text-sm sm:text-base font-medium border-2 border-green-300\",\n                        children: question.correctAnswer || question.correctOption || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 394,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 23\n                  }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => fetchExplanation(question.name, question.correctAnswer || question.correctOption, userAnswer, question.image),\n                      className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base\",\n                      children: [/*#__PURE__*/_jsxDEV(TbBulb, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 412,\n                        columnNumber: 29\n                      }, this), \"Get AI Explanation\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 27\n                    }, this), explanations[question.name] && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"font-semibold text-blue-900 mb-2 text-sm sm:text-base\",\n                        children: \"AI Explanation:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 418,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                        content: explanations[question.name],\n                        className: \"text-blue-800 text-sm sm:text-base\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 419,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white border-t border-gray-200 p-4 sm:p-6 flex-shrink-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowReview(false),\n            className: \"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm sm:text-base touch-manipulation\",\n            children: \"Back to Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/user/quiz'),\n            className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base touch-manipulation\",\n            children: \"More Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\",\n    children: [config.confetti && /*#__PURE__*/_jsxDEV(Confetti, {\n      width: width,\n      height: height\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 27\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-6 sm:py-8 lg:py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `bg-gradient-to-br ${config.bgGradient} rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 text-center relative overflow-hidden mb-6 sm:mb-8 lg:mb-12 shadow-2xl`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-10 -right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse delay-1000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `inline-flex items-center justify-center w-24 h-24 sm:w-32 sm:h-32 lg:w-40 lg:h-40 bg-gradient-to-br ${config.iconBg} rounded-full mb-6 sm:mb-8 shadow-2xl relative overflow-hidden`,\n              style: {\n                animation: performanceLevel === 'perfect' ? 'megaBounce 1.2s infinite, rainbowGlow 3s infinite, rotate360 4s infinite linear' : performanceLevel === 'excellent' ? 'bigBounce 1s infinite, excellentGlow 2.5s infinite' : performanceLevel === 'pass' || performanceLevel === 'good' ? 'gentlePulse 2s infinite, passGlow 3s infinite' : 'encourageShake 0.8s ease-in-out 3, failGlow 2s infinite',\n                transform: 'scale(1)',\n                transformOrigin: 'center'\n              },\n              children: [performanceLevel === 'perfect' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 rounded-full\",\n                  style: {\n                    background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)',\n                    animation: 'sparkle 1.5s infinite'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-2 right-2 w-3 h-3 bg-white rounded-full\",\n                  style: {\n                    animation: 'twinkle 1s infinite alternate'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-3 left-3 w-2 h-2 bg-yellow-300 rounded-full\",\n                  style: {\n                    animation: 'twinkle 1.2s infinite alternate-reverse'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(config.icon, {\n                className: \"w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 text-white relative z-10\",\n                style: {\n                  filter: performanceLevel === 'perfect' ? 'drop-shadow(0 0 10px rgba(255,255,255,0.8))' : 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl sm:text-4xl lg:text-6xl font-black mb-4 sm:mb-6 relative\",\n              style: {\n                animation: performanceLevel === 'perfect' ? 'titleBounce 1.5s infinite, rainbowText 4s infinite' : performanceLevel === 'excellent' ? 'titlePulse 2s infinite, excellentText 3s infinite' : performanceLevel === 'pass' || performanceLevel === 'good' ? 'titleFadeIn 1s ease-out, passText 3s infinite' : 'titleShake 0.6s ease-in-out 2, failText 2s infinite',\n                color: 'white',\n                textShadow: performanceLevel === 'perfect' ? '3px 3px 6px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.6)' : performanceLevel === 'excellent' ? '2px 2px 4px rgba(0,0,0,0.8), 0 0 15px rgba(255,255,255,0.4)' : '2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(0,0,0,0.5)',\n                transformOrigin: 'center'\n              },\n              children: [config.title, performanceLevel === 'perfect' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-0 left-1/4 w-1 h-1 bg-yellow-300 rounded-full\",\n                  style: {\n                    animation: 'float 3s infinite ease-in-out'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-1/4 right-1/4 w-1 h-1 bg-white rounded-full\",\n                  style: {\n                    animation: 'float 2.5s infinite ease-in-out 0.5s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-1/4 left-1/3 w-1 h-1 bg-yellow-400 rounded-full\",\n                  style: {\n                    animation: 'float 3.5s infinite ease-in-out 1s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-base sm:text-lg lg:text-xl font-medium px-4 py-2 rounded-lg\",\n              style: {\n                color: 'white',\n                textShadow: '1px 1px 3px rgba(0,0,0,0.8)',\n                backgroundColor: 'rgba(0,0,0,0.3)',\n                backdropFilter: 'blur(10px)'\n              },\n              children: config.subtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8 lg:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-blue-600 mb-2\",\n              children: [result.score || 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-green-600 mb-2\",\n              children: ((_result$correctAnswer2 = result.correctAnswers) === null || _result$correctAnswer2 === void 0 ? void 0 : _result$correctAnswer2.length) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Correct\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-red-600 mb-2\",\n              children: ((_result$wrongAnswers = result.wrongAnswers) === null || _result$wrongAnswers === void 0 ? void 0 : _result$wrongAnswers.length) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Wrong\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-purple-600 mb-2\",\n              children: [Math.floor((result.timeSpent || 0) / 60), \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this), result.xpData && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 sm:mb-8 lg:mb-12\",\n          children: /*#__PURE__*/_jsxDEV(XPResultDisplay, {\n            xpData: result.xpData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 sm:mb-8 lg:mb-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 lg:p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-6 sm:mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2\",\n                children: \"\\uD83D\\uDCDA Learning Summary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm sm:text-base\",\n                children: \"Review your answers and learn from explanations to improve your understanding\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6 sm:space-y-8\",\n              children: questions.map((question, index) => {\n                var _result$correctAnswer3, _result$wrongAnswers$2;\n                const userAnswer = ((_result$correctAnswer3 = result.correctAnswers.find(q => q._id === question._id)) === null || _result$correctAnswer3 === void 0 ? void 0 : _result$correctAnswer3.userAnswer) || ((_result$wrongAnswers$2 = result.wrongAnswers.find(q => q._id === question._id)) === null || _result$wrongAnswers$2 === void 0 ? void 0 : _result$wrongAnswers$2.userAnswer) || \"\";\n                const isCorrect = result.correctAnswers.some(q => q._id === question._id);\n                const correctAnswer = question.correctAnswer || question.correctOption || 'N/A';\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `rounded-xl sm:rounded-2xl p-5 sm:p-6 lg:p-8 border-2 transition-all duration-300 ${isCorrect ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 border-green-400 shadow-green-200' : 'bg-gradient-to-br from-red-50 via-pink-50 to-red-100 border-red-400 shadow-red-200'} shadow-lg hover:shadow-xl hover:scale-[1.02]`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-4 sm:mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3 sm:gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 rounded-full font-bold text-white text-base sm:text-lg shadow-lg ${isCorrect ? 'bg-gradient-to-br from-green-500 to-emerald-600' : 'bg-gradient-to-br from-red-500 to-pink-600'}`,\n                        children: index + 1\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 626,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"text-lg sm:text-xl font-bold text-gray-900\",\n                          children: [\"Question \", index + 1]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 632,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs sm:text-sm text-gray-600 font-medium\",\n                          children: isCorrect ? 'Well done! You got this right ✨' : 'Learning opportunity 💡'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 635,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 631,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 625,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `flex items-center gap-2 px-4 sm:px-5 py-2 sm:py-3 rounded-full font-bold text-sm sm:text-base shadow-md ${isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                      children: isCorrect ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 648,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Correct\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 649,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(TbX, {\n                          className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 653,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Incorrect\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 654,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 624,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-5 sm:mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-blue-600\",\n                        children: \"\\u2753\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 663,\n                        columnNumber: 27\n                      }, this), \"Question:\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 662,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `p-4 sm:p-5 rounded-lg border-2 shadow-sm ${isCorrect ? 'bg-gray-50 border-green-300' : 'bg-gray-50 border-red-300'}`,\n                      children: /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                        content: question.name,\n                        className: \"text-gray-900 text-sm sm:text-base leading-relaxed font-semibold\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 671,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 661,\n                    columnNumber: 23\n                  }, this), question.image && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-5 sm:mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-purple-600\",\n                        children: \"\\uD83D\\uDDBC\\uFE0F\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 679,\n                        columnNumber: 29\n                      }, this), \"Reference Image:\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 678,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"inline-block p-3 sm:p-4 bg-white rounded-lg border-2 border-gray-200 shadow-sm\",\n                        children: /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: question.image,\n                          alt: \"Question Reference\",\n                          className: \"max-w-full max-h-40 sm:max-h-56 rounded-lg\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 684,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 683,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 677,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4 sm:space-y-5\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-blue-600\",\n                          children: \"\\uD83D\\uDC64\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 699,\n                          columnNumber: 29\n                        }, this), \"Your Answer:\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 698,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `flex items-start gap-3 sm:gap-4 p-4 sm:p-5 rounded-lg font-medium text-sm sm:text-base border-2 shadow-sm ${isCorrect ? 'bg-green-100 text-green-900 border-green-400' : 'bg-red-100 text-red-900 border-red-400'}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 mt-1\",\n                          children: isCorrect ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 bg-green-500 rounded-full\",\n                            children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                              className: \"w-4 h-4 sm:w-5 sm:h-5 text-white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 710,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 709,\n                            columnNumber: 33\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 bg-red-500 rounded-full\",\n                            children: /*#__PURE__*/_jsxDEV(TbX, {\n                              className: \"w-4 h-4 sm:w-5 sm:h-5 text-white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 714,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 713,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 707,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"font-semibold\",\n                            children: userAnswer || 'No answer provided'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 719,\n                            columnNumber: 31\n                          }, this), isCorrect && /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-green-700 text-xs sm:text-sm mt-1 font-medium\",\n                            children: \"\\uD83C\\uDF89 Excellent! This is the correct answer.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 721,\n                            columnNumber: 33\n                          }, this), !isCorrect && userAnswer && /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-red-700 text-xs sm:text-sm mt-1 font-medium\",\n                            children: \"\\u274C This answer is incorrect. Let's learn why below.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 726,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 718,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 702,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 697,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-green-600\",\n                          children: \"\\u2705\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 737,\n                          columnNumber: 29\n                        }, this), \"Correct Answer:\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 736,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-start gap-3 sm:gap-4 p-4 sm:p-5 bg-green-100 text-green-900 rounded-lg font-medium text-sm sm:text-base border-2 border-green-400 shadow-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 mt-1\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 bg-green-500 rounded-full\",\n                            children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                              className: \"w-4 h-4 sm:w-5 sm:h-5 text-white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 743,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 742,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 741,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"font-semibold\",\n                            children: correctAnswer\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 747,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-green-700 text-xs sm:text-sm mt-1 font-medium\",\n                            children: \"\\uD83D\\uDCA1 Remember this answer for future reference!\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 748,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 746,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 740,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 735,\n                      columnNumber: 25\n                    }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-4 sm:mt-5\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => fetchExplanation(question.name, correctAnswer, userAnswer, question.image),\n                        className: \"w-full flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 font-semibold text-sm sm:text-base shadow-lg hover:shadow-xl\",\n                        children: [/*#__PURE__*/_jsxDEV(TbBulb, {\n                          className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 767,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Get AI Explanation - Why is this wrong?\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 768,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 758,\n                        columnNumber: 29\n                      }, this), explanations[question.name] && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-4 p-4 sm:p-5 bg-blue-50 border-2 border-blue-200 rounded-lg shadow-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-start gap-3 mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-center w-8 h-8 bg-blue-500 rounded-full flex-shrink-0\",\n                            children: /*#__PURE__*/_jsxDEV(TbBulb, {\n                              className: \"w-5 h-5 text-white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 775,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 774,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                            className: \"font-bold text-blue-900 text-base sm:text-lg\",\n                            children: \"AI Explanation:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 777,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 773,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                          content: explanations[question.name],\n                          className: \"text-blue-800 text-sm sm:text-base leading-relaxed\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 779,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 772,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 757,\n                      columnNumber: 27\n                    }, this), isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-4 p-4 bg-green-50 border-2 border-green-200 rounded-lg\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-center w-8 h-8 bg-green-500 rounded-full\",\n                          children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                            className: \"w-5 h-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 790,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 789,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                            className: \"font-bold text-green-900 text-sm sm:text-base\",\n                            children: \"Great job!\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 793,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-green-700 text-xs sm:text-sm\",\n                            children: \"You demonstrated good understanding of this concept. Keep it up! \\uD83C\\uDF1F\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 794,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 792,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 788,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 787,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center mb-6 sm:mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\",\n            onClick: () => setShowReview(true),\n            children: [/*#__PURE__*/_jsxDEV(TbEye, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 15\n            }, this), \"Review Answers\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 811,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\",\n            onClick: () => navigate(`/quiz/${id}/start`),\n            children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:rotate-180 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 15\n            }, this), \"Retake Quiz\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\",\n            onClick: () => navigate('/user/quiz'),\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"More Quizzes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 832,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sm:hidden\",\n              children: \"More\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 810,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 454,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizResult, \"D4V/cGZGP63efKMIrBtSCOFOvoE=\", false, function () {\n  return [useParams, useNavigate, useLocation, useDispatch, useWindowSize];\n});\n_c = QuizResult;\nexport default QuizResult;\nvar _c;\n$RefreshReg$(_c, \"QuizResult\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useLocation", "useDispatch", "message", "Confetti", "useWindowSize", "TbCheck", "TbX", "TbTrophy", "TbBrain", "TbTarget", "TbRefresh", "TbEye", "TbBulb", "getExamById", "chatWithChatGPTToExplainAns", "HideLoading", "ShowLoading", "Content<PERSON><PERSON><PERSON>", "XPResultDisplay", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizResult", "_s", "_location$state", "_result$correctAnswer2", "_result$wrongAnswers", "examData", "setExamData", "questions", "setQuestions", "explanations", "setExplanations", "showReview", "setShowReview", "id", "navigate", "location", "dispatch", "width", "height", "result", "state", "fetchExamData", "response", "examId", "success", "_response$data", "data", "error", "console", "log", "verdict", "playSound", "score", "createEnhancedSound", "frequencies", "durations", "volumes", "types", "audioContext", "window", "AudioContext", "for<PERSON>ach", "frequency", "index", "oscillator", "createOscillator", "gainNode", "createGain", "delay", "connect", "destination", "setValueAtTime", "currentTime", "type", "volume", "duration", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "createCelebrationSound", "createExcellentSound", "createPassSound", "createFailSound", "setTimeout", "document", "body", "classList", "add", "remove", "fetchExplanation", "question", "expectedAnswer", "userAnswer", "imageUrl", "prev", "explanation", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "getPerformanceLevel", "performanceLevel", "getPerformanceConfig", "bgGradient", "iconBg", "icon", "title", "subtitle", "confetti", "soundFile", "config", "map", "_result$correctAnswer", "_result$wrongAnswers$", "correctAnswers", "find", "q", "_id", "wrongAnswers", "isCorrect", "some", "content", "name", "image", "src", "alt", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "style", "animation", "transform", "transform<PERSON><PERSON>in", "background", "filter", "color", "textShadow", "backgroundColor", "<PERSON><PERSON>ilter", "length", "Math", "floor", "timeSpent", "xpData", "_result$correctAnswer3", "_result$wrongAnswers$2", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport Confetti from 'react-confetti';\nimport useWindowSize from 'react-use/lib/useWindowSize';\nimport {\n  TbCheck,\n  TbX,\n  TbTrophy,\n  TbBrain,\n  TbTarget,\n  TbRefresh,\n  TbEye,\n  TbBulb\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport ContentRenderer from '../../../components/ContentRenderer';\nimport XPResultDisplay from '../../../components/modern/XPResultDisplay';\nimport './responsive.css';\n\nconst QuizResult = () => {\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [showReview, setShowReview] = useState(false);\n\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const { width, height } = useWindowSize();\n\n  const result = location.state?.result;\n\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({ examId: id });\n        dispatch(HideLoading());\n\n        if (response.success) {\n          setExamData(response.data);\n          setQuestions(response.data?.questions || []);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n\n  // Play sound effect based on performance\n  useEffect(() => {\n    if (result) {\n      console.log(`Quiz ${result.verdict === \"Pass\" ? \"passed\" : \"failed\"}!`);\n\n      // Play performance-based sound\n      const playSound = () => {\n        try {\n          const score = result.score || 0;\n\n          // Create enhanced sound effects using Web Audio API\n          const createEnhancedSound = (frequencies, durations, volumes = [0.3], types = ['sine']) => {\n            try {\n              const audioContext = new window.AudioContext();\n\n              frequencies.forEach((frequency, index) => {\n                const oscillator = audioContext.createOscillator();\n                const gainNode = audioContext.createGain();\n                const delay = index * 0.15; // Stagger notes\n\n                oscillator.connect(gainNode);\n                gainNode.connect(audioContext.destination);\n\n                oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + delay);\n                oscillator.type = types[index] || types[0] || 'sine';\n\n                const volume = volumes[index] || volumes[0] || 0.3;\n                const duration = durations[index] || durations[0] || 0.5;\n\n                gainNode.gain.setValueAtTime(0, audioContext.currentTime + delay);\n                gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + delay + 0.02);\n                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + delay + duration);\n\n                oscillator.start(audioContext.currentTime + delay);\n                oscillator.stop(audioContext.currentTime + delay + duration);\n              });\n\n              return true;\n            } catch (error) {\n              console.log('Enhanced Audio failed:', error);\n              return false;\n            }\n          };\n\n          // Create celebration sound with multiple harmonies\n          const createCelebrationSound = () => {\n            const frequencies = [523, 659, 784, 1047]; // C5, E5, G5, C6\n            const durations = [0.3, 0.3, 0.3, 0.6];\n            const volumes = [0.4, 0.4, 0.4, 0.5];\n            const types = ['sine', 'triangle', 'sine', 'triangle'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create excellent sound with rich harmonies\n          const createExcellentSound = () => {\n            const frequencies = [440, 554, 659, 880]; // A4, C#5, E5, A5\n            const durations = [0.4, 0.4, 0.4, 0.7];\n            const volumes = [0.35, 0.35, 0.35, 0.4];\n            const types = ['sine', 'triangle', 'sine', 'sawtooth'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create encouraging pass sound\n          const createPassSound = () => {\n            const frequencies = [349, 440, 523]; // F4, A4, C5\n            const durations = [0.3, 0.3, 0.5];\n            const volumes = [0.3, 0.3, 0.35];\n            const types = ['sine', 'triangle', 'sine'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create gentle fail sound\n          const createFailSound = () => {\n            const frequencies = [220, 196]; // A3, G3\n            const durations = [0.4, 0.6];\n            const volumes = [0.25, 0.2];\n            const types = ['sine', 'triangle'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Play different enhanced sounds based on performance\n          if (score === 100) {\n            // Perfect score - triumphant celebration\n            createCelebrationSound();\n            console.log('🏆 PERFECT SCORE! 🎉');\n          } else if (score >= 80) {\n            // Excellent - rich harmonies\n            createExcellentSound();\n            console.log('🎉 EXCELLENT! ⭐');\n          } else if (result.verdict === \"Pass\") {\n            // Pass - encouraging melody\n            createPassSound();\n            console.log('✅ Well Done! 🚀');\n          } else {\n            // Fail - gentle, encouraging tone\n            createFailSound();\n            console.log('💪 Keep Trying! 🌱');\n          }\n\n        } catch (error) {\n          console.log('Audio not supported:', error);\n          // Visual feedback as fallback\n          if (result.verdict === \"Pass\") {\n            console.log('🎉 Quiz Passed!');\n          } else {\n            console.log('💪 Keep trying!');\n          }\n        }\n      };\n\n      // Delay sound to sync with animation\n      setTimeout(playSound, 500);\n    }\n  }, [result]);\n\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\n      dispatch(HideLoading());\n\n      if (response.success) {\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n\n  // Handle missing result data\n  if (!result) {\n    return (\n      <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <TbTarget className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h2 className=\"text-xl font-semibold text-gray-700 mb-2\">No Result Data</h2>\n          <p className=\"text-gray-500 mb-4\">Unable to load quiz results.</p>\n          <button\n            onClick={() => navigate('/user/quiz')}\n            className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Back to Quizzes\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Calculate performance level for animations and sounds\n  const getPerformanceLevel = () => {\n    const score = result.score || 0;\n    if (score === 100) return 'perfect';\n    if (score >= 80) return 'excellent';\n    if (score >= 60) return 'good';\n    if (result.verdict === \"Pass\") return 'pass';\n    return 'fail';\n  };\n\n  const performanceLevel = getPerformanceLevel();\n\n  // Performance-based styling and content\n  const getPerformanceConfig = () => {\n    switch (performanceLevel) {\n      case 'perfect':\n        return {\n          bgGradient: 'from-yellow-400 via-orange-500 to-red-500',\n          iconBg: 'from-yellow-400 to-orange-500',\n          icon: TbTrophy,\n          title: '🏆 PERFECT SCORE!',\n          subtitle: 'Outstanding! You\\'re a quiz master! 🌟',\n          confetti: true,\n          soundFile: '/sounds/perfect.mp3'\n        };\n      case 'excellent':\n        return {\n          bgGradient: 'from-green-400 via-emerald-500 to-teal-600',\n          iconBg: 'from-green-400 to-emerald-500',\n          icon: TbTrophy,\n          title: '🎉 EXCELLENT!',\n          subtitle: 'Amazing work! You\\'re doing great! ✨',\n          confetti: true,\n          soundFile: '/sounds/excellent.mp3'\n        };\n      case 'good':\n      case 'pass':\n        return {\n          bgGradient: 'from-blue-400 via-indigo-500 to-purple-600',\n          iconBg: 'from-blue-400 to-indigo-500',\n          icon: TbCheck,\n          title: '✅ Well Done!',\n          subtitle: 'Good job! Keep up the great work! 🚀',\n          confetti: result.verdict === \"Pass\",\n          soundFile: '/sounds/pass.mp3'\n        };\n      default:\n        return {\n          bgGradient: 'from-red-400 via-pink-500 to-rose-600',\n          iconBg: 'from-red-400 to-pink-500',\n          icon: TbX,\n          title: '💪 Keep Trying!',\n          subtitle: 'Don\\'t give up! Practice makes perfect! 🌱',\n          confetti: false,\n          soundFile: '/sounds/fail.mp3'\n        };\n    }\n  };\n\n  const config = getPerformanceConfig();\n\n  // Review Section Component\n  if (showReview) {\n    return (\n      <div className=\"h-screen bg-gray-50 flex flex-col overflow-hidden\">\n        {/* Review Header */}\n        <div className=\"bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 p-4 sm:p-6 lg:p-8 text-center flex-shrink-0\">\n          <div className=\"inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-white/20 rounded-full mb-3 sm:mb-4\">\n            <TbEye className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />\n          </div>\n          <h2 className=\"text-xl sm:text-2xl lg:text-3xl font-black text-white mb-2 sm:mb-3\">\n            Review Your Answers\n          </h2>\n          <p className=\"text-white/90 text-sm sm:text-base lg:text-lg font-medium\">\n            Detailed breakdown of your quiz performance\n          </p>\n        </div>\n\n        {/* Review Content */}\n        <div className=\"flex-1 overflow-y-auto\">\n          <div className=\"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8\">\n            <div className=\"space-y-4 sm:space-y-6\">\n              {questions.map((question, index) => {\n                const userAnswer = result.correctAnswers.find(q => q._id === question._id)?.userAnswer ||\n                                  result.wrongAnswers.find(q => q._id === question._id)?.userAnswer || \"\";\n                const isCorrect = result.correctAnswers.some(q => q._id === question._id);\n\n                return (\n                  <div key={index} className=\"bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 overflow-hidden\">\n                    {/* Question Header */}\n                    <div className={`px-4 sm:px-6 py-3 sm:py-4 border-b-2 ${\n                      isCorrect\n                        ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-300'\n                        : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300'\n                    }`}>\n                      <div className=\"flex items-center justify-between\">\n                        <h3 className=\"text-base sm:text-lg font-bold text-gray-900 flex items-center flex-1 min-w-0\">\n                          <span className={`inline-flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 rounded-full mr-2 sm:mr-3 flex-shrink-0 ${\n                            isCorrect ? 'bg-green-100' : 'bg-red-100'\n                          }`}>\n                            {isCorrect ? (\n                              <TbCheck className=\"w-3 h-3 sm:w-5 sm:h-5 text-green-600\" />\n                            ) : (\n                              <TbX className=\"w-3 h-3 sm:w-5 sm:h-5 text-red-600\" />\n                            )}\n                          </span>\n                          <span className=\"truncate\">Question {index + 1}</span>\n                        </h3>\n                        <span className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-semibold ${\n                          isCorrect\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                        }`}>\n                          {isCorrect ? 'Correct' : 'Wrong'}\n                        </span>\n                      </div>\n                    </div>\n\n                    {/* Question Content */}\n                    <div className=\"p-4 sm:p-6\">\n                      <div className=\"mb-6\">\n                        <h4 className=\"font-bold text-gray-900 mb-3 text-base sm:text-lg\">Question:</h4>\n                        <div className=\"p-4 bg-gray-50 rounded-lg border border-gray-200\">\n                          <ContentRenderer content={question.name} className=\"text-gray-800 text-sm sm:text-base leading-relaxed\" />\n                        </div>\n                      </div>\n\n                      {question.image && (\n                        <div className=\"mb-6 text-center\">\n                          <div className=\"inline-block p-3 bg-gray-50 rounded-lg border border-gray-200\">\n                            <img\n                              src={question.image}\n                              alt=\"Question\"\n                              className=\"max-w-full max-h-48 sm:max-h-64 rounded-lg shadow-md\"\n                            />\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Answer Comparison - Full Width */}\n                      <div className=\"space-y-4\">\n                        {/* Your Answer */}\n                        <div>\n                          <h5 className=\"font-bold text-gray-900 mb-3 text-sm sm:text-base flex items-center\">\n                            <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full mr-2 ${\n                              isCorrect ? 'bg-green-100' : 'bg-red-100'\n                            }`}>\n                              {isCorrect ? (\n                                <TbCheck className=\"w-4 h-4 text-green-600\" />\n                              ) : (\n                                <TbX className=\"w-4 h-4 text-red-600\" />\n                              )}\n                            </span>\n                            Your Answer:\n                          </h5>\n                          <div className={`p-4 rounded-lg text-sm sm:text-base font-medium border-2 ${\n                            isCorrect\n                              ? 'bg-green-50 text-green-900 border-green-300'\n                              : 'bg-red-50 text-red-900 border-red-300'\n                          }`}>\n                            {userAnswer || 'No answer provided'}\n                          </div>\n                        </div>\n\n                        {/* Correct Answer */}\n                        <div>\n                          <h5 className=\"font-bold text-gray-900 mb-3 text-sm sm:text-base flex items-center\">\n                            <span className=\"inline-flex items-center justify-center w-6 h-6 rounded-full mr-2 bg-green-100\">\n                              <TbCheck className=\"w-4 h-4 text-green-600\" />\n                            </span>\n                            Correct Answer:\n                          </h5>\n                          <div className=\"p-4 bg-green-50 text-green-900 rounded-lg text-sm sm:text-base font-medium border-2 border-green-300\">\n                            {question.correctAnswer || question.correctOption || 'N/A'}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* AI Explanation Button for Wrong Answers */}\n                      {!isCorrect && (\n                        <div className=\"mt-4\">\n                          <button\n                            onClick={() => fetchExplanation(\n                              question.name,\n                              question.correctAnswer || question.correctOption,\n                              userAnswer,\n                              question.image\n                            )}\n                            className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base\"\n                          >\n                            <TbBulb className=\"w-4 h-4\" />\n                            Get AI Explanation\n                          </button>\n\n                          {explanations[question.name] && (\n                            <div className=\"mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n                              <h6 className=\"font-semibold text-blue-900 mb-2 text-sm sm:text-base\">AI Explanation:</h6>\n                              <ContentRenderer content={explanations[question.name]} className=\"text-blue-800 text-sm sm:text-base\" />\n                            </div>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n\n        {/* Review Footer */}\n        <div className=\"bg-white border-t border-gray-200 p-4 sm:p-6 flex-shrink-0\">\n          <div className=\"max-w-4xl mx-auto flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center\">\n            <button\n              onClick={() => setShowReview(false)}\n              className=\"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm sm:text-base touch-manipulation\"\n            >\n              Back to Results\n            </button>\n            <button\n              onClick={() => navigate('/user/quiz')}\n              className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base touch-manipulation\"\n            >\n              More Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\">\n      {config.confetti && <Confetti width={width} height={height} />}\n\n      {/* Main Content - Scrollable */}\n      <div className=\"flex-1 overflow-y-auto\">\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-6 sm:py-8 lg:py-12\">\n\n          {/* Hero Section with Performance Animation */}\n          <div className={`bg-gradient-to-br ${config.bgGradient} rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 text-center relative overflow-hidden mb-6 sm:mb-8 lg:mb-12 shadow-2xl`}>\n            {/* Animated Background Elements */}\n            <div className=\"absolute inset-0 overflow-hidden\">\n              <div className=\"absolute -top-10 -right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse\"></div>\n              <div className=\"absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse delay-1000\"></div>\n            </div>\n\n            <div className=\"relative z-10\">\n              {/* Animated Icon with Enhanced Effects */}\n              <div className={`inline-flex items-center justify-center w-24 h-24 sm:w-32 sm:h-32 lg:w-40 lg:h-40 bg-gradient-to-br ${config.iconBg} rounded-full mb-6 sm:mb-8 shadow-2xl relative overflow-hidden`}\n                   style={{\n                     animation: performanceLevel === 'perfect' ? 'megaBounce 1.2s infinite, rainbowGlow 3s infinite, rotate360 4s infinite linear' :\n                               performanceLevel === 'excellent' ? 'bigBounce 1s infinite, excellentGlow 2.5s infinite' :\n                               performanceLevel === 'pass' || performanceLevel === 'good' ? 'gentlePulse 2s infinite, passGlow 3s infinite' :\n                               'encourageShake 0.8s ease-in-out 3, failGlow 2s infinite',\n                     transform: 'scale(1)',\n                     transformOrigin: 'center'\n                   }}>\n\n                {/* Sparkle Effects for Perfect Score */}\n                {performanceLevel === 'perfect' && (\n                  <>\n                    <div className=\"absolute inset-0 rounded-full\" style={{\n                      background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)',\n                      animation: 'sparkle 1.5s infinite'\n                    }}></div>\n                    <div className=\"absolute top-2 right-2 w-3 h-3 bg-white rounded-full\" style={{\n                      animation: 'twinkle 1s infinite alternate'\n                    }}></div>\n                    <div className=\"absolute bottom-3 left-3 w-2 h-2 bg-yellow-300 rounded-full\" style={{\n                      animation: 'twinkle 1.2s infinite alternate-reverse'\n                    }}></div>\n                  </>\n                )}\n\n                <config.icon className=\"w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 text-white relative z-10\"\n                            style={{\n                              filter: performanceLevel === 'perfect' ? 'drop-shadow(0 0 10px rgba(255,255,255,0.8))' : 'none'\n                            }} />\n              </div>\n\n              {/* Title with Enhanced Animation */}\n              <h1 className=\"text-3xl sm:text-4xl lg:text-6xl font-black mb-4 sm:mb-6 relative\"\n                  style={{\n                    animation: performanceLevel === 'perfect' ? 'titleBounce 1.5s infinite, rainbowText 4s infinite' :\n                              performanceLevel === 'excellent' ? 'titlePulse 2s infinite, excellentText 3s infinite' :\n                              performanceLevel === 'pass' || performanceLevel === 'good' ? 'titleFadeIn 1s ease-out, passText 3s infinite' :\n                              'titleShake 0.6s ease-in-out 2, failText 2s infinite',\n                    color: 'white',\n                    textShadow: performanceLevel === 'perfect' ? '3px 3px 6px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.6)' :\n                               performanceLevel === 'excellent' ? '2px 2px 4px rgba(0,0,0,0.8), 0 0 15px rgba(255,255,255,0.4)' :\n                               '2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(0,0,0,0.5)',\n                    transformOrigin: 'center'\n                  }}>\n                {config.title}\n\n                {/* Floating particles for perfect score */}\n                {performanceLevel === 'perfect' && (\n                  <div className=\"absolute inset-0 pointer-events-none\">\n                    <div className=\"absolute top-0 left-1/4 w-1 h-1 bg-yellow-300 rounded-full\" style={{\n                      animation: 'float 3s infinite ease-in-out'\n                    }}></div>\n                    <div className=\"absolute top-1/4 right-1/4 w-1 h-1 bg-white rounded-full\" style={{\n                      animation: 'float 2.5s infinite ease-in-out 0.5s'\n                    }}></div>\n                    <div className=\"absolute bottom-1/4 left-1/3 w-1 h-1 bg-yellow-400 rounded-full\" style={{\n                      animation: 'float 3.5s infinite ease-in-out 1s'\n                    }}></div>\n                  </div>\n                )}\n              </h1>\n\n              {/* Subtitle */}\n              <p className=\"text-base sm:text-lg lg:text-xl font-medium px-4 py-2 rounded-lg\"\n                 style={{\n                   color: 'white',\n                   textShadow: '1px 1px 3px rgba(0,0,0,0.8)',\n                   backgroundColor: 'rgba(0,0,0,0.3)',\n                   backdropFilter: 'blur(10px)'\n                 }}>\n                {config.subtitle}\n              </p>\n            </div>\n          </div>\n\n          {/* Stats Cards */}\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8 lg:mb-12\">\n            {/* Score Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-blue-600 mb-2\">\n                {result.score || 0}%\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Score\n              </div>\n            </div>\n\n            {/* Correct Answers Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-green-600 mb-2\">\n                {result.correctAnswers?.length || 0}\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Correct\n              </div>\n            </div>\n\n            {/* Wrong Answers Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-red-600 mb-2\">\n                {result.wrongAnswers?.length || 0}\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Wrong\n              </div>\n            </div>\n\n            {/* Time Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-purple-600 mb-2\">\n                {Math.floor((result.timeSpent || 0) / 60)}m\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Time\n              </div>\n            </div>\n          </div>\n\n          {/* XP Display */}\n          {result.xpData && (\n            <div className=\"mb-6 sm:mb-8 lg:mb-12\">\n              <XPResultDisplay xpData={result.xpData} />\n            </div>\n          )}\n\n          {/* Enhanced Questions Summary for Learning */}\n          <div className=\"mb-6 sm:mb-8 lg:mb-12\">\n            <div className=\"bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 lg:p-8\">\n              <div className=\"text-center mb-6 sm:mb-8\">\n                <h3 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2\">\n                  📚 Learning Summary\n                </h3>\n                <p className=\"text-gray-600 text-sm sm:text-base\">\n                  Review your answers and learn from explanations to improve your understanding\n                </p>\n              </div>\n\n              <div className=\"space-y-6 sm:space-y-8\">\n                {questions.map((question, index) => {\n                  const userAnswer = result.correctAnswers.find(q => q._id === question._id)?.userAnswer ||\n                                    result.wrongAnswers.find(q => q._id === question._id)?.userAnswer || \"\";\n                  const isCorrect = result.correctAnswers.some(q => q._id === question._id);\n                  const correctAnswer = question.correctAnswer || question.correctOption || 'N/A';\n\n                  return (\n                    <div key={index} className={`rounded-xl sm:rounded-2xl p-5 sm:p-6 lg:p-8 border-2 transition-all duration-300 ${\n                      isCorrect\n                        ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 border-green-400 shadow-green-200'\n                        : 'bg-gradient-to-br from-red-50 via-pink-50 to-red-100 border-red-400 shadow-red-200'\n                    } shadow-lg hover:shadow-xl hover:scale-[1.02]`}>\n\n                      {/* Enhanced Question Header */}\n                      <div className=\"flex items-center justify-between mb-4 sm:mb-6\">\n                        <div className=\"flex items-center gap-3 sm:gap-4\">\n                          <div className={`flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 rounded-full font-bold text-white text-base sm:text-lg shadow-lg ${\n                            isCorrect ? 'bg-gradient-to-br from-green-500 to-emerald-600' : 'bg-gradient-to-br from-red-500 to-pink-600'\n                          }`}>\n                            {index + 1}\n                          </div>\n                          <div>\n                            <h4 className=\"text-lg sm:text-xl font-bold text-gray-900\">\n                              Question {index + 1}\n                            </h4>\n                            <p className=\"text-xs sm:text-sm text-gray-600 font-medium\">\n                              {isCorrect ? 'Well done! You got this right ✨' : 'Learning opportunity 💡'}\n                            </p>\n                          </div>\n                        </div>\n\n                        <div className={`flex items-center gap-2 px-4 sm:px-5 py-2 sm:py-3 rounded-full font-bold text-sm sm:text-base shadow-md ${\n                          isCorrect\n                            ? 'bg-green-500 text-white'\n                            : 'bg-red-500 text-white'\n                        }`}>\n                          {isCorrect ? (\n                            <>\n                              <TbCheck className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n                              <span>Correct</span>\n                            </>\n                          ) : (\n                            <>\n                              <TbX className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n                              <span>Incorrect</span>\n                            </>\n                          )}\n                        </div>\n                      </div>\n\n                      {/* Full Question Display */}\n                      <div className=\"mb-5 sm:mb-6\">\n                        <h5 className=\"font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2\">\n                          <span className=\"text-blue-600\">❓</span>\n                          Question:\n                        </h5>\n                        <div className={`p-4 sm:p-5 rounded-lg border-2 shadow-sm ${\n                          isCorrect\n                            ? 'bg-gray-50 border-green-300'\n                            : 'bg-gray-50 border-red-300'\n                        }`}>\n                          <ContentRenderer content={question.name} className=\"text-gray-900 text-sm sm:text-base leading-relaxed font-semibold\" />\n                        </div>\n                      </div>\n\n                      {/* Question Image */}\n                      {question.image && (\n                        <div className=\"mb-5 sm:mb-6\">\n                          <h5 className=\"font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2\">\n                            <span className=\"text-purple-600\">🖼️</span>\n                            Reference Image:\n                          </h5>\n                          <div className=\"text-center\">\n                            <div className=\"inline-block p-3 sm:p-4 bg-white rounded-lg border-2 border-gray-200 shadow-sm\">\n                              <img\n                                src={question.image}\n                                alt=\"Question Reference\"\n                                className=\"max-w-full max-h-40 sm:max-h-56 rounded-lg\"\n                              />\n                            </div>\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Answer Analysis */}\n                      <div className=\"space-y-4 sm:space-y-5\">\n                        {/* Your Answer */}\n                        <div>\n                          <h5 className=\"font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2\">\n                            <span className=\"text-blue-600\">👤</span>\n                            Your Answer:\n                          </h5>\n                          <div className={`flex items-start gap-3 sm:gap-4 p-4 sm:p-5 rounded-lg font-medium text-sm sm:text-base border-2 shadow-sm ${\n                            isCorrect\n                              ? 'bg-green-100 text-green-900 border-green-400'\n                              : 'bg-red-100 text-red-900 border-red-400'\n                          }`}>\n                            <div className=\"flex-shrink-0 mt-1\">\n                              {isCorrect ? (\n                                <div className=\"flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 bg-green-500 rounded-full\">\n                                  <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5 text-white\" />\n                                </div>\n                              ) : (\n                                <div className=\"flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 bg-red-500 rounded-full\">\n                                  <TbX className=\"w-4 h-4 sm:w-5 sm:h-5 text-white\" />\n                                </div>\n                              )}\n                            </div>\n                            <div className=\"flex-1\">\n                              <p className=\"font-semibold\">{userAnswer || 'No answer provided'}</p>\n                              {isCorrect && (\n                                <p className=\"text-green-700 text-xs sm:text-sm mt-1 font-medium\">\n                                  🎉 Excellent! This is the correct answer.\n                                </p>\n                              )}\n                              {!isCorrect && userAnswer && (\n                                <p className=\"text-red-700 text-xs sm:text-sm mt-1 font-medium\">\n                                  ❌ This answer is incorrect. Let's learn why below.\n                                </p>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Correct Answer */}\n                        <div>\n                          <h5 className=\"font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2\">\n                            <span className=\"text-green-600\">✅</span>\n                            Correct Answer:\n                          </h5>\n                          <div className=\"flex items-start gap-3 sm:gap-4 p-4 sm:p-5 bg-green-100 text-green-900 rounded-lg font-medium text-sm sm:text-base border-2 border-green-400 shadow-sm\">\n                            <div className=\"flex-shrink-0 mt-1\">\n                              <div className=\"flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 bg-green-500 rounded-full\">\n                                <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5 text-white\" />\n                              </div>\n                            </div>\n                            <div className=\"flex-1\">\n                              <p className=\"font-semibold\">{correctAnswer}</p>\n                              <p className=\"text-green-700 text-xs sm:text-sm mt-1 font-medium\">\n                                💡 Remember this answer for future reference!\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* AI Explanation for Wrong Answers */}\n                        {!isCorrect && (\n                          <div className=\"mt-4 sm:mt-5\">\n                            <button\n                              onClick={() => fetchExplanation(\n                                question.name,\n                                correctAnswer,\n                                userAnswer,\n                                question.image\n                              )}\n                              className=\"w-full flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 font-semibold text-sm sm:text-base shadow-lg hover:shadow-xl\"\n                            >\n                              <TbBulb className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n                              <span>Get AI Explanation - Why is this wrong?</span>\n                            </button>\n\n                            {explanations[question.name] && (\n                              <div className=\"mt-4 p-4 sm:p-5 bg-blue-50 border-2 border-blue-200 rounded-lg shadow-sm\">\n                                <div className=\"flex items-start gap-3 mb-3\">\n                                  <div className=\"flex items-center justify-center w-8 h-8 bg-blue-500 rounded-full flex-shrink-0\">\n                                    <TbBulb className=\"w-5 h-5 text-white\" />\n                                  </div>\n                                  <h6 className=\"font-bold text-blue-900 text-base sm:text-lg\">AI Explanation:</h6>\n                                </div>\n                                <ContentRenderer content={explanations[question.name]} className=\"text-blue-800 text-sm sm:text-base leading-relaxed\" />\n                              </div>\n                            )}\n                          </div>\n                        )}\n\n                        {/* Encouragement for Correct Answers */}\n                        {isCorrect && (\n                          <div className=\"mt-4 p-4 bg-green-50 border-2 border-green-200 rounded-lg\">\n                            <div className=\"flex items-center gap-3\">\n                              <div className=\"flex items-center justify-center w-8 h-8 bg-green-500 rounded-full\">\n                                <TbCheck className=\"w-5 h-5 text-white\" />\n                              </div>\n                              <div>\n                                <h6 className=\"font-bold text-green-900 text-sm sm:text-base\">Great job!</h6>\n                                <p className=\"text-green-700 text-xs sm:text-sm\">\n                                  You demonstrated good understanding of this concept. Keep it up! 🌟\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center mb-6 sm:mb-8\">\n            <button\n              className=\"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\"\n              onClick={() => setShowReview(true)}\n            >\n              <TbEye className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300\" />\n              Review Answers\n            </button>\n\n            <button\n              className=\"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\"\n              onClick={() => navigate(`/quiz/${id}/start`)}\n            >\n              <TbRefresh className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:rotate-180 transition-transform duration-300\" />\n              Retake Quiz\n            </button>\n\n            <button\n              className=\"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\"\n              onClick={() => navigate('/user/quiz')}\n            >\n              <TbBrain className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300\" />\n              <span className=\"hidden sm:inline\">More Quizzes</span>\n              <span className=\"sm:hidden\">More</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n\n    </div>\n  );\n};\n\nexport default QuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACtE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SACEC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,2BAA2B,QAAQ,wBAAwB;AACpE,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,sBAAA,EAAAC,oBAAA;EACvB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAM;IAAEwC;EAAG,CAAC,GAAGtC,SAAS,CAAC,CAAC;EAC1B,MAAMuC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuC,KAAK;IAAEC;EAAO,CAAC,GAAGrC,aAAa,CAAC,CAAC;EAEzC,MAAMsC,MAAM,IAAAjB,eAAA,GAAGa,QAAQ,CAACK,KAAK,cAAAlB,eAAA,uBAAdA,eAAA,CAAgBiB,MAAM;EAErC7C,SAAS,CAAC,MAAM;IACd,MAAM+C,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFL,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;QACvB,MAAM6B,QAAQ,GAAG,MAAMhC,WAAW,CAAC;UAAEiC,MAAM,EAAEV;QAAG,CAAC,CAAC;QAClDG,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAI8B,QAAQ,CAACE,OAAO,EAAE;UAAA,IAAAC,cAAA;UACpBnB,WAAW,CAACgB,QAAQ,CAACI,IAAI,CAAC;UAC1BlB,YAAY,CAAC,EAAAiB,cAAA,GAAAH,QAAQ,CAACI,IAAI,cAAAD,cAAA,uBAAbA,cAAA,CAAelB,SAAS,KAAI,EAAE,CAAC;QAC9C,CAAC,MAAM;UACL5B,OAAO,CAACgD,KAAK,CAACL,QAAQ,CAAC3C,OAAO,CAAC;UAC/BmC,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,CAAC,OAAOa,KAAK,EAAE;QACdX,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;QACvBb,OAAO,CAACgD,KAAK,CAACA,KAAK,CAAChD,OAAO,CAAC;QAC5BmC,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC;IAED,IAAID,EAAE,EAAE;MACNQ,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACR,EAAE,EAAEG,QAAQ,EAAEF,QAAQ,CAAC,CAAC;;EAE5B;EACAxC,SAAS,CAAC,MAAM;IACd,IAAI6C,MAAM,EAAE;MACVS,OAAO,CAACC,GAAG,CAAE,QAAOV,MAAM,CAACW,OAAO,KAAK,MAAM,GAAG,QAAQ,GAAG,QAAS,GAAE,CAAC;;MAEvE;MACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;QACtB,IAAI;UACF,MAAMC,KAAK,GAAGb,MAAM,CAACa,KAAK,IAAI,CAAC;;UAE/B;UACA,MAAMC,mBAAmB,GAAGA,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK;YACzF,IAAI;cACF,MAAMC,YAAY,GAAG,IAAIC,MAAM,CAACC,YAAY,CAAC,CAAC;cAE9CN,WAAW,CAACO,OAAO,CAAC,CAACC,SAAS,EAAEC,KAAK,KAAK;gBACxC,MAAMC,UAAU,GAAGN,YAAY,CAACO,gBAAgB,CAAC,CAAC;gBAClD,MAAMC,QAAQ,GAAGR,YAAY,CAACS,UAAU,CAAC,CAAC;gBAC1C,MAAMC,KAAK,GAAGL,KAAK,GAAG,IAAI,CAAC,CAAC;;gBAE5BC,UAAU,CAACK,OAAO,CAACH,QAAQ,CAAC;gBAC5BA,QAAQ,CAACG,OAAO,CAACX,YAAY,CAACY,WAAW,CAAC;gBAE1CN,UAAU,CAACF,SAAS,CAACS,cAAc,CAACT,SAAS,EAAEJ,YAAY,CAACc,WAAW,GAAGJ,KAAK,CAAC;gBAChFJ,UAAU,CAACS,IAAI,GAAGhB,KAAK,CAACM,KAAK,CAAC,IAAIN,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM;gBAEpD,MAAMiB,MAAM,GAAGlB,OAAO,CAACO,KAAK,CAAC,IAAIP,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG;gBAClD,MAAMmB,QAAQ,GAAGpB,SAAS,CAACQ,KAAK,CAAC,IAAIR,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG;gBAExDW,QAAQ,CAACU,IAAI,CAACL,cAAc,CAAC,CAAC,EAAEb,YAAY,CAACc,WAAW,GAAGJ,KAAK,CAAC;gBACjEF,QAAQ,CAACU,IAAI,CAACC,uBAAuB,CAACH,MAAM,EAAEhB,YAAY,CAACc,WAAW,GAAGJ,KAAK,GAAG,IAAI,CAAC;gBACtFF,QAAQ,CAACU,IAAI,CAACE,4BAA4B,CAAC,KAAK,EAAEpB,YAAY,CAACc,WAAW,GAAGJ,KAAK,GAAGO,QAAQ,CAAC;gBAE9FX,UAAU,CAACe,KAAK,CAACrB,YAAY,CAACc,WAAW,GAAGJ,KAAK,CAAC;gBAClDJ,UAAU,CAACgB,IAAI,CAACtB,YAAY,CAACc,WAAW,GAAGJ,KAAK,GAAGO,QAAQ,CAAC;cAC9D,CAAC,CAAC;cAEF,OAAO,IAAI;YACb,CAAC,CAAC,OAAO5B,KAAK,EAAE;cACdC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEF,KAAK,CAAC;cAC5C,OAAO,KAAK;YACd;UACF,CAAC;;UAED;UACA,MAAMkC,sBAAsB,GAAGA,CAAA,KAAM;YACnC,MAAM3B,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;YAC3C,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACtC,MAAMC,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACpC,MAAMC,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC;YACtD,OAAOJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;UACpE,CAAC;;UAED;UACA,MAAMyB,oBAAoB,GAAGA,CAAA,KAAM;YACjC,MAAM5B,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAC1C,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACtC,MAAMC,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;YACvC,MAAMC,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC;YACtD,OAAOJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;UACpE,CAAC;;UAED;UACA,MAAM0B,eAAe,GAAGA,CAAA,KAAM;YAC5B,MAAM7B,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YACrC,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACjC,MAAMC,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;YAChC,MAAMC,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;YAC1C,OAAOJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;UACpE,CAAC;;UAED;UACA,MAAM2B,eAAe,GAAGA,CAAA,KAAM;YAC5B,MAAM9B,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAChC,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;YAC5B,MAAMC,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;YAC3B,MAAMC,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC;YAClC,OAAOJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;UACpE,CAAC;;UAED;UACA,IAAIL,KAAK,KAAK,GAAG,EAAE;YACjB;YACA6B,sBAAsB,CAAC,CAAC;YACxBjC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;UACrC,CAAC,MAAM,IAAIG,KAAK,IAAI,EAAE,EAAE;YACtB;YACA8B,oBAAoB,CAAC,CAAC;YACtBlC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAChC,CAAC,MAAM,IAAIV,MAAM,CAACW,OAAO,KAAK,MAAM,EAAE;YACpC;YACAiC,eAAe,CAAC,CAAC;YACjBnC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAChC,CAAC,MAAM;YACL;YACAmC,eAAe,CAAC,CAAC;YACjBpC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACnC;QAEF,CAAC,CAAC,OAAOF,KAAK,EAAE;UACdC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEF,KAAK,CAAC;UAC1C;UACA,IAAIR,MAAM,CAACW,OAAO,KAAK,MAAM,EAAE;YAC7BF,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAChC,CAAC,MAAM;YACLD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAChC;QACF;MACF,CAAC;;MAED;MACAoC,UAAU,CAAClC,SAAS,EAAE,GAAG,CAAC;IAC5B;EACF,CAAC,EAAE,CAACZ,MAAM,CAAC,CAAC;EAEZ7C,SAAS,CAAC,MAAM;IACd4F,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,cAAc,EAAEC,UAAU,EAAEC,QAAQ,KAAK;IACjF,IAAI;MACF3D,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM6B,QAAQ,GAAG,MAAM/B,2BAA2B,CAAC;QAAEiF,QAAQ;QAAEC,cAAc;QAAEC,UAAU;QAAEC;MAAS,CAAC,CAAC;MACtG3D,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAI8B,QAAQ,CAACE,OAAO,EAAE;QACpBd,eAAe,CAAEkE,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAACJ,QAAQ,GAAGlD,QAAQ,CAACuD;QAAY,CAAC,CAAC,CAAC;MAC5E,CAAC,MAAM;QACLlG,OAAO,CAACgD,KAAK,CAACL,QAAQ,CAACK,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdX,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;MACvBb,OAAO,CAACgD,KAAK,CAACA,KAAK,CAAChD,OAAO,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,IAAI,CAACwC,MAAM,EAAE;IACX,oBACEtB,OAAA;MAAKiF,SAAS,EAAC,wFAAwF;MAAAC,QAAA,eACrGlF,OAAA;QAAKiF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlF,OAAA,CAACX,QAAQ;UAAC4F,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DtF,OAAA;UAAIiF,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EtF,OAAA;UAAGiF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClEtF,OAAA;UACEuF,OAAO,EAAEA,CAAA,KAAMtE,QAAQ,CAAC,YAAY,CAAE;UACtCgE,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMrD,KAAK,GAAGb,MAAM,CAACa,KAAK,IAAI,CAAC;IAC/B,IAAIA,KAAK,KAAK,GAAG,EAAE,OAAO,SAAS;IACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,WAAW;IACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,MAAM;IAC9B,IAAIb,MAAM,CAACW,OAAO,KAAK,MAAM,EAAE,OAAO,MAAM;IAC5C,OAAO,MAAM;EACf,CAAC;EAED,MAAMwD,gBAAgB,GAAGD,mBAAmB,CAAC,CAAC;;EAE9C;EACA,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC,QAAQD,gBAAgB;MACtB,KAAK,SAAS;QACZ,OAAO;UACLE,UAAU,EAAE,2CAA2C;UACvDC,MAAM,EAAE,+BAA+B;UACvCC,IAAI,EAAE1G,QAAQ;UACd2G,KAAK,EAAE,mBAAmB;UAC1BC,QAAQ,EAAE,wCAAwC;UAClDC,QAAQ,EAAE,IAAI;UACdC,SAAS,EAAE;QACb,CAAC;MACH,KAAK,WAAW;QACd,OAAO;UACLN,UAAU,EAAE,4CAA4C;UACxDC,MAAM,EAAE,+BAA+B;UACvCC,IAAI,EAAE1G,QAAQ;UACd2G,KAAK,EAAE,eAAe;UACtBC,QAAQ,EAAE,sCAAsC;UAChDC,QAAQ,EAAE,IAAI;UACdC,SAAS,EAAE;QACb,CAAC;MACH,KAAK,MAAM;MACX,KAAK,MAAM;QACT,OAAO;UACLN,UAAU,EAAE,4CAA4C;UACxDC,MAAM,EAAE,6BAA6B;UACrCC,IAAI,EAAE5G,OAAO;UACb6G,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,sCAAsC;UAChDC,QAAQ,EAAE1E,MAAM,CAACW,OAAO,KAAK,MAAM;UACnCgE,SAAS,EAAE;QACb,CAAC;MACH;QACE,OAAO;UACLN,UAAU,EAAE,uCAAuC;UACnDC,MAAM,EAAE,0BAA0B;UAClCC,IAAI,EAAE3G,GAAG;UACT4G,KAAK,EAAE,iBAAiB;UACxBC,QAAQ,EAAE,4CAA4C;UACtDC,QAAQ,EAAE,KAAK;UACfC,SAAS,EAAE;QACb,CAAC;IACL;EACF,CAAC;EAED,MAAMC,MAAM,GAAGR,oBAAoB,CAAC,CAAC;;EAErC;EACA,IAAI5E,UAAU,EAAE;IACd,oBACEd,OAAA;MAAKiF,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAEhElF,OAAA;QAAKiF,SAAS,EAAC,yGAAyG;QAAAC,QAAA,gBACtHlF,OAAA;UAAKiF,SAAS,EAAC,yGAAyG;UAAAC,QAAA,eACtHlF,OAAA,CAACT,KAAK;YAAC0F,SAAS,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACNtF,OAAA;UAAIiF,SAAS,EAAC,oEAAoE;UAAAC,QAAA,EAAC;QAEnF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtF,OAAA;UAAGiF,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNtF,OAAA;QAAKiF,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrClF,OAAA;UAAKiF,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1ElF,OAAA;YAAKiF,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EACpCxE,SAAS,CAACyF,GAAG,CAAC,CAACxB,QAAQ,EAAE7B,KAAK,KAAK;cAAA,IAAAsD,qBAAA,EAAAC,qBAAA;cAClC,MAAMxB,UAAU,GAAG,EAAAuB,qBAAA,GAAA9E,MAAM,CAACgF,cAAc,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAK9B,QAAQ,CAAC8B,GAAG,CAAC,cAAAL,qBAAA,uBAAvDA,qBAAA,CAAyDvB,UAAU,OAAAwB,qBAAA,GACpE/E,MAAM,CAACoF,YAAY,CAACH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAK9B,QAAQ,CAAC8B,GAAG,CAAC,cAAAJ,qBAAA,uBAArDA,qBAAA,CAAuDxB,UAAU,KAAI,EAAE;cACzF,MAAM8B,SAAS,GAAGrF,MAAM,CAACgF,cAAc,CAACM,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAK9B,QAAQ,CAAC8B,GAAG,CAAC;cAEzE,oBACEzG,OAAA;gBAAiBiF,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,gBAE7GlF,OAAA;kBAAKiF,SAAS,EAAG,wCACf0B,SAAS,GACL,+DAA+D,GAC/D,wDACL,EAAE;kBAAAzB,QAAA,eACDlF,OAAA;oBAAKiF,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDlF,OAAA;sBAAIiF,SAAS,EAAC,+EAA+E;sBAAAC,QAAA,gBAC3FlF,OAAA;wBAAMiF,SAAS,EAAG,yGAChB0B,SAAS,GAAG,cAAc,GAAG,YAC9B,EAAE;wBAAAzB,QAAA,EACAyB,SAAS,gBACR3G,OAAA,CAACf,OAAO;0BAACgG,SAAS,EAAC;wBAAsC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAE5DtF,OAAA,CAACd,GAAG;0BAAC+F,SAAS,EAAC;wBAAoC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBACtD;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC,eACPtF,OAAA;wBAAMiF,SAAS,EAAC,UAAU;wBAAAC,QAAA,GAAC,WAAS,EAACpC,KAAK,GAAG,CAAC;sBAAA;wBAAAqC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACLtF,OAAA;sBAAMiF,SAAS,EAAG,mEAChB0B,SAAS,GACL,6BAA6B,GAC7B,yBACL,EAAE;sBAAAzB,QAAA,EACAyB,SAAS,GAAG,SAAS,GAAG;oBAAO;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNtF,OAAA;kBAAKiF,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlF,OAAA;oBAAKiF,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlF,OAAA;sBAAIiF,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChFtF,OAAA;sBAAKiF,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC/DlF,OAAA,CAACH,eAAe;wBAACgH,OAAO,EAAElC,QAAQ,CAACmC,IAAK;wBAAC7B,SAAS,EAAC;sBAAoD;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAELX,QAAQ,CAACoC,KAAK,iBACb/G,OAAA;oBAAKiF,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,eAC/BlF,OAAA;sBAAKiF,SAAS,EAAC,+DAA+D;sBAAAC,QAAA,eAC5ElF,OAAA;wBACEgH,GAAG,EAAErC,QAAQ,CAACoC,KAAM;wBACpBE,GAAG,EAAC,UAAU;wBACdhC,SAAS,EAAC;sBAAsD;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAGDtF,OAAA;oBAAKiF,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBAExBlF,OAAA;sBAAAkF,QAAA,gBACElF,OAAA;wBAAIiF,SAAS,EAAC,qEAAqE;wBAAAC,QAAA,gBACjFlF,OAAA;0BAAMiF,SAAS,EAAG,qEAChB0B,SAAS,GAAG,cAAc,GAAG,YAC9B,EAAE;0BAAAzB,QAAA,EACAyB,SAAS,gBACR3G,OAAA,CAACf,OAAO;4BAACgG,SAAS,EAAC;0BAAwB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAE9CtF,OAAA,CAACd,GAAG;4BAAC+F,SAAS,EAAC;0BAAsB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBACxC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACG,CAAC,gBAET;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLtF,OAAA;wBAAKiF,SAAS,EAAG,4DACf0B,SAAS,GACL,6CAA6C,GAC7C,uCACL,EAAE;wBAAAzB,QAAA,EACAL,UAAU,IAAI;sBAAoB;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGNtF,OAAA;sBAAAkF,QAAA,gBACElF,OAAA;wBAAIiF,SAAS,EAAC,qEAAqE;wBAAAC,QAAA,gBACjFlF,OAAA;0BAAMiF,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,eAC9FlF,OAAA,CAACf,OAAO;4BAACgG,SAAS,EAAC;0BAAwB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1C,CAAC,mBAET;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLtF,OAAA;wBAAKiF,SAAS,EAAC,sGAAsG;wBAAAC,QAAA,EAClHP,QAAQ,CAACuC,aAAa,IAAIvC,QAAQ,CAACwC,aAAa,IAAI;sBAAK;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGL,CAACqB,SAAS,iBACT3G,OAAA;oBAAKiF,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlF,OAAA;sBACEuF,OAAO,EAAEA,CAAA,KAAMb,gBAAgB,CAC7BC,QAAQ,CAACmC,IAAI,EACbnC,QAAQ,CAACuC,aAAa,IAAIvC,QAAQ,CAACwC,aAAa,EAChDtC,UAAU,EACVF,QAAQ,CAACoC,KACX,CAAE;sBACF9B,SAAS,EAAC,8HAA8H;sBAAAC,QAAA,gBAExIlF,OAAA,CAACR,MAAM;wBAACyF,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,sBAEhC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAER1E,YAAY,CAAC+D,QAAQ,CAACmC,IAAI,CAAC,iBAC1B9G,OAAA;sBAAKiF,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,gBACpElF,OAAA;wBAAIiF,SAAS,EAAC,uDAAuD;wBAAAC,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1FtF,OAAA,CAACH,eAAe;wBAACgH,OAAO,EAAEjG,YAAY,CAAC+D,QAAQ,CAACmC,IAAI,CAAE;wBAAC7B,SAAS,EAAC;sBAAoC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAlHExC,KAAK;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmHV,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtF,OAAA;QAAKiF,SAAS,EAAC,4DAA4D;QAAAC,QAAA,eACzElF,OAAA;UAAKiF,SAAS,EAAC,2EAA2E;UAAAC,QAAA,gBACxFlF,OAAA;YACEuF,OAAO,EAAEA,CAAA,KAAMxE,aAAa,CAAC,KAAK,CAAE;YACpCkE,SAAS,EAAC,yHAAyH;YAAAC,QAAA,EACpI;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtF,OAAA;YACEuF,OAAO,EAAEA,CAAA,KAAMtE,QAAQ,CAAC,YAAY,CAAE;YACtCgE,SAAS,EAAC,yHAAyH;YAAAC,QAAA,EACpI;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtF,OAAA;IAAKiF,SAAS,EAAC,qFAAqF;IAAAC,QAAA,GACjGgB,MAAM,CAACF,QAAQ,iBAAIhG,OAAA,CAACjB,QAAQ;MAACqC,KAAK,EAAEA,KAAM;MAACC,MAAM,EAAEA;IAAO;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG9DtF,OAAA;MAAKiF,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrClF,OAAA;QAAKiF,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAG3ElF,OAAA;UAAKiF,SAAS,EAAG,qBAAoBiB,MAAM,CAACP,UAAW,sHAAsH;UAAAT,QAAA,gBAE3KlF,OAAA;YAAKiF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/ClF,OAAA;cAAKiF,SAAS,EAAC;YAAsF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5GtF,OAAA;cAAKiF,SAAS,EAAC;YAAmG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtH,CAAC,eAENtF,OAAA;YAAKiF,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAE5BlF,OAAA;cAAKiF,SAAS,EAAG,uGAAsGiB,MAAM,CAACN,MAAO,gEAAgE;cAChMwB,KAAK,EAAE;gBACLC,SAAS,EAAE5B,gBAAgB,KAAK,SAAS,GAAG,iFAAiF,GACnHA,gBAAgB,KAAK,WAAW,GAAG,oDAAoD,GACvFA,gBAAgB,KAAK,MAAM,IAAIA,gBAAgB,KAAK,MAAM,GAAG,+CAA+C,GAC5G,yDAAyD;gBACnE6B,SAAS,EAAE,UAAU;gBACrBC,eAAe,EAAE;cACnB,CAAE;cAAArC,QAAA,GAGJO,gBAAgB,KAAK,SAAS,iBAC7BzF,OAAA,CAAAE,SAAA;gBAAAgF,QAAA,gBACElF,OAAA;kBAAKiF,SAAS,EAAC,+BAA+B;kBAACmC,KAAK,EAAE;oBACpDI,UAAU,EAAE,oEAAoE;oBAChFH,SAAS,EAAE;kBACb;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACTtF,OAAA;kBAAKiF,SAAS,EAAC,sDAAsD;kBAACmC,KAAK,EAAE;oBAC3EC,SAAS,EAAE;kBACb;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACTtF,OAAA;kBAAKiF,SAAS,EAAC,6DAA6D;kBAACmC,KAAK,EAAE;oBAClFC,SAAS,EAAE;kBACb;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eACT,CACH,eAEDtF,OAAA,CAACkG,MAAM,CAACL,IAAI;gBAACZ,SAAS,EAAC,oEAAoE;gBAC/EmC,KAAK,EAAE;kBACLK,MAAM,EAAEhC,gBAAgB,KAAK,SAAS,GAAG,6CAA6C,GAAG;gBAC3F;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAGNtF,OAAA;cAAIiF,SAAS,EAAC,mEAAmE;cAC7EmC,KAAK,EAAE;gBACLC,SAAS,EAAE5B,gBAAgB,KAAK,SAAS,GAAG,oDAAoD,GACtFA,gBAAgB,KAAK,WAAW,GAAG,mDAAmD,GACtFA,gBAAgB,KAAK,MAAM,IAAIA,gBAAgB,KAAK,MAAM,GAAG,+CAA+C,GAC5G,qDAAqD;gBAC/DiC,KAAK,EAAE,OAAO;gBACdC,UAAU,EAAElC,gBAAgB,KAAK,SAAS,GAAG,6DAA6D,GAC/FA,gBAAgB,KAAK,WAAW,GAAG,6DAA6D,GAChG,uDAAuD;gBAClE8B,eAAe,EAAE;cACnB,CAAE;cAAArC,QAAA,GACHgB,MAAM,CAACJ,KAAK,EAGZL,gBAAgB,KAAK,SAAS,iBAC7BzF,OAAA;gBAAKiF,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,gBACnDlF,OAAA;kBAAKiF,SAAS,EAAC,4DAA4D;kBAACmC,KAAK,EAAE;oBACjFC,SAAS,EAAE;kBACb;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACTtF,OAAA;kBAAKiF,SAAS,EAAC,0DAA0D;kBAACmC,KAAK,EAAE;oBAC/EC,SAAS,EAAE;kBACb;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACTtF,OAAA;kBAAKiF,SAAS,EAAC,iEAAiE;kBAACmC,KAAK,EAAE;oBACtFC,SAAS,EAAE;kBACb;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGLtF,OAAA;cAAGiF,SAAS,EAAC,kEAAkE;cAC5EmC,KAAK,EAAE;gBACLM,KAAK,EAAE,OAAO;gBACdC,UAAU,EAAE,6BAA6B;gBACzCC,eAAe,EAAE,iBAAiB;gBAClCC,cAAc,EAAE;cAClB,CAAE;cAAA3C,QAAA,EACFgB,MAAM,CAACH;YAAQ;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtF,OAAA;UAAKiF,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBAEnFlF,OAAA;YAAKiF,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJlF,OAAA;cAAKiF,SAAS,EAAC,gEAAgE;cAAAC,QAAA,GAC5E5D,MAAM,CAACa,KAAK,IAAI,CAAC,EAAC,GACrB;YAAA;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNtF,OAAA;cAAKiF,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtF,OAAA;YAAKiF,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJlF,OAAA;cAAKiF,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAC7E,EAAA5E,sBAAA,GAAAgB,MAAM,CAACgF,cAAc,cAAAhG,sBAAA,uBAArBA,sBAAA,CAAuBwH,MAAM,KAAI;YAAC;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACNtF,OAAA;cAAKiF,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtF,OAAA;YAAKiF,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJlF,OAAA;cAAKiF,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC3E,EAAA3E,oBAAA,GAAAe,MAAM,CAACoF,YAAY,cAAAnG,oBAAA,uBAAnBA,oBAAA,CAAqBuH,MAAM,KAAI;YAAC;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACNtF,OAAA;cAAKiF,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtF,OAAA;YAAKiF,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJlF,OAAA;cAAKiF,SAAS,EAAC,kEAAkE;cAAAC,QAAA,GAC9E6C,IAAI,CAACC,KAAK,CAAC,CAAC1G,MAAM,CAAC2G,SAAS,IAAI,CAAC,IAAI,EAAE,CAAC,EAAC,GAC5C;YAAA;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNtF,OAAA;cAAKiF,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLhE,MAAM,CAAC4G,MAAM,iBACZlI,OAAA;UAAKiF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpClF,OAAA,CAACF,eAAe;YAACoI,MAAM,EAAE5G,MAAM,CAAC4G;UAAO;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN,eAGDtF,OAAA;UAAKiF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpClF,OAAA;YAAKiF,SAAS,EAAC,gEAAgE;YAAAC,QAAA,gBAC7ElF,OAAA;cAAKiF,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvClF,OAAA;gBAAIiF,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,EAAC;cAE7E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtF,OAAA;gBAAGiF,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAElD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENtF,OAAA;cAAKiF,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpCxE,SAAS,CAACyF,GAAG,CAAC,CAACxB,QAAQ,EAAE7B,KAAK,KAAK;gBAAA,IAAAqF,sBAAA,EAAAC,sBAAA;gBAClC,MAAMvD,UAAU,GAAG,EAAAsD,sBAAA,GAAA7G,MAAM,CAACgF,cAAc,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAK9B,QAAQ,CAAC8B,GAAG,CAAC,cAAA0B,sBAAA,uBAAvDA,sBAAA,CAAyDtD,UAAU,OAAAuD,sBAAA,GACpE9G,MAAM,CAACoF,YAAY,CAACH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAK9B,QAAQ,CAAC8B,GAAG,CAAC,cAAA2B,sBAAA,uBAArDA,sBAAA,CAAuDvD,UAAU,KAAI,EAAE;gBACzF,MAAM8B,SAAS,GAAGrF,MAAM,CAACgF,cAAc,CAACM,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAK9B,QAAQ,CAAC8B,GAAG,CAAC;gBACzE,MAAMS,aAAa,GAAGvC,QAAQ,CAACuC,aAAa,IAAIvC,QAAQ,CAACwC,aAAa,IAAI,KAAK;gBAE/E,oBACEnH,OAAA;kBAAiBiF,SAAS,EAAG,oFAC3B0B,SAAS,GACL,+FAA+F,GAC/F,oFACL,+CAA+C;kBAAAzB,QAAA,gBAG9ClF,OAAA;oBAAKiF,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,gBAC7DlF,OAAA;sBAAKiF,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/ClF,OAAA;wBAAKiF,SAAS,EAAG,+HACf0B,SAAS,GAAG,iDAAiD,GAAG,4CACjE,EAAE;wBAAAzB,QAAA,EACApC,KAAK,GAAG;sBAAC;wBAAAqC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC,eACNtF,OAAA;wBAAAkF,QAAA,gBACElF,OAAA;0BAAIiF,SAAS,EAAC,4CAA4C;0BAAAC,QAAA,GAAC,WAChD,EAACpC,KAAK,GAAG,CAAC;wBAAA;0BAAAqC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,eACLtF,OAAA;0BAAGiF,SAAS,EAAC,8CAA8C;0BAAAC,QAAA,EACxDyB,SAAS,GAAG,iCAAiC,GAAG;wBAAyB;0BAAAxB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENtF,OAAA;sBAAKiF,SAAS,EAAG,2GACf0B,SAAS,GACL,yBAAyB,GACzB,uBACL,EAAE;sBAAAzB,QAAA,EACAyB,SAAS,gBACR3G,OAAA,CAAAE,SAAA;wBAAAgF,QAAA,gBACElF,OAAA,CAACf,OAAO;0BAACgG,SAAS,EAAC;wBAAuB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CtF,OAAA;0BAAAkF,QAAA,EAAM;wBAAO;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,eACpB,CAAC,gBAEHtF,OAAA,CAAAE,SAAA;wBAAAgF,QAAA,gBACElF,OAAA,CAACd,GAAG;0BAAC+F,SAAS,EAAC;wBAAuB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACzCtF,OAAA;0BAAAkF,QAAA,EAAM;wBAAS;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,eACtB;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNtF,OAAA;oBAAKiF,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BlF,OAAA;sBAAIiF,SAAS,EAAC,2EAA2E;sBAAAC,QAAA,gBACvFlF,OAAA;wBAAMiF,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,aAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLtF,OAAA;sBAAKiF,SAAS,EAAG,4CACf0B,SAAS,GACL,6BAA6B,GAC7B,2BACL,EAAE;sBAAAzB,QAAA,eACDlF,OAAA,CAACH,eAAe;wBAACgH,OAAO,EAAElC,QAAQ,CAACmC,IAAK;wBAAC7B,SAAS,EAAC;sBAAkE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGLX,QAAQ,CAACoC,KAAK,iBACb/G,OAAA;oBAAKiF,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BlF,OAAA;sBAAIiF,SAAS,EAAC,2EAA2E;sBAAAC,QAAA,gBACvFlF,OAAA;wBAAMiF,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,EAAC;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,oBAE9C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLtF,OAAA;sBAAKiF,SAAS,EAAC,aAAa;sBAAAC,QAAA,eAC1BlF,OAAA;wBAAKiF,SAAS,EAAC,gFAAgF;wBAAAC,QAAA,eAC7FlF,OAAA;0BACEgH,GAAG,EAAErC,QAAQ,CAACoC,KAAM;0BACpBE,GAAG,EAAC,oBAAoB;0BACxBhC,SAAS,EAAC;wBAA4C;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAGDtF,OAAA;oBAAKiF,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBAErClF,OAAA;sBAAAkF,QAAA,gBACElF,OAAA;wBAAIiF,SAAS,EAAC,2EAA2E;wBAAAC,QAAA,gBACvFlF,OAAA;0BAAMiF,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAC;wBAAE;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,gBAE3C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLtF,OAAA;wBAAKiF,SAAS,EAAG,6GACf0B,SAAS,GACL,8CAA8C,GAC9C,wCACL,EAAE;wBAAAzB,QAAA,gBACDlF,OAAA;0BAAKiF,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,EAChCyB,SAAS,gBACR3G,OAAA;4BAAKiF,SAAS,EAAC,kFAAkF;4BAAAC,QAAA,eAC/FlF,OAAA,CAACf,OAAO;8BAACgG,SAAS,EAAC;4BAAkC;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrD,CAAC,gBAENtF,OAAA;4BAAKiF,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,eAC7FlF,OAAA,CAACd,GAAG;8BAAC+F,SAAS,EAAC;4BAAkC;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjD;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACNtF,OAAA;0BAAKiF,SAAS,EAAC,QAAQ;0BAAAC,QAAA,gBACrBlF,OAAA;4BAAGiF,SAAS,EAAC,eAAe;4BAAAC,QAAA,EAAEL,UAAU,IAAI;0BAAoB;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,EACpEqB,SAAS,iBACR3G,OAAA;4BAAGiF,SAAS,EAAC,oDAAoD;4BAAAC,QAAA,EAAC;0BAElE;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CACJ,EACA,CAACqB,SAAS,IAAI9B,UAAU,iBACvB7E,OAAA;4BAAGiF,SAAS,EAAC,kDAAkD;4BAAAC,QAAA,EAAC;0BAEhE;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CACJ;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGNtF,OAAA;sBAAAkF,QAAA,gBACElF,OAAA;wBAAIiF,SAAS,EAAC,2EAA2E;wBAAAC,QAAA,gBACvFlF,OAAA;0BAAMiF,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,EAAC;wBAAC;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,mBAE3C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLtF,OAAA;wBAAKiF,SAAS,EAAC,wJAAwJ;wBAAAC,QAAA,gBACrKlF,OAAA;0BAAKiF,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,eACjClF,OAAA;4BAAKiF,SAAS,EAAC,kFAAkF;4BAAAC,QAAA,eAC/FlF,OAAA,CAACf,OAAO;8BAACgG,SAAS,EAAC;4BAAkC;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrD;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNtF,OAAA;0BAAKiF,SAAS,EAAC,QAAQ;0BAAAC,QAAA,gBACrBlF,OAAA;4BAAGiF,SAAS,EAAC,eAAe;4BAAAC,QAAA,EAAEgC;0BAAa;4BAAA/B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAChDtF,OAAA;4BAAGiF,SAAS,EAAC,oDAAoD;4BAAAC,QAAA,EAAC;0BAElE;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EAGL,CAACqB,SAAS,iBACT3G,OAAA;sBAAKiF,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BlF,OAAA;wBACEuF,OAAO,EAAEA,CAAA,KAAMb,gBAAgB,CAC7BC,QAAQ,CAACmC,IAAI,EACbI,aAAa,EACbrC,UAAU,EACVF,QAAQ,CAACoC,KACX,CAAE;wBACF9B,SAAS,EAAC,gTAAgT;wBAAAC,QAAA,gBAE1TlF,OAAA,CAACR,MAAM;0BAACyF,SAAS,EAAC;wBAAuB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC5CtF,OAAA;0BAAAkF,QAAA,EAAM;wBAAuC;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC,EAER1E,YAAY,CAAC+D,QAAQ,CAACmC,IAAI,CAAC,iBAC1B9G,OAAA;wBAAKiF,SAAS,EAAC,0EAA0E;wBAAAC,QAAA,gBACvFlF,OAAA;0BAAKiF,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,gBAC1ClF,OAAA;4BAAKiF,SAAS,EAAC,iFAAiF;4BAAAC,QAAA,eAC9FlF,OAAA,CAACR,MAAM;8BAACyF,SAAS,EAAC;4BAAoB;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CAAC,eACNtF,OAAA;4BAAIiF,SAAS,EAAC,8CAA8C;4BAAAC,QAAA,EAAC;0BAAe;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9E,CAAC,eACNtF,OAAA,CAACH,eAAe;0BAACgH,OAAO,EAAEjG,YAAY,CAAC+D,QAAQ,CAACmC,IAAI,CAAE;0BAAC7B,SAAS,EAAC;wBAAoD;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrH,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN,EAGAqB,SAAS,iBACR3G,OAAA;sBAAKiF,SAAS,EAAC,2DAA2D;sBAAAC,QAAA,eACxElF,OAAA;wBAAKiF,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtClF,OAAA;0BAAKiF,SAAS,EAAC,oEAAoE;0BAAAC,QAAA,eACjFlF,OAAA,CAACf,OAAO;4BAACgG,SAAS,EAAC;0BAAoB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvC,CAAC,eACNtF,OAAA;0BAAAkF,QAAA,gBACElF,OAAA;4BAAIiF,SAAS,EAAC,+CAA+C;4BAAAC,QAAA,EAAC;0BAAU;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC7EtF,OAAA;4BAAGiF,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAC;0BAEjD;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GAxLExC,KAAK;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyLV,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtF,OAAA;UAAKiF,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFlF,OAAA;YACEiF,SAAS,EAAC,mVAAmV;YAC7VM,OAAO,EAAEA,CAAA,KAAMxE,aAAa,CAAC,IAAI,CAAE;YAAAmE,QAAA,gBAEnClF,OAAA,CAACT,KAAK;cAAC0F,SAAS,EAAC;YAAoF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAE1G;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETtF,OAAA;YACEiF,SAAS,EAAC,uVAAuV;YACjWM,OAAO,EAAEA,CAAA,KAAMtE,QAAQ,CAAE,SAAQD,EAAG,QAAO,CAAE;YAAAkE,QAAA,gBAE7ClF,OAAA,CAACV,SAAS;cAAC2F,SAAS,EAAC;YAAqF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE/G;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETtF,OAAA;YACEiF,SAAS,EAAC,uVAAuV;YACjWM,OAAO,EAAEA,CAAA,KAAMtE,QAAQ,CAAC,YAAY,CAAE;YAAAiE,QAAA,gBAEtClF,OAAA,CAACZ,OAAO;cAAC6F,SAAS,EAAC;YAAoF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1GtF,OAAA;cAAMiF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDtF,OAAA;cAAMiF,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV,CAAC;AAAClF,EAAA,CAlzBID,UAAU;EAAA,QAMCzB,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW,EACFG,aAAa;AAAA;AAAAqJ,EAAA,GAVnClI,UAAU;AAozBhB,eAAeA,UAAU;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}