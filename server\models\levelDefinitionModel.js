const mongoose = require("mongoose");

const levelDefinitionSchema = new mongoose.Schema(
  {
    level: {
      type: Number,
      required: true,
      unique: true,
    },
    
    // XP Requirements
    xpRequired: {
      type: Number,
      required: true,
    },
    xpToNext: {
      type: Number,
      default: 0, // 0 for max level
    },
    
    // Level Information
    name: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    description: String,
    
    // Visual Elements
    icon: String,
    color: {
      primary: String,
      secondary: String,
      gradient: [String], // Array of colors for gradient
    },
    badge: {
      shape: {
        type: String,
        enum: ["circle", "hexagon", "star", "crown", "diamond"],
        default: "circle",
      },
      animation: {
        type: String,
        enum: ["none", "pulse", "glow", "rotate", "bounce"],
        default: "none",
      },
    },
    
    // Level Benefits
    benefits: {
      xpMultiplier: {
        type: Number,
        default: 1.0,
      },
      unlocks: [String], // Array of feature names
      perks: [{
        name: String,
        description: String,
        icon: String,
      }],
      
      // UI Customizations
      customizations: {
        themes: [String],
        profileFrames: [String],
        specialEffects: [String],
      },
    },
    
    // Rarity and Prestige
    rarity: {
      type: String,
      enum: ["common", "uncommon", "rare", "epic", "legendary", "mythic"],
      default: "common",
    },
    
    // Achievement Requirements (optional)
    requirements: {
      achievements: [String], // Required achievement IDs
      streakMinimum: Number,
      subjectMastery: [String], // Required subjects to master
      socialRequirements: {
        helpOthers: Number,
        mentorStudents: Number,
      },
    },
    
    // Level Category
    category: {
      type: String,
      enum: ["beginner", "intermediate", "advanced", "expert", "master", "legend"],
      default: "beginner",
    },
    
    // Seasonal Information
    isSeasonalLevel: {
      type: Boolean,
      default: false,
    },
    availableSeasons: [String],
    
    // Statistics
    stats: {
      usersAtLevel: {
        type: Number,
        default: 0,
      },
      averageTimeToReach: Number, // in days
      completionRate: Number, // percentage of users who reach this level
    },
    
    // Admin Settings
    isActive: {
      type: Boolean,
      default: true,
    },
    isHidden: {
      type: Boolean,
      default: false,
    },
    
    // Metadata
    metadata: {
      type: Object,
      default: {},
    },
  },
  {
    timestamps: true,
  }
);

// Indexes
levelDefinitionSchema.index({ level: 1 });
levelDefinitionSchema.index({ xpRequired: 1 });
levelDefinitionSchema.index({ category: 1, level: 1 });
levelDefinitionSchema.index({ isActive: 1, isHidden: 1 });

// Virtual for level range
levelDefinitionSchema.virtual('xpRange').get(function() {
  return {
    min: this.xpRequired,
    max: this.xpRequired + this.xpToNext - 1,
  };
});

// Method to check if user qualifies for this level
levelDefinitionSchema.methods.userQualifies = function(user) {
  // Check XP requirement
  if (user.totalXP < this.xpRequired) {
    return { qualifies: false, reason: 'Insufficient XP' };
  }
  
  // Check achievement requirements
  if (this.requirements.achievements && this.requirements.achievements.length > 0) {
    const userAchievements = user.achievements.map(a => a.id);
    const hasAllAchievements = this.requirements.achievements.every(req => 
      userAchievements.includes(req)
    );
    if (!hasAllAchievements) {
      return { qualifies: false, reason: 'Missing required achievements' };
    }
  }
  
  // Check streak requirement
  if (this.requirements.streakMinimum && user.bestStreak < this.requirements.streakMinimum) {
    return { qualifies: false, reason: 'Insufficient streak' };
  }
  
  return { qualifies: true };
};

// Static method to get level for XP amount
levelDefinitionSchema.statics.getLevelForXP = function(xpAmount) {
  return this.findOne({
    xpRequired: { $lte: xpAmount },
    isActive: true,
  }).sort({ level: -1 });
};

// Static method to get next level
levelDefinitionSchema.statics.getNextLevel = function(currentLevel) {
  return this.findOne({
    level: currentLevel + 1,
    isActive: true,
  });
};

// Static method to get level progression path
levelDefinitionSchema.statics.getLevelPath = function(fromLevel, toLevel) {
  return this.find({
    level: { $gte: fromLevel, $lte: toLevel },
    isActive: true,
  }).sort({ level: 1 });
};

// Pre-save middleware to calculate xpToNext
levelDefinitionSchema.pre('save', async function(next) {
  if (this.isModified('level') || this.isModified('xpRequired')) {
    const nextLevel = await this.constructor.findOne({
      level: this.level + 1,
      isActive: true,
    });
    
    if (nextLevel) {
      this.xpToNext = nextLevel.xpRequired - this.xpRequired;
    } else {
      this.xpToNext = 0; // Max level
    }
  }
  next();
});

const LevelDefinition = mongoose.model("level_definitions", levelDefinitionSchema);

module.exports = LevelDefinition;
