const mongoose = require('mongoose');
const User = require('./models/userModel');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/brainwave', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const checkSubscriptionData = async () => {
  try {
    await connectDB();
    
    console.log('🔍 Checking current subscription data in database...\n');
    
    // Get all non-admin users
    const users = await User.find({ isAdmin: { $ne: true } }).limit(10);
    
    console.log(`👥 Found ${users.length} users\n`);
    
    users.forEach((user, index) => {
      console.log(`👤 User ${index + 1}: ${user.name}`);
      console.log(`   📧 Email: ${user.email}`);
      console.log(`   📊 Subscription Status: ${user.subscriptionStatus || 'undefined'}`);
      console.log(`   📅 Subscription End Date: ${user.subscriptionEndDate || 'undefined'}`);
      console.log(`   📋 Subscription Plan: ${user.subscriptionPlan || 'undefined'}`);
      console.log(`   🆔 User ID: ${user._id}`);
      console.log('');
    });
    
    // Count subscription statuses
    const statusCounts = await User.aggregate([
      { $match: { isAdmin: { $ne: true } } },
      { $group: { 
          _id: "$subscriptionStatus", 
          count: { $sum: 1 } 
        } 
      }
    ]);
    
    console.log('📊 Subscription Status Summary:');
    statusCounts.forEach(status => {
      console.log(`   ${status._id || 'undefined'}: ${status.count} users`);
    });
    
    // Count subscription plans
    const planCounts = await User.aggregate([
      { $match: { isAdmin: { $ne: true } } },
      { $group: { 
          _id: "$subscriptionPlan", 
          count: { $sum: 1 } 
        } 
      }
    ]);
    
    console.log('\n📋 Subscription Plan Summary:');
    planCounts.forEach(plan => {
      console.log(`   ${plan._id || 'undefined'}: ${plan.count} users`);
    });
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Check failed:', error);
    process.exit(1);
  }
};

checkSubscriptionData();
