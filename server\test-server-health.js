const axios = require('axios');

const testServerHealth = async () => {
  try {
    console.log('🔍 Testing server health...');
    
    // Test basic server response
    const response = await axios.get('http://localhost:5000/api/users/get-current-user', {
      timeout: 5000
    });
    
    console.log('✅ Server is responding');
    console.log('📊 Status:', response.status);
    console.log('📊 Response:', response.data?.success ? 'Success' : 'Failed');
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server is not running or not accessible on port 5000');
    } else if (error.response) {
      console.log('✅ Server is running but returned error:', error.response.status);
      console.log('📊 Error:', error.response.data?.message || 'Unknown error');
    } else {
      console.log('❌ Network error:', error.message);
    }
  }
};

testServerHealth();
