/* ===== RESPONSIVE HOME PAGE ===== */

.Home {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #f8fafc 50%, #ffffff 100%);
    min-height: 100vh;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
    position: relative;
    overflow-x: hidden;
}

.Home::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 123, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 86, 210, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 212, 255, 0.04) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.Home > * {
    position: relative;
    z-index: 1;
}

/* ===== ENHANCED BUTTONS ===== */
.btn-large {
    padding: 0.75rem 1.5rem !important;
    font-size: 0.9375rem !important;
    font-weight: 600 !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-large:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4) !important;
}

.cta-section {
    margin: 1.5rem 0;
}

/* Tablet Buttons */
@media (min-width: 640px) {
    .btn-large {
        padding: 0.875rem 1.75rem !important;
        font-size: 1rem !important;
        border-radius: 0.8125rem !important;
    }

    .cta-section {
        margin: 1.75rem 0;
    }
}

@media (min-width: 768px) {
    .btn-large {
        padding: 1rem 2rem !important;
        font-size: 1.125rem !important;
        border-radius: 0.875rem !important;
    }

    .cta-section {
        margin: 2rem 0;
    }
}

/* ===== NAVIGATION ===== */
.nav-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(0, 123, 255, 0.15);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 32px rgba(0, 123, 255, 0.12);
    position: sticky;
    top: 0;
    z-index: 1000;
    position: relative;
    overflow: hidden;
}

.nav-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    pointer-events: none;
    z-index: 0;
}

.nav-header > * {
    position: relative;
    z-index: 1;
}

/* Navigation responsive height */
.nav-header .container > div {
    height: 4rem; /* 64px */
}

@media (min-width: 768px) {
    .nav-header .container > div {
        height: 4.5rem; /* 72px */
    }
}

@media (min-width: 1024px) {
    .nav-header .container > div {
        height: 5rem; /* 80px */
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    width: 100%;
}

/* ===== RESPONSIVE CONTAINERS ===== */
@media (min-width: 640px) {
    .container {
        padding: 0 1.5rem;
    }
}

@media (min-width: 768px) {
    .container {
        padding: 0 2rem;
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 2.5rem;
    }
}

@media (min-width: 1280px) {
    .container {
        padding: 0 3rem;
    }
}

/* Navigation Items */
.nav-item {
    color: #374151;
    font-weight: 600;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 0.875rem;
    text-decoration: none !important;
    display: inline-block;
    font-family: inherit;
    position: relative;
    overflow: hidden;
}

/* Tablet navigation items */
@media (min-width: 768px) {
    .nav-item {
        padding: 0.875rem 1.25rem;
        border-radius: 0.625rem;
        font-size: 0.9375rem;
    }
}

/* Desktop navigation items */
@media (min-width: 1024px) {
    .nav-item {
        padding: 0.875rem 1.5rem;
        border-radius: 0.75rem;
        font-size: 1rem;
    }
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.nav-item:hover::before {
    left: 100%;
}

.nav-item:hover {
    color: #007BFF !important;
    background: rgba(0, 123, 255, 0.08);
    transform: translateY(-2px);
    text-decoration: none !important;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.nav-item:active {
    transform: translateY(-1px);
    background: rgba(0, 123, 255, 0.12);
}

.nav-item:focus {
    outline: 2px solid rgba(0, 123, 255, 0.4);
    outline-offset: 2px;
}

/* Logo Styling */
.logo-text {
    font-size: 1.25rem;
    font-weight: 900;
    background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #007BFF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
    transition: all 0.3s ease;
}

/* Tablet logo */
@media (min-width: 640px) {
    .logo-text {
        font-size: 1.5rem;
    }
}

/* Desktop logo */
@media (min-width: 768px) {
    .logo-text {
        font-size: 1.75rem;
    }
}

@media (min-width: 1024px) {
    .logo-text {
        font-size: 1.875rem;
    }
}

.logo-text:hover {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #4338CA 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transform: scale(1.05);
}

.logo-accent {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Mobile Navigation */
.mobile-nav {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    margin-top: 1rem;
    padding: 1.5rem;
    border: 1px solid rgba(0, 123, 255, 0.1);
}

.mobile-nav .nav-item {
    display: block;
    width: 100%;
    text-align: center;
    padding: 1.25rem 1rem;
    margin-bottom: 0.75rem;
    border-radius: 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    background: rgba(0, 123, 255, 0.05);
    border: 1px solid rgba(0, 123, 255, 0.1);
    color: #1f2937;
}

.mobile-nav .nav-item:hover {
    background: rgba(0, 123, 255, 0.1);
    color: #007BFF;
    transform: translateY(-2px);
}

.mobile-nav .nav-item:last-child {
    margin-bottom: 0;
}

/* ===== TYPOGRAPHY ===== */
.hero-title {
    font-size: 1.875rem;
    font-weight: 800;
    line-height: 1.2;
    color: #1f2937;
    margin-bottom: 1rem;
    text-align: center;
}

.hero-subtitle {
    font-size: 1rem;
    line-height: 1.6;
    color: #374151;
    margin-bottom: 1.5rem;
    font-weight: 500;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Tablet Typography */
@media (min-width: 640px) {
    .hero-title {
        font-size: 2.25rem;
        margin-bottom: 1.25rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
        margin-bottom: 1.75rem;
    }
}

@media (min-width: 768px) {
    .hero-title {
        font-size: 2.75rem;
        margin-bottom: 1.5rem;
    }

    .hero-subtitle {
        font-size: 1.25rem;
        margin-bottom: 2rem;
    }
}

/* Desktop Typography */
@media (min-width: 1024px) {
    .hero-title {
        font-size: 3.25rem;
        text-align: left;
    }

    .hero-subtitle {
        font-size: 1.375rem;
        text-align: left;
    }
}

@media (min-width: 1280px) {
    .hero-title {
        font-size: 3.75rem;
    }

    .hero-subtitle {
        font-size: 1.5rem;
    }
}

.text-gradient {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: inline-block;
}

/* ===== HERO SECTION ===== */
.hero-section {
    padding: 5rem 1rem 3rem;
    min-height: 90vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    backdrop-filter: blur(10px);
    scroll-margin-top: 80px;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 70%, rgba(0, 123, 255, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 70% 30%, rgba(0, 86, 210, 0.08) 0%, transparent 40%);
    pointer-events: none;
    z-index: 0;
}

.hero-section > * {
    position: relative;
    z-index: 1;
}

.hero-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    text-align: center;
}

.hero-content {
    padding: 1rem 0;
    order: 1;
}

/* Tablet Hero */
@media (min-width: 768px) {
    .hero-section {
        padding: 6rem 1.5rem 4rem;
        min-height: 95vh;
    }

    .hero-grid {
        gap: 3rem;
    }

    .hero-content {
        padding: 1.5rem 0;
    }
}

/* Desktop Hero */
@media (min-width: 1024px) {
    .hero-section {
        padding: 8rem 2rem 4rem;
        min-height: 100vh;
    }

    .hero-grid {
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        text-align: left;
    }

    .hero-content {
        padding: 2rem 0;
        order: 0;
    }
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 86, 210, 0.15) 100%);
    border: 1px solid rgba(0, 123, 255, 0.2);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
    color: #007BFF;
    border-radius: 1.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(0, 123, 255, 0.2);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

/* Tablet Badge */
@media (min-width: 640px) {
    .hero-badge {
        padding: 0.625rem 1.25rem;
        font-size: 0.8125rem;
        margin-bottom: 1.75rem;
    }
}

@media (min-width: 768px) {
    .hero-badge {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
        margin-bottom: 2rem;
        border-radius: 2rem;
    }
}

/* ===== BUTTONS ===== */
.cta-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
    align-items: center;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none !important;
    border: none;
    cursor: pointer;
    white-space: nowrap;
    min-height: 52px;
    min-width: 160px;
    position: relative;
    overflow: hidden;
    font-family: inherit;
    line-height: 1.2;
}

.btn-primary {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    border: 2px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
    color: white;
}

.btn-secondary {
    background: white;
    color: #007BFF;
    border: 2px solid #007BFF;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background: #007BFF;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

/* Button Icons */
.btn svg {
    width: 1.25rem;
    height: 1.25rem;
}

/* ===== TRUST INDICATORS ===== */
.trust-indicators {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.trust-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.625rem 1rem;
    background: rgba(0, 123, 255, 0.05);
    border-radius: 1.5rem;
    border: 1px solid rgba(0, 123, 255, 0.1);
}

.trust-indicator svg {
    width: 1rem;
    height: 1rem;
}

/* Hide third indicator on mobile */
.trust-indicator:nth-child(3) {
    display: none;
}

/* Tablet Trust Indicators */
@media (min-width: 640px) {
    .trust-indicators {
        gap: 1.25rem;
        margin-top: 1.75rem;
    }

    .trust-indicator {
        gap: 0.625rem;
        font-size: 0.8125rem;
        padding: 0.75rem 1.125rem;
    }

    .trust-indicator svg {
        width: 1.125rem;
        height: 1.125rem;
    }

    /* Show third indicator on tablet */
    .trust-indicator:nth-child(3) {
        display: flex;
    }
}

@media (min-width: 768px) {
    .trust-indicators {
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .trust-indicator {
        gap: 0.75rem;
        font-size: 0.875rem;
        padding: 0.875rem 1.25rem;
        border-radius: 1.75rem;
    }

    .trust-indicator svg {
        width: 1.25rem;
        height: 1.25rem;
    }
}

/* Desktop Trust Indicators */
@media (min-width: 1024px) {
    .trust-indicators {
        justify-content: flex-start;
        gap: 2rem;
        margin-top: 2.5rem;
    }

    .trust-indicator {
        padding: 1rem 1.5rem;
        border-radius: 2rem;
        font-weight: 500;
    }
}

/* ===== HERO IMAGE ===== */
.hero-image {
    position: relative;
    max-width: 100%;
    height: auto;
    padding: 0.5rem;
    order: 2;
    margin-top: 1rem;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 1rem;
    box-shadow: 0 15px 35px -8px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
}

.hero-image:hover img {
    transform: scale(1.02);
}

/* Tablet Hero Image */
@media (min-width: 640px) {
    .hero-image {
        padding: 0.75rem;
        margin-top: 1.5rem;
        max-width: 90%;
        margin-left: auto;
        margin-right: auto;
    }

    .hero-image img {
        border-radius: 1.25rem;
        box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.22);
    }
}

@media (min-width: 768px) {
    .hero-image {
        padding: 1rem;
        margin-top: 2rem;
        max-width: 85%;
    }

    .hero-image img {
        border-radius: 1.5rem;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }
}

/* Desktop Hero Image */
@media (min-width: 1024px) {
    .hero-image {
        order: 0;
        margin-top: 0;
        max-width: 100%;
        padding: 1rem;
    }
}

/* Floating elements */
.floating-element {
    position: absolute;
    background: white;
    border-radius: 1rem;
    padding: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 10;
}

.floating-element svg {
    width: 2rem;
    height: 2rem;
}

/* ===== STATS SECTION ===== */
.stats-section {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    padding: 4rem 1rem;
    color: white;
    scroll-margin-top: 80px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.stat-number {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    line-height: 1;
}

.stat-text {
    font-size: 0.875rem;
    opacity: 0.9;
    font-weight: 500;
}

/* ===== ABOUT SECTION ===== */
.about-section {
    padding: 6rem 1rem;
    background: white;
    scroll-margin-top: 80px;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.about-content {
    order: 2;
}

.about-image {
    order: 1;
}

.about-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.about-text {
    font-size: 1.125rem;
    color: #6b7280;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* ===== REVIEWS SECTION ===== */
.reviews-section {
    padding: 6rem 1rem;
    background: #f8fafc;
    scroll-margin-top: 80px;
}

.reviews-container {
    max-width: 1200px;
    margin: 0 auto;
}

.reviews-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    text-align: center;
    color: #1f2937;
    margin-bottom: 3rem;
}

.reviews-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.review-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.review-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.review-rating {
    margin-bottom: 1rem;
}

.review-text {
    flex: 1;
    font-size: 0.875rem;
    line-height: 1.6;
    color: #6b7280;
    margin-bottom: 1.5rem;
}

.review-divider {
    height: 1px;
    background: #e5e7eb;
    margin-bottom: 1rem;
}

.review-author {
    font-weight: 600;
    color: #007BFF;
    font-size: 0.875rem;
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    padding: 6rem 1rem;
    background: white;
    scroll-margin-top: 80px;
}

.contact-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.contact-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1rem;
}

.contact-subtitle {
    font-size: 1.125rem;
    color: #6b7280;
    margin-bottom: 3rem;
}

.contact-form {
    display: grid;
    gap: 1.5rem;
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-input {
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #007BFF;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
}

.form-submit {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 1rem;
}

.form-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
}

/* ===== FOOTER ===== */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 1rem 2rem;
    text-align: center;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
}

.footer-text {
    font-size: 0.875rem;
    color: #9ca3af;
}

/* ===== RESPONSIVE DESIGN ===== */
/* Note: Mobile-first responsive design implemented above */
