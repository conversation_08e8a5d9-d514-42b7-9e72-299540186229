const axios = require('axios');
const jwt = require('jsonwebtoken');
require('dotenv').config();

async function testProfileUpdate() {
  try {
    console.log('🧪 Testing Profile Update with Capitalized Levels...');

    // Create a test token for <PERSON>itti
    const testUserId = '65c06d5341641692a0ee4e11'; // <PERSON><PERSON><PERSON>'s user ID from previous tests
    const token = jwt.sign({ userId: testUserId }, process.env.JWT_SECRET, { expiresIn: '1h' });

    console.log('📡 Testing profile update with different level cases...');
    
    // Test cases for different level formats
    const testCases = [
      { level: 'Primary', description: 'Capitalized Primary' },
      { level: 'Secondary', description: 'Capitalized Secondary' },
      { level: 'Advance', description: 'Capitalized Advance' },
      { level: 'primary', description: 'Lowercase primary' },
      { level: 'secondary', description: 'Lowercase secondary' },
      { level: 'advance', description: 'Lowercase advance' }
    ];

    for (const testCase of testCases) {
      console.log(`\n🔄 Testing ${testCase.description}...`);
      
      try {
        const response = await axios.post('http://localhost:5000/api/users/update-user-info', 
          { 
            userId: testUserId,
            name: 'Sawitti',
            email: '<EMAIL>',
            school: 'St. Carlos',
            class_: testCase.level === 'Primary' || testCase.level === 'primary' ? '7' : 
                   testCase.level === 'Secondary' || testCase.level === 'secondary' ? 'Form-2' : 'Form-5',
            level: testCase.level,
            phoneNumber: '1234567890'
          },
          { headers: { Authorization: `Bearer ${token}` } }
        );

        if (response.data.success) {
          console.log(`✅ ${testCase.description}: SUCCESS`);
          console.log(`   Message: ${response.data.message}`);
          if (response.data.levelChanged) {
            console.log(`   Level changed to: ${response.data.newLevel}`);
          }
        } else {
          console.log(`❌ ${testCase.description}: FAILED`);
          console.log(`   Error: ${response.data.message}`);
        }
      } catch (error) {
        console.log(`❌ ${testCase.description}: ERROR`);
        if (error.response) {
          console.log(`   Error: ${error.response.data.message}`);
        } else {
          console.log(`   Error: ${error.message}`);
        }
      }
    }

    // Test invalid level
    console.log(`\n🔄 Testing invalid level...`);
    try {
      const response = await axios.post('http://localhost:5000/api/users/update-user-info', 
        { 
          userId: testUserId,
          name: 'Sawitti',
          email: '<EMAIL>',
          school: 'St. Carlos',
          class_: '7',
          level: 'invalid_level',
          phoneNumber: '1234567890'
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      console.log(`❌ Invalid level test: Should have failed but didn't`);
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log(`✅ Invalid level test: Correctly rejected`);
        console.log(`   Error: ${error.response.data.message}`);
      } else {
        console.log(`❌ Invalid level test: Unexpected error`);
        console.log(`   Error: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testProfileUpdate();
