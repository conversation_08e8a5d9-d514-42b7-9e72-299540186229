<!DOCTYPE html>
<html>
<head>
    <title>Debug Exam Questions</title>
</head>
<body>
    <h1>Debug Exam Questions</h1>
    <button onclick="debugExam()">Debug Exam 685f80c63916d9527839d161</button>
    <pre id="result"></pre>

    <script>
        async function debugExam() {
            try {
                const response = await fetch('http://localhost:5000/api/exams/debug-exam-questions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer YOUR_TOKEN_HERE' // You'll need to replace this
                    },
                    body: JSON.stringify({
                        examId: '685f80c63916d9527839d161'
                    })
                });
                
                const data = await response.json();
                document.getElementById('result').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('result').textContent = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
