<!DOCTYPE html>
<html>
<head>
    <title>Debug Token</title>
</head>
<body>
    <h1>Token Debug</h1>
    <div id="output"></div>
    
    <script>
        function debugToken() {
            const output = document.getElementById('output');
            
            // Get token from localStorage
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            
            output.innerHTML = '<h2>Token Debug Results:</h2>';
            
            if (!token) {
                output.innerHTML += '<p style="color: red;">❌ No token found in localStorage</p>';
                return;
            }
            
            output.innerHTML += '<p>✅ Token found</p>';
            output.innerHTML += '<p><strong>Token:</strong> ' + token.substring(0, 50) + '...</p>';
            
            try {
                // Decode token payload
                const parts = token.split('.');
                if (parts.length !== 3) {
                    output.innerHTML += '<p style="color: red;">❌ Invalid token format</p>';
                    return;
                }
                
                const payload = JSON.parse(atob(parts[1]));
                output.innerHTML += '<p><strong>Token Payload:</strong></p>';
                output.innerHTML += '<pre>' + JSON.stringify(payload, null, 2) + '</pre>';
                
                // Check expiry
                const currentTime = Date.now() / 1000;
                const timeLeft = payload.exp - currentTime;
                
                if (timeLeft > 0) {
                    output.innerHTML += '<p style="color: green;">✅ Token is valid (expires in ' + Math.floor(timeLeft / 3600) + 'h ' + Math.floor((timeLeft % 3600) / 60) + 'm)</p>';
                } else {
                    output.innerHTML += '<p style="color: red;">❌ Token is expired</p>';
                }
                
            } catch (e) {
                output.innerHTML += '<p style="color: red;">❌ Error decoding token: ' + e.message + '</p>';
            }
            
            if (user) {
                output.innerHTML += '<p><strong>User Data:</strong></p>';
                output.innerHTML += '<pre>' + user + '</pre>';
            } else {
                output.innerHTML += '<p style="color: red;">❌ No user data found</p>';
            }
        }
        
        // Run debug on page load
        debugToken();
        
        // Add refresh button
        document.body.innerHTML += '<br><button onclick="debugToken()">Refresh Debug</button>';
        document.body.innerHTML += '<br><button onclick="localStorage.clear(); debugToken();">Clear Storage & Debug</button>';
    </script>
</body>
</html>
