const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('./server/models/userModel');
const Report = require('./server/models/reportModel');

async function debugRanking() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to MongoDB');

        // Check total users
        const totalUsers = await User.countDocuments();
        console.log(`📊 Total users in database: ${totalUsers}`);

        // Check non-admin users
        const nonAdminUsers = await User.countDocuments({ isAdmin: { $ne: true } });
        console.log(`👥 Non-admin users: ${nonAdminUsers}`);

        // Check users with quiz attempts
        const usersWithReports = await Report.distinct('user');
        console.log(`🎯 Users with quiz attempts: ${usersWithReports.length}`);

        // Get sample users
        const sampleUsers = await User.find({ isAdmin: { $ne: true } })
            .select('name email class level totalXP totalPoints isBlocked')
            .limit(5);
        
        console.log('\n📋 Sample users:');
        sampleUsers.forEach((user, index) => {
            console.log(`${index + 1}. ${user.name} - Class: ${user.class}, Level: ${user.level}, XP: ${user.totalXP || 0}, Points: ${user.totalPoints || 0}, Blocked: ${user.isBlocked || false}`);
        });

        // Test XP leaderboard aggregation
        console.log('\n🏆 Testing XP leaderboard aggregation...');
        
        const pipeline = [
            {
                $match: {
                    isAdmin: { $ne: true },
                    isBlocked: { $ne: true }
                }
            },
            {
                $addFields: {
                    totalXP: { $ifNull: ['$totalXP', 0] },
                    totalPoints: { $ifNull: ['$totalPoints', 0] },
                    currentLevel: { $ifNull: ['$currentLevel', 1] }
                }
            },
            {
                $sort: { totalXP: -1, totalPoints: -1, name: 1 }
            },
            {
                $limit: 10
            },
            {
                $project: {
                    name: 1,
                    email: 1,
                    class: 1,
                    level: 1,
                    totalXP: 1,
                    totalPoints: 1,
                    currentLevel: 1,
                    profileImage: 1,
                    subscriptionStatus: 1
                }
            }
        ];

        const leaderboard = await User.aggregate(pipeline);
        console.log(`📈 Leaderboard results: ${leaderboard.length} users`);
        
        if (leaderboard.length > 0) {
            console.log('\n🥇 Top users:');
            leaderboard.forEach((user, index) => {
                console.log(`${index + 1}. ${user.name} - XP: ${user.totalXP}, Points: ${user.totalPoints}`);
            });
        } else {
            console.log('❌ No users found in leaderboard');
        }

        // Check if XP system is initialized
        const usersWithXP = await User.countDocuments({ totalXP: { $gt: 0 } });
        console.log(`\n⚡ Users with XP > 0: ${usersWithXP}`);

        const usersWithPoints = await User.countDocuments({ totalPoints: { $gt: 0 } });
        console.log(`🎯 Users with Points > 0: ${usersWithPoints}`);

    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.disconnect();
        console.log('✅ Disconnected from MongoDB');
    }
}

debugRanking();
