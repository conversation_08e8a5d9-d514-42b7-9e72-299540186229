const mongoose = require('mongoose');
require('dotenv').config();
const User = require('./models/userModel');

async function fixUserValidation() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('Connected to MongoDB');
    
    // Find the Brainwave user
    const user = await User.findOne({name: 'Brainwave✓'});
    if (!user) {
      console.log('User not found');
      process.exit(1);
    }
    
    console.log('Current user data:');
    console.log('Phone number:', user.phoneNumber);
    console.log('Achievements:', user.achievements?.length || 0);
    
    // Fix the user data
    const updates = {};
    
    // Ensure achievements array is properly initialized
    if (!user.achievements) {
      updates.achievements = [];
      console.log('Initializing achievements array');
    } else if (user.achievements.some(ach => !ach.id)) {
      // Remove any achievements without proper id
      updates.achievements = user.achievements.filter(ach => ach.id);
      console.log('Filtering out invalid achievements');
    }
    
    // Set a default phone number if missing
    if (!user.phoneNumber) {
      updates.phoneNumber = null; // Set to null since we made it optional
      console.log('Setting phone number to null');
    }
    
    // Update the user
    if (Object.keys(updates).length > 0) {
      await User.findByIdAndUpdate(user._id, updates, { runValidators: false });
      console.log('User updated successfully');
    } else {
      console.log('No updates needed');
    }
    
    // Verify the fix
    const updatedUser = await User.findById(user._id);
    console.log('Updated user data:');
    console.log('Phone number:', updatedUser.phoneNumber);
    console.log('Achievements:', updatedUser.achievements?.length || 0);
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

fixUserValidation();
