const router = require("express").Router();
const jwt = require("jsonwebtoken");
const User = require("../models/userModel");
const authMiddleware = require("../middlewares/authMiddleware");

/**
 * Refresh authentication token
 * Provides a new token if the current one is still valid but expiring soon
 */
router.post("/refresh-token", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    
    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    if (user.isBlocked) {
      return res.status(403).send({
        message: "Account is blocked",
        success: false,
      });
    }

    // Generate new token with extended expiry
    const newToken = jwt.sign(
      { userId: user._id },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || "7d" }
    );

    res.send({
      message: "Token refreshed successfully",
      success: true,
      data: {
        token: newToken,
        user: {
          _id: user._id,
          name: user.name,
          email: user.email,
          isAdmin: user.isAdmin,
          level: user.level,
          class: user.class,
          school: user.school,
        },
      },
    });

  } catch (error) {
    console.error("Token refresh error:", error);
    res.status(500).send({
      message: "Failed to refresh token",
      success: false,
      error: error.message,
    });
  }
});

/**
 * Validate current session
 * Checks if the current token is valid and returns user info
 */
router.get("/validate-session", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    // Check token expiry
    const token = req.headers.authorization.split(" ")[1];
    const decoded = jwt.decode(token);
    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = decoded.exp - currentTime;
    
    res.send({
      message: "Session is valid",
      success: true,
      data: {
        user: {
          _id: user._id,
          name: user.name,
          email: user.email,
          isAdmin: user.isAdmin,
          level: user.level,
          class: user.class,
          school: user.school,
        },
        tokenInfo: {
          expiresAt: decoded.exp,
          timeUntilExpiry: timeUntilExpiry,
          expiresIn: `${Math.floor(timeUntilExpiry / 3600)}h ${Math.floor((timeUntilExpiry % 3600) / 60)}m`,
          needsRefresh: timeUntilExpiry < 3600 // Less than 1 hour
        }
      },
    });

  } catch (error) {
    console.error("Session validation error:", error);
    res.status(500).send({
      message: "Failed to validate session",
      success: false,
      error: error.message,
    });
  }
});

/**
 * Quick login for AI features
 * Streamlined login specifically for AI operations
 */
router.post("/quick-login", async (req, res) => {
  try {
    const { email, password, rememberMe } = req.body;

    if (!email || !password) {
      return res.status(400).send({
        message: "Email and password are required",
        success: false,
      });
    }

    // Find user
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).send({
        message: "Invalid email or password",
        success: false,
      });
    }

    if (user.isBlocked) {
      return res.status(403).send({
        message: "Your account has been blocked. Please contact support.",
        success: false,
      });
    }

    // Verify password
    const bcrypt = require("bcryptjs");
    const validPassword = await bcrypt.compare(password, user.password);
    if (!validPassword) {
      return res.status(401).send({
        message: "Invalid email or password",
        success: false,
      });
    }

    // Generate token with appropriate expiry
    const tokenExpiry = rememberMe ? "30d" : "7d";
    const token = jwt.sign(
      { userId: user._id },
      process.env.JWT_SECRET,
      { expiresIn: tokenExpiry }
    );

    res.send({
      message: "Login successful",
      success: true,
      data: {
        token,
        user: {
          _id: user._id,
          name: user.name,
          email: user.email,
          isAdmin: user.isAdmin,
          level: user.level,
          class: user.class,
          school: user.school,
          paymentRequired: user.paymentRequired,
        },
        aiAccess: {
          enabled: user.isAdmin || !user.paymentRequired,
          requiresUpgrade: !user.isAdmin && user.paymentRequired,
        }
      },
    });

  } catch (error) {
    console.error("Quick login error:", error);
    res.status(500).send({
      message: "Login failed. Please try again.",
      success: false,
      error: error.message,
    });
  }
});

module.exports = router;
