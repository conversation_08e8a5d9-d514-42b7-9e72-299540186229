const mongoose = require("mongoose");
const User = require("../models/userModel");
const LevelDefinition = require("../models/levelDefinitionModel");
const xpCalculationService = require("../services/xpCalculationService");

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL || "mongodb://localhost:27017/brainwave", {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log("✅ Connected to MongoDB");
  } catch (error) {
    console.error("❌ MongoDB connection error:", error);
    process.exit(1);
  }
};

// Convert legacy points to XP
const convertPointsToXP = (points) => {
  // Simple conversion: 1 point = 1.5 XP (to make XP feel more rewarding)
  return Math.round(points * 1.5);
};

// Calculate level from XP
const calculateLevelFromXP = async (xp) => {
  try {
    const level = await LevelDefinition.findOne({
      xpRequired: { $lte: xp },
      isActive: true,
    }).sort({ level: -1 });
    
    return level ? level.level : 1;
  } catch (error) {
    console.error('Error calculating level:', error);
    return 1;
  }
};

// Calculate XP to next level
const calculateXPToNextLevel = async (currentXP, currentLevel) => {
  try {
    const nextLevel = await LevelDefinition.findOne({
      level: currentLevel + 1,
      isActive: true,
    });
    
    if (nextLevel) {
      return Math.max(0, nextLevel.xpRequired - currentXP);
    }
    
    return 0; // Max level reached
  } catch (error) {
    console.error('Error calculating XP to next level:', error);
    return 0;
  }
};

// Migrate user data
const migrateUserToXP = async (user) => {
  try {
    // Convert legacy points to XP
    const convertedXP = convertPointsToXP(user.totalPointsEarned || 0);
    
    // Calculate level based on converted XP
    const calculatedLevel = await calculateLevelFromXP(convertedXP);
    
    // Calculate XP to next level
    const xpToNext = await calculateXPToNextLevel(convertedXP, calculatedLevel);
    
    // Initialize XP stats
    const xpStats = {
      dailyXP: 0,
      weeklyXP: 0,
      monthlyXP: 0,
      lastXPGain: new Date(),
      averageXPPerQuiz: user.totalQuizzesTaken > 0 ? Math.round(convertedXP / user.totalQuizzesTaken) : 0,
      bestXPGain: 0,
    };
    
    // Update user with XP system fields
    const updateData = {
      totalXP: convertedXP,
      currentLevel: calculatedLevel,
      xpToNextLevel: xpToNext,
      lifetimeXP: convertedXP,
      seasonXP: Math.round(convertedXP * 0.3), // Assume 30% of XP is from current season
      currentSeason: "2024-S1",
      xpStats: xpStats,
      levelHistory: [{
        level: calculatedLevel,
        reachedAt: new Date(),
        xpAtLevel: convertedXP,
      }]
    };
    
    await User.findByIdAndUpdate(user._id, updateData);
    
    return {
      userId: user._id,
      name: user.name,
      oldPoints: user.totalPointsEarned || 0,
      newXP: convertedXP,
      newLevel: calculatedLevel,
      xpToNext: xpToNext
    };
    
  } catch (error) {
    console.error(`Error migrating user ${user.name}:`, error);
    return null;
  }
};

// Main migration function
const runMigration = async () => {
  try {
    console.log("🚀 Starting User XP Migration...");
    
    // Connect to database
    await connectDB();
    
    // Get all users (excluding admins)
    const users = await User.find({ 
      role: { $ne: 'admin' },
      totalXP: { $exists: false } // Only migrate users who haven't been migrated yet
    });
    
    console.log(`📊 Found ${users.length} users to migrate`);
    
    if (users.length === 0) {
      console.log("✅ No users need migration (all users already have XP data)");
      return;
    }
    
    const migrationResults = [];
    let successCount = 0;
    let errorCount = 0;
    
    // Migrate users in batches
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      console.log(`🔄 Migrating user ${i + 1}/${users.length}: ${user.name}`);
      
      const result = await migrateUserToXP(user);
      
      if (result) {
        migrationResults.push(result);
        successCount++;
        console.log(`   ✅ ${result.name}: ${result.oldPoints} pts → ${result.newXP} XP (Level ${result.newLevel})`);
      } else {
        errorCount++;
        console.log(`   ❌ Failed to migrate ${user.name}`);
      }
      
      // Small delay to avoid overwhelming the database
      if (i % 10 === 0 && i > 0) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    console.log("\n📈 Migration Summary:");
    console.log(`✅ Successfully migrated: ${successCount} users`);
    console.log(`❌ Failed migrations: ${errorCount} users`);
    console.log(`📊 Total XP awarded: ${migrationResults.reduce((sum, r) => sum + r.newXP, 0).toLocaleString()}`);
    
    // Show top users by XP
    const topUsers = migrationResults
      .sort((a, b) => b.newXP - a.newXP)
      .slice(0, 10);
    
    console.log("\n🏆 Top 10 Users by XP:");
    topUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name}: ${user.newXP.toLocaleString()} XP (Level ${user.newLevel})`);
    });
    
    console.log("\n✅ Migration completed successfully!");
    console.log("\n📋 Next steps:");
    console.log("1. Restart your server");
    console.log("2. Test the new XP system with quiz completions");
    console.log("3. Check the ranking page to see XP-based leaderboards");
    console.log("4. Users can now earn XP and level up!");
    
  } catch (error) {
    console.error("❌ Migration failed:", error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log("🔌 Database connection closed");
    process.exit(0);
  }
};

// Run the migration
runMigration();
