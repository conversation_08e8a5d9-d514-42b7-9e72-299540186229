# ============================================================================
# ST. JOSEPH KIBADA QUIZ APP - GIT IGNORE CONFIGURATION
# ============================================================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Environment Variables & Secrets
.env
.env.local
.env.development
.env.production
.env.staging
.env.test
*.env

# Google Cloud Service Account Keys
proud-stage-416018-d682ff695aac.json
solid-daylight-387916-eaa8d8847c7a.json
*.json

# AWS Credentials
.aws/
aws-credentials.json

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Upload directories
uploads/
public/uploads/
static/uploads/

# Backup files
*.backup
*.bak
*.tmp

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Docker
.dockerignore
Dockerfile.prod

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Local development
.local/
.cache/

# Build outputs
build/
dist/
out/

# Test outputs
test-results/
coverage/

# Documentation build
docs/build/

# ============================================================================
# END OF GITIGNORE
# ============================================================================