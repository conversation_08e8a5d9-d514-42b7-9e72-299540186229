const User = require('../models/userModel');
const Report = require('../models/reportModel');

class StreakTrackingService {
  constructor() {
    this.streakTypes = {
      LOGIN: 'login',
      QUIZ_COMPLETION: 'quiz_completion',
      QUIZ_PASS: 'quiz_pass',
      PERFECT_SCORE: 'perfect_score',
      SUBJECT_SPECIFIC: 'subject_specific'
    };
  }

  /**
   * Update login streak for user
   */
  async updateLoginStreak(userId) {
    try {
      const user = await User.findById(userId);
      if (!user) throw new Error('User not found');

      if (!user.activityTracking) {
        user.activityTracking = this.initializeActivityTracking();
      }

      const today = new Date();
      const todayStr = today.toDateString();
      const lastLoginDate = user.activityTracking.lastLoginDate ? 
        new Date(user.activityTracking.lastLoginDate) : null;
      const lastLoginStr = lastLoginDate ? lastLoginDate.toDateString() : null;

      // Check if user already logged in today
      if (lastLoginStr === todayStr) {
        return { streakUpdated: false, currentStreak: user.activityTracking.dailyLoginStreak };
      }

      // Check if login is consecutive
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toDateString();

      if (lastLoginStr === yesterdayStr) {
        // Consecutive login - increment streak
        user.activityTracking.dailyLoginStreak += 1;
      } else if (lastLoginStr !== todayStr) {
        // Non-consecutive login - reset streak
        user.activityTracking.dailyLoginStreak = 1;
      }

      // Update best login streak
      user.activityTracking.bestLoginStreak = Math.max(
        user.activityTracking.bestLoginStreak || 0,
        user.activityTracking.dailyLoginStreak
      );

      // Update last login date and total login days
      user.activityTracking.lastLoginDate = today;
      user.activityTracking.totalLoginDays = (user.activityTracking.totalLoginDays || 0) + 1;

      await user.save();

      return {
        streakUpdated: true,
        currentStreak: user.activityTracking.dailyLoginStreak,
        bestStreak: user.activityTracking.bestLoginStreak,
        isNewRecord: user.activityTracking.dailyLoginStreak === user.activityTracking.bestLoginStreak
      };

    } catch (error) {
      console.error('Error updating login streak:', error);
      throw error;
    }
  }

  /**
   * Update quiz completion and performance streaks
   */
  async updateQuizStreaks(userId, quizResult, examData) {
    try {
      const user = await User.findById(userId);
      if (!user) throw new Error('User not found');

      if (!user.activityTracking) {
        user.activityTracking = this.initializeActivityTracking();
      }

      const { score, verdict, correctAnswers = [], wrongAnswers = [] } = quizResult;
      const totalQuestions = correctAnswers.length + wrongAnswers.length;
      const scorePercentage = score || Math.round((correctAnswers.length / totalQuestions) * 100);
      const isPerfectScore = scorePercentage === 100;
      const isPassed = verdict === 'Pass';

      // Update quiz completion streak (always increments)
      user.activityTracking.quizCompletionStreak += 1;
      user.activityTracking.bestQuizStreak = Math.max(
        user.activityTracking.bestQuizStreak || 0,
        user.activityTracking.quizCompletionStreak
      );

      // Update quiz pass streak
      if (isPassed) {
        user.currentStreak = (user.currentStreak || 0) + 1;
        user.bestStreak = Math.max(user.bestStreak || 0, user.currentStreak);
      } else {
        user.currentStreak = 0;
      }

      // Update perfect score streak
      if (isPerfectScore) {
        user.activityTracking.perfectScoreStreak = (user.activityTracking.perfectScoreStreak || 0) + 1;
        user.activityTracking.bestPerfectStreak = Math.max(
          user.activityTracking.bestPerfectStreak || 0,
          user.activityTracking.perfectScoreStreak
        );
      } else {
        user.activityTracking.perfectScoreStreak = 0;
      }

      // Update performance counters
      if (isPassed) {
        user.activityTracking.quizzesPassed = (user.activityTracking.quizzesPassed || 0) + 1;
      } else {
        user.activityTracking.quizzesFailed = (user.activityTracking.quizzesFailed || 0) + 1;
      }

      user.activityTracking.totalQuestionsAnswered = (user.activityTracking.totalQuestionsAnswered || 0) + totalQuestions;
      user.activityTracking.totalCorrectAnswers = (user.activityTracking.totalCorrectAnswers || 0) + correctAnswers.length;

      // Update subject-specific performance
      await this.updateSubjectPerformance(user, examData.subject, scorePercentage, isPassed);

      // Update weekly and monthly stats
      this.updatePeriodStats(user);

      // Save user using findByIdAndUpdate to avoid validation issues
      await User.findByIdAndUpdate(user._id, {
        currentStreak: user.currentStreak,
        bestStreak: user.bestStreak,
        lastQuizDate: user.lastQuizDate,
        activityTracking: user.activityTracking,
        totalQuizzesTaken: user.totalQuizzesTaken,
        averageScore: user.averageScore
      }, { runValidators: false });

      return {
        quizCompletionStreak: user.activityTracking.quizCompletionStreak,
        quizPassStreak: user.currentStreak,
        perfectScoreStreak: user.activityTracking.perfectScoreStreak,
        newRecords: {
          quizCompletion: user.activityTracking.quizCompletionStreak === user.activityTracking.bestQuizStreak,
          quizPass: user.currentStreak === user.bestStreak,
          perfectScore: user.activityTracking.perfectScoreStreak === user.activityTracking.bestPerfectStreak
        }
      };

    } catch (error) {
      console.error('Error updating quiz streaks:', error);
      throw error;
    }
  }

  /**
   * Update subject-specific performance tracking
   */
  async updateSubjectPerformance(user, subject, score, passed) {
    if (!user.activityTracking.subjectPerformance) {
      user.activityTracking.subjectPerformance = [];
    }

    let subjectPerf = user.activityTracking.subjectPerformance.find(s => s.subject === subject);
    
    if (!subjectPerf) {
      subjectPerf = {
        subject: subject,
        quizzesTaken: 0,
        quizzesPassed: 0,
        averageScore: 0,
        totalXP: 0,
        streak: 0,
        bestStreak: 0,
        lastAttempt: new Date()
      };
      user.activityTracking.subjectPerformance.push(subjectPerf);
    }

    // Update subject stats
    const previousTotal = subjectPerf.averageScore * subjectPerf.quizzesTaken;
    subjectPerf.quizzesTaken += 1;
    subjectPerf.averageScore = Math.round((previousTotal + score) / subjectPerf.quizzesTaken);
    subjectPerf.lastAttempt = new Date();

    if (passed) {
      subjectPerf.quizzesPassed += 1;
      subjectPerf.streak += 1;
      subjectPerf.bestStreak = Math.max(subjectPerf.bestStreak, subjectPerf.streak);
    } else {
      subjectPerf.streak = 0;
    }
  }

  /**
   * Update weekly and monthly statistics
   */
  updatePeriodStats(user) {
    const now = new Date();
    const currentWeek = this.getWeekString(now);
    const currentMonth = this.getMonthString(now);

    // Initialize if needed
    if (!user.activityTracking.weeklyStats) {
      user.activityTracking.weeklyStats = {
        currentWeek: currentWeek,
        quizzesThisWeek: 0,
        xpThisWeek: 0,
        studyTimeThisWeek: 0
      };
    }

    if (!user.activityTracking.monthlyStats) {
      user.activityTracking.monthlyStats = {
        currentMonth: currentMonth,
        quizzesThisMonth: 0,
        xpThisMonth: 0,
        studyTimeThisMonth: 0
      };
    }

    // Reset weekly stats if new week
    if (user.activityTracking.weeklyStats.currentWeek !== currentWeek) {
      user.activityTracking.weeklyStats = {
        currentWeek: currentWeek,
        quizzesThisWeek: 1,
        xpThisWeek: 0,
        studyTimeThisWeek: 0
      };
    } else {
      user.activityTracking.weeklyStats.quizzesThisWeek += 1;
    }

    // Reset monthly stats if new month
    if (user.activityTracking.monthlyStats.currentMonth !== currentMonth) {
      user.activityTracking.monthlyStats = {
        currentMonth: currentMonth,
        quizzesThisMonth: 1,
        xpThisMonth: 0,
        studyTimeThisMonth: 0
      };
    } else {
      user.activityTracking.monthlyStats.quizzesThisMonth += 1;
    }
  }

  /**
   * Calculate streaks for existing users based on their report history
   */
  async calculateHistoricalStreaks(userId) {
    try {
      const user = await User.findById(userId);
      if (!user) throw new Error('User not found');

      // Get all user reports sorted by creation date
      const reports = await Report.find({ user: userId })
        .populate('exam', 'subject name')
        .sort({ createdAt: 1 });

      if (reports.length === 0) {
        return { message: 'No reports found for user' };
      }

      // Initialize activity tracking
      if (!user.activityTracking) {
        user.activityTracking = this.initializeActivityTracking();
      }

      // Calculate streaks from historical data
      let currentQuizStreak = 0;
      let bestQuizStreak = 0;
      let currentPerfectStreak = 0;
      let bestPerfectStreak = 0;
      let quizzesPassed = 0;
      let quizzesFailed = 0;
      let totalCorrect = 0;
      let totalQuestions = 0;

      // Subject performance tracking
      const subjectPerformance = new Map();

      for (const report of reports) {
        const { result } = report;
        const score = result.score || 0;
        const verdict = result.verdict;
        const correctAnswers = result.correctAnswers || [];
        const wrongAnswers = result.wrongAnswers || [];
        const totalQs = correctAnswers.length + wrongAnswers.length;

        totalCorrect += correctAnswers.length;
        totalQuestions += totalQs;

        // Quiz pass streak
        if (verdict === 'Pass') {
          currentQuizStreak += 1;
          bestQuizStreak = Math.max(bestQuizStreak, currentQuizStreak);
          quizzesPassed += 1;
        } else {
          currentQuizStreak = 0;
          quizzesFailed += 1;
        }

        // Perfect score streak
        if (score === 100) {
          currentPerfectStreak += 1;
          bestPerfectStreak = Math.max(bestPerfectStreak, currentPerfectStreak);
        } else {
          currentPerfectStreak = 0;
        }

        // Subject performance
        const subject = report.exam?.subject || 'Unknown';
        if (!subjectPerformance.has(subject)) {
          subjectPerformance.set(subject, {
            subject: subject,
            quizzesTaken: 0,
            quizzesPassed: 0,
            totalScore: 0,
            averageScore: 0,
            streak: 0,
            bestStreak: 0,
            lastAttempt: report.createdAt
          });
        }

        const subjectData = subjectPerformance.get(subject);
        subjectData.quizzesTaken += 1;
        subjectData.totalScore += score;
        subjectData.averageScore = Math.round(subjectData.totalScore / subjectData.quizzesTaken);
        subjectData.lastAttempt = report.createdAt;

        if (verdict === 'Pass') {
          subjectData.quizzesPassed += 1;
          subjectData.streak += 1;
          subjectData.bestStreak = Math.max(subjectData.bestStreak, subjectData.streak);
        } else {
          subjectData.streak = 0;
        }
      }

      // Estimate login streak based on performance (users with more points likely have better consistency)
      const estimatedLoginStreak = Math.min(
        Math.floor((user.totalPointsEarned || 0) / 200), // Rough estimate
        Math.floor(reports.length / 2) // Conservative estimate
      );

      // Update user activity tracking
      user.activityTracking.quizCompletionStreak = reports.length; // Total quizzes as completion streak
      user.activityTracking.bestQuizStreak = reports.length;
      user.activityTracking.perfectScoreStreak = currentPerfectStreak;
      user.activityTracking.bestPerfectStreak = bestPerfectStreak;
      user.activityTracking.dailyLoginStreak = estimatedLoginStreak;
      user.activityTracking.bestLoginStreak = Math.max(estimatedLoginStreak, bestQuizStreak);
      user.activityTracking.quizzesPassed = quizzesPassed;
      user.activityTracking.quizzesFailed = quizzesFailed;
      user.activityTracking.totalQuestionsAnswered = totalQuestions;
      user.activityTracking.totalCorrectAnswers = totalCorrect;
      user.activityTracking.subjectPerformance = Array.from(subjectPerformance.values());

      // Update legacy streak fields
      user.currentStreak = currentQuizStreak;
      user.bestStreak = bestQuizStreak;

      await user.save();

      return {
        success: true,
        streaksCalculated: {
          currentQuizPassStreak: currentQuizStreak,
          bestQuizPassStreak: bestQuizStreak,
          currentPerfectStreak: currentPerfectStreak,
          bestPerfectStreak: bestPerfectStreak,
          estimatedLoginStreak: estimatedLoginStreak,
          quizzesPassed: quizzesPassed,
          quizzesFailed: quizzesFailed,
          subjectsTracked: subjectPerformance.size
        }
      };

    } catch (error) {
      console.error('Error calculating historical streaks:', error);
      throw error;
    }
  }

  /**
   * Initialize activity tracking object
   */
  initializeActivityTracking() {
    return {
      dailyLoginStreak: 0,
      bestLoginStreak: 0,
      lastLoginDate: new Date(),
      totalLoginDays: 1,
      quizCompletionStreak: 0,
      bestQuizStreak: 0,
      perfectScoreStreak: 0,
      bestPerfectStreak: 0,
      totalStudyTimeMinutes: 0,
      averageStudySessionMinutes: 0,
      longestStudySessionMinutes: 0,
      quizzesPassed: 0,
      quizzesFailed: 0,
      totalQuestionsAnswered: 0,
      totalCorrectAnswers: 0,
      subjectPerformance: [],
      weeklyStats: {
        currentWeek: this.getWeekString(new Date()),
        quizzesThisWeek: 0,
        xpThisWeek: 0,
        studyTimeThisWeek: 0
      },
      monthlyStats: {
        currentMonth: this.getMonthString(new Date()),
        quizzesThisMonth: 0,
        xpThisMonth: 0,
        studyTimeThisMonth: 0
      }
    };
  }

  /**
   * Get week string in format "2024-W01"
   */
  getWeekString(date) {
    const year = date.getFullYear();
    const week = this.getWeekNumber(date);
    return `${year}-W${week.toString().padStart(2, '0')}`;
  }

  /**
   * Get month string in format "2024-01"
   */
  getMonthString(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    return `${year}-${month}`;
  }

  /**
   * Get week number of the year
   */
  getWeekNumber(date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  }
}

module.exports = new StreakTrackingService();
