.study-material-manager {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.manager-header {
  margin-bottom: 30px;
  text-align: center;
}

.manager-header h2 {
  color: #2c3e50;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

.manager-header p {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
}

/* Filters Section */
.filters-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.filters-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.filter-group label {
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

/* Materials Table */
.materials-table {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.materials-table .ant-table {
  border-radius: 12px;
}

.materials-table .ant-table-thead > tr > th {
  background: #f8f9fa;
  border-bottom: 2px solid #e9ecef;
  font-weight: 600;
  color: #2c3e50;
}

.materials-table .ant-table-tbody > tr:hover > td {
  background: #f8f9fa;
}

/* Material Info */
.material-info {
  display: flex;
  align-items: center;
}

.material-header {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.material-icon {
  font-size: 20px;
  padding: 8px;
  border-radius: 8px;
  background: #f8f9fa;
}

.material-icon.video {
  color: #e74c3c;
  background: #fdf2f2;
}

.material-icon.note {
  color: #3498db;
  background: #f2f8fd;
}

.material-icon.paper {
  color: #9b59b6;
  background: #f8f4fd;
}

.material-icon.book {
  color: #27ae60;
  background: #f2fdf6;
}

.material-details {
  flex: 1;
}

.material-title {
  font-weight: 600;
  color: #2c3e50;
  font-size: 16px;
  margin-bottom: 4px;
  line-height: 1.3;
}

.material-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.meta-text {
  color: #7f8c8d;
  font-size: 13px;
}

/* Class Badge */
.class-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

/* Action Buttons */
.materials-table .ant-btn {
  border-radius: 6px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.materials-table .ant-btn-primary {
  background: #3498db;
  border-color: #3498db;
}

.materials-table .ant-btn-primary:hover {
  background: #2980b9;
  border-color: #2980b9;
}

.materials-table .ant-btn-dangerous {
  background: #e74c3c;
  border-color: #e74c3c;
  color: white;
}

.materials-table .ant-btn-dangerous:hover {
  background: #c0392b;
  border-color: #c0392b;
}

/* Tags */
.ant-tag {
  border-radius: 6px;
  font-weight: 500;
  font-size: 11px;
  padding: 2px 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .study-material-manager {
    padding: 15px;
  }

  .filters-row {
    flex-direction: column;
    gap: 15px;
  }

  .filter-group {
    width: 100%;
  }

  .filter-group .ant-select,
  .filter-group .ant-input {
    width: 100% !important;
  }

  .material-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .material-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .materials-table .ant-table {
    font-size: 12px;
  }

  .material-title {
    font-size: 14px;
  }
}

/* Loading States */
.ant-table-placeholder {
  padding: 40px;
  text-align: center;
  color: #7f8c8d;
}

.ant-spin-container {
  min-height: 200px;
}

/* Empty State */
.ant-empty {
  padding: 40px;
}

.ant-empty-description {
  color: #7f8c8d;
  font-size: 14px;
}

/* Pagination */
.ant-pagination {
  margin-top: 20px;
  text-align: center;
}

.ant-pagination-total-text {
  color: #7f8c8d;
  font-size: 14px;
}

/* Modal Styles */
.ant-modal-confirm-title {
  color: #2c3e50;
  font-weight: 600;
}

.ant-modal-confirm-content {
  color: #7f8c8d;
  margin-top: 8px;
}

/* Search Input */
.ant-input-search {
  border-radius: 6px;
}

.ant-input-search .ant-input {
  border-radius: 6px 0 0 6px;
}

.ant-input-search .ant-input-search-button {
  border-radius: 0 6px 6px 0;
}

/* Select Dropdown */
.ant-select {
  border-radius: 6px;
}

.ant-select-selector {
  border-radius: 6px !important;
}

/* Tooltip */
.ant-tooltip-inner {
  background: #2c3e50;
  border-radius: 6px;
}

.ant-tooltip-arrow-content {
  background: #2c3e50;
}
