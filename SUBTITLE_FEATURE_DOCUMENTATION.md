# Video Subtitle Generation Feature

## Overview
This feature adds comprehensive subtitle support to the educational platform, enabling automatic subtitle generation using OpenAI's Whisper API and custom subtitle upload functionality. This greatly enhances learning accessibility and comprehension for students.

## Features Implemented

### 1. **Automatic Subtitle Generation**
- **AI-Powered**: Uses OpenAI Whisper API for high-quality speech-to-text conversion
- **Multiple Languages**: Supports 20+ languages including English, Spanish, French, German, Arabic, Hindi, Urdu, Bengali, and more
- **Auto-Processing**: Automatically generates English subtitles when videos are uploaded
- **S3 Integration**: Stores subtitle files in AWS S3 with proper caching and public access

### 2. **Custom Subtitle Upload**
- **Admin Control**: Administrators can upload custom SRT subtitle files
- **Multi-Language Support**: Support for multiple subtitle tracks per video
- **Format Validation**: Validates SRT format before upload
- **Override Capability**: Custom subtitles can override auto-generated ones

### 3. **Enhanced Video Player**
- **Native HTML5 Support**: Uses browser's native subtitle rendering
- **Multiple Tracks**: Displays all available subtitle languages
- **Default Language**: Automatically selects default subtitle track
- **User Control**: Students can enable/disable subtitles and switch languages

### 4. **Admin Management Interface**
- **Subtitle Manager**: Dedicated admin panel for subtitle management
- **Status Tracking**: Shows subtitle generation status for all videos
- **Bulk Operations**: Generate subtitles for multiple videos
- **Language Management**: Add/remove subtitle tracks per video

## Technical Implementation

### Backend Components

#### 1. **Database Schema Updates**
```javascript
// Added to studyVideos.js model
subtitles: [{
  language: String,        // Language code (e.g., 'en', 'es')
  languageName: String,    // Display name (e.g., 'English', 'Spanish')
  url: String,            // S3 URL to subtitle file
  isDefault: Boolean,     // Whether this is the default track
  isAutoGenerated: Boolean, // Whether generated by AI
  createdAt: Date
}],
hasSubtitles: Boolean,
subtitleGenerationStatus: String // 'pending', 'processing', 'completed', 'failed'
```

#### 2. **Subtitle Service** (`server/services/subtitleService.js`)
- **Audio Extraction**: Uses FFmpeg to extract audio from video files
- **Whisper Integration**: Calls OpenAI Whisper API for transcription
- **File Management**: Handles S3 upload/download operations
- **Format Conversion**: Converts between SRT and VTT formats
- **Validation**: Validates subtitle file formats

#### 3. **API Endpoints**
- `POST /api/study/generate-subtitles/:videoId` - Generate subtitles using AI
- `POST /api/study/upload-subtitle/:videoId` - Upload custom subtitle file
- `GET /api/study/video/:videoId` - Get video with subtitle information
- `DELETE /api/study/subtitle/:videoId/:language` - Delete specific subtitle
- `GET /api/study/videos-subtitle-status` - Get all videos with subtitle status

### Frontend Components

#### 1. **Enhanced Video Player**
- Added `crossOrigin="anonymous"` for subtitle support
- Dynamic `<track>` elements for each subtitle language
- Subtitle status indicators
- Processing status display

#### 2. **Admin Subtitle Manager** (`client/src/pages/admin/StudyMaterials/SubtitleManager.js`)
- Table view of all videos with subtitle status
- Generate subtitles button for S3 videos
- Upload custom subtitle files
- Language selection and management
- Real-time status updates

#### 3. **API Integration** (`client/src/apicalls/subtitles.js`)
- Subtitle generation requests
- File upload handling
- Status polling
- Error handling

## Usage Instructions

### For Administrators

#### 1. **Automatic Subtitle Generation**
1. Navigate to Admin → Study Materials → Subtitle Management
2. Find videos with "S3 Video" source type
3. Click "Generate" button next to the video
4. Wait for processing to complete (may take several minutes)
5. Subtitles will appear in the video player automatically

#### 2. **Upload Custom Subtitles**
1. In Subtitle Management, click "Upload" for any video
2. Select the target language from dropdown
3. Upload a valid SRT subtitle file
4. Click "Upload" to save
5. Custom subtitles will override auto-generated ones

#### 3. **Managing Subtitles**
- View subtitle status for all videos in one place
- See which subtitles are auto-generated vs custom
- Monitor generation progress
- Delete unwanted subtitle tracks

### For Students

#### 1. **Using Subtitles**
1. Open any video in Study Materials
2. Click the CC (closed captions) button in video controls
3. Select desired language if multiple options available
4. Subtitles will display automatically
5. Can be toggled on/off as needed

#### 2. **Subtitle Indicators**
- Videos with subtitles show "📝 Subtitles available" below video
- Language badges indicate available subtitle languages
- "(Auto)" indicates AI-generated subtitles
- Processing status shows when subtitles are being generated

## Configuration Requirements

### Environment Variables
```bash
# Required for subtitle generation
OPENAI_API_KEY=your_openai_api_key_here

# AWS S3 configuration (already required for video storage)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=your_aws_region
AWS_S3_BUCKET_NAME=your_s3_bucket_name
```

### Dependencies
- `form-data` - For multipart form uploads to OpenAI API
- `fluent-ffmpeg` - For audio extraction (already installed)
- `aws-sdk` - For S3 operations (already installed)
- `axios` - For HTTP requests (already installed)

## Supported Languages

The system supports subtitle generation and upload for:
- English (en)
- Spanish (es)
- French (fr)
- German (de)
- Italian (it)
- Portuguese (pt)
- Russian (ru)
- Japanese (ja)
- Korean (ko)
- Chinese (zh)
- Arabic (ar)
- Hindi (hi)
- Urdu (ur)
- Bengali (bn)
- Tamil (ta)
- Telugu (te)
- Malayalam (ml)
- Kannada (kn)
- Gujarati (gu)
- Punjabi (pa)
- Marathi (mr)
- Nepali (ne)
- Sinhala (si)

## File Formats

### Supported Input
- **Video**: MP4, AVI, MKV, WMV (for audio extraction)
- **Subtitle Upload**: SRT format only

### Generated Output
- **Storage**: SRT format in S3
- **Player**: Automatic conversion to VTT for browser compatibility

## Performance Considerations

### Subtitle Generation
- **Processing Time**: 2-5 minutes for typical educational videos
- **File Size**: Audio extraction creates temporary files (~10% of video size)
- **API Limits**: OpenAI Whisper has file size limits (25MB audio)
- **Async Processing**: Generation happens in background, doesn't block video upload

### Storage
- **S3 Costs**: Subtitle files are small (typically <50KB)
- **Caching**: 1-year cache headers for subtitle files
- **Public Access**: Subtitle files are publicly readable for browser access

## Error Handling

### Common Issues
1. **Audio Extraction Fails**: Video format not supported by FFmpeg
2. **Whisper API Fails**: File too large, API quota exceeded, or network issues
3. **S3 Upload Fails**: Permissions or network connectivity issues
4. **Invalid SRT**: Uploaded subtitle file doesn't match SRT format

### Recovery Mechanisms
- Failed generations can be retried manually
- Status tracking prevents duplicate processing
- Graceful fallback to original video without subtitles
- Detailed error logging for troubleshooting

## Future Enhancements

### Potential Improvements
1. **Batch Processing**: Generate subtitles for multiple videos simultaneously
2. **Translation**: Auto-translate subtitles to multiple languages
3. **Editing Interface**: In-browser subtitle editing capabilities
4. **Synchronization**: Auto-sync subtitle timing with video
5. **Analytics**: Track subtitle usage and effectiveness
6. **Quality Control**: AI-powered subtitle quality assessment

## Testing

### Manual Testing
1. Upload a video through admin panel
2. Verify automatic subtitle generation starts
3. Check subtitle appears in video player
4. Test custom subtitle upload
5. Verify multiple language support
6. Test subtitle deletion

### Automated Testing
- Run `node test-subtitle.js` to test core subtitle service functions
- Validates language mapping, SRT validation, and format conversion

## Troubleshooting

### Common Problems
1. **Subtitles not generating**: Check OpenAI API key and quota
2. **Player not showing subtitles**: Verify CORS settings and file accessibility
3. **Upload fails**: Check file format and size limits
4. **Audio extraction fails**: Verify FFmpeg installation and video format

### Debug Steps
1. Check server logs for detailed error messages
2. Verify S3 bucket permissions and CORS configuration
3. Test OpenAI API connectivity manually
4. Validate video file integrity and format

This comprehensive subtitle feature significantly enhances the educational platform's accessibility and learning effectiveness by providing high-quality, multi-language subtitle support for all educational videos.
