const mongoose = require('mongoose');
const User = require('./models/userModel');
const xpRankingService = require('./services/xpRankingService');

// Connect to MongoDB
async function connectDB() {
    try {
        await mongoose.connect(process.env.MONGO_URL || 'mongodb+srv://brainwave:<EMAIL>/brainwave');
        console.log('✅ MongoDB Connected Successfully');
    } catch (error) {
        console.error('❌ MongoDB Connection Failed:', error);
        process.exit(1);
    }
}

// Test the ranking system
async function testRankingSystem() {
    console.log('\n🧪 Testing Ranking System...\n');

    try {
        // Test 1: Get XP Leaderboard
        console.log('📊 Test 1: Getting XP Leaderboard...');
        const leaderboard = await xpRankingService.getXPLeaderboard({ limit: 10 });
        
        if (leaderboard.success) {
            console.log('✅ XP Leaderboard retrieved successfully');
            console.log(`📈 Total users in leaderboard: ${leaderboard.data.length}`);
            
            if (leaderboard.data.length > 0) {
                const topUser = leaderboard.data[0];
                console.log('🏆 Top user:', {
                    name: topUser.name,
                    rank: topUser.rank,
                    totalXP: topUser.totalXP,
                    rankingScore: topUser.rankingScore,
                    subscriptionStatus: topUser.subscriptionStatus
                });
                
                // Show top 3 users
                console.log('\n🥇 Top 3 Users:');
                leaderboard.data.slice(0, 3).forEach((user, index) => {
                    console.log(`${index + 1}. ${user.name} - ${user.rankingScore} pts (${user.totalXP} XP)`);
                });
            }
        } else {
            console.log('❌ Failed to get XP leaderboard:', leaderboard.error);
        }

        // Test 2: Get User Statistics
        console.log('\n📊 Test 2: Getting User Statistics...');
        const users = await User.find({ isAdmin: false }).limit(5);
        
        if (users.length > 0) {
            console.log(`✅ Found ${users.length} users for testing`);
            
            for (const user of users) {
                console.log(`\n👤 User: ${user.name}`);
                console.log(`   - Total XP: ${user.totalXP || 0}`);
                console.log(`   - Current Level: ${user.currentLevel || 1}`);
                console.log(`   - Quizzes Taken: ${user.totalQuizzesTaken || 0}`);
                console.log(`   - Average Score: ${user.averageScore || 0}%`);
                console.log(`   - Subscription: ${user.subscriptionStatus || 'free'}`);
                console.log(`   - Achievements: ${user.achievements ? user.achievements.length : 0}`);
            }
        } else {
            console.log('❌ No users found for testing');
        }

        // Test 3: Test User Ranking Position
        if (users.length > 0) {
            console.log('\n📍 Test 3: Getting User Ranking Position...');
            const testUser = users[0];
            const ranking = await xpRankingService.getUserRankingPosition(testUser._id, 2);
            
            if (ranking.success) {
                console.log('✅ User ranking retrieved successfully');
                console.log(`🎯 User ${testUser.name} is ranked #${ranking.userRank} out of ${ranking.totalUsers}`);
                
                if (ranking.nearbyUsers && ranking.nearbyUsers.length > 0) {
                    console.log('\n👥 Nearby users:');
                    ranking.nearbyUsers.forEach(user => {
                        console.log(`   ${user.rank}. ${user.name} - ${user.rankingScore} pts`);
                    });
                }
            } else {
                console.log('❌ Failed to get user ranking:', ranking.error);
            }
        }

        // Test 4: Test Class Rankings
        console.log('\n🏫 Test 4: Testing Class Rankings...');
        const classRankings = await xpRankingService.getClassRankings('7', 5);
        
        if (classRankings.success) {
            console.log('✅ Class rankings retrieved successfully');
            console.log(`📚 Class 7 has ${classRankings.data.length} users`);
            
            if (classRankings.data.length > 0) {
                console.log('\n🏆 Top users in Class 7:');
                classRankings.data.slice(0, 3).forEach((user, index) => {
                    console.log(`   ${index + 1}. ${user.name} - ${user.rankingScore} pts`);
                });
            }
        } else {
            console.log('❌ Failed to get class rankings:', classRankings.error);
        }

        // Test 5: Validate Data Consistency
        console.log('\n🔍 Test 5: Validating Data Consistency...');
        const allUsers = await User.find({ isAdmin: false, totalQuizzesTaken: { $gt: 0 } }).limit(20);
        
        let validUsers = 0;
        let usersWithXP = 0;
        let usersWithAchievements = 0;
        
        for (const user of allUsers) {
            validUsers++;
            
            if (user.totalXP > 0) {
                usersWithXP++;
            }
            
            if (user.achievements && user.achievements.length > 0) {
                usersWithAchievements++;
            }
        }
        
        console.log(`✅ Data consistency check completed:`);
        console.log(`   - Total active users: ${validUsers}`);
        console.log(`   - Users with XP: ${usersWithXP} (${Math.round((usersWithXP/validUsers)*100)}%)`);
        console.log(`   - Users with achievements: ${usersWithAchievements} (${Math.round((usersWithAchievements/validUsers)*100)}%)`);

        console.log('\n🎉 All ranking system tests completed successfully!');

    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Main function
async function main() {
    await connectDB();
    await testRankingSystem();
    
    console.log('\n✅ Test completed. Closing connection...');
    await mongoose.connection.close();
    process.exit(0);
}

// Run the test
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Test script failed:', error);
        process.exit(1);
    });
}

module.exports = { testRankingSystem };
