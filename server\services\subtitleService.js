const axios = require("axios");
const FormData = require("form-data");
const AWS = require("aws-sdk");
const ffmpeg = require("fluent-ffmpeg");
const ffmpegStatic = require("ffmpeg-static");
const { v4: uuidv4 } = require("uuid");
const fs = require("fs");
const path = require("path");
const os = require("os");
require("dotenv").config();

// Configure FFmpeg
ffmpeg.setFfmpegPath(ffmpegStatic);

// Configure AWS S3
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
});

class SubtitleService {
  constructor() {
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.tempDir = os.tmpdir();
  }

  /**
   * Extract audio from video buffer
   * @param {Buffer} videoBuffer - Video file buffer
   * @param {string} outputPath - Output audio file path
   * @returns {Promise<string>} - Path to extracted audio file
   */
  async extractAudioFromVideo(videoBuffer, outputPath) {
    return new Promise((resolve, reject) => {
      const tempVideoPath = path.join(this.tempDir, `temp_video_${uuidv4()}.mp4`);
      
      try {
        // Write video buffer to temporary file
        fs.writeFileSync(tempVideoPath, videoBuffer);

        ffmpeg(tempVideoPath)
          .audioCodec('pcm_s16le') // Use PCM codec which is widely supported
          .audioFrequency(16000) // Whisper works best with 16kHz
          .audioChannels(1) // Mono audio
          .format('wav') // Use WAV format instead of MP3
          .output(outputPath)
          .on('end', () => {
            // Clean up temp video file
            try {
              fs.unlinkSync(tempVideoPath);
            } catch (cleanupError) {
              console.warn('Failed to cleanup temp video file:', cleanupError.message);
            }
            resolve(outputPath);
          })
          .on('error', (error) => {
            // Clean up temp video file
            try {
              fs.unlinkSync(tempVideoPath);
            } catch (cleanupError) {
              console.warn('Failed to cleanup temp video file:', cleanupError.message);
            }
            reject(new Error(`Audio extraction failed: ${error.message}`));
          })
          .run();
      } catch (error) {
        reject(new Error(`Failed to process video: ${error.message}`));
      }
    });
  }

  /**
   * Generate subtitles using OpenAI Whisper API
   * @param {string} audioFilePath - Path to audio file
   * @param {string} language - Language code (optional, auto-detect if not provided)
   * @returns {Promise<string>} - SRT subtitle content
   */
  async generateSubtitlesWithWhisper(audioFilePath, language = null) {
    try {
      const formData = new FormData();
      formData.append('file', fs.createReadStream(audioFilePath));
      formData.append('model', 'whisper-1');
      formData.append('response_format', 'srt');
      
      if (language) {
        formData.append('language', language);
      }

      const response = await axios.post(
        'https://api.openai.com/v1/audio/transcriptions',
        formData,
        {
          headers: {
            'Authorization': `Bearer ${this.openaiApiKey}`,
            ...formData.getHeaders(),
          },
          maxContentLength: Infinity,
          maxBodyLength: Infinity,
        }
      );

      return response.data;
    } catch (error) {
      console.error('Whisper API error:', error.response?.data || error.message);
      throw new Error(`Subtitle generation failed: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * Upload subtitle file to S3
   * @param {string} subtitleContent - SRT subtitle content
   * @param {string} filename - S3 filename
   * @returns {Promise<string>} - S3 URL
   */
  async uploadSubtitleToS3(subtitleContent, filename) {
    try {
      const params = {
        Bucket: process.env.AWS_S3_BUCKET_NAME,
        Key: filename,
        Body: subtitleContent,
        ContentType: 'text/plain',
        ACL: 'public-read',
        CacheControl: 'max-age=31536000', // Cache for 1 year
        Metadata: {
          'content-type': 'subtitle/srt',
          'generated-by': 'openai-whisper'
        }
      };

      const s3Response = await s3.upload(params).promise();
      return s3Response.Location;
    } catch (error) {
      throw new Error(`Failed to upload subtitle to S3: ${error.message}`);
    }
  }

  /**
   * Generate subtitles from video buffer
   * @param {Buffer} videoBuffer - Video file buffer
   * @param {string} videoId - Video ID for naming
   * @param {string} language - Language code (optional)
   * @returns {Promise<Object>} - Subtitle data with URL and metadata
   */
  async generateSubtitlesFromVideo(videoBuffer, videoId, language = 'en') {
    const audioPath = path.join(this.tempDir, `audio_${uuidv4()}.wav`);
    
    try {
      console.log('🎵 Extracting audio from video...');
      await this.extractAudioFromVideo(videoBuffer, audioPath);

      console.log('🤖 Generating subtitles with Whisper...');
      const subtitleContent = await this.generateSubtitlesWithWhisper(audioPath, language);

      console.log('☁️ Uploading subtitles to S3...');
      const subtitleFilename = `StudyMaterials/Subtitles/${videoId}_${language}.srt`;
      const subtitleUrl = await this.uploadSubtitleToS3(subtitleContent, subtitleFilename);

      // Clean up audio file
      try {
        fs.unlinkSync(audioPath);
      } catch (cleanupError) {
        console.warn('Failed to cleanup audio file:', cleanupError.message);
      }

      return {
        language: language,
        languageName: this.getLanguageName(language),
        url: subtitleUrl,
        isDefault: language === 'en',
        isAutoGenerated: true,
        content: subtitleContent
      };
    } catch (error) {
      // Clean up audio file in case of error
      try {
        fs.unlinkSync(audioPath);
      } catch (cleanupError) {
        console.warn('Failed to cleanup audio file:', cleanupError.message);
      }
      throw error;
    }
  }

  /**
   * Generate subtitles from S3 video URL
   * @param {string} videoUrl - S3 video URL
   * @param {string} videoId - Video ID for naming
   * @param {string} language - Language code (optional)
   * @returns {Promise<Object>} - Subtitle data with URL and metadata
   */
  async generateSubtitlesFromS3Video(videoUrl, videoId, language = 'en') {
    try {
      console.log('📥 Downloading video from S3...');
      const response = await axios.get(videoUrl, { responseType: 'arraybuffer' });
      const videoBuffer = Buffer.from(response.data);

      return await this.generateSubtitlesFromVideo(videoBuffer, videoId, language);
    } catch (error) {
      throw new Error(`Failed to process S3 video: ${error.message}`);
    }
  }

  /**
   * Get language name from language code
   * @param {string} languageCode - Language code
   * @returns {string} - Language name
   */
  getLanguageName(languageCode) {
    const languageMap = {
      'en': 'English',
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'ru': 'Russian',
      'ja': 'Japanese',
      'ko': 'Korean',
      'zh': 'Chinese',
      'ar': 'Arabic',
      'hi': 'Hindi',
      'ur': 'Urdu',
      'bn': 'Bengali',
      'ta': 'Tamil',
      'te': 'Telugu',
      'ml': 'Malayalam',
      'kn': 'Kannada',
      'gu': 'Gujarati',
      'pa': 'Punjabi',
      'mr': 'Marathi',
      'ne': 'Nepali',
      'si': 'Sinhala'
    };
    
    return languageMap[languageCode] || languageCode.toUpperCase();
  }

  /**
   * Validate SRT subtitle format
   * @param {string} srtContent - SRT subtitle content
   * @returns {boolean} - Whether the SRT is valid
   */
  validateSRTFormat(srtContent) {
    try {
      // Basic SRT format validation
      const srtPattern = /^\d+\s*\n\d{2}:\d{2}:\d{2},\d{3}\s*-->\s*\d{2}:\d{2}:\d{2},\d{3}\s*\n[\s\S]*?\n\s*$/m;
      return srtPattern.test(srtContent.trim());
    } catch (error) {
      return false;
    }
  }

  /**
   * Convert SRT to VTT format
   * @param {string} srtContent - SRT subtitle content
   * @returns {string} - VTT subtitle content
   */
  convertSRTtoVTT(srtContent) {
    let vttContent = 'WEBVTT\n\n';
    
    // Replace SRT timestamp format with VTT format
    vttContent += srtContent.replace(/(\d{2}):(\d{2}):(\d{2}),(\d{3})/g, '$1:$2:$3.$4');
    
    return vttContent;
  }
}

module.exports = new SubtitleService();
