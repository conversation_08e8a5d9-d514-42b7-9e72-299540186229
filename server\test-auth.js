const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const User = require('./models/userModel');
require('dotenv').config();

async function testAuth() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    // Find a test user
    const user = await User.findOne({}).limit(1);
    if (!user) {
      console.log('❌ No users found in database');
      return;
    }
    
    console.log('✅ Found user:', user.email);
    console.log('   User ID:', user._id);
    console.log('   User level:', user.level);
    console.log('   User class:', user.className);
    
    // Generate a test token
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '1d' });
    console.log('✅ Generated token:', token.substring(0, 50) + '...');
    
    // Test token verification
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log('✅ Token verified, userId:', decoded.userId);
    
    // Test API call with token
    const axios = require('axios');
    try {
      const response = await axios.post('http://localhost:5000/api/study/get-study-content', {
        content: 'videos',
        className: user.className || '7',
        subject: 'Science and Technology',
        level: user.level || 'primary'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('✅ API call successful');
      console.log('   Videos found:', response.data.data.length);
      if (response.data.data.length > 0) {
        console.log('   First video:', response.data.data[0].title);
      }
    } catch (apiError) {
      console.error('❌ API call failed:', apiError.response?.data || apiError.message);
    }
    
    await mongoose.connection.close();
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testAuth();
