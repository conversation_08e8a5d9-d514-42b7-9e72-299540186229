{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizStart.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { Loading } from '../../../components/modern';\nimport { TbClock, TbQuestionMark, TbTrophy, TbPlayerPlay, TbBrain } from 'react-icons/tb';\nimport './responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizStart = () => {\n  _s();\n  var _examData$questions, _user$name, _user$name$charAt;\n  const [examData, setExamData] = useState(null);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({\n          examId: id\n        });\n        dispatch(HideLoading());\n        if (response.success) {\n          setExamData(response.data);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  const handleStartQuiz = () => {\n    navigate(`/quiz/${id}/play`);\n  };\n  if (!examData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(Loading, {\n        fullScreen: true,\n        text: \"Loading quiz details...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 flex items-center justify-center p-3 sm:p-4 lg:p-6 relative overflow-y-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -top-20 -right-20 sm:-top-40 sm:-right-40 w-40 h-40 sm:w-80 sm:h-80 bg-white/10 rounded-full blur-3xl animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -bottom-20 -left-20 sm:-bottom-40 sm:-left-40 w-40 h-40 sm:w-80 sm:h-80 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 sm:w-96 sm:h-96 bg-white/5 rounded-full blur-3xl animate-pulse delay-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 30,\n        scale: 0.9\n      },\n      animate: {\n        opacity: 1,\n        y: 0,\n        scale: 1\n      },\n      transition: {\n        duration: 0.8,\n        ease: \"easeOut\"\n      },\n      className: \"relative z-10 max-w-2xl w-full mx-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/95 backdrop-blur-xl rounded-2xl sm:rounded-3xl shadow-2xl border border-white/20 overflow-hidden quiz-start-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 p-4 sm:p-6 lg:p-8 text-center relative overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              scale: 0\n            },\n            animate: {\n              scale: 1\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            className: \"relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-white/20 rounded-full mb-4 sm:mb-6 backdrop-blur-sm shadow-xl\",\n              children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                className: \"w-8 h-8 sm:w-10 sm:h-10 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl sm:text-3xl lg:text-4xl font-black text-white mb-3 sm:mb-4 leading-tight px-2\",\n              style: {\n                textShadow: '2px 2px 4px rgba(0,0,0,0.3)'\n              },\n              children: examData.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-base sm:text-lg text-white/90 font-medium leading-relaxed px-2\",\n              style: {\n                textShadow: '1px 1px 2px rgba(0,0,0,0.3)'\n              },\n              children: \"Challenge your brain, Beat the rest! \\uD83E\\uDDE0\\u2728\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 sm:p-6 lg:p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.3\n              },\n              className: \"text-center p-3 sm:p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl sm:rounded-2xl border border-blue-100\",\n              children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 text-blue-600 mx-auto mb-2 sm:mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl sm:text-2xl font-black text-gray-800 mb-1\",\n                children: ((_examData$questions = examData.questions) === null || _examData$questions === void 0 ? void 0 : _examData$questions.length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs sm:text-sm font-semibold text-gray-600\",\n                children: \"Questions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.4\n              },\n              className: \"text-center p-3 sm:p-4 bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl sm:rounded-2xl border border-emerald-100\",\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 text-emerald-600 mx-auto mb-2 sm:mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl sm:text-2xl font-black text-gray-800 mb-1\",\n                children: examData.duration || 30\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs sm:text-sm font-semibold text-gray-600\",\n                children: \"Minutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.5\n              },\n              className: \"text-center p-3 sm:p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl sm:rounded-2xl border border-purple-100\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 text-purple-600 mx-auto mb-2 sm:mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl sm:text-2xl font-black text-gray-800 mb-1\",\n                children: [examData.passingPercentage || 70, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs sm:text-sm font-semibold text-gray-600\",\n                children: \"Pass Mark\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.6\n            },\n            className: \"bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8 border border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 sm:space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-lg sm:text-xl\",\n                  children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || 'U'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"min-w-0 flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-gray-900 text-base sm:text-lg truncate\",\n                  children: [\"Welcome, \", (user === null || user === void 0 ? void 0 : user.name) || 'Student', \"!\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col sm:flex-row gap-2 mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm bg-blue-100 text-blue-800\",\n                    children: (user === null || user === void 0 ? void 0 : user.level) || 'Primary'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm bg-emerald-100 text-emerald-800\",\n                    children: [\"Class \", (user === null || user === void 0 ? void 0 : user.class) || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.7\n            },\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleStartQuiz,\n              className: \"group relative inline-flex items-center justify-center w-full sm:w-auto px-8 sm:px-12 py-3 sm:py-4 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white font-bold text-base sm:text-lg rounded-xl sm:rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 overflow-hidden touch-manipulation\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TbPlayerPlay, {\n                className: \"w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 group-hover:scale-110 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"relative z-10\",\n                children: \"Start Quiz\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-3 sm:mt-4 text-gray-500 text-xs sm:text-sm font-medium px-4\",\n              children: \"Ready to test your knowledge? Let's begin! \\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizStart, \"+k6XuQiozenodmM12ysuBxR6GPY=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector];\n});\n_c = QuizStart;\nexport default QuizStart;\nvar _c;\n$RefreshReg$(_c, \"QuizStart\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useDispatch", "useSelector", "motion", "message", "getExamById", "HideLoading", "ShowLoading", "Loading", "TbClock", "TbQuestionMark", "TbTrophy", "TbPlayerPlay", "TbBrain", "jsxDEV", "_jsxDEV", "QuizStart", "_s", "_examData$questions", "_user$name", "_user$name$charAt", "examData", "setExamData", "id", "navigate", "dispatch", "user", "state", "fetchExamData", "response", "examId", "success", "data", "error", "document", "body", "classList", "add", "remove", "handleStartQuiz", "className", "children", "fullScreen", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "scale", "animate", "transition", "duration", "ease", "delay", "style", "textShadow", "name", "questions", "length", "passingPercentage", "char<PERSON>t", "toUpperCase", "level", "class", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizStart.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { Loading } from '../../../components/modern';\nimport {\n  TbClock,\n  TbQuestionMark,\n  TbTrophy,\n  TbPlayerPlay,\n  TbBrain\n} from 'react-icons/tb';\nimport './responsive.css';\n\nconst QuizStart = () => {\n  const [examData, setExamData] = useState(null);\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({ examId: id });\n        dispatch(HideLoading());\n\n        if (response.success) {\n          setExamData(response.data);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  const handleStartQuiz = () => {\n    navigate(`/quiz/${id}/play`);\n  };\n\n  if (!examData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center\">\n        <Loading fullScreen text=\"Loading quiz details...\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 flex items-center justify-center p-3 sm:p-4 lg:p-6 relative overflow-y-auto\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-20 -right-20 sm:-top-40 sm:-right-40 w-40 h-40 sm:w-80 sm:h-80 bg-white/10 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute -bottom-20 -left-20 sm:-bottom-40 sm:-left-40 w-40 h-40 sm:w-80 sm:h-80 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 sm:w-96 sm:h-96 bg-white/5 rounded-full blur-3xl animate-pulse delay-500\"></div>\n      </div>\n\n      {/* Main Content Card */}\n      <motion.div\n        initial={{ opacity: 0, y: 30, scale: 0.9 }}\n        animate={{ opacity: 1, y: 0, scale: 1 }}\n        transition={{ duration: 0.8, ease: \"easeOut\" }}\n        className=\"relative z-10 max-w-2xl w-full mx-auto\"\n      >\n        <div className=\"bg-white/95 backdrop-blur-xl rounded-2xl sm:rounded-3xl shadow-2xl border border-white/20 overflow-hidden quiz-start-card\">\n          {/* Header Section */}\n          <div className=\"bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 p-4 sm:p-6 lg:p-8 text-center relative overflow-hidden\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent\"></div>\n            <motion.div\n              initial={{ scale: 0 }}\n              animate={{ scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              className=\"relative z-10\"\n            >\n              <div className=\"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-white/20 rounded-full mb-4 sm:mb-6 backdrop-blur-sm shadow-xl\">\n                <TbBrain className=\"w-8 h-8 sm:w-10 sm:h-10 text-white\" />\n              </div>\n              <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-black text-white mb-3 sm:mb-4 leading-tight px-2\" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.3)' }}>\n                {examData.name}\n              </h1>\n              <p className=\"text-base sm:text-lg text-white/90 font-medium leading-relaxed px-2\" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.3)' }}>\n                Challenge your brain, Beat the rest! 🧠✨\n              </p>\n            </motion.div>\n          </div>\n\n          {/* Quiz Stats */}\n          <div className=\"p-4 sm:p-6 lg:p-8\">\n            <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n                className=\"text-center p-3 sm:p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl sm:rounded-2xl border border-blue-100\"\n              >\n                <TbQuestionMark className=\"w-6 h-6 sm:w-8 sm:h-8 text-blue-600 mx-auto mb-2 sm:mb-3\" />\n                <div className=\"text-xl sm:text-2xl font-black text-gray-800 mb-1\">{examData.questions?.length || 0}</div>\n                <div className=\"text-xs sm:text-sm font-semibold text-gray-600\">Questions</div>\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.4 }}\n                className=\"text-center p-3 sm:p-4 bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl sm:rounded-2xl border border-emerald-100\"\n              >\n                <TbClock className=\"w-6 h-6 sm:w-8 sm:h-8 text-emerald-600 mx-auto mb-2 sm:mb-3\" />\n                <div className=\"text-xl sm:text-2xl font-black text-gray-800 mb-1\">{examData.duration || 30}</div>\n                <div className=\"text-xs sm:text-sm font-semibold text-gray-600\">Minutes</div>\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.5 }}\n                className=\"text-center p-3 sm:p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl sm:rounded-2xl border border-purple-100\"\n              >\n                <TbTrophy className=\"w-6 h-6 sm:w-8 sm:h-8 text-purple-600 mx-auto mb-2 sm:mb-3\" />\n                <div className=\"text-xl sm:text-2xl font-black text-gray-800 mb-1\">{examData.passingPercentage || 70}%</div>\n                <div className=\"text-xs sm:text-sm font-semibold text-gray-600\">Pass Mark</div>\n              </motion.div>\n            </div>\n\n            {/* User Info Section */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6 }}\n              className=\"bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8 border border-gray-200\"\n            >\n              <div className=\"flex items-center space-x-3 sm:space-x-4\">\n                <div className=\"w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg flex-shrink-0\">\n                  <span className=\"text-white font-bold text-lg sm:text-xl\">\n                    {user?.name?.charAt(0)?.toUpperCase() || 'U'}\n                  </span>\n                </div>\n                <div className=\"min-w-0 flex-1\">\n                  <h3 className=\"font-bold text-gray-900 text-base sm:text-lg truncate\">Welcome, {user?.name || 'Student'}!</h3>\n                  <div className=\"flex flex-col sm:flex-row gap-2 mt-1\">\n                    <span className=\"inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm bg-blue-100 text-blue-800\">\n                      {user?.level || 'Primary'}\n                    </span>\n                    <span className=\"inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm bg-emerald-100 text-emerald-800\">\n                      Class {user?.class || 'N/A'}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Action Button */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.7 }}\n              className=\"text-center\"\n            >\n              <button\n                onClick={handleStartQuiz}\n                className=\"group relative inline-flex items-center justify-center w-full sm:w-auto px-8 sm:px-12 py-3 sm:py-4 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white font-bold text-base sm:text-lg rounded-xl sm:rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 overflow-hidden touch-manipulation\"\n              >\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <TbPlayerPlay className=\"w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 group-hover:scale-110 transition-transform duration-300\" />\n                <span className=\"relative z-10\">Start Quiz</span>\n                <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700\"></div>\n              </button>\n\n              <p className=\"mt-3 sm:mt-4 text-gray-500 text-xs sm:text-sm font-medium px-4\">\n                Ready to test your knowledge? Let's begin! 🚀\n              </p>\n            </motion.div>\n          </div>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default QuizStart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SACEC,OAAO,EACPC,cAAc,EACdC,QAAQ,EACRC,YAAY,EACZC,OAAO,QACF,gBAAgB;AACvB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,UAAA,EAAAC,iBAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM;IAAE0B;EAAG,CAAC,GAAGxB,SAAS,CAAC,CAAC;EAC1B,MAAMyB,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAMyB,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyB;EAAK,CAAC,GAAGxB,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD5B,SAAS,CAAC,MAAM;IACd,MAAM8B,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFH,QAAQ,CAAClB,WAAW,CAAC,CAAC,CAAC;QACvB,MAAMsB,QAAQ,GAAG,MAAMxB,WAAW,CAAC;UAAEyB,MAAM,EAAEP;QAAG,CAAC,CAAC;QAClDE,QAAQ,CAACnB,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAIuB,QAAQ,CAACE,OAAO,EAAE;UACpBT,WAAW,CAACO,QAAQ,CAACG,IAAI,CAAC;QAC5B,CAAC,MAAM;UACL5B,OAAO,CAAC6B,KAAK,CAACJ,QAAQ,CAACzB,OAAO,CAAC;UAC/BoB,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdR,QAAQ,CAACnB,WAAW,CAAC,CAAC,CAAC;QACvBF,OAAO,CAAC6B,KAAK,CAACA,KAAK,CAAC7B,OAAO,CAAC;QAC5BoB,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC;IAED,IAAID,EAAE,EAAE;MACNK,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACL,EAAE,EAAEE,QAAQ,EAAED,QAAQ,CAAC,CAAC;EAE5B1B,SAAS,CAAC,MAAM;IACdoC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5Bf,QAAQ,CAAE,SAAQD,EAAG,OAAM,CAAC;EAC9B,CAAC;EAED,IAAI,CAACF,QAAQ,EAAE;IACb,oBACEN,OAAA;MAAKyB,SAAS,EAAC,yFAAyF;MAAAC,QAAA,eACtG1B,OAAA,CAACP,OAAO;QAACkC,UAAU;QAACC,IAAI,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAEV;EAEA,oBACEhC,OAAA;IAAKyB,SAAS,EAAC,mJAAmJ;IAAAC,QAAA,gBAEhK1B,OAAA;MAAKyB,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/C1B,OAAA;QAAKyB,SAAS,EAAC;MAA8H;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpJhC,OAAA;QAAKyB,SAAS,EAAC;MAA6I;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnKhC,OAAA;QAAKyB,SAAS,EAAC;MAA0J;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7K,CAAC,eAGNhC,OAAA,CAACZ,MAAM,CAAC6C,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3CC,OAAO,EAAE;QAAEH,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAE;MACxCE,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,IAAI,EAAE;MAAU,CAAE;MAC/ChB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eAElD1B,OAAA;QAAKyB,SAAS,EAAC,2HAA2H;QAAAC,QAAA,gBAExI1B,OAAA;UAAKyB,SAAS,EAAC,oHAAoH;UAAAC,QAAA,gBACjI1B,OAAA;YAAKyB,SAAS,EAAC;UAAgE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtFhC,OAAA,CAACZ,MAAM,CAAC6C,GAAG;YACTC,OAAO,EAAE;cAAEG,KAAK,EAAE;YAAE,CAAE;YACtBC,OAAO,EAAE;cAAED,KAAK,EAAE;YAAE,CAAE;YACtBE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEE,KAAK,EAAE;YAAI,CAAE;YAC1CjB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAEzB1B,OAAA;cAAKyB,SAAS,EAAC,oIAAoI;cAAAC,QAAA,eACjJ1B,OAAA,CAACF,OAAO;gBAAC2B,SAAS,EAAC;cAAoC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNhC,OAAA;cAAIyB,SAAS,EAAC,wFAAwF;cAACkB,KAAK,EAAE;gBAAEC,UAAU,EAAE;cAA8B,CAAE;cAAAlB,QAAA,EACzJpB,QAAQ,CAACuC;YAAI;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLhC,OAAA;cAAGyB,SAAS,EAAC,qEAAqE;cAACkB,KAAK,EAAE;gBAAEC,UAAU,EAAE;cAA8B,CAAE;cAAAlB,QAAA,EAAC;YAEzI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNhC,OAAA;UAAKyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1B,OAAA;YAAKyB,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAC1E1B,OAAA,CAACZ,MAAM,CAAC6C,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BG,UAAU,EAAE;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC3BjB,SAAS,EAAC,qHAAqH;cAAAC,QAAA,gBAE/H1B,OAAA,CAACL,cAAc;gBAAC8B,SAAS,EAAC;cAA0D;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvFhC,OAAA;gBAAKyB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAE,EAAAvB,mBAAA,GAAAG,QAAQ,CAACwC,SAAS,cAAA3C,mBAAA,uBAAlBA,mBAAA,CAAoB4C,MAAM,KAAI;cAAC;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1GhC,OAAA;gBAAKyB,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eAEbhC,OAAA,CAACZ,MAAM,CAAC6C,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BG,UAAU,EAAE;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC3BjB,SAAS,EAAC,0HAA0H;cAAAC,QAAA,gBAEpI1B,OAAA,CAACN,OAAO;gBAAC+B,SAAS,EAAC;cAA6D;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnFhC,OAAA;gBAAKyB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAEpB,QAAQ,CAACkC,QAAQ,IAAI;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClGhC,OAAA;gBAAKyB,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eAEbhC,OAAA,CAACZ,MAAM,CAAC6C,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BG,UAAU,EAAE;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC3BjB,SAAS,EAAC,uHAAuH;cAAAC,QAAA,gBAEjI1B,OAAA,CAACJ,QAAQ;gBAAC6B,SAAS,EAAC;cAA4D;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnFhC,OAAA;gBAAKyB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,GAAEpB,QAAQ,CAAC0C,iBAAiB,IAAI,EAAE,EAAC,GAAC;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5GhC,OAAA;gBAAKyB,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNhC,OAAA,CAACZ,MAAM,CAAC6C,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BE,OAAO,EAAE;cAAEH,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC3BjB,SAAS,EAAC,mHAAmH;YAAAC,QAAA,eAE7H1B,OAAA;cAAKyB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACvD1B,OAAA;gBAAKyB,SAAS,EAAC,+IAA+I;gBAAAC,QAAA,eAC5J1B,OAAA;kBAAMyB,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EACtD,CAAAf,IAAI,aAAJA,IAAI,wBAAAP,UAAA,GAAJO,IAAI,CAAEkC,IAAI,cAAAzC,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAY6C,MAAM,CAAC,CAAC,CAAC,cAAA5C,iBAAA,uBAArBA,iBAAA,CAAuB6C,WAAW,CAAC,CAAC,KAAI;gBAAG;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNhC,OAAA;gBAAKyB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B1B,OAAA;kBAAIyB,SAAS,EAAC,uDAAuD;kBAAAC,QAAA,GAAC,WAAS,EAAC,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,IAAI,KAAI,SAAS,EAAC,GAAC;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9GhC,OAAA;kBAAKyB,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,gBACnD1B,OAAA;oBAAMyB,SAAS,EAAC,sGAAsG;oBAAAC,QAAA,EACnH,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,KAAK,KAAI;kBAAS;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACPhC,OAAA;oBAAMyB,SAAS,EAAC,4GAA4G;oBAAAC,QAAA,GAAC,QACrH,EAAC,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,KAAK,KAAI,KAAK;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbhC,OAAA,CAACZ,MAAM,CAAC6C,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BE,OAAO,EAAE;cAAEH,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC3BjB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAEvB1B,OAAA;cACEqD,OAAO,EAAE7B,eAAgB;cACzBC,SAAS,EAAC,8WAA8W;cAAAC,QAAA,gBAExX1B,OAAA;gBAAKyB,SAAS,EAAC;cAAkI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxJhC,OAAA,CAACH,YAAY;gBAAC4B,SAAS,EAAC;cAA4F;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvHhC,OAAA;gBAAMyB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjDhC,OAAA;gBAAKyB,SAAS,EAAC;cAAiK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjL,CAAC,eAEThC,OAAA;cAAGyB,SAAS,EAAC,gEAAgE;cAAAC,QAAA,EAAC;YAE9E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAnLID,SAAS;EAAA,QAEEjB,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAAmE,EAAA,GALxBrD,SAAS;AAqLf,eAAeA,SAAS;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}