const axios = require('axios');
const jwt = require('jsonwebtoken');
require('dotenv').config();

async function testTimeoutFix() {
  try {
    console.log('🧪 Testing timeout fix for AI question generation...');
    
    // Generate a test token
    const testUserId = '507f1f77bcf86cd799439011'; // Valid ObjectId format
    const token = jwt.sign({ userId: testUserId }, process.env.JWT_SECRET, { expiresIn: '1h' });
    
    console.log('✅ Generated test token');
    
    // Test payload with a reasonable number of questions
    const payload = {
      questionTypes: ['multiple_choice'],
      subjects: ['Mathematics'],
      level: 'primary',
      class: '3',
      difficultyLevels: ['medium'],
      syllabusTopics: ['Basic arithmetic', 'Addition'],
      totalQuestions: 2, // Small number for testing
      questionDistribution: {
        multiple_choice: 2,
        fill_blank: 0,
        picture_based: 0
      },
      userId: testUserId
    };
    
    console.log('📤 Sending test request...');
    console.log('Payload:', JSON.stringify(payload, null, 2));
    
    const startTime = Date.now();
    
    const response = await axios.post(
      'http://localhost:5000/api/ai-questions/generate-questions',
      payload,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 600000 // 10 minutes
      }
    );
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`✅ Request completed in ${duration}ms`);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    if (response.data.success) {
      console.log('🎉 Test passed! AI question generation works without timeout errors.');
    } else {
      console.log('❌ Test failed:', response.data.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    
    if (error.code === 'ECONNABORTED') {
      console.error('💥 Timeout error still occurring!');
    } else if (error.response) {
      console.error('Server error:', error.response.status, error.response.data);
    } else {
      console.error('Network error:', error.message);
    }
  }
}

// Run the test
if (require.main === module) {
  testTimeoutFix();
}

module.exports = testTimeoutFix;
