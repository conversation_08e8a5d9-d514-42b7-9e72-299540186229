const mongoose = require('mongoose');

// Simple direct MongoDB update
const updateSubscriptions = async () => {
  try {
    // Connect directly to MongoDB
    await mongoose.connect('mongodb+srv://brainwave:<EMAIL>/brainwave?retryWrites=true&w=majority', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB');
    
    // Get users collection
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    
    // Find non-admin users
    const users = await usersCollection.find({ isAdmin: { $ne: true } }).limit(10).toArray();
    
    console.log(`👥 Found ${users.length} users`);
    
    // Update users with different subscription plans
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      let subscriptionStatus, subscriptionPlan, subscriptionEndDate;
      
      if (i % 4 === 0) {
        // Premium users
        subscriptionStatus = 'active';
        subscriptionPlan = 'premium';
        subscriptionEndDate = new Date(Date.now() + 180 * 24 * 60 * 60 * 1000); // 6 months from now
      } else if (i % 4 === 1) {
        // Standard users
        subscriptionStatus = 'active';
        subscriptionPlan = 'standard';
        subscriptionEndDate = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000); // 3 months from now
      } else if (i % 4 === 2) {
        // Basic users
        subscriptionStatus = 'active';
        subscriptionPlan = 'basic';
        subscriptionEndDate = new Date(Date.now() + 60 * 24 * 60 * 60 * 1000); // 2 months from now
      } else {
        // Expired users
        subscriptionStatus = 'expired';
        subscriptionPlan = 'basic';
        subscriptionEndDate = new Date(Date.now() - 10 * 24 * 60 * 60 * 1000); // 10 days ago
      }
      
      await usersCollection.updateOne(
        { _id: user._id },
        {
          $set: {
            subscriptionStatus: subscriptionStatus,
            subscriptionPlan: subscriptionPlan,
            subscriptionEndDate: subscriptionEndDate
          }
        }
      );
      
      console.log(`✅ Updated ${user.name}: ${subscriptionStatus} - ${subscriptionPlan}`);
    }
    
    console.log('\n🎉 Subscription data updated successfully!');
    
    // Show summary
    const summary = await usersCollection.aggregate([
      { $match: { isAdmin: { $ne: true } } },
      { $group: { 
          _id: { status: "$subscriptionStatus", plan: "$subscriptionPlan" }, 
          count: { $sum: 1 } 
        } 
      }
    ]).toArray();
    
    console.log('\n📊 Summary:');
    summary.forEach(item => {
      console.log(`   ${item._id.status} - ${item._id.plan}: ${item.count} users`);
    });
    
    await mongoose.connection.close();
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Update failed:', error);
    process.exit(1);
  }
};

updateSubscriptions();
