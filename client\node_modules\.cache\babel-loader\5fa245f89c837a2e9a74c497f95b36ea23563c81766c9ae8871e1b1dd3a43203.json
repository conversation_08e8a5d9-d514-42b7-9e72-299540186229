{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Forum\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport \"./index.css\";\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message, Button, Input, Form, Avatar, Pagination } from \"antd\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addQuestion, addReply, getAllQuestions, deleteQuestion, updateQuestion, updateReplyStatus } from \"../../../apicalls/forum\";\nimport image from \"../../../assets/person.png\";\nimport { FaPencilAlt } from \"react-icons/fa\";\nimport { MdDelete, MdMessage } from \"react-icons/md\";\nimport { FaCheck } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Forum = () => {\n  _s();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [userData, setUserData] = useState(\"\");\n  const [questions, setQuestions] = useState([]);\n  const [expandedReplies, setExpandedReplies] = useState({});\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\n  const [editQuestion, setEditQuestion] = useState(null);\n  const [form] = Form.useForm();\n  const [form2] = Form.useForm();\n  const dispatch = useDispatch();\n  const [replyRefs, setReplyRefs] = useState({});\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [limit] = useState(10);\n  const [totalQuestions, setTotalQuestions] = useState(0);\n  const fetchQuestions = async page => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllQuestions({\n        page,\n        limit\n      }); // Pass query params to API call\n      if (response.success) {\n        console.log(response.data);\n        setQuestions(response.data); // No need to reverse as backend will handle order\n        // setCurrentPage(page);\n        setTotalQuestions(response.totalQuestions);\n        setTotalPages(response.totalPages);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  useEffect(() => {\n    fetchQuestions(currentPage);\n  }, [currentPage, limit]);\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  const getUserData = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n          setUserData(response.data);\n          await fetchQuestions();\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchQuestions();\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      getUserData();\n    }\n  }, []);\n  const toggleReplies = questionId => {\n    setExpandedReplies(prevExpandedReplies => ({\n      ...prevExpandedReplies,\n      [questionId]: !prevExpandedReplies[questionId]\n    }));\n  };\n  const handleAskQuestion = async values => {\n    try {\n      const response = await addQuestion(values);\n      if (response.success) {\n        message.success(response.message);\n        setAskQuestionVisible(false);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleReply = questionId => {\n    setReplyQuestionId(questionId);\n  };\n  const handleReplySubmit = async values => {\n    try {\n      const payload = {\n        questionId: replyQuestionId,\n        text: values.text\n      };\n      const response = await addReply(payload);\n      if (response.success) {\n        message.success(response.message);\n        setReplyQuestionId(null);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\n      setReplyRefs(prevRefs => ({\n        ...prevRefs,\n        [replyQuestionId]: /*#__PURE__*/React.createRef()\n      }));\n    }\n  }, [replyQuestionId, replyRefs]);\n  useEffect(() => {\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\n      replyRefs[replyQuestionId].current.scrollIntoView({\n        behavior: \"smooth\"\n      });\n    }\n  }, [replyQuestionId, replyRefs]);\n  const handleEdit = question => {\n    setEditQuestion(question);\n  };\n  const handleDelete = async question => {\n    try {\n      const confirmDelete = window.confirm(\"Are you sure you want to delete this question?\");\n      if (!confirmDelete) {\n        return;\n      }\n      const response = await deleteQuestion(question._id);\n      if (response.success) {\n        message.success(response.message);\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleUpdateQuestion = async values => {\n    try {\n      const response = await updateQuestion(values, editQuestion._id);\n      if (response.success) {\n        message.success(response.message);\n        setEditQuestion(null);\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleCancelUpdate = () => {\n    setEditQuestion(\"\");\n  };\n  const handleCancelAdd = () => {\n    setAskQuestionVisible(false);\n    form.resetFields();\n  };\n  useEffect(() => {\n    if (editQuestion) {\n      form2.setFieldsValue({\n        title: editQuestion.title,\n        body: editQuestion.body\n      });\n    } else {\n      form2.resetFields();\n    }\n  }, [editQuestion]);\n  const handleUpdateStatus = async (questionId, replyId, status) => {\n    try {\n      const response = await updateReplyStatus({\n        replyId,\n        status\n      }, questionId);\n      if (response.success) {\n        message.success(response.message);\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"Forum\",\n      children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n        title: \"Forum\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"divider\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Welcome to the forum! Feel free to ask questions, share your thoughts, and engage with the community.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setAskQuestionVisible(true),\n          style: {\n            marginBottom: 20\n          },\n          children: \"Ask a Question\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), askQuestionVisible && /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        onFinish: handleAskQuestion,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"title\",\n          label: \"Title\",\n          rules: [{\n            required: true,\n            message: \"Please enter the title\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            style: {\n              padding: \"18px 12px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"body\",\n          label: \"Body\",\n          rules: [{\n            required: true,\n            message: \"Please enter the body\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            children: \"Ask Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCancelAdd,\n            style: {\n              marginLeft: 10\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this), questions.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 36\n      }, this), questions.map(question => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"forum-question-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"question\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-details\",\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                src: question.user.profileImage ? question.user.profileImage : image,\n                alt: \"profile\",\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: question.user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"date\",\n                children: new Date(question.createdAt).toLocaleString(undefined, {\n                  minute: \"numeric\",\n                  hour: \"numeric\",\n                  day: \"numeric\",\n                  month: \"numeric\",\n                  year: \"numeric\"\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this), (userData._id === question.user._id || userData.isAdmin) && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"icons\",\n              children: [/*#__PURE__*/_jsxDEV(FaPencilAlt, {\n                onClick: () => handleEdit(question)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(MdDelete, {\n                size: 22,\n                color: \"red\",\n                onClick: () => handleDelete(question)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"title\",\n            children: question.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"body\",\n            children: question.body\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => toggleReplies(question._id),\n            children: expandedReplies[question._id] ? \"Collapse Replies\" : \"Expand Replies\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handleReply(question._id),\n            children: \"Reply\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            className: \"ml-auto w-fit \",\n            style: {\n              float: \"inline-end\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  padding: \"6px\",\n                  display: \"flex\"\n                },\n                children: /*#__PURE__*/_jsxDEV(MdMessage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: question.replies.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), editQuestion && editQuestion._id === question._id && /*#__PURE__*/_jsxDEV(Form, {\n          form: form2,\n          onFinish: handleUpdateQuestion,\n          layout: \"vertical\",\n          initialValues: {\n            title: editQuestion.title,\n            body: editQuestion.body\n          },\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"title\",\n            label: \"Title\",\n            rules: [{\n              required: true,\n              message: \"Please enter the title\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              style: {\n                padding: \"18px 12px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"body\",\n            label: \"Body\",\n            rules: [{\n              required: true,\n              message: \"Please enter the body\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input.TextArea, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: \"Update Question\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleCancelUpdate,\n              style: {\n                marginLeft: 10\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 15\n        }, this), expandedReplies[question._id] && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"replies\",\n          children: question.replies.map(reply => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `reply ${reply.user.isAdmin ? \"admin-reply\" : reply.isVerified ? \"verified-reply\" : \"\"}`,\n            children: [reply.isVerified && /*#__PURE__*/_jsxDEV(FaCheck, {\n              color: \"green\",\n              size: 30\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 42\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"profile-details\",\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  src: reply.user.profileImage ? reply.user.profileImage : image,\n                  alt: \"profile\",\n                  size: 32\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: reply.user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"date\",\n                  children: new Date(question.createdAt).toLocaleString(undefined, {\n                    minute: \"numeric\",\n                    hour: \"numeric\",\n                    day: \"numeric\",\n                    month: \"numeric\",\n                    year: \"numeric\"\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text\",\n                children: reply.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 23\n              }, this), isAdmin && !reply.user.isAdmin && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"verification-btn\",\n                onClick: () => handleUpdateStatus(question._id, reply._id, !reply.isVerified),\n                children: !reply.isVerified ? \"Approve Reply\" : \"Disapprove Reply\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 21\n            }, this)]\n          }, reply._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: replyRefs[question._id],\n          children: replyQuestionId === question._id && /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            onFinish: handleReplySubmit,\n            layout: \"vertical\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"text\",\n              label: \"Your Reply\",\n              rules: [{\n                required: true,\n                message: \"Please enter your reply\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                rows: 4\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                children: \"Submit Reply\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => setReplyQuestionId(null),\n                style: {\n                  marginLeft: 10\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 13\n        }, this)]\n      }, question._id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(Pagination, {\n        current: currentPage,\n        total: totalQuestions,\n        pageSize: limit,\n        onChange: handlePageChange,\n        style: {\n          marginTop: \"20px\",\n          textAlign: \"center\"\n        },\n        showSizeChanger: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 230,\n    columnNumber: 5\n  }, this);\n};\n_s(Forum, \"XBuKUcnCQkZV3E4NAnmvM9R/I3I=\", false, function () {\n  return [Form.useForm, Form.useForm, useDispatch];\n});\n_c = Forum;\nexport default Forum;\nvar _c;\n$RefreshReg$(_c, \"Forum\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getUserInfo", "message", "<PERSON><PERSON>", "Input", "Form", "Avatar", "Pagination", "Page<PERSON><PERSON>le", "useDispatch", "HideLoading", "ShowLoading", "addQuestion", "addReply", "getAllQuestions", "deleteQuestion", "updateQuestion", "updateReplyStatus", "image", "FaPencilAlt", "MdDelete", "MdMessage", "FaCheck", "jsxDEV", "_jsxDEV", "Forum", "_s", "isAdmin", "setIsAdmin", "userData", "setUserData", "questions", "setQuestions", "expandedReplies", "setExpandedReplies", "askQuestionVisible", "setAskQuestionVisible", "replyQuestionId", "setReplyQuestionId", "editQuestion", "setEditQuestion", "form", "useForm", "form2", "dispatch", "replyRefs", "setReplyRefs", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "limit", "totalQuestions", "setTotalQuestions", "fetchQuestions", "page", "response", "success", "console", "log", "data", "error", "handlePageChange", "getUserData", "localStorage", "getItem", "toggleReplies", "questionId", "prevExpandedReplies", "handleAskQuestion", "values", "resetFields", "handleReply", "handleReplySubmit", "payload", "text", "prevRefs", "createRef", "current", "scrollIntoView", "behavior", "handleEdit", "question", "handleDelete", "confirmDelete", "window", "confirm", "_id", "handleUpdateQuestion", "handleCancelUpdate", "handleCancelAdd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title", "body", "handleUpdateStatus", "replyId", "status", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "marginBottom", "onFinish", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "padding", "TextArea", "type", "htmlType", "marginLeft", "length", "map", "src", "user", "profileImage", "alt", "size", "Date", "createdAt", "toLocaleString", "undefined", "minute", "hour", "day", "month", "year", "color", "float", "display", "replies", "initialValues", "reply", "isVerified", "ref", "rows", "total", "pageSize", "onChange", "marginTop", "textAlign", "showSizeChanger", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Pagination } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport {\r\n  addQuestion,\r\n  addReply,\r\n  getAllQuestions,\r\n  deleteQuestion,\r\n  updateQuestion,\r\n  updateReplyStatus,\r\n} from \"../../../apicalls/forum\";\r\nimport image from \"../../../assets/person.png\";\r\nimport { FaPencilAlt } from \"react-icons/fa\";\r\nimport { MdDelete, MdMessage } from \"react-icons/md\";\r\nimport { FaCheck } from \"react-icons/fa\";\r\n\r\nconst Forum = () => {\r\n  const [isAdmin, setIsAdmin] = useState(false);\r\n  const [userData, setUserData] = useState(\"\");\r\n  const [questions, setQuestions] = useState([]);\r\n  const [expandedReplies, setExpandedReplies] = useState({});\r\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n  const [editQuestion, setEditQuestion] = useState(null);\r\n  const [form] = Form.useForm();\r\n  const [form2] = Form.useForm();\r\n  const dispatch = useDispatch();\r\n  const [replyRefs, setReplyRefs] = useState({});\r\n\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [limit] = useState(10);\r\n  const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n  const fetchQuestions = async (page) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllQuestions({ page, limit }); // Pass query params to API call\r\n      if (response.success) {\r\n        console.log(response.data);\r\n        setQuestions(response.data); // No need to reverse as backend will handle order\r\n        // setCurrentPage(page);\r\n        setTotalQuestions(response.totalQuestions);\r\n        setTotalPages(response.totalPages);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchQuestions(currentPage);\r\n  }, [currentPage, limit]);\r\n\r\n  const handlePageChange = (page) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        if (response.data.isAdmin) {\r\n          setIsAdmin(true);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        } else {\r\n          setIsAdmin(false);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const toggleReplies = (questionId) => {\r\n    setExpandedReplies((prevExpandedReplies) => ({\r\n      ...prevExpandedReplies,\r\n      [questionId]: !prevExpandedReplies[questionId],\r\n    }));\r\n  };\r\n\r\n  const handleAskQuestion = async (values) => {\r\n    try {\r\n      const response = await addQuestion(values);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setAskQuestionVisible(false);\r\n        form.resetFields();\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleReply = (questionId) => {\r\n    setReplyQuestionId(questionId);\r\n  };\r\n\r\n  const handleReplySubmit = async (values) => {\r\n    try {\r\n      const payload = {\r\n        questionId: replyQuestionId,\r\n        text: values.text,\r\n      };\r\n      const response = await addReply(payload);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setReplyQuestionId(null);\r\n        form.resetFields();\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n      setReplyRefs((prevRefs) => ({\r\n        ...prevRefs,\r\n        [replyQuestionId]: React.createRef(),\r\n      }));\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n      replyRefs[replyQuestionId].current.scrollIntoView({ behavior: \"smooth\" });\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  const handleEdit = (question) => {\r\n    setEditQuestion(question);\r\n  };\r\n\r\n  const handleDelete = async (question) => {\r\n    try {\r\n      const confirmDelete = window.confirm(\r\n        \"Are you sure you want to delete this question?\"\r\n      );\r\n      if (!confirmDelete) {\r\n        return;\r\n      }\r\n      const response = await deleteQuestion(question._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleUpdateQuestion = async (values) => {\r\n    try {\r\n      const response = await updateQuestion(values, editQuestion._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEditQuestion(null);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleCancelUpdate = () => {\r\n    setEditQuestion(\"\");\r\n  };\r\n  const handleCancelAdd = () => {\r\n    setAskQuestionVisible(false);\r\n    form.resetFields();\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (editQuestion) {\r\n      form2.setFieldsValue({\r\n        title: editQuestion.title,\r\n        body: editQuestion.body,\r\n      });\r\n    } else {\r\n      form2.resetFields();\r\n    }\r\n  }, [editQuestion]);\r\n\r\n  const handleUpdateStatus = async (questionId, replyId, status) => {\r\n    try {\r\n      const response = await updateReplyStatus({ replyId, status }, questionId);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"Forum\">\r\n        <PageTitle title=\"Forum\" />\r\n        <div className=\"divider\"></div>\r\n\r\n        <div>\r\n          <p>\r\n            Welcome to the forum! Feel free to ask questions, share your\r\n            thoughts, and engage with the community.\r\n          </p>\r\n          <Button\r\n            onClick={() => setAskQuestionVisible(true)}\r\n            style={{ marginBottom: 20 }}\r\n          >\r\n            Ask a Question\r\n          </Button>\r\n        </div>\r\n\r\n        {askQuestionVisible && (\r\n          <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\">\r\n            <Form.Item\r\n              name=\"title\"\r\n              label=\"Title\"\r\n              rules={[{ required: true, message: \"Please enter the title\" }]}\r\n            >\r\n              <Input style={{ padding: \"18px 12px\" }} />\r\n            </Form.Item>\r\n            <Form.Item\r\n              name=\"body\"\r\n              label=\"Body\"\r\n              rules={[{ required: true, message: \"Please enter the body\" }]}\r\n            >\r\n              <Input.TextArea />\r\n            </Form.Item>\r\n            <Form.Item>\r\n              <Button type=\"primary\" htmlType=\"submit\">\r\n                Ask Question\r\n              </Button>\r\n              <Button onClick={handleCancelAdd} style={{ marginLeft: 10 }}>\r\n                Cancel\r\n              </Button>\r\n            </Form.Item>\r\n          </Form>\r\n        )}\r\n\r\n        {questions.length === 0 && <div>Loading...</div>}\r\n\r\n        {questions.map((question) => (\r\n          <div key={question._id} className=\"forum-question-container\">\r\n            <div className=\"question\">\r\n              <div className=\"profile-row\">\r\n                <div className=\"profile-details\">\r\n                  <Avatar\r\n                    src={\r\n                      question.user.profileImage\r\n                        ? question.user.profileImage\r\n                        : image\r\n                    }\r\n                    alt=\"profile\"\r\n                    size={32}\r\n                  />\r\n                  <p>{question.user.name}</p>\r\n                  <p className=\"date\">\r\n                    {new Date(question.createdAt).toLocaleString(undefined, {\r\n                      minute: \"numeric\",\r\n                      hour: \"numeric\",\r\n                      day: \"numeric\",\r\n                      month: \"numeric\",\r\n                      year: \"numeric\",\r\n                    })}\r\n                  </p>\r\n                </div>\r\n                {(userData._id === question.user._id || userData.isAdmin) && (\r\n                  <div className=\"icons\">\r\n                    <FaPencilAlt onClick={() => handleEdit(question)} />\r\n                    <MdDelete\r\n                      size={22}\r\n                      color=\"red\"\r\n                      onClick={() => handleDelete(question)}\r\n                    />\r\n                  </div>\r\n                )}\r\n              </div>\r\n              <div className=\"title\">{question.title}</div>\r\n              <div className=\"body\">{question.body}</div>\r\n              <Button onClick={() => toggleReplies(question._id)}>\r\n                {expandedReplies[question._id]\r\n                  ? \"Collapse Replies\"\r\n                  : \"Expand Replies\"}\r\n              </Button>\r\n              <Button onClick={() => handleReply(question._id)}>Reply</Button>\r\n              <Button\r\n                className=\"ml-auto w-fit \"\r\n                style={{ float: \"inline-end\" }}\r\n              >\r\n                <div style={{ display: \"flex\" }}>\r\n                  <span style={{ padding: \"6px\", display: \"flex\" }}>\r\n                    <MdMessage />\r\n                  </span>\r\n                  <span>{question.replies.length}</span>\r\n                </div>\r\n              </Button>\r\n            </div>\r\n            {editQuestion && editQuestion._id === question._id && (\r\n              <Form\r\n                form={form2}\r\n                onFinish={handleUpdateQuestion}\r\n                layout=\"vertical\"\r\n                initialValues={{\r\n                  title: editQuestion.title,\r\n                  body: editQuestion.body,\r\n                }}\r\n              >\r\n                <Form.Item\r\n                  name=\"title\"\r\n                  label=\"Title\"\r\n                  rules={[\r\n                    { required: true, message: \"Please enter the title\" },\r\n                  ]}\r\n                >\r\n                  <Input style={{ padding: \"18px 12px\" }} />\r\n                </Form.Item>\r\n                <Form.Item\r\n                  name=\"body\"\r\n                  label=\"Body\"\r\n                  rules={[{ required: true, message: \"Please enter the body\" }]}\r\n                >\r\n                  <Input.TextArea />\r\n                </Form.Item>\r\n                <Form.Item>\r\n                  <Button type=\"primary\" htmlType=\"submit\">\r\n                    Update Question\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleCancelUpdate}\r\n                    style={{ marginLeft: 10 }}\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </Form.Item>\r\n              </Form>\r\n            )}\r\n            {expandedReplies[question._id] && (\r\n              <div className=\"replies\">\r\n                {question.replies.map((reply) => (\r\n                  <div\r\n                    key={reply._id}\r\n                    className={`reply ${\r\n                      reply.user.isAdmin\r\n                        ? \"admin-reply\"\r\n                        : reply.isVerified\r\n                        ? \"verified-reply\"\r\n                        : \"\"\r\n                    }`}\r\n                  >\r\n                    {reply.isVerified && <FaCheck color=\"green\" size={30} />}\r\n                    <div>\r\n                      <div className=\"profile-details\">\r\n                        <Avatar\r\n                          src={\r\n                            reply.user.profileImage\r\n                              ? reply.user.profileImage\r\n                              : image\r\n                          }\r\n                          alt=\"profile\"\r\n                          size={32}\r\n                        />\r\n                        <p>{reply.user.name}</p>\r\n                        <p className=\"date\">\r\n                          {new Date(question.createdAt).toLocaleString(\r\n                            undefined,\r\n                            {\r\n                              minute: \"numeric\",\r\n                              hour: \"numeric\",\r\n                              day: \"numeric\",\r\n                              month: \"numeric\",\r\n                              year: \"numeric\",\r\n                            }\r\n                          )}\r\n                        </p>\r\n                      </div>\r\n                      <div className=\"text\">{reply.text}</div>\r\n                      {isAdmin && !reply.user.isAdmin && (\r\n                        <button\r\n                          className=\"verification-btn\"\r\n                          onClick={() =>\r\n                            handleUpdateStatus(\r\n                              question._id,\r\n                              reply._id,\r\n                              !reply.isVerified\r\n                            )\r\n                          }\r\n                        >\r\n                          {!reply.isVerified\r\n                            ? \"Approve Reply\"\r\n                            : \"Disapprove Reply\"}\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            <div ref={replyRefs[question._id]}>\r\n              {replyQuestionId === question._id && (\r\n                <Form\r\n                  form={form}\r\n                  onFinish={handleReplySubmit}\r\n                  layout=\"vertical\"\r\n                >\r\n                  <Form.Item\r\n                    name=\"text\"\r\n                    label=\"Your Reply\"\r\n                    rules={[\r\n                      { required: true, message: \"Please enter your reply\" },\r\n                    ]}\r\n                  >\r\n                    <Input.TextArea rows={4} />\r\n                  </Form.Item>\r\n                  <Form.Item>\r\n                    <Button type=\"primary\" htmlType=\"submit\">\r\n                      Submit Reply\r\n                    </Button>\r\n                    <Button\r\n                      onClick={() => setReplyQuestionId(null)}\r\n                      style={{ marginLeft: 10 }}\r\n                    >\r\n                      Cancel\r\n                    </Button>\r\n                  </Form.Item>\r\n                </Form>\r\n              )}\r\n            </div>\r\n          </div>\r\n        ))}\r\n\r\n        <Pagination\r\n          current={currentPage}\r\n          total={totalQuestions}\r\n          pageSize={limit}\r\n          onChange={handlePageChange}\r\n          style={{ marginTop: \"20px\", textAlign: \"center\" }}\r\n          showSizeChanger={false}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Forum;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,QAAQ,MAAM;AACvE,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SACEC,WAAW,EACXC,QAAQ,EACRC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,iBAAiB,QACZ,yBAAyB;AAChC,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AACpD,SAASC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACqC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2C,IAAI,CAAC,GAAGpC,IAAI,CAACqC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,KAAK,CAAC,GAAGtC,IAAI,CAACqC,OAAO,CAAC,CAAC;EAC9B,MAAME,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE9C,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACqD,KAAK,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC5B,MAAM,CAACsD,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAMwD,cAAc,GAAG,MAAOC,IAAI,IAAK;IACrC,IAAI;MACFX,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM6C,QAAQ,GAAG,MAAM1C,eAAe,CAAC;QAAEyC,IAAI;QAAEJ;MAAM,CAAC,CAAC,CAAC,CAAC;MACzD,IAAIK,QAAQ,CAACC,OAAO,EAAE;QACpBC,OAAO,CAACC,GAAG,CAACH,QAAQ,CAACI,IAAI,CAAC;QAC1B5B,YAAY,CAACwB,QAAQ,CAACI,IAAI,CAAC,CAAC,CAAC;QAC7B;QACAP,iBAAiB,CAACG,QAAQ,CAACJ,cAAc,CAAC;QAC1CF,aAAa,CAACM,QAAQ,CAACP,UAAU,CAAC;MACpC,CAAC,MAAM;QACL/C,OAAO,CAAC2D,KAAK,CAACL,QAAQ,CAACtD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACd3D,OAAO,CAAC2D,KAAK,CAACA,KAAK,CAAC3D,OAAO,CAAC;IAC9B,CAAC,SAAS;MACR0C,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAEDX,SAAS,CAAC,MAAM;IACduD,cAAc,CAACP,WAAW,CAAC;EAC7B,CAAC,EAAE,CAACA,WAAW,EAAEI,KAAK,CAAC,CAAC;EAExB,MAAMW,gBAAgB,GAAIP,IAAI,IAAK;IACjCP,cAAc,CAACO,IAAI,CAAC;EACtB,CAAC;EAED,MAAMQ,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BnB,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM6C,QAAQ,GAAG,MAAMvD,WAAW,CAAC,CAAC;MACpC,IAAIuD,QAAQ,CAACC,OAAO,EAAE;QACpB,IAAID,QAAQ,CAACI,IAAI,CAACjC,OAAO,EAAE;UACzBC,UAAU,CAAC,IAAI,CAAC;UAChBE,WAAW,CAAC0B,QAAQ,CAACI,IAAI,CAAC;UAC1B,MAAMN,cAAc,CAAC,CAAC;QACxB,CAAC,MAAM;UACL1B,UAAU,CAAC,KAAK,CAAC;UACjBE,WAAW,CAAC0B,QAAQ,CAACI,IAAI,CAAC;UAC1B,MAAMN,cAAc,CAAC,CAAC;QACxB;MACF,CAAC,MAAM;QACLpD,OAAO,CAAC2D,KAAK,CAACL,QAAQ,CAACtD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACd3D,OAAO,CAAC2D,KAAK,CAACA,KAAK,CAAC3D,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDH,SAAS,CAAC,MAAM;IACd,IAAIiE,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MACjCF,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAIC,UAAU,IAAK;IACpCjC,kBAAkB,CAAEkC,mBAAmB,KAAM;MAC3C,GAAGA,mBAAmB;MACtB,CAACD,UAAU,GAAG,CAACC,mBAAmB,CAACD,UAAU;IAC/C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IAC1C,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAM5C,WAAW,CAAC0D,MAAM,CAAC;MAC1C,IAAId,QAAQ,CAACC,OAAO,EAAE;QACpBvD,OAAO,CAACuD,OAAO,CAACD,QAAQ,CAACtD,OAAO,CAAC;QACjCkC,qBAAqB,CAAC,KAAK,CAAC;QAC5BK,IAAI,CAAC8B,WAAW,CAAC,CAAC;QAClB,MAAMjB,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLpD,OAAO,CAAC2D,KAAK,CAACL,QAAQ,CAACtD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACd3D,OAAO,CAAC2D,KAAK,CAACA,KAAK,CAAC3D,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMsE,WAAW,GAAIL,UAAU,IAAK;IAClC7B,kBAAkB,CAAC6B,UAAU,CAAC;EAChC,CAAC;EAED,MAAMM,iBAAiB,GAAG,MAAOH,MAAM,IAAK;IAC1C,IAAI;MACF,MAAMI,OAAO,GAAG;QACdP,UAAU,EAAE9B,eAAe;QAC3BsC,IAAI,EAAEL,MAAM,CAACK;MACf,CAAC;MACD,MAAMnB,QAAQ,GAAG,MAAM3C,QAAQ,CAAC6D,OAAO,CAAC;MACxC,IAAIlB,QAAQ,CAACC,OAAO,EAAE;QACpBvD,OAAO,CAACuD,OAAO,CAACD,QAAQ,CAACtD,OAAO,CAAC;QACjCoC,kBAAkB,CAAC,IAAI,CAAC;QACxBG,IAAI,CAAC8B,WAAW,CAAC,CAAC;QAClB,MAAMjB,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLpD,OAAO,CAAC2D,KAAK,CAACL,QAAQ,CAACtD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACd3D,OAAO,CAAC2D,KAAK,CAACA,KAAK,CAAC3D,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDH,SAAS,CAAC,MAAM;IACd,IAAIsC,eAAe,IAAI,CAACQ,SAAS,CAACR,eAAe,CAAC,EAAE;MAClDS,YAAY,CAAE8B,QAAQ,KAAM;QAC1B,GAAGA,QAAQ;QACX,CAACvC,eAAe,gBAAGxC,KAAK,CAACgF,SAAS,CAAC;MACrC,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACxC,eAAe,EAAEQ,SAAS,CAAC,CAAC;EAEhC9C,SAAS,CAAC,MAAM;IACd,IAAIsC,eAAe,IAAIQ,SAAS,CAACR,eAAe,CAAC,EAAE;MACjDQ,SAAS,CAACR,eAAe,CAAC,CAACyC,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC3E;EACF,CAAC,EAAE,CAAC3C,eAAe,EAAEQ,SAAS,CAAC,CAAC;EAEhC,MAAMoC,UAAU,GAAIC,QAAQ,IAAK;IAC/B1C,eAAe,CAAC0C,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOD,QAAQ,IAAK;IACvC,IAAI;MACF,MAAME,aAAa,GAAGC,MAAM,CAACC,OAAO,CAClC,gDACF,CAAC;MACD,IAAI,CAACF,aAAa,EAAE;QAClB;MACF;MACA,MAAM5B,QAAQ,GAAG,MAAMzC,cAAc,CAACmE,QAAQ,CAACK,GAAG,CAAC;MACnD,IAAI/B,QAAQ,CAACC,OAAO,EAAE;QACpBvD,OAAO,CAACuD,OAAO,CAACD,QAAQ,CAACtD,OAAO,CAAC;QACjC,MAAMoD,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLpD,OAAO,CAAC2D,KAAK,CAACL,QAAQ,CAACtD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACd3D,OAAO,CAAC2D,KAAK,CAACA,KAAK,CAAC3D,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMsF,oBAAoB,GAAG,MAAOlB,MAAM,IAAK;IAC7C,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMxC,cAAc,CAACsD,MAAM,EAAE/B,YAAY,CAACgD,GAAG,CAAC;MAC/D,IAAI/B,QAAQ,CAACC,OAAO,EAAE;QACpBvD,OAAO,CAACuD,OAAO,CAACD,QAAQ,CAACtD,OAAO,CAAC;QACjCsC,eAAe,CAAC,IAAI,CAAC;QACrB,MAAMc,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLpD,OAAO,CAAC2D,KAAK,CAACL,QAAQ,CAACtD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACd3D,OAAO,CAAC2D,KAAK,CAACA,KAAK,CAAC3D,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMuF,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjD,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EACD,MAAMkD,eAAe,GAAGA,CAAA,KAAM;IAC5BtD,qBAAqB,CAAC,KAAK,CAAC;IAC5BK,IAAI,CAAC8B,WAAW,CAAC,CAAC;EACpB,CAAC;EAEDxE,SAAS,CAAC,MAAM;IACd,IAAIwC,YAAY,EAAE;MAChBI,KAAK,CAACgD,cAAc,CAAC;QACnBC,KAAK,EAAErD,YAAY,CAACqD,KAAK;QACzBC,IAAI,EAAEtD,YAAY,CAACsD;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLlD,KAAK,CAAC4B,WAAW,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAAChC,YAAY,CAAC,CAAC;EAElB,MAAMuD,kBAAkB,GAAG,MAAAA,CAAO3B,UAAU,EAAE4B,OAAO,EAAEC,MAAM,KAAK;IAChE,IAAI;MACF,MAAMxC,QAAQ,GAAG,MAAMvC,iBAAiB,CAAC;QAAE8E,OAAO;QAAEC;MAAO,CAAC,EAAE7B,UAAU,CAAC;MACzE,IAAIX,QAAQ,CAACC,OAAO,EAAE;QACpBvD,OAAO,CAACuD,OAAO,CAACD,QAAQ,CAACtD,OAAO,CAAC;QACjC,MAAMoD,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLpD,OAAO,CAAC2D,KAAK,CAACL,QAAQ,CAACtD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACd3D,OAAO,CAAC2D,KAAK,CAACA,KAAK,CAAC3D,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,oBACEsB,OAAA;IAAAyE,QAAA,eACEzE,OAAA;MAAK0E,SAAS,EAAC,OAAO;MAAAD,QAAA,gBACpBzE,OAAA,CAAChB,SAAS;QAACoF,KAAK,EAAC;MAAO;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3B9E,OAAA;QAAK0E,SAAS,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE/B9E,OAAA;QAAAyE,QAAA,gBACEzE,OAAA;UAAAyE,QAAA,EAAG;QAGH;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ9E,OAAA,CAACrB,MAAM;UACLoG,OAAO,EAAEA,CAAA,KAAMnE,qBAAqB,CAAC,IAAI,CAAE;UAC3CoE,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAR,QAAA,EAC7B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELnE,kBAAkB,iBACjBX,OAAA,CAACnB,IAAI;QAACoC,IAAI,EAAEA,IAAK;QAACiE,QAAQ,EAAErC,iBAAkB;QAACsC,MAAM,EAAC,UAAU;QAAAV,QAAA,gBAC9DzE,OAAA,CAACnB,IAAI,CAACuG,IAAI;UACRC,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,OAAO;UACbC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE9G,OAAO,EAAE;UAAyB,CAAC,CAAE;UAAA+F,QAAA,eAE/DzE,OAAA,CAACpB,KAAK;YAACoG,KAAK,EAAE;cAAES,OAAO,EAAE;YAAY;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACZ9E,OAAA,CAACnB,IAAI,CAACuG,IAAI;UACRC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAC,MAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE9G,OAAO,EAAE;UAAwB,CAAC,CAAE;UAAA+F,QAAA,eAE9DzE,OAAA,CAACpB,KAAK,CAAC8G,QAAQ;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACZ9E,OAAA,CAACnB,IAAI,CAACuG,IAAI;UAAAX,QAAA,gBACRzE,OAAA,CAACrB,MAAM;YAACgH,IAAI,EAAC,SAAS;YAACC,QAAQ,EAAC,QAAQ;YAAAnB,QAAA,EAAC;UAEzC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9E,OAAA,CAACrB,MAAM;YAACoG,OAAO,EAAEb,eAAgB;YAACc,KAAK,EAAE;cAAEa,UAAU,EAAE;YAAG,CAAE;YAAApB,QAAA,EAAC;UAE7D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACP,EAEAvE,SAAS,CAACuF,MAAM,KAAK,CAAC,iBAAI9F,OAAA;QAAAyE,QAAA,EAAK;MAAU;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EAE/CvE,SAAS,CAACwF,GAAG,CAAErC,QAAQ,iBACtB1D,OAAA;QAAwB0E,SAAS,EAAC,0BAA0B;QAAAD,QAAA,gBAC1DzE,OAAA;UAAK0E,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACvBzE,OAAA;YAAK0E,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC1BzE,OAAA;cAAK0E,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9BzE,OAAA,CAAClB,MAAM;gBACLkH,GAAG,EACDtC,QAAQ,CAACuC,IAAI,CAACC,YAAY,GACtBxC,QAAQ,CAACuC,IAAI,CAACC,YAAY,GAC1BxG,KACL;gBACDyG,GAAG,EAAC,SAAS;gBACbC,IAAI,EAAE;cAAG;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACF9E,OAAA;gBAAAyE,QAAA,EAAIf,QAAQ,CAACuC,IAAI,CAACZ;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3B9E,OAAA;gBAAG0E,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAChB,IAAI4B,IAAI,CAAC3C,QAAQ,CAAC4C,SAAS,CAAC,CAACC,cAAc,CAACC,SAAS,EAAE;kBACtDC,MAAM,EAAE,SAAS;kBACjBC,IAAI,EAAE,SAAS;kBACfC,GAAG,EAAE,SAAS;kBACdC,KAAK,EAAE,SAAS;kBAChBC,IAAI,EAAE;gBACR,CAAC;cAAC;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACL,CAACzE,QAAQ,CAAC0D,GAAG,KAAKL,QAAQ,CAACuC,IAAI,CAAClC,GAAG,IAAI1D,QAAQ,CAACF,OAAO,kBACtDH,OAAA;cAAK0E,SAAS,EAAC,OAAO;cAAAD,QAAA,gBACpBzE,OAAA,CAACL,WAAW;gBAACoF,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAACC,QAAQ;cAAE;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpD9E,OAAA,CAACJ,QAAQ;gBACPwG,IAAI,EAAE,EAAG;gBACTU,KAAK,EAAC,KAAK;gBACX/B,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAACD,QAAQ;cAAE;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN9E,OAAA;YAAK0E,SAAS,EAAC,OAAO;YAAAD,QAAA,EAAEf,QAAQ,CAACU;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C9E,OAAA;YAAK0E,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAEf,QAAQ,CAACW;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3C9E,OAAA,CAACrB,MAAM;YAACoG,OAAO,EAAEA,CAAA,KAAMrC,aAAa,CAACgB,QAAQ,CAACK,GAAG,CAAE;YAAAU,QAAA,EAChDhE,eAAe,CAACiD,QAAQ,CAACK,GAAG,CAAC,GAC1B,kBAAkB,GAClB;UAAgB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACT9E,OAAA,CAACrB,MAAM;YAACoG,OAAO,EAAEA,CAAA,KAAM/B,WAAW,CAACU,QAAQ,CAACK,GAAG,CAAE;YAAAU,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChE9E,OAAA,CAACrB,MAAM;YACL+F,SAAS,EAAC,gBAAgB;YAC1BM,KAAK,EAAE;cAAE+B,KAAK,EAAE;YAAa,CAAE;YAAAtC,QAAA,eAE/BzE,OAAA;cAAKgF,KAAK,EAAE;gBAAEgC,OAAO,EAAE;cAAO,CAAE;cAAAvC,QAAA,gBAC9BzE,OAAA;gBAAMgF,KAAK,EAAE;kBAAES,OAAO,EAAE,KAAK;kBAAEuB,OAAO,EAAE;gBAAO,CAAE;gBAAAvC,QAAA,eAC/CzE,OAAA,CAACH,SAAS;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACP9E,OAAA;gBAAAyE,QAAA,EAAOf,QAAQ,CAACuD,OAAO,CAACnB;cAAM;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACL/D,YAAY,IAAIA,YAAY,CAACgD,GAAG,KAAKL,QAAQ,CAACK,GAAG,iBAChD/D,OAAA,CAACnB,IAAI;UACHoC,IAAI,EAAEE,KAAM;UACZ+D,QAAQ,EAAElB,oBAAqB;UAC/BmB,MAAM,EAAC,UAAU;UACjB+B,aAAa,EAAE;YACb9C,KAAK,EAAErD,YAAY,CAACqD,KAAK;YACzBC,IAAI,EAAEtD,YAAY,CAACsD;UACrB,CAAE;UAAAI,QAAA,gBAEFzE,OAAA,CAACnB,IAAI,CAACuG,IAAI;YACRC,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,OAAO;YACbC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAE9G,OAAO,EAAE;YAAyB,CAAC,CACrD;YAAA+F,QAAA,eAEFzE,OAAA,CAACpB,KAAK;cAACoG,KAAK,EAAE;gBAAES,OAAO,EAAE;cAAY;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACZ9E,OAAA,CAACnB,IAAI,CAACuG,IAAI;YACRC,IAAI,EAAC,MAAM;YACXC,KAAK,EAAC,MAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE9G,OAAO,EAAE;YAAwB,CAAC,CAAE;YAAA+F,QAAA,eAE9DzE,OAAA,CAACpB,KAAK,CAAC8G,QAAQ;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACZ9E,OAAA,CAACnB,IAAI,CAACuG,IAAI;YAAAX,QAAA,gBACRzE,OAAA,CAACrB,MAAM;cAACgH,IAAI,EAAC,SAAS;cAACC,QAAQ,EAAC,QAAQ;cAAAnB,QAAA,EAAC;YAEzC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9E,OAAA,CAACrB,MAAM;cACLoG,OAAO,EAAEd,kBAAmB;cAC5Be,KAAK,EAAE;gBAAEa,UAAU,EAAE;cAAG,CAAE;cAAApB,QAAA,EAC3B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACP,EACArE,eAAe,CAACiD,QAAQ,CAACK,GAAG,CAAC,iBAC5B/D,OAAA;UAAK0E,SAAS,EAAC,SAAS;UAAAD,QAAA,EACrBf,QAAQ,CAACuD,OAAO,CAAClB,GAAG,CAAEoB,KAAK,iBAC1BnH,OAAA;YAEE0E,SAAS,EAAG,SACVyC,KAAK,CAAClB,IAAI,CAAC9F,OAAO,GACd,aAAa,GACbgH,KAAK,CAACC,UAAU,GAChB,gBAAgB,GAChB,EACL,EAAE;YAAA3C,QAAA,GAEF0C,KAAK,CAACC,UAAU,iBAAIpH,OAAA,CAACF,OAAO;cAACgH,KAAK,EAAC,OAAO;cAACV,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxD9E,OAAA;cAAAyE,QAAA,gBACEzE,OAAA;gBAAK0E,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9BzE,OAAA,CAAClB,MAAM;kBACLkH,GAAG,EACDmB,KAAK,CAAClB,IAAI,CAACC,YAAY,GACnBiB,KAAK,CAAClB,IAAI,CAACC,YAAY,GACvBxG,KACL;kBACDyG,GAAG,EAAC,SAAS;kBACbC,IAAI,EAAE;gBAAG;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACF9E,OAAA;kBAAAyE,QAAA,EAAI0C,KAAK,CAAClB,IAAI,CAACZ;gBAAI;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxB9E,OAAA;kBAAG0E,SAAS,EAAC,MAAM;kBAAAD,QAAA,EAChB,IAAI4B,IAAI,CAAC3C,QAAQ,CAAC4C,SAAS,CAAC,CAACC,cAAc,CAC1CC,SAAS,EACT;oBACEC,MAAM,EAAE,SAAS;oBACjBC,IAAI,EAAE,SAAS;oBACfC,GAAG,EAAE,SAAS;oBACdC,KAAK,EAAE,SAAS;oBAChBC,IAAI,EAAE;kBACR,CACF;gBAAC;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN9E,OAAA;gBAAK0E,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAE0C,KAAK,CAAChE;cAAI;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACvC3E,OAAO,IAAI,CAACgH,KAAK,CAAClB,IAAI,CAAC9F,OAAO,iBAC7BH,OAAA;gBACE0E,SAAS,EAAC,kBAAkB;gBAC5BK,OAAO,EAAEA,CAAA,KACPT,kBAAkB,CAChBZ,QAAQ,CAACK,GAAG,EACZoD,KAAK,CAACpD,GAAG,EACT,CAACoD,KAAK,CAACC,UACT,CACD;gBAAA3C,QAAA,EAEA,CAAC0C,KAAK,CAACC,UAAU,GACd,eAAe,GACf;cAAkB;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GApDDqC,KAAK,CAACpD,GAAG;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqDX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eACD9E,OAAA;UAAKqH,GAAG,EAAEhG,SAAS,CAACqC,QAAQ,CAACK,GAAG,CAAE;UAAAU,QAAA,EAC/B5D,eAAe,KAAK6C,QAAQ,CAACK,GAAG,iBAC/B/D,OAAA,CAACnB,IAAI;YACHoC,IAAI,EAAEA,IAAK;YACXiE,QAAQ,EAAEjC,iBAAkB;YAC5BkC,MAAM,EAAC,UAAU;YAAAV,QAAA,gBAEjBzE,OAAA,CAACnB,IAAI,CAACuG,IAAI;cACRC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,YAAY;cAClBC,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9G,OAAO,EAAE;cAA0B,CAAC,CACtD;cAAA+F,QAAA,eAEFzE,OAAA,CAACpB,KAAK,CAAC8G,QAAQ;gBAAC4B,IAAI,EAAE;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACZ9E,OAAA,CAACnB,IAAI,CAACuG,IAAI;cAAAX,QAAA,gBACRzE,OAAA,CAACrB,MAAM;gBAACgH,IAAI,EAAC,SAAS;gBAACC,QAAQ,EAAC,QAAQ;gBAAAnB,QAAA,EAAC;cAEzC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9E,OAAA,CAACrB,MAAM;gBACLoG,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,IAAI,CAAE;gBACxCkE,KAAK,EAAE;kBAAEa,UAAU,EAAE;gBAAG,CAAE;gBAAApB,QAAA,EAC3B;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,GAxLEpB,QAAQ,CAACK,GAAG;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAyLjB,CACN,CAAC,eAEF9E,OAAA,CAACjB,UAAU;QACTuE,OAAO,EAAE/B,WAAY;QACrBgG,KAAK,EAAE3F,cAAe;QACtB4F,QAAQ,EAAE7F,KAAM;QAChB8F,QAAQ,EAAEnF,gBAAiB;QAC3B0C,KAAK,EAAE;UAAE0C,SAAS,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAClDC,eAAe,EAAE;MAAM;QAAAjD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAxcID,KAAK;EAAA,QAQMpB,IAAI,CAACqC,OAAO,EACXrC,IAAI,CAACqC,OAAO,EACXjC,WAAW;AAAA;AAAA4I,EAAA,GAVxB5H,KAAK;AA0cX,eAAeA,KAAK;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}