import React, { useState, useEffect, useCallback } from "react";
import { useDispatch } from "react-redux";
import {
  Card,
  Form,
  Select,
  InputNumber,
  Button,
  Row,
  Col,
  Checkbox,
  message,
  Divider,
  Alert,
  Progress
} from "antd";
import { FaArrowLeft, FaRobot } from "react-icons/fa";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { getAllExams } from "../../../apicalls/exams";
import {
  generateQuestions,
  getSubjectsForLevel,
  getSyllabusTopics
} from "../../../apicalls/aiQuestions";
import AutoGenerateExamModal from "./AutoGenerateExamModal";
import AILoginModal from "../../../components/AILoginModal";
import { useAIAuth } from "../../../hooks/useAIAuth";

const { Option } = Select;

function QuestionGenerationForm({ onBack, onSuccess }) {
  const dispatch = useDispatch();
  const [form] = Form.useForm();

  // Enhanced authentication
  const {
    isAuthenticated,
    hasAIAccess,
    user,
    loading: authLoading,
    requiresUpgrade,
    needsLogin,
    handleLoginSuccess,
    requireAIAuth,
    sessionExpiringSoon,
    timeUntilExpiry
  } = useAIAuth();

  const [exams, setExams] = useState([]);
  const [availableSubjects, setAvailableSubjects] = useState([]);
  const [availableTopics, setAvailableTopics] = useState([]);
  const [selectedLevel, setSelectedLevel] = useState("");
  const [selectedClass, setSelectedClass] = useState("");
  const [selectedSubjects, setSelectedSubjects] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [showAutoGenerateModal, setShowAutoGenerateModal] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);

  const fetchExams = useCallback(async () => {
    try {
      dispatch(ShowLoading());
      const response = await getAllExams();
      if (response.success && Array.isArray(response.data)) {
        // Filter out any null or undefined values
        const validExams = response.data.filter(exam => exam && exam._id);
        setExams(validExams);
      } else {
        message.error("Failed to fetch exams");
        setExams([]); // Ensure exams is always an array
      }
    } catch (error) {
      message.error("Error fetching exams");
      setExams([]); // Ensure exams is always an array
    } finally {
      dispatch(HideLoading());
    }
  }, [dispatch]);

  useEffect(() => {
    // Only fetch exams if we have authentication, otherwise let the auth hook handle it
    if (isAuthenticated && hasAIAccess && !authLoading) {
      fetchExams();
    }
  }, [isAuthenticated, hasAIAccess, authLoading, fetchExams]);

  const handleLevelChange = async (level) => {
    setSelectedLevel(level);
    setSelectedClass("");
    setSelectedSubjects([]);
    setAvailableTopics([]);
    form.setFieldsValue({
      class: undefined,
      subjects: [],
      syllabusTopics: []
    });

    try {
      const response = await getSubjectsForLevel(level);
      if (response.success) {
        setAvailableSubjects(response.data);
      } else {
        message.error("Failed to fetch subjects");
      }
    } catch (error) {
      message.error("Error fetching subjects");
    }
  };

  const handleClassChange = (className) => {
    setSelectedClass(className);
    setAvailableTopics([]);
    form.setFieldsValue({ syllabusTopics: [] });

    // If subjects are already selected, fetch topics
    if (selectedSubjects.length > 0) {
      fetchTopicsForSubjects(selectedLevel, className, selectedSubjects);
    }
  };

  const handleSubjectsChange = (subjects) => {
    setSelectedSubjects(subjects);
    setAvailableTopics([]);
    form.setFieldsValue({ syllabusTopics: [] });

    // If class is selected, fetch topics
    if (selectedClass) {
      fetchTopicsForSubjects(selectedLevel, selectedClass, subjects);
    }

    // Note: Auto-generate exam functionality moved to modal
  };

  const fetchTopicsForSubjects = async (level, className, subjects) => {
    if (!level || !className || subjects.length === 0) return;

    try {
      const allTopics = [];

      for (const subject of subjects) {
        const response = await getSyllabusTopics(level, className, subject);
        if (response.success) {
          const subjectTopics = response.data.topics.map(topic => ({
            ...topic,
            subject: subject,
            fullName: `${subject}: ${topic.topicName}`,
          }));
          allTopics.push(...subjectTopics);
        }
      }

      setAvailableTopics(allTopics);
    } catch (error) {
      console.error("Error fetching topics:", error);
      message.error("Failed to fetch syllabus topics");
    }
  };

  const handleAutoGenerateExamSuccess = (newExam) => {
    // Add the new exam to the list and select it
    if (newExam && newExam._id) {
      const updatedExams = [...exams, newExam];
      setExams(updatedExams);
      form.setFieldsValue({ examId: newExam._id });
      setShowAutoGenerateModal(false);
      message.success(`Exam created successfully: ${newExam.name}`);
    } else {
      message.error("Invalid exam data received");
      setShowAutoGenerateModal(false);
    }
  };

  const openAutoGenerateModal = () => {
    setShowAutoGenerateModal(true);
  };

  const onFinish = async (values) => {
    console.log("🚀 Form submission started");
    console.log("📝 Form values:", values);

    try {
      setIsGenerating(true);
      setGenerationProgress(10);

      // Validate question distribution
      const totalDistribution = Object.values(values.questionDistribution || {}).reduce((sum, count) => sum + (count || 0), 0);
      console.log("📊 Total distribution:", totalDistribution, "Total questions:", values.totalQuestions);

      if (totalDistribution !== values.totalQuestions) {
        console.error("❌ Distribution validation failed");
        message.error("Question distribution must equal total questions");
        setIsGenerating(false);
        return;
      }

      console.log("✅ Distribution validation passed");

      setGenerationProgress(30);

      // Check authentication and AI access
      if (!isAuthenticated || !hasAIAccess) {
        setIsGenerating(false);
        setShowLoginModal(true);
        message.warning("Please login to access AI question generation features.");
        return;
      }

      // Double-check with server-side validation
      const authCheck = await requireAIAuth();
      if (!authCheck.success) {
        setIsGenerating(false);

        switch (authCheck.reason) {
          case 'not_authenticated':
          case 'refresh_failed':
            setShowLoginModal(true);
            message.warning("Please login to generate AI questions.");
            return;
          case 'no_ai_access':
            message.error("AI features are not available for your account.");
            return;
          case 'requires_upgrade':
            message.warning("AI question generation requires a premium subscription. Please upgrade your account.");
            return;
          default:
            setShowLoginModal(true);
            message.warning("Authentication check failed. Please login again.");
            return;
        }
      }

      const payload = {
        examId: values.examId,
        questionTypes: values.questionTypes,
        subjects: values.subjects,
        level: values.level,
        class: values.class,
        difficultyLevels: values.difficultyLevels,
        syllabusTopics: values.syllabusTopics || [],
        totalQuestions: values.totalQuestions,
        questionDistribution: values.questionDistribution,
        userId: user._id,
      };

      console.log("📤 Sending payload:", payload);

      setGenerationProgress(50);

      console.log("🌐 Making API call to generate questions...");

      // Show progress message to user
      message.info("AI is generating your questions... This may take a few minutes.", 5);

      const response = await generateQuestions(payload);
      console.log("📥 API response received:", response);

      setGenerationProgress(90);

      if (response.success) {
        setGenerationProgress(100);
        message.success("Questions generated successfully!");
        setTimeout(() => {
          onSuccess();
        }, 1000);
      } else {
        message.error(response.message || "Failed to generate questions");
      }
    } catch (error) {
      console.error("Question generation error:", error);

      // More detailed error handling
      let errorMessage = "Error generating questions";

      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        // Handle timeout errors specifically
        errorMessage = "Question generation is taking longer than expected. This might be due to high server load. Please try again with fewer questions or check your internet connection.";
      } else if (error.response) {
        // Server responded with error status
        console.error("Server error response:", error.response.data);
        console.error("Server error status:", error.response.status);

        if (error.response.status === 401) {
          // Handle authentication errors for AI requests
          const errorData = error.response.data;

          if (errorData?.requiresLogin) {
            // Show the AI login modal instead of redirecting
            setShowLoginModal(true);
            errorMessage = errorData.message || "Authentication required for AI features.";
          } else {
            errorMessage = errorData?.message || "Authentication failed. Please login again.";
          }
        } else if (error.response.status === 403) {
          // Handle permission/subscription errors
          const errorData = error.response.data;
          if (errorData?.upgradeRequired) {
            errorMessage = "AI question generation requires a premium subscription. Please upgrade your account.";
          } else {
            errorMessage = errorData?.message || "Access denied for AI features.";
          }
        } else if (error.response.status === 504 || error.response.status === 502) {
          errorMessage = "Server timeout. The AI generation process is taking longer than expected. Please try again with fewer questions.";
        } else {
          errorMessage = error.response.data?.message || `Server error: ${error.response.status}`;
        }
      } else if (error.request) {
        // Request was made but no response received
        console.error("Network error:", error.request);
        errorMessage = "Network error - please check your connection. If the problem persists, try generating fewer questions at once.";
      } else {
        // Something else happened
        console.error("Error:", error.message);
        errorMessage = error.message || "Unknown error occurred";
      }

      message.error(errorMessage);
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  };

  const questionTypeOptions = [
    { label: "Multiple Choice", value: "multiple_choice" },
    { label: "Fill in the Blank", value: "fill_blank" },
    { label: "Picture-based", value: "picture_based" },
  ];

  const difficultyOptions = [
    { label: "Easy", value: "easy" },
    { label: "Medium", value: "medium" },
    { label: "Hard", value: "hard" },
  ];

  const levelOptions = [
    { label: "Primary Education (Standards I-VI)", value: "primary" },
    { label: "Ordinary Secondary (Forms I-IV)", value: "ordinary_secondary" },
    { label: "Advanced Secondary (Forms V-VI)", value: "advanced_secondary" },
  ];

  const classOptions = {
    primary: ["I", "II", "III", "IV", "V", "VI"], // Standards I-VI (TIE Primary)
    ordinary_secondary: ["I", "II", "III", "IV"], // Forms I-IV (TIE Ordinary Secondary)
    advanced_secondary: ["V", "VI"], // Forms V-VI (TIE Advanced Secondary)
  };

  return (
    <div className="question-generation-form">
      <Card
        title={
          <div className="form-header">
            <Button
              type="text"
              icon={<FaArrowLeft />}
              onClick={onBack}
              className="back-button"
            >
              Back to Dashboard
            </Button>
            <div className="title-section">
              <FaRobot className="title-icon" />
              <span>Generate AI Questions</span>
            </div>
          </div>
        }
      >
        {/* Authentication Status */}
        {authLoading ? (
          <Alert
            message="Checking Authentication..."
            description="Verifying your access to AI features."
            type="info"
            showIcon
            className="mb-4"
          />
        ) : !isAuthenticated ? (
          <Alert
            message="Login Required"
            description={
              <div>
                <p>Please login to access AI question generation features.</p>
                <Button
                  type="primary"
                  size="small"
                  onClick={() => setShowLoginModal(true)}
                  style={{ marginTop: 8 }}
                >
                  Login Now
                </Button>
              </div>
            }
            type="warning"
            showIcon
            className="mb-4"
          />
        ) : !hasAIAccess ? (
          <Alert
            message={requiresUpgrade ? "Upgrade Required" : "AI Access Restricted"}
            description={
              requiresUpgrade
                ? "AI question generation requires a premium subscription. Please upgrade your account."
                : "AI features are not available for your account. Please contact support."
            }
            type="error"
            showIcon
            className="mb-4"
          />
        ) : sessionExpiringSoon ? (
          <Alert
            message="Session Expiring Soon"
            description={`Your session will expire in ${timeUntilExpiry}. Consider refreshing your login.`}
            type="warning"
            showIcon
            className="mb-4"
            action={
              <Button
                size="small"
                onClick={() => setShowLoginModal(true)}
              >
                Refresh Login
              </Button>
            }
          />
        ) : (
          <Alert
            message="AI Features Ready"
            description={`Welcome ${user?.name}! You have full access to AI question generation.`}
            type="success"
            showIcon
            className="mb-4"
          />
        )}

        {isGenerating && (
          <Alert
            message="Generating Questions"
            description={
              <div>
                <p>AI is generating your questions. This may take a few moments...</p>
                <Progress percent={generationProgress} status="active" />
              </div>
            }
            type="info"
            showIcon
            className="mb-4"
          />
        )}

        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          disabled={isGenerating || !hasAIAccess || authLoading}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24}>
              <Alert
                message="Exam Selection"
                description="You can either select an existing exam or create a new one using the auto-generate feature. Questions can also be generated independently without an exam."
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            </Col>

            <Col xs={24} md={16}>
              <Form.Item
                name="examId"
                label="Target Exam (Optional)"
                extra="Leave empty to generate standalone questions, or select an existing exam"
              >
                <Select
                  placeholder="Optional: Choose an existing exam"
                  allowClear
                >
                  {exams && exams.length > 0 && exams.map((exam) => (
                    exam && exam._id ? (
                      <Option key={exam._id} value={exam._id}>
                        {exam.name} - {exam.category}
                      </Option>
                    ) : null
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} md={8}>
              <Form.Item label="Or Create New Exam">
                <Button
                  type="dashed"
                  icon={<FaRobot />}
                  onClick={openAutoGenerateModal}
                  style={{ width: "100%" }}
                  disabled={isGenerating || !hasAIAccess || authLoading}
                >
                  Auto-Generate New Exam
                </Button>
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                name="level"
                label="Education Level"
                rules={[{ required: true, message: "Please select a level" }]}
              >
                <Select 
                  placeholder="Choose education level"
                  onChange={handleLevelChange}
                >
                  {levelOptions.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                name="class"
                label="Class"
                rules={[{ required: true, message: "Please select a class" }]}
              >
                <Select
                  placeholder="Choose class"
                  disabled={!selectedLevel}
                  onChange={handleClassChange}
                >
                  {selectedLevel && classOptions[selectedLevel]?.map((cls) => (
                    <Option key={cls} value={cls}>
                      Class {cls}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                name="subjects"
                label="Subjects"
                rules={[{ required: true, message: "Please select at least one subject" }]}
              >
                <Select
                  mode="multiple"
                  placeholder="Choose subjects"
                  disabled={!selectedLevel}
                  onChange={handleSubjectsChange}
                >
                  {availableSubjects.map((subject) => (
                    <Option key={subject} value={subject}>
                      {subject}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                name="questionTypes"
                label="Question Types"
                rules={[{ required: true, message: "Please select at least one question type" }]}
              >
                <Checkbox.Group options={questionTypeOptions} />
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                name="difficultyLevels"
                label="Difficulty Levels"
                rules={[{ required: true, message: "Please select at least one difficulty level" }]}
              >
                <Checkbox.Group options={difficultyOptions} />
              </Form.Item>
            </Col>

            <Col xs={24} md={8}>
              <Form.Item
                name="totalQuestions"
                label="Total Questions"
                rules={[
                  { required: true, message: "Please enter total questions" },
                  { type: "number", min: 1, max: 50, message: "Must be between 1 and 50" }
                ]}
              >
                <InputNumber
                  min={1}
                  max={50}
                  placeholder="Enter total questions"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider>Question Distribution</Divider>

          {selectedLevel && selectedClass && selectedSubjects.length > 0 && (
            <Alert
              message="Tanzania Syllabus Information"
              description={
                <div>
                  <p><strong>Level:</strong> {selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</p>
                  <p><strong>Class:</strong> {selectedClass}</p>
                  <p><strong>Subjects:</strong> {selectedSubjects.join(", ")}</p>
                  <p><strong>Available Topics:</strong> {availableTopics.length} topics from Tanzania National Curriculum</p>
                  <p><strong>Auto-generate:</strong> Use the button above to create a new exam with proper structure</p>
                </div>
              }
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}

          <Row gutter={[16, 16]}>
            <Col xs={24} md={8}>
              <Form.Item
                name={["questionDistribution", "multiple_choice"]}
                label="Multiple Choice"
              >
                <InputNumber
                  min={0}
                  placeholder="0"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>

            <Col xs={24} md={8}>
              <Form.Item
                name={["questionDistribution", "fill_blank"]}
                label="Fill in the Blank"
              >
                <InputNumber
                  min={0}
                  placeholder="0"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>

            <Col xs={24} md={8}>
              <Form.Item
                name={["questionDistribution", "picture_based"]}
                label="Picture-based"
              >
                <InputNumber
                  min={0}
                  placeholder="0"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="syllabusTopics"
            label={`Tanzania Syllabus Topics (${availableTopics.length} available)`}
            extra={availableTopics.length === 0 ? "Select level, class, and subjects to see available topics" : "Select specific topics from Tanzania National Curriculum"}
          >
            <Select
              mode="multiple"
              placeholder={availableTopics.length === 0 ? "No topics available - select level, class, and subjects first" : "Choose specific topics from Tanzania syllabus"}
              style={{ width: "100%" }}
              disabled={availableTopics.length === 0}
              optionFilterProp="children"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {availableTopics.map((topic, index) => (
                <Option key={`${topic.subject}-${topic.topicName}-${index}`} value={topic.topicName}>
                  <div>
                    <strong>{topic.topicName}</strong>
                    <div style={{ fontSize: "12px", color: "#666" }}>
                      {topic.subject} • Difficulty: {topic.difficulty}
                    </div>
                    {topic.subtopics && topic.subtopics.length > 0 && (
                      <div style={{ fontSize: "11px", color: "#999" }}>
                        Subtopics: {topic.subtopics.slice(0, 3).join(", ")}
                        {topic.subtopics.length > 3 && ` +${topic.subtopics.length - 3} more`}
                      </div>
                    )}
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <div className="form-actions">
            <Button onClick={onBack} disabled={isGenerating}>
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={isGenerating}
              disabled={!hasAIAccess || authLoading}
              icon={<FaRobot />}
            >
              {isGenerating ? "Generating..." : !hasAIAccess ? "Login Required" : "Generate Questions"}
            </Button>
          </div>
        </Form>
      </Card>

      <AutoGenerateExamModal
        visible={showAutoGenerateModal}
        onCancel={() => setShowAutoGenerateModal(false)}
        onSuccess={handleAutoGenerateExamSuccess}
        prefilledData={{
          level: selectedLevel,
          class: selectedClass,
          subjects: selectedSubjects,
        }}
      />

      <AILoginModal
        visible={showLoginModal}
        onCancel={() => setShowLoginModal(false)}
        onSuccess={(userData) => {
          handleLoginSuccess(userData);
          setShowLoginModal(false);
        }}
        title="AI Features Login Required"
        description="Please login to access AI question generation features. Your session may have expired or you need enhanced permissions."
      />
    </div>
  );
}

export default QuestionGenerationForm;
