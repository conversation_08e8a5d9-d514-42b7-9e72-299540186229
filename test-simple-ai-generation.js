const AIQuestionGenerationService = require('./services/aiQuestionGenerationService');
const mongoose = require('mongoose');
require('dotenv').config();

async function testSimpleAIGeneration() {
  try {
    console.log('🔍 Testing Simple AI Question Generation...');
    
    // Connect to database
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    const service = new AIQuestionGenerationService();
    console.log('✅ Service created, API key present:', service.openaiApiKey ? 'Yes' : 'No');
    
    // Test the parseJSONResponse method first
    console.log('\n1️⃣ Testing JSON response parsing...');
    
    const testResponse = '{"question": "Test question", "options": {"A": "Option A", "B": "Option B"}, "correctOption": "A"}';
    try {
      const parsed = service.parseJSONResponse(testResponse);
      console.log('✅ JSON parsing works:', parsed.question);
    } catch (error) {
      console.log('❌ JSON parsing failed:', error.message);
      return;
    }
    
    // Test with markdown wrapped JSON
    const markdownResponse = '```json\n{"question": "Test question", "options": {"A": "Option A"}, "correctOption": "A"}\n```';
    try {
      const parsed = service.parseJSONResponse(markdownResponse);
      console.log('✅ Markdown JSON parsing works:', parsed.question);
    } catch (error) {
      console.log('❌ Markdown JSON parsing failed:', error.message);
      return;
    }
    
    // Test actual question generation
    console.log('\n2️⃣ Testing actual question generation...');
    
    const generationParams = {
      questionTypes: ['multiple_choice'],
      subjects: ['Mathematics'],
      level: 'primary',
      class: '3',
      difficultyLevels: ['medium'],
      syllabusTopics: ['Basic arithmetic', 'Addition'],
      totalQuestions: 1,
      questionDistribution: {
        multiple_choice: 1,
        fill_blank: 0,
        picture_based: 0
      }
    };
    
    console.log('📝 Generation parameters:', JSON.stringify(generationParams, null, 2));
    
    const result = await service.generateQuestions(
      generationParams,
      new mongoose.Types.ObjectId(), // Use a valid ObjectId for requestedBy
      null // No exam ID
    );
    
    if (result.success) {
      console.log('\n✅ Question generation successful!');
      console.log('📊 Results:');
      console.log('  - Generated questions:', result.questions.length);
      console.log('  - Generation time:', result.generationTime, 'ms');
      console.log('  - Generation ID:', result.generationId);
      
      if (result.questions.length > 0) {
        const question = result.questions[0];
        console.log('\n📝 Sample question:');
        console.log('  Question:', question.name);
        console.log('  Options:', JSON.stringify(question.options, null, 4));
        console.log('  Correct answer:', question.correctOption);
        console.log('  Difficulty:', question.difficultyLevel);
        console.log('  Topics:', question.syllabusTopics);
        console.log('  Type:', question.questionType);
      }
    } else {
      console.log('\n❌ Question generation failed:', result.error);
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('HTTP Status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
}

testSimpleAIGeneration();
