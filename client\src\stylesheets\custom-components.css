/* Enhanced card components */
.card {
  box-shadow: var(--shadow-md) !important;
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--gray-200) !important;
  background-color: var(--white) !important;
  transition: var(--transition-normal) !important;
  overflow: hidden;
  position: relative;
  padding: var(--space-4) !important;
  margin-bottom: var(--space-4) !important;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary, linear-gradient(135deg, #007BFF 0%, #0056D2 100%));
  opacity: 0;
  transition: var(--transition-normal, 300ms cubic-bezier(0.4, 0, 0.2, 1));
}

.card:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-2px) !important;
  border-color: var(--primary) !important;
}

.card:hover::before {
  opacity: 1;
}

.card-lg {
  box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1));
  border-radius: var(--radius-2xl, 1.5rem);
  border: 1px solid var(--gray-200, #e5e7eb);
  padding: var(--space-8, 2rem);
}

.card-sm {
  padding: var(--space-4, 1rem);
  border-radius: var(--radius-lg, 0.75rem);
}

.card-glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.divider {
  border-bottom: 1px solid #e5e7eb;
  margin: 16px 0;
}

/* Modern loader styles */
.loader-parent {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: var(--z-modal, 1050);
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.loader {
  height: 60px;
  width: 60px;
  border: 4px solid var(--gray-200, #e5e7eb);
  border-top: 4px solid var(--primary, #007BFF);
  border-radius: var(--radius-full, 50%);
  animation: spin 1s linear infinite;
  position: relative;
}

.loader::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  background: var(--primary, #007BFF);
  border-radius: var(--radius-full, 50%);
  transform: translate(-50%, -50%);
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.5; transform: translate(-50%, -50%) scale(0.8); }
}

/* Legacy option styles - replaced by quiz-option classes */
.option {
  padding: var(--space-3) !important;
  border: 2px solid var(--gray-200) !important;
  border-radius: var(--radius-md) !important;
  background: var(--white) !important;
  cursor: pointer !important;
  transition: var(--transition-normal) !important;
  margin-bottom: var(--space-2) !important;
}

.selected-option {
  padding: var(--space-3) !important;
  border: 2px solid var(--primary) !important;
  border-radius: var(--radius-md) !important;
  background: var(--primary) !important;
  color: var(--white) !important;
}

.result {
  background-color: #a5c8c9;
  max-width: max-content;
  padding: 20px;
  color: black !important;
  border-radius: 5px;
}

.lottie-animation {
  height: 300px;
}

/* Legacy timer styles - replaced by quiz-timer classes */
.timer {
  background-color: var(--primary);
  color: var(--white) !important;
  padding: var(--space-3);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
  width: 80px;
  border-radius: var(--radius-full);
  font-weight: 600;
  transition: var(--transition-normal);
}

@keyframes loader {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}