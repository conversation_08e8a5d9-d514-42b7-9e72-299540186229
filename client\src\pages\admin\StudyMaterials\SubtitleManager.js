import React, { useState, useEffect } from 'react';
import { Table, Button, Upload, Select, message, Modal, Tag, Space, Tooltip } from 'antd';
import {
  UploadOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  RobotOutlined,
  FileTextOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import { generateSubtitles, uploadSubtitle } from '../../../apicalls/subtitles';
import { getAllVideos } from '../../../apicalls/study';
import './SubtitleManager.css';

const { Option } = Select;

const SubtitleManager = () => {
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [languageName, setLanguageName] = useState('English');
  const [fileList, setFileList] = useState([]);
  const [generatingSubtitles, setGeneratingSubtitles] = useState({});
  
  const dispatch = useDispatch();

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Spanish' },
    { code: 'fr', name: 'French' },
    { code: 'de', name: 'German' },
    { code: 'it', name: 'Italian' },
    { code: 'pt', name: 'Portuguese' },
    { code: 'ru', name: 'Russian' },
    { code: 'ja', name: 'Japanese' },
    { code: 'ko', name: 'Korean' },
    { code: 'zh', name: 'Chinese' },
    { code: 'ar', name: 'Arabic' },
    { code: 'hi', name: 'Hindi' },
    { code: 'ur', name: 'Urdu' },
    { code: 'bn', name: 'Bengali' }
  ];

  useEffect(() => {
    fetchVideos();
  }, []);

  const fetchVideos = async () => {
    try {
      setLoading(true);
      const response = await getAllVideos();
      if (response.success) {
        setVideos(response.data);
      } else {
        message.error('Failed to fetch videos');
      }
    } catch (error) {
      message.error('Error fetching videos');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateSubtitles = async (videoId, language = 'en') => {
    try {
      setGeneratingSubtitles(prev => ({ ...prev, [videoId]: true }));
      message.info('Generating subtitles... This may take a few minutes.');
      
      const response = await generateSubtitles(videoId, language);
      if (response.success) {
        message.success('Subtitles generated successfully!');
        fetchVideos(); // Refresh the list
      } else {
        message.error(response.message || 'Failed to generate subtitles');
      }
    } catch (error) {
      message.error('Error generating subtitles');
    } finally {
      setGeneratingSubtitles(prev => ({ ...prev, [videoId]: false }));
    }
  };

  const handleUploadSubtitle = async () => {
    if (!selectedVideo || fileList.length === 0) {
      message.warning('Please select a video and upload a subtitle file');
      return;
    }

    try {
      dispatch(ShowLoading());
      
      const formData = new FormData();
      formData.append('subtitle', fileList[0]);
      formData.append('language', selectedLanguage);
      formData.append('languageName', languageName);
      formData.append('isDefault', selectedLanguage === 'en');

      const response = await uploadSubtitle(selectedVideo._id, formData);
      
      if (response.success) {
        message.success('Subtitle uploaded successfully!');
        setUploadModalVisible(false);
        setFileList([]);
        setSelectedVideo(null);
        fetchVideos();
      } else {
        message.error(response.message || 'Failed to upload subtitle');
      }
    } catch (error) {
      message.error('Error uploading subtitle');
    } finally {
      dispatch(HideLoading());
    }
  };

  const getSubtitleStatus = (video) => {
    if (video.subtitleGenerationStatus === 'processing') {
      return <Tag color="blue" icon={<RobotOutlined />}>Generating...</Tag>;
    }
    if (video.hasSubtitles && video.subtitles?.length > 0) {
      return (
        <Space>
          <Tag color="green" icon={<FileTextOutlined />}>
            {video.subtitles.length} subtitle{video.subtitles.length > 1 ? 's' : ''}
          </Tag>
          {video.subtitles.map(sub => (
            <Tag 
              key={sub.language} 
              color={sub.isAutoGenerated ? 'blue' : 'purple'}
              size="small"
            >
              {sub.languageName} {sub.isAutoGenerated ? '(Auto)' : '(Custom)'}
            </Tag>
          ))}
        </Space>
      );
    }
    if (video.subtitleGenerationStatus === 'failed') {
      return <Tag color="red">Generation Failed</Tag>;
    }
    return <Tag color="default">No Subtitles</Tag>;
  };

  const columns = [
    {
      title: 'Video',
      dataIndex: 'title',
      key: 'title',
      width: '25%',
      render: (title, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{title}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.subject} • Class {record.className} • {record.level}
          </div>
        </div>
      ),
    },
    {
      title: 'Subtitle Status',
      key: 'subtitleStatus',
      width: '30%',
      render: (_, record) => getSubtitleStatus(record),
    },
    {
      title: 'Video Source',
      key: 'videoSource',
      width: '15%',
      render: (_, record) => {
        if (record.videoUrl && record.videoUrl.includes('amazonaws.com')) {
          return <Tag color="orange" icon={<GlobalOutlined />}>S3 Video</Tag>;
        }
        if (record.videoID && !record.videoUrl) {
          return <Tag color="red" icon={<PlayCircleOutlined />}>YouTube</Tag>;
        }
        return <Tag color="default">Unknown</Tag>;
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '30%',
      render: (_, record) => (
        <Space>
          {record.videoUrl && record.videoUrl.includes('amazonaws.com') && (
            <Tooltip title="Generate subtitles using AI">
              <Button
                type="primary"
                icon={<RobotOutlined />}
                size="small"
                loading={generatingSubtitles[record._id]}
                onClick={() => handleGenerateSubtitles(record._id)}
                disabled={record.subtitleGenerationStatus === 'processing'}
              >
                Generate
              </Button>
            </Tooltip>
          )}
          
          <Tooltip title="Upload custom subtitle file">
            <Button
              icon={<UploadOutlined />}
              size="small"
              onClick={() => {
                setSelectedVideo(record);
                setUploadModalVisible(true);
              }}
            >
              Upload
            </Button>
          </Tooltip>
          
          <Tooltip title="Refresh video data">
            <Button
              icon={<ReloadOutlined />}
              size="small"
              onClick={fetchVideos}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const uploadProps = {
    beforeUpload: (file) => {
      const isSRT = file.name.toLowerCase().endsWith('.srt');
      if (!isSRT) {
        message.error('Please upload a .srt subtitle file');
        return false;
      }
      setFileList([file]);
      return false; // Prevent automatic upload
    },
    fileList,
    onRemove: () => {
      setFileList([]);
    },
  };

  return (
    <div className="subtitle-manager">
      <div className="subtitle-manager-header">
        <h2>Subtitle Management</h2>
        <p>Manage subtitles for educational videos to enhance learning accessibility</p>
      </div>

      <Table
        columns={columns}
        dataSource={videos}
        rowKey="_id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} videos`,
        }}
        scroll={{ x: 1000 }}
      />

      <Modal
        title="Upload Custom Subtitle"
        open={uploadModalVisible}
        onOk={handleUploadSubtitle}
        onCancel={() => {
          setUploadModalVisible(false);
          setFileList([]);
          setSelectedVideo(null);
        }}
        okText="Upload"
        cancelText="Cancel"
      >
        {selectedVideo && (
          <div style={{ marginBottom: '16px' }}>
            <strong>Video:</strong> {selectedVideo.title}
          </div>
        )}
        
        <div style={{ marginBottom: '16px' }}>
          <label>Language:</label>
          <Select
            value={selectedLanguage}
            onChange={(value) => {
              setSelectedLanguage(value);
              const lang = languages.find(l => l.code === value);
              setLanguageName(lang?.name || value);
            }}
            style={{ width: '100%', marginTop: '8px' }}
          >
            {languages.map(lang => (
              <Option key={lang.code} value={lang.code}>
                {lang.name}
              </Option>
            ))}
          </Select>
        </div>

        <div>
          <label>Subtitle File (.srt):</label>
          <Upload {...uploadProps} style={{ marginTop: '8px' }}>
            <Button icon={<UploadOutlined />}>Select SRT File</Button>
          </Upload>
        </div>
      </Modal>
    </div>
  );
};

export default SubtitleManager;
