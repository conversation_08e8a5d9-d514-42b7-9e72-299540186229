// Tanzania Institute of Education (TIE) Official Syllabus Structure
// Based on official syllabuses from https://www.tie.go.tz/
// Updated to reflect current TIE curriculum standards

// PRIMARY EDUCATION SYLLABUS (Standards I-VI)
const primarySyllabus = {
  "Mathematics": {
    "I": {
      topics: [
        {
          topicName: "Numbers and Numeration",
          competencies: ["Count objects up to 10", "Recognize and write numbers 1-10", "Compare quantities"],
          subtopics: [
            { name: "Counting 1-10", difficulty: "easy", keyTerms: ["counting", "numbers", "sequence"] },
            { name: "Number Recognition", difficulty: "easy", keyTerms: ["digits", "symbols", "identification"] },
            { name: "Number Writing", difficulty: "medium", keyTerms: ["writing", "formation", "practice"] }
          ]
        },
        {
          topicName: "Basic Operations",
          competencies: ["Perform addition within 10", "Perform subtraction within 10"],
          subtopics: [
            { name: "Addition within 10", difficulty: "medium", keyTerms: ["addition", "plus", "sum", "altogether"] },
            { name: "Subtraction within 10", difficulty: "medium", keyTerms: ["subtraction", "minus", "difference", "take away"] }
          ]
        },
        {
          topicName: "Geometry",
          competencies: ["Identify basic shapes", "Describe properties of shapes"],
          subtopics: [
            { name: "Basic Shapes", difficulty: "easy", keyTerms: ["circle", "square", "triangle", "rectangle"] },
            { name: "Shape Properties", difficulty: "medium", keyTerms: ["sides", "corners", "round", "straight"] }
          ]
        }
      ]
    },
    "II": {
      topics: [
        {
          topicName: "Numbers and Numeration",
          competencies: ["Count and write numbers up to 100", "Understand place value", "Compare and order numbers"],
          subtopics: [
            { name: "Numbers up to 100", difficulty: "easy", keyTerms: ["hundreds", "tens", "ones", "place value"] },
            { name: "Number Comparison", difficulty: "medium", keyTerms: ["greater than", "less than", "equal to"] },
            { name: "Number Patterns", difficulty: "medium", keyTerms: ["patterns", "sequences", "skip counting"] }
          ]
        },
        {
          topicName: "Operations",
          competencies: ["Add and subtract within 100", "Understand multiplication as repeated addition"],
          subtopics: [
            { name: "Addition within 100", difficulty: "medium", keyTerms: ["addition", "carrying", "regrouping"] },
            { name: "Subtraction within 100", difficulty: "medium", keyTerms: ["subtraction", "borrowing", "difference"] },
            { name: "Introduction to Multiplication", difficulty: "hard", keyTerms: ["groups", "repeated addition", "times"] }
          ]
        }
      ]
    },
    "III": {
      topics: [
        {
          topicName: "Numbers and Operations",
          competencies: ["Work with numbers up to 1000", "Multiply and divide within 100", "Understand fractions"],
          subtopics: [
            { name: "Numbers up to 1000", difficulty: "medium", keyTerms: ["thousands", "hundreds", "tens", "ones"] },
            { name: "Multiplication Tables", difficulty: "medium", keyTerms: ["multiplication", "times tables", "product"] },
            { name: "Simple Division", difficulty: "hard", keyTerms: ["division", "sharing", "groups", "quotient"] },
            { name: "Introduction to Fractions", difficulty: "medium", keyTerms: ["half", "quarter", "whole", "part"] }
          ]
        }
      ]
    },
    "IV": {
      topics: [
        {
          topicName: "Numbers and Operations",
          competencies: ["Master multiplication and division", "Work with fractions and decimals", "Solve word problems"],
          subtopics: [
            { name: "Advanced Multiplication", difficulty: "medium", keyTerms: ["multiplication", "factors", "multiples"] },
            { name: "Division with Remainders", difficulty: "hard", keyTerms: ["division", "remainder", "quotient"] },
            { name: "Fractions", difficulty: "medium", keyTerms: ["numerator", "denominator", "equivalent fractions"] },
            { name: "Introduction to Decimals", difficulty: "hard", keyTerms: ["decimal point", "tenths", "hundredths"] }
          ]
        },
        {
          topicName: "Measurement",
          competencies: ["Measure length, mass, and capacity", "Tell time accurately", "Calculate perimeter"],
          subtopics: [
            { name: "Length and Distance", difficulty: "medium", keyTerms: ["meter", "centimeter", "kilometer", "millimeter"] },
            { name: "Mass and Weight", difficulty: "medium", keyTerms: ["kilogram", "gram", "weighing", "balance"] },
            { name: "Time", difficulty: "medium", keyTerms: ["hours", "minutes", "seconds", "clock", "calendar"] },
            { name: "Perimeter", difficulty: "hard", keyTerms: ["perimeter", "around", "boundary", "measurement"] }
          ]
        }
      ]
    },
    "V": {
      topics: [
        {
          topicName: "Advanced Numbers",
          competencies: ["Work with decimals and percentages", "Understand ratio and proportion", "Solve complex problems"],
          subtopics: [
            { name: "Decimal Operations", difficulty: "hard", keyTerms: ["adding decimals", "subtracting decimals", "decimal places"] },
            { name: "Percentages", difficulty: "hard", keyTerms: ["percent", "percentage", "out of 100", "proportion"] },
            { name: "Ratio and Proportion", difficulty: "hard", keyTerms: ["ratio", "proportion", "comparison", "relationship"] }
          ]
        },
        {
          topicName: "Geometry and Measurement",
          competencies: ["Calculate area and volume", "Understand angles", "Work with coordinate geometry"],
          subtopics: [
            { name: "Area", difficulty: "hard", keyTerms: ["area", "square units", "rectangle", "triangle"] },
            { name: "Volume", difficulty: "hard", keyTerms: ["volume", "cubic units", "capacity", "space"] },
            { name: "Angles", difficulty: "medium", keyTerms: ["angle", "degrees", "right angle", "acute", "obtuse"] }
          ]
        }
      ]
    },
    "VI": {
      topics: [
        {
          topicName: "Pre-Algebra",
          competencies: ["Solve simple equations", "Understand variables", "Work with number patterns"],
          subtopics: [
            { name: "Simple Equations", difficulty: "hard", keyTerms: ["equation", "variable", "solve", "unknown"] },
            { name: "Number Patterns and Sequences", difficulty: "medium", keyTerms: ["pattern", "sequence", "rule", "term"] },
            { name: "Algebraic Expressions", difficulty: "hard", keyTerms: ["expression", "variable", "coefficient", "term"] }
          ]
        },
        {
          topicName: "Data Handling",
          competencies: ["Collect and organize data", "Create and interpret graphs", "Calculate averages"],
          subtopics: [
            { name: "Data Collection", difficulty: "medium", keyTerms: ["data", "survey", "tally", "frequency"] },
            { name: "Graphs and Charts", difficulty: "medium", keyTerms: ["bar graph", "pie chart", "line graph", "pictograph"] },
            { name: "Average and Statistics", difficulty: "hard", keyTerms: ["average", "mean", "mode", "median"] }
          ]
        }
      ]
    }
  },

  "Science and Technology": {
    "I": {
      topics: [
        {
          topicName: "Living and Non-living Things",
          competencies: ["Distinguish between living and non-living things", "Identify basic needs of living things"],
          subtopics: [
            { name: "Characteristics of Living Things", difficulty: "easy", keyTerms: ["living", "non-living", "movement", "growth"] },
            { name: "Animals Around Us", difficulty: "easy", keyTerms: ["animals", "pets", "wild animals", "domestic"] },
            { name: "Plants Around Us", difficulty: "easy", keyTerms: ["plants", "trees", "flowers", "leaves"] }
          ]
        },
        {
          topicName: "Our Environment",
          competencies: ["Identify components of environment", "Practice environmental care"],
          subtopics: [
            { name: "Clean and Dirty Environment", difficulty: "easy", keyTerms: ["clean", "dirty", "hygiene", "health"] },
            { name: "Day and Night", difficulty: "easy", keyTerms: ["sun", "moon", "day", "night", "light", "dark"] }
          ]
        }
      ]
    },
    "II": {
      topics: [
        {
          topicName: "Human Body",
          competencies: ["Identify body parts and their functions", "Understand the five senses"],
          subtopics: [
            { name: "Body Parts and Functions", difficulty: "easy", keyTerms: ["head", "hands", "legs", "body", "function"] },
            { name: "The Five Senses", difficulty: "medium", keyTerms: ["sight", "hearing", "touch", "taste", "smell", "eyes", "ears"] },
            { name: "Personal Hygiene", difficulty: "easy", keyTerms: ["washing", "brushing", "cleanliness", "health"] }
          ]
        }
      ]
    },
    "III": {
      topics: [
        {
          topicName: "Matter and Materials",
          competencies: ["Classify materials by properties", "Understand states of matter"],
          subtopics: [
            { name: "States of Matter", difficulty: "medium", keyTerms: ["solid", "liquid", "gas", "matter", "state"] },
            { name: "Properties of Materials", difficulty: "medium", keyTerms: ["hard", "soft", "rough", "smooth", "transparent"] },
            { name: "Uses of Materials", difficulty: "medium", keyTerms: ["wood", "metal", "plastic", "glass", "fabric"] }
          ]
        }
      ]
    },
    "IV": {
      topics: [
        {
          topicName: "Energy and Forces",
          competencies: ["Identify sources of energy", "Understand basic forces"],
          subtopics: [
            { name: "Sources of Energy", difficulty: "medium", keyTerms: ["sun", "wind", "water", "energy", "renewable"] },
            { name: "Heat and Light", difficulty: "medium", keyTerms: ["heat", "light", "temperature", "hot", "cold"] },
            { name: "Forces and Motion", difficulty: "medium", keyTerms: ["push", "pull", "force", "motion", "movement"] }
          ]
        }
      ]
    },
    "V": {
      topics: [
        {
          topicName: "Earth and Space",
          competencies: ["Understand solar system", "Explain weather patterns"],
          subtopics: [
            { name: "Solar System", difficulty: "medium", keyTerms: ["sun", "planets", "moon", "stars", "earth"] },
            { name: "Weather and Climate", difficulty: "medium", keyTerms: ["weather", "rain", "sunshine", "wind", "temperature"] },
            { name: "Rocks and Soil", difficulty: "medium", keyTerms: ["rocks", "soil", "minerals", "erosion", "formation"] }
          ]
        }
      ]
    },
    "VI": {
      topics: [
        {
          topicName: "Technology and Innovation",
          competencies: ["Understand simple machines", "Appreciate technological development"],
          subtopics: [
            { name: "Simple Machines", difficulty: "hard", keyTerms: ["lever", "pulley", "wheel", "inclined plane", "machine"] },
            { name: "Communication Technology", difficulty: "medium", keyTerms: ["telephone", "radio", "internet", "communication"] },
            { name: "Transportation", difficulty: "medium", keyTerms: ["cars", "planes", "ships", "transport", "movement"] }
          ]
        }
      ]
    }
  },

  "English Language": {
    "I": {
      topics: [
        {
          topicName: "Language Use",
          competencies: ["Use English for basic communication", "Recognize letters and sounds"],
          subtopics: [
            { name: "Alphabet and Sounds", difficulty: "easy", keyTerms: ["letters", "alphabet", "sounds", "phonics"] },
            { name: "Simple Words", difficulty: "easy", keyTerms: ["words", "vocabulary", "meaning", "pronunciation"] },
            { name: "Greetings and Politeness", difficulty: "easy", keyTerms: ["hello", "goodbye", "please", "thank you"] }
          ]
        }
      ]
    },
    "II": {
      topics: [
        {
          topicName: "Reading and Writing",
          competencies: ["Read simple texts", "Write simple sentences"],
          subtopics: [
            { name: "Reading Simple Texts", difficulty: "medium", keyTerms: ["reading", "comprehension", "understanding", "text"] },
            { name: "Writing Simple Sentences", difficulty: "medium", keyTerms: ["writing", "sentences", "capital letters", "full stops"] }
          ]
        }
      ]
    }
  }
};

// ORDINARY SECONDARY EDUCATION SYLLABUS (Forms I-IV)
const ordinarySecondarySyllabus = {
  "Mathematics": {
    "I": {
      topics: [
        {
          topicName: "Numbers",
          competencies: ["Work with integers and rational numbers", "Understand number systems"],
          subtopics: [
            { name: "Integers", difficulty: "medium", keyTerms: ["positive", "negative", "zero", "number line"] },
            { name: "Rational Numbers", difficulty: "hard", keyTerms: ["fractions", "decimals", "ratios", "proportions"] },
            { name: "Irrational Numbers", difficulty: "hard", keyTerms: ["square roots", "pi", "irrational", "real numbers"] }
          ]
        }
      ]
    }
  },
  "Physics": {
    "I": {
      topics: [
        {
          topicName: "Measurement",
          competencies: ["Make accurate measurements", "Understand units and instruments"],
          subtopics: [
            { name: "Length and Time", difficulty: "easy", keyTerms: ["meter", "second", "measurement", "accuracy"] },
            { name: "Mass and Weight", difficulty: "medium", keyTerms: ["kilogram", "newton", "gravity", "mass"] }
          ]
        }
      ]
    }
  }
};

// ADVANCED SECONDARY EDUCATION SYLLABUS (Forms V-VI)
const advancedSecondarySyllabus = {
  "Advanced Mathematics": {
    "V": {
      topics: [
        {
          topicName: "Functions",
          competencies: ["Understand and work with various functions", "Solve complex equations"],
          subtopics: [
            { name: "Polynomial Functions", difficulty: "hard", keyTerms: ["polynomial", "degree", "coefficients", "roots"] },
            { name: "Exponential Functions", difficulty: "hard", keyTerms: ["exponential", "logarithm", "base", "growth"] }
          ]
        }
      ]
    }
  }
};

// Question generation guidelines based on TIE standards
const tieQuestionGuidelines = {
  primary: {
    languageLevel: "Simple, clear language appropriate for young learners (Standards I-VI)",
    contextualReferences: "Use familiar Tanzanian contexts (local animals, foods, places, culture)",
    culturalSensitivity: "Include diverse Tanzanian cultural references and Swahili integration",
    assessmentFocus: "Basic understanding, application, and foundational skills development",
    competencyBased: "Align with TIE competency-based curriculum objectives"
  },
  ordinary_secondary: {
    languageLevel: "Intermediate academic language (Forms I-IV)",
    contextualReferences: "Real-world applications relevant to Tanzanian society and economy",
    culturalSensitivity: "Include examples from Tanzanian geography, economy, and social issues",
    assessmentFocus: "Analysis, synthesis, problem-solving, and critical thinking skills",
    competencyBased: "Focus on practical application and life skills"
  },
  advanced_secondary: {
    languageLevel: "Advanced academic and technical language (Forms V-VI)",
    contextualReferences: "Complex real-world scenarios and university preparation",
    culturalSensitivity: "Global perspectives with strong Tanzanian context and development focus",
    assessmentFocus: "Advanced critical thinking, evaluation, research, and university readiness",
    competencyBased: "Prepare for higher education and professional development"
  }
};

module.exports = {
  primarySyllabus,
  ordinarySecondarySyllabus,
  advancedSecondarySyllabus,
  tieQuestionGuidelines,
};
