const axios = require('axios');
const jwt = require('jsonwebtoken');
require('dotenv').config();

async function testAuthFlow() {
  console.log('🧪 Testing Authentication Flow...\n');

  try {
    // Test 1: Valid token
    console.log('📋 Test 1: Testing with valid token...');
    const validToken = jwt.sign({ userId: 'test-user-id' }, process.env.JWT_SECRET, { expiresIn: '1h' });
    
    const validResponse = await axios.post('http://localhost:5000/api/ai-questions/generate-questions', {
      questionTypes: ['multiple_choice'],
      subjects: ['Mathematics'],
      level: 'primary',
      class: '3',
      totalQuestions: 1,
      questionDistribution: { multiple_choice: 1 }
    }, {
      headers: { Authorization: `Bearer ${validToken}` }
    });
    
    console.log('✅ Valid token test passed');

  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('❌ Valid token test failed with 401 - check JWT_SECRET');
    } else {
      console.log('✅ Valid token test passed (expected error for missing user)');
    }
  }

  try {
    // Test 2: Expired token
    console.log('\n📋 Test 2: Testing with expired token...');
    const expiredToken = jwt.sign({ userId: 'test-user-id' }, process.env.JWT_SECRET, { expiresIn: '-1h' });
    
    await axios.post('http://localhost:5000/api/ai-questions/generate-questions', {
      questionTypes: ['multiple_choice'],
      subjects: ['Mathematics'],
      level: 'primary',
      class: '3',
      totalQuestions: 1,
      questionDistribution: { multiple_choice: 1 }
    }, {
      headers: { Authorization: `Bearer ${expiredToken}` }
    });
    
    console.log('❌ Expired token test failed - should have returned 401');

  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Expired token test passed - correctly returned 401');
      console.log('📊 Error message:', error.response.data.message);
    } else {
      console.log('❌ Expired token test failed - unexpected error:', error.message);
    }
  }

  try {
    // Test 3: Invalid token
    console.log('\n📋 Test 3: Testing with invalid token...');
    
    await axios.post('http://localhost:5000/api/ai-questions/generate-questions', {
      questionTypes: ['multiple_choice'],
      subjects: ['Mathematics'],
      level: 'primary',
      class: '3',
      totalQuestions: 1,
      questionDistribution: { multiple_choice: 1 }
    }, {
      headers: { Authorization: 'Bearer invalid-token-here' }
    });
    
    console.log('❌ Invalid token test failed - should have returned 401');

  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Invalid token test passed - correctly returned 401');
      console.log('📊 Error message:', error.response.data.message);
    } else {
      console.log('❌ Invalid token test failed - unexpected error:', error.message);
    }
  }

  try {
    // Test 4: No token
    console.log('\n📋 Test 4: Testing with no token...');
    
    await axios.post('http://localhost:5000/api/ai-questions/generate-questions', {
      questionTypes: ['multiple_choice'],
      subjects: ['Mathematics'],
      level: 'primary',
      class: '3',
      totalQuestions: 1,
      questionDistribution: { multiple_choice: 1 }
    });
    
    console.log('❌ No token test failed - should have returned 401');

  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ No token test passed - correctly returned 401');
      console.log('📊 Error message:', error.response.data.message);
    } else {
      console.log('❌ No token test failed - unexpected error:', error.message);
    }
  }

  console.log('\n🎉 Authentication flow tests completed!');
  console.log('\n💡 If you\'re still seeing "user session expire please login again":');
  console.log('   1. Check browser extensions (password managers, security tools)');
  console.log('   2. Check browser console for JavaScript errors');
  console.log('   3. Try in incognito/private browsing mode');
  console.log('   4. Clear browser cache and localStorage');
}

// Run the test
testAuthFlow().catch(console.error);
