{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\QuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON>lock, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport '../pages/user/Quiz/responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Early return for invalid question\n  if (!question || !question.name) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-6xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"Question Not Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"This question could not be loaded. Please try refreshing the page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this);\n  }\n  const renderMCQ = () => {\n    if (!question || !question.options || Object.keys(question.options).length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-red-50 rounded-xl p-6 border border-red-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-4xl mb-2\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-700\",\n          children: \"No options available for this question.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this);\n    }\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2 sm:space-y-3\",\n      children: Object.entries(question.options).map(([key, value], index) => {\n        const optionKey = String(key).trim();\n        const optionValue = String(value || '').trim();\n        const label = optionLabels[index] || optionKey;\n        const isSelected = currentAnswer === optionKey;\n\n        // Skip empty options\n        if (!optionValue) return null;\n        return /*#__PURE__*/_jsxDEV(motion.button, {\n          onClick: () => handleAnswerSelect(optionKey),\n          whileHover: {\n            scale: 1.02\n          },\n          whileTap: {\n            scale: 0.98\n          },\n          className: `w-full text-left p-3 sm:p-4 rounded-lg sm:rounded-xl border-2 transition-all duration-300 quiz-option touch-manipulation ${isSelected ? 'bg-blue-600 text-white border-blue-600 shadow-lg' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3 sm:gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center font-bold text-xs sm:text-sm transition-all quiz-option-letter ${isSelected ? 'bg-white text-blue-600' : 'bg-blue-100 text-blue-700'}`,\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `flex-1 font-medium text-sm sm:text-base quiz-option-text ${isSelected ? 'text-white' : 'text-gray-800'}`,\n              children: optionValue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 17\n            }, this), isSelected && /*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-5 h-5 sm:w-6 sm:h-6 text-white flex-shrink-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 15\n          }, this)\n        }, optionKey, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFillBlank = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 sm:space-y-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-xs sm:text-sm font-medium text-gray-700 mb-2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u270F\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Your Answer:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: currentAnswer,\n        onChange: e => handleAnswerSelect(e.target.value),\n        placeholder: \"Type your answer here...\",\n        className: \"w-full px-3 sm:px-4 py-3 sm:py-4 border-2 border-gray-200 rounded-lg sm:rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all text-base sm:text-lg font-medium bg-white quiz-fill-input\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute right-3 sm:right-4 top-1/2 transform -translate-y-1/2\",\n        children: currentAnswer ? /*#__PURE__*/_jsxDEV(TbCheck, {\n          className: \"w-5 h-5 sm:w-6 sm:h-6 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-5 h-5 sm:w-6 sm:h-6 bg-gray-200 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 right-0 h-1 bg-gray-200 z-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"h-full bg-gradient-to-r from-blue-500 to-indigo-600\",\n        initial: {\n          width: 0\n        },\n        animate: {\n          width: `${progressPercentage}%`\n        },\n        transition: {\n          duration: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-100 pt-1 flex-shrink-0 z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:grid sm:grid-cols-3 items-center gap-3 sm:gap-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center sm:text-left w-full sm:w-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 truncate\",\n              children: examTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs sm:text-sm text-gray-600 hidden sm:block\",\n              children: \"Challenge your brain, beat the rest\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center order-first sm:order-none\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 rounded-lg sm:rounded-xl font-mono text-sm sm:text-lg lg:text-xl font-bold transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 border-2 border-red-300 animate-pulse' : 'bg-blue-100 text-blue-700 border-2 border-blue-300'}`,\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"TIME\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: formatTime(timeLeft)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center sm:justify-end w-full sm:w-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-100 text-gray-700 px-2 sm:px-3 py-1 sm:py-2 rounded-lg text-xs sm:text-sm lg:text-base font-semibold\",\n              children: [questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"bg-white rounded-xl sm:rounded-2xl lg:rounded-3xl shadow-lg p-4 sm:p-6 lg:p-10 mb-6 sm:mb-8 lg:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 sm:mb-6 lg:mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 sm:px-4 lg:px-6 py-1.5 sm:py-2 lg:py-3 rounded-full text-xs sm:text-sm lg:text-base font-semibold\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Question \", questionIndex + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 mb-6 sm:mb-8 lg:mb-10 leading-relaxed\",\n            children: question.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), question.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 sm:mb-8 lg:mb-10 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-block bg-gray-50 rounded-lg sm:rounded-xl p-3 sm:p-4 lg:p-6 border border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: question.image,\n                alt: \"Question\",\n                className: \"max-w-full max-h-64 sm:max-h-80 lg:max-h-96 rounded-lg shadow-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-options\",\n            children: question.options ? renderMCQ() : renderFillBlank()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)]\n        }, questionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t border-gray-200 shadow-lg flex-shrink-0 z-30\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 lg:px-8 py-3 sm:py-4 lg:py-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between gap-3 sm:gap-4 lg:gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onPrevious,\n            disabled: questionIndex === 0,\n            className: `flex items-center gap-1 sm:gap-2 px-3 sm:px-6 lg:px-8 py-2 sm:py-3 lg:py-4 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base lg:text-lg touch-manipulation ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 sm:gap-4 flex-1 justify-center\",\n            children: !isAnswered && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 sm:gap-2 text-amber-600 bg-amber-50 px-2 sm:px-3 lg:px-4 py-1 sm:py-2 lg:py-3 rounded-lg text-xs sm:text-sm lg:text-base border border-amber-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"Select an answer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: \"Answer required\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onNext,\n            disabled: !isAnswered,\n            className: `flex items-center gap-1 sm:gap-2 px-3 sm:px-6 lg:px-8 py-2 sm:py-3 lg:py-4 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base lg:text-lg touch-manipulation ${!isAnswered ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : questionIndex === totalQuestions - 1 ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg' : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg'}`,\n            children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"Submit Quiz\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: \"Submit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"Next\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: \"Next\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizRenderer, \"GLXCrRLAt2Wgb0CPI+PSeCbLXgs=\");\n_c = QuizRenderer;\nexport default QuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Quiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "handleAnswerSelect", "answer", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "progressPercentage", "name", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderMCQ", "options", "Object", "keys", "length", "optionLabels", "entries", "map", "key", "value", "index", "optionKey", "String", "trim", "optionValue", "label", "isSelected", "button", "onClick", "whileHover", "scale", "whileTap", "renderFillBlank", "type", "onChange", "e", "target", "placeholder", "div", "initial", "width", "animate", "transition", "duration", "opacity", "x", "image", "src", "alt", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/QuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport '../pages/user/Quiz/responsive.css';\n\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Early return for invalid question\n  if (!question || !question.name) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg\">\n          <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">Question Not Available</h3>\n          <p className=\"text-gray-600\">This question could not be loaded. Please try refreshing the page.</p>\n        </div>\n      </div>\n    );\n  }\n\n  const renderMCQ = () => {\n    if (!question || !question.options || Object.keys(question.options).length === 0) {\n      return (\n        <div className=\"text-center bg-red-50 rounded-xl p-6 border border-red-200\">\n          <div className=\"text-red-500 text-4xl mb-2\">⚠️</div>\n          <p className=\"text-red-700\">No options available for this question.</p>\n        </div>\n      );\n    }\n\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n\n    return (\n      <div className=\"space-y-2 sm:space-y-3\">\n        {Object.entries(question.options).map(([key, value], index) => {\n          const optionKey = String(key).trim();\n          const optionValue = String(value || '').trim();\n          const label = optionLabels[index] || optionKey;\n          const isSelected = currentAnswer === optionKey;\n\n          // Skip empty options\n          if (!optionValue) return null;\n\n          return (\n            <motion.button\n              key={optionKey}\n              onClick={() => handleAnswerSelect(optionKey)}\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              className={`w-full text-left p-3 sm:p-4 rounded-lg sm:rounded-xl border-2 transition-all duration-300 quiz-option touch-manipulation ${\n                isSelected\n                  ? 'bg-blue-600 text-white border-blue-600 shadow-lg'\n                  : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800'\n              }`}\n            >\n              <div className=\"flex items-center gap-3 sm:gap-4\">\n                <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center font-bold text-xs sm:text-sm transition-all quiz-option-letter ${\n                  isSelected\n                    ? 'bg-white text-blue-600'\n                    : 'bg-blue-100 text-blue-700'\n                }`}>\n                  {label}\n                </div>\n                <span className={`flex-1 font-medium text-sm sm:text-base quiz-option-text ${\n                  isSelected ? 'text-white' : 'text-gray-800'\n                }`}>\n                  {optionValue}\n                </span>\n                {isSelected && (\n                  <TbCheck className=\"w-5 h-5 sm:w-6 sm:h-6 text-white flex-shrink-0\" />\n                )}\n              </div>\n            </motion.button>\n          );\n        })}\n      </div>\n    );\n  };\n\n  const renderFillBlank = () => (\n    <div className=\"space-y-3 sm:space-y-4\">\n      <label className=\"block text-xs sm:text-sm font-medium text-gray-700 mb-2\">\n        <div className=\"flex items-center gap-2\">\n          <span>✏️</span>\n          <span>Your Answer:</span>\n        </div>\n      </label>\n      <div className=\"relative\">\n        <input\n          type=\"text\"\n          value={currentAnswer}\n          onChange={(e) => handleAnswerSelect(e.target.value)}\n          placeholder=\"Type your answer here...\"\n          className=\"w-full px-3 sm:px-4 py-3 sm:py-4 border-2 border-gray-200 rounded-lg sm:rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all text-base sm:text-lg font-medium bg-white quiz-fill-input\"\n        />\n        <div className=\"absolute right-3 sm:right-4 top-1/2 transform -translate-y-1/2\">\n          {currentAnswer ? (\n            <TbCheck className=\"w-5 h-5 sm:w-6 sm:h-6 text-green-500\" />\n          ) : (\n            <div className=\"w-5 h-5 sm:w-6 sm:h-6 bg-gray-200 rounded-full\"></div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\">\n      {/* Progress Bar */}\n      <div className=\"fixed top-0 left-0 right-0 h-1 bg-gray-200 z-50\">\n        <motion.div\n          className=\"h-full bg-gradient-to-r from-blue-500 to-indigo-600\"\n          initial={{ width: 0 }}\n          animate={{ width: `${progressPercentage}%` }}\n          transition={{ duration: 0.5 }}\n        />\n      </div>\n\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-100 pt-1 flex-shrink-0 z-40\">\n        <div className=\"max-w-6xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\">\n          <div className=\"flex flex-col sm:grid sm:grid-cols-3 items-center gap-3 sm:gap-0\">\n            {/* Quiz Title */}\n            <div className=\"text-center sm:text-left w-full sm:w-auto\">\n              <h1 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 truncate\">{examTitle}</h1>\n              <p className=\"text-xs sm:text-sm text-gray-600 hidden sm:block\">Challenge your brain, beat the rest</p>\n            </div>\n\n            {/* Centered Timer */}\n            <div className=\"flex justify-center order-first sm:order-none\">\n              <div className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 rounded-lg sm:rounded-xl font-mono text-sm sm:text-lg lg:text-xl font-bold transition-all ${\n                isTimeWarning\n                  ? 'bg-red-100 text-red-700 border-2 border-red-300 animate-pulse'\n                  : 'bg-blue-100 text-blue-700 border-2 border-blue-300'\n              }`}>\n                <TbClock className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\" />\n                <span className=\"hidden sm:inline\">TIME</span>\n                <span>{formatTime(timeLeft)}</span>\n              </div>\n            </div>\n\n            {/* Question Counter */}\n            <div className=\"flex justify-center sm:justify-end w-full sm:w-auto\">\n              <div className=\"bg-gray-100 text-gray-700 px-2 sm:px-3 py-1 sm:py-2 rounded-lg text-xs sm:text-sm lg:text-base font-semibold\">\n                {questionIndex + 1} of {totalQuestions}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content - Scrollable Area */}\n      <div className=\"flex-1 overflow-y-auto\">\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\">\n          <motion.div\n            key={questionIndex}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-xl sm:rounded-2xl lg:rounded-3xl shadow-lg p-4 sm:p-6 lg:p-10 mb-6 sm:mb-8 lg:mb-12\"\n          >\n            {/* Question Number Badge */}\n            <div className=\"mb-4 sm:mb-6 lg:mb-8\">\n              <div className=\"inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 sm:px-4 lg:px-6 py-1.5 sm:py-2 lg:py-3 rounded-full text-xs sm:text-sm lg:text-base font-semibold\">\n                <span>Question {questionIndex + 1}</span>\n              </div>\n            </div>\n\n            {/* Question Text */}\n            <div className=\"text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 mb-6 sm:mb-8 lg:mb-10 leading-relaxed\">\n              {question.name}\n            </div>\n\n            {/* Question Image */}\n            {question.image && (\n              <div className=\"mb-6 sm:mb-8 lg:mb-10 text-center\">\n                <div className=\"inline-block bg-gray-50 rounded-lg sm:rounded-xl p-3 sm:p-4 lg:p-6 border border-gray-200\">\n                  <img\n                    src={question.image}\n                    alt=\"Question\"\n                    className=\"max-w-full max-h-64 sm:max-h-80 lg:max-h-96 rounded-lg shadow-md\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Question Content */}\n            <div className=\"quiz-options\">\n              {question.options ? renderMCQ() : renderFillBlank()}\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Bottom Navigation */}\n      <div className=\"bg-white border-t border-gray-200 shadow-lg flex-shrink-0 z-30\">\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 lg:px-8 py-3 sm:py-4 lg:py-6\">\n          <div className=\"flex items-center justify-between gap-3 sm:gap-4 lg:gap-6\">\n            <button\n              onClick={onPrevious}\n              disabled={questionIndex === 0}\n              className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-6 lg:px-8 py-2 sm:py-3 lg:py-4 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base lg:text-lg touch-manipulation ${\n                questionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:shadow-md'\n              }`}\n            >\n              <TbArrowLeft className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\" />\n              <span className=\"hidden sm:inline\">Previous</span>\n            </button>\n\n            <div className=\"flex items-center gap-2 sm:gap-4 flex-1 justify-center\">\n              {!isAnswered && (\n                <div className=\"flex items-center gap-1 sm:gap-2 text-amber-600 bg-amber-50 px-2 sm:px-3 lg:px-4 py-1 sm:py-2 lg:py-3 rounded-lg text-xs sm:text-sm lg:text-base border border-amber-200\">\n                  <span>⚠️</span>\n                  <span className=\"hidden sm:inline\">Select an answer</span>\n                  <span className=\"sm:hidden\">Answer required</span>\n                </div>\n              )}\n            </div>\n\n            <button\n              onClick={onNext}\n              disabled={!isAnswered}\n              className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-6 lg:px-8 py-2 sm:py-3 lg:py-4 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base lg:text-lg touch-manipulation ${\n                !isAnswered\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : questionIndex === totalQuestions - 1\n                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg'\n              }`}\n            >\n              {questionIndex === totalQuestions - 1 ? (\n                <>\n                  <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\" />\n                  <span className=\"hidden sm:inline\">Submit Quiz</span>\n                  <span className=\"sm:hidden\">Submit</span>\n                </>\n              ) : (\n                <>\n                  <span className=\"hidden sm:inline\">Next</span>\n                  <span className=\"sm:hidden\">Next</span>\n                  <TbArrowRight className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\" />\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,OAAO,QAAQ,gBAAgB;AAC5E,OAAO,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE3C,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAACe,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACduB,gBAAgB,CAACT,cAAc,IAAI,EAAE,CAAC;IACtCW,aAAa,CAAC,CAAC,CAACX,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;EAEnC,MAAMc,kBAAkB,GAAIC,MAAM,IAAK;IACrCJ,gBAAgB,CAACI,MAAM,CAAC;IACxBF,aAAa,CAAC,IAAI,CAAC;IACnBV,cAAc,CAACY,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACxB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,IAAI,CAACF,QAAQ,IAAI,CAACA,QAAQ,CAAC0B,IAAI,EAAE;IAC/B,oBACE9B,OAAA;MAAK+B,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGhC,OAAA;QAAK+B,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DhC,OAAA;UAAK+B,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDpC,OAAA;UAAI+B,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpFpC,OAAA;UAAG+B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAACjC,QAAQ,IAAI,CAACA,QAAQ,CAACkC,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACpC,QAAQ,CAACkC,OAAO,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;MAChF,oBACEzC,OAAA;QAAK+B,SAAS,EAAC,4DAA4D;QAAAC,QAAA,gBACzEhC,OAAA;UAAK+B,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDpC,OAAA;UAAG+B,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC;IAEV;IAEA,MAAMM,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAEnD,oBACE1C,OAAA;MAAK+B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EACpCO,MAAM,CAACI,OAAO,CAACvC,QAAQ,CAACkC,OAAO,CAAC,CAACM,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;QAC7D,MAAMC,SAAS,GAAGC,MAAM,CAACJ,GAAG,CAAC,CAACK,IAAI,CAAC,CAAC;QACpC,MAAMC,WAAW,GAAGF,MAAM,CAACH,KAAK,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC;QAC9C,MAAME,KAAK,GAAGV,YAAY,CAACK,KAAK,CAAC,IAAIC,SAAS;QAC9C,MAAMK,UAAU,GAAGtC,aAAa,KAAKiC,SAAS;;QAE9C;QACA,IAAI,CAACG,WAAW,EAAE,OAAO,IAAI;QAE7B,oBACEnD,OAAA,CAACN,MAAM,CAAC4D,MAAM;UAEZC,OAAO,EAAEA,CAAA,KAAMpC,kBAAkB,CAAC6B,SAAS,CAAE;UAC7CQ,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1B1B,SAAS,EAAG,4HACVsB,UAAU,GACN,kDAAkD,GAClD,+EACL,EAAE;UAAArB,QAAA,eAEHhC,OAAA;YAAK+B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/ChC,OAAA;cAAK+B,SAAS,EAAG,wIACfsB,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;cAAArB,QAAA,EACAoB;YAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpC,OAAA;cAAM+B,SAAS,EAAG,4DAChBsB,UAAU,GAAG,YAAY,GAAG,eAC7B,EAAE;cAAArB,QAAA,EACAmB;YAAW;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EACNiB,UAAU,iBACTrD,OAAA,CAACF,OAAO;cAACiC,SAAS,EAAC;YAAgD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACtE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GA1BDY,SAAS;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BD,CAAC;MAEpB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,MAAMuB,eAAe,GAAGA,CAAA,kBACtB3D,OAAA;IAAK+B,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBACrChC,OAAA;MAAO+B,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACxEhC,OAAA;QAAK+B,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtChC,OAAA;UAAAgC,QAAA,EAAM;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfpC,OAAA;UAAAgC,QAAA,EAAM;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACRpC,OAAA;MAAK+B,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBhC,OAAA;QACE4D,IAAI,EAAC,MAAM;QACXd,KAAK,EAAE/B,aAAc;QACrB8C,QAAQ,EAAGC,CAAC,IAAK3C,kBAAkB,CAAC2C,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE;QACpDkB,WAAW,EAAC,0BAA0B;QACtCjC,SAAS,EAAC;MAAoN;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/N,CAAC,eACFpC,OAAA;QAAK+B,SAAS,EAAC,gEAAgE;QAAAC,QAAA,EAC5EjB,aAAa,gBACZf,OAAA,CAACF,OAAO;UAACiC,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE5DpC,OAAA;UAAK+B,SAAS,EAAC;QAAgD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MACtE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEpC,OAAA;IAAK+B,SAAS,EAAC,qFAAqF;IAAAC,QAAA,gBAElGhC,OAAA;MAAK+B,SAAS,EAAC,iDAAiD;MAAAC,QAAA,eAC9DhC,OAAA,CAACN,MAAM,CAACuE,GAAG;QACTlC,SAAS,EAAC,qDAAqD;QAC/DmC,OAAO,EAAE;UAAEC,KAAK,EAAE;QAAE,CAAE;QACtBC,OAAO,EAAE;UAAED,KAAK,EAAG,GAAEtC,kBAAmB;QAAG,CAAE;QAC7CwC,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAAE;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNpC,OAAA;MAAK+B,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClFhC,OAAA;QAAK+B,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClEhC,OAAA;UAAK+B,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAE/EhC,OAAA;YAAK+B,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBACxDhC,OAAA;cAAI+B,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAEpB;YAAS;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChGpC,OAAA;cAAG+B,SAAS,EAAC,kDAAkD;cAAAC,QAAA,EAAC;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpG,CAAC,eAGNpC,OAAA;YAAK+B,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eAC5DhC,OAAA;cAAK+B,SAAS,EAAG,gJACflB,aAAa,GACT,+DAA+D,GAC/D,oDACL,EAAE;cAAAmB,QAAA,gBACDhC,OAAA,CAACL,OAAO;gBAACoC,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3DpC,OAAA;gBAAM+B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CpC,OAAA;gBAAAgC,QAAA,EAAOX,UAAU,CAACZ,QAAQ;cAAC;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpC,OAAA;YAAK+B,SAAS,EAAC,qDAAqD;YAAAC,QAAA,eAClEhC,OAAA;cAAK+B,SAAS,EAAC,8GAA8G;cAAAC,QAAA,GAC1H3B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpC,OAAA;MAAK+B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrChC,OAAA;QAAK+B,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eAC1EhC,OAAA,CAACN,MAAM,CAACuE,GAAG;UAETC,OAAO,EAAE;YAAEK,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEG,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BvC,SAAS,EAAC,sGAAsG;UAAAC,QAAA,gBAGhHhC,OAAA;YAAK+B,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnChC,OAAA;cAAK+B,SAAS,EAAC,+LAA+L;cAAAC,QAAA,eAC5MhC,OAAA;gBAAAgC,QAAA,GAAM,WAAS,EAAC3B,aAAa,GAAG,CAAC;cAAA;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpC,OAAA;YAAK+B,SAAS,EAAC,kGAAkG;YAAAC,QAAA,EAC9G5B,QAAQ,CAAC0B;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,EAGLhC,QAAQ,CAACqE,KAAK,iBACbzE,OAAA;YAAK+B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChDhC,OAAA;cAAK+B,SAAS,EAAC,2FAA2F;cAAAC,QAAA,eACxGhC,OAAA;gBACE0E,GAAG,EAAEtE,QAAQ,CAACqE,KAAM;gBACpBE,GAAG,EAAC,UAAU;gBACd5C,SAAS,EAAC;cAAkE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDpC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1B5B,QAAQ,CAACkC,OAAO,GAAGD,SAAS,CAAC,CAAC,GAAGsB,eAAe,CAAC;UAAC;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA,GAlCD/B,aAAa;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmCR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpC,OAAA;MAAK+B,SAAS,EAAC,gEAAgE;MAAAC,QAAA,eAC7EhC,OAAA;QAAK+B,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eAC1EhC,OAAA;UAAK+B,SAAS,EAAC,2DAA2D;UAAAC,QAAA,gBACxEhC,OAAA;YACEuD,OAAO,EAAE5C,UAAW;YACpBiE,QAAQ,EAAEvE,aAAa,KAAK,CAAE;YAC9B0B,SAAS,EAAG,uLACV1B,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,6DACL,EAAE;YAAA2B,QAAA,gBAEHhC,OAAA,CAACJ,WAAW;cAACmC,SAAS,EAAC;YAAqC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DpC,OAAA;cAAM+B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAETpC,OAAA;YAAK+B,SAAS,EAAC,wDAAwD;YAAAC,QAAA,EACpE,CAACf,UAAU,iBACVjB,OAAA;cAAK+B,SAAS,EAAC,0KAA0K;cAAAC,QAAA,gBACvLhC,OAAA;gBAAAgC,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACfpC,OAAA;gBAAM+B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1DpC,OAAA;gBAAM+B,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENpC,OAAA;YACEuD,OAAO,EAAE7C,MAAO;YAChBkE,QAAQ,EAAE,CAAC3D,UAAW;YACtBc,SAAS,EAAG,uLACV,CAACd,UAAU,GACP,8CAA8C,GAC9CZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GAClC,4DAA4D,GAC5D,0DACP,EAAE;YAAA0B,QAAA,EAEF3B,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCN,OAAA,CAAAE,SAAA;cAAA8B,QAAA,gBACEhC,OAAA,CAACF,OAAO;gBAACiC,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3DpC,OAAA;gBAAM+B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDpC,OAAA;gBAAM+B,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACzC,CAAC,gBAEHpC,OAAA,CAAAE,SAAA;cAAA8B,QAAA,gBACEhC,OAAA;gBAAM+B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CpC,OAAA;gBAAM+B,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvCpC,OAAA,CAACH,YAAY;gBAACkC,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,eAChE;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CAtRIX,YAAY;AAAA0E,EAAA,GAAZ1E,YAAY;AAwRlB,eAAeA,YAAY;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}