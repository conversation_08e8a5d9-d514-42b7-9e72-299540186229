{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { Suspense } from \"react\";\nimport \"./stylesheets/theme.css\";\nimport \"./stylesheets/alignments.css\";\nimport \"./stylesheets/textelements.css\";\nimport \"./stylesheets/form-elements.css\";\nimport \"./stylesheets/custom-components.css\";\nimport \"./stylesheets/layout.css\";\nimport \"./styles/modern.css\";\nimport \"./styles/animations.css\";\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\nimport Login from \"./pages/common/Login\";\nimport Register from \"./pages/common/Register\";\nimport ProtectedRoute from \"./components/ProtectedRoute\";\nimport Quiz from \"./pages/user/Quiz\";\nimport QuizStart from \"./pages/user/Quiz/QuizStart\";\nimport QuizPlay from \"./pages/user/Quiz/QuizPlay\";\nimport QuizResult from \"./pages/user/Quiz/QuizResult\";\nimport Exams from \"./pages/admin/Exams\";\nimport AddEditExam from \"./pages/admin/Exams/AddEditExam\";\nimport Users from \"./pages/admin/Users\";\nimport Loader from \"./components/Loader\";\nimport { useSelector } from \"react-redux\";\nimport WriteExam from \"./pages/user/WriteExam\";\nimport UserReports from \"./pages/user/UserReports\";\nimport AdminReports from \"./pages/admin/AdminReports\";\nimport StudyMaterial from \"./pages/user/StudyMaterial\";\nimport Ranking from \"./pages/user/Ranking\";\nimport RankingErrorBoundary from \"./components/RankingErrorBoundary\";\nimport Profile from \"./pages/common/Profile\";\nimport AboutUs from \"./pages/user/AboutUs\";\nimport Forum from \"./pages/common/Forum\";\nimport Home from \"./pages/common/Home\";\nimport Test from \"./pages/user/Test\";\nimport Chat from \"./pages/user/Chat\";\nimport Plans from \"./pages/user/Plans/Plans\";\nimport Hub from \"./pages/user/Hub\";\nimport AnnouncementModal from \"./components/Announcement/AnnouncementModal\";\nimport Announcement from \"./pages/admin/Announcement/Announcement\";\nimport AdminStudyMaterials from \"./pages/admin/StudyMaterials\";\nimport AIQuestionGeneration from \"./pages/admin/AIQuestionGeneration\";\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\nimport { ErrorBoundary } from \"./components/modern\";\nimport RankingDemo from \"./components/modern/RankingDemo\";\n\n// Global error handler for CSS style errors\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nwindow.addEventListener('error', event => {\n  if (event.message && event.message.includes('Indexed property setter is not supported')) {\n    console.warn('CSS Style Error caught and handled:', event.message);\n    event.preventDefault();\n    return false;\n  }\n});\n\n// Handle unhandled promise rejections that might be related to style errors\nwindow.addEventListener('unhandledrejection', event => {\n  if (event.reason && event.reason.message && event.reason.message.includes('Indexed property setter is not supported')) {\n    console.warn('CSS Style Promise Rejection caught and handled:', event.reason.message);\n    event.preventDefault();\n  }\n});\n// const LazyComponent = lazy(() => import('./pages/user/Test'));\n\nfunction App() {\n  _s();\n  const {\n    loading\n  } = useSelector(state => state.loader);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      children: [loading && /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(AnnouncementModal, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/ranking-demo\",\n            element: /*#__PURE__*/_jsxDEV(RankingDemo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/test\",\n            element: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"Loading(App.js)...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 33\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Test, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/forum\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Forum, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/profile\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/chat\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Chat, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/plans\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Plans, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/hub\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Hub, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/quiz\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Quiz, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/write-exam/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(WriteExam, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quiz/:id/start\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(QuizStart, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quiz/:id/play\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(QuizPlay, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quiz/:id/result\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(QuizResult, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/reports\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(UserReports, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/study-material\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(StudyMaterial, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/ranking\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(RankingErrorBoundary, {\n                children: /*#__PURE__*/_jsxDEV(Ranking, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/about-us\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AboutUs, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/users\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Users, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams/add\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams/edit/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/study-materials\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminStudyMaterials, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/ai-questions\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AIQuestionGeneration, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/reports\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminReports, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/announcements\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Announcement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"qPH7E8ZBRBZG+ChS5bGqdjAK4Pc=\", false, function () {\n  return [useSelector];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Suspense", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "<PERSON><PERSON>", "Register", "ProtectedRoute", "Quiz", "QuizStart", "QuizPlay", "QuizResult", "<PERSON><PERSON>", "AddEditExam", "Users", "Loader", "useSelector", "WriteExam", "UserReports", "AdminReports", "StudyMaterial", "Ranking", "RankingError<PERSON><PERSON><PERSON>ry", "Profile", "AboutUs", "Forum", "Home", "Test", "Cha<PERSON>", "Plans", "<PERSON><PERSON>", "AnnouncementModal", "Announcement", "AdminStudyMaterials", "AIQuestionGeneration", "ThemeProvider", "Error<PERSON>ou<PERSON><PERSON>", "RankingDemo", "jsxDEV", "_jsxDEV", "window", "addEventListener", "event", "message", "includes", "console", "warn", "preventDefault", "reason", "App", "_s", "loading", "state", "loader", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "fallback", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/App.js"], "sourcesContent": ["import React, { Suspense } from \"react\";\r\nimport \"./stylesheets/theme.css\";\r\nimport \"./stylesheets/alignments.css\";\r\nimport \"./stylesheets/textelements.css\";\r\nimport \"./stylesheets/form-elements.css\";\r\nimport \"./stylesheets/custom-components.css\";\r\nimport \"./stylesheets/layout.css\";\r\nimport \"./styles/modern.css\";\r\nimport \"./styles/animations.css\";\r\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\r\nimport Login from \"./pages/common/Login\";\r\nimport Register from \"./pages/common/Register\";\r\nimport ProtectedRoute from \"./components/ProtectedRoute\";\r\nimport Quiz from \"./pages/user/Quiz\";\r\nimport QuizStart from \"./pages/user/Quiz/QuizStart\";\r\nimport QuizPlay from \"./pages/user/Quiz/QuizPlay\";\r\nimport QuizResult from \"./pages/user/Quiz/QuizResult\";\r\nimport Exams from \"./pages/admin/Exams\";\r\nimport AddEditExam from \"./pages/admin/Exams/AddEditExam\";\r\nimport Users from \"./pages/admin/Users\";\r\nimport Loader from \"./components/Loader\";\r\nimport { useSelector } from \"react-redux\";\r\nimport WriteExam from \"./pages/user/WriteExam\";\r\nimport UserReports from \"./pages/user/UserReports\";\r\nimport AdminReports from \"./pages/admin/AdminReports\";\r\nimport StudyMaterial from \"./pages/user/StudyMaterial\";\r\nimport Ranking from \"./pages/user/Ranking\";\r\nimport RankingErrorBoundary from \"./components/RankingErrorBoundary\";\r\nimport Profile from \"./pages/common/Profile\";\r\nimport AboutUs from \"./pages/user/AboutUs\";\r\nimport Forum from \"./pages/common/Forum\";\r\nimport Home from \"./pages/common/Home\";\r\nimport Test from \"./pages/user/Test\";\r\nimport Chat from \"./pages/user/Chat\"\r\nimport Plans from \"./pages/user/Plans/Plans\";\r\nimport Hub from \"./pages/user/Hub\";\r\nimport AnnouncementModal from \"./components/Announcement/AnnouncementModal\";\r\nimport Announcement from \"./pages/admin/Announcement/Announcement\";\r\nimport AdminStudyMaterials from \"./pages/admin/StudyMaterials\";\r\nimport AIQuestionGeneration from \"./pages/admin/AIQuestionGeneration\";\r\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\r\nimport { ErrorBoundary } from \"./components/modern\";\r\nimport RankingDemo from \"./components/modern/RankingDemo\";\r\n\r\n// Global error handler for CSS style errors\r\nwindow.addEventListener('error', (event) => {\r\n  if (event.message && event.message.includes('Indexed property setter is not supported')) {\r\n    console.warn('CSS Style Error caught and handled:', event.message);\r\n    event.preventDefault();\r\n    return false;\r\n  }\r\n});\r\n\r\n// Handle unhandled promise rejections that might be related to style errors\r\nwindow.addEventListener('unhandledrejection', (event) => {\r\n  if (event.reason && event.reason.message && event.reason.message.includes('Indexed property setter is not supported')) {\r\n    console.warn('CSS Style Promise Rejection caught and handled:', event.reason.message);\r\n    event.preventDefault();\r\n  }\r\n});\r\n// const LazyComponent = lazy(() => import('./pages/user/Test'));\r\n\r\nfunction App() {\r\n  const { loading } = useSelector((state) => state.loader);\r\n  return (\r\n    <ErrorBoundary>\r\n      <ThemeProvider>\r\n        {loading && <Loader />}\r\n        <AnnouncementModal />\r\n        <BrowserRouter>\r\n        <Routes>\r\n          {/* Common Routes */}\r\n          <Route path=\"/login\" element={<Login />} />\r\n          <Route path=\"/register\" element={<Register />} />\r\n          <Route path=\"/\" element={<Home />} />\r\n          <Route path=\"/ranking-demo\" element={<RankingDemo />} />\r\n          <Route path=\"/test\" element={\r\n            <Suspense fallback={<div>Loading(App.js)...</div>}>\r\n              <Test />\r\n            </Suspense>\r\n          } />\r\n          <Route\r\n            path=\"/forum\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Forum />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* User Routes */}\r\n          <Route\r\n            path=\"/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Profile />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/chat\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Chat />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/plans\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Plans />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/hub\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Hub />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/quiz\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Quiz />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/write-exam/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <WriteExam />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* New Quiz Routes */}\r\n          <Route\r\n            path=\"/quiz/:id/start\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <QuizStart />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/quiz/:id/play\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <QuizPlay />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/quiz/:id/result\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <QuizResult />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <UserReports />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/study-material\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <StudyMaterial />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/ranking\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <RankingErrorBoundary>\r\n                  <Ranking />\r\n                </RankingErrorBoundary>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/about-us\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AboutUs />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* Admin Routes */}\r\n          <Route\r\n            path=\"/admin/users\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Users />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Exams />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams/add\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AddEditExam />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/exams/edit/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AddEditExam />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/study-materials\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminStudyMaterials />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/ai-questions\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AIQuestionGeneration />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminReports />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/announcements\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Announcement />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n        </Routes>\r\n      </BrowserRouter>\r\n    </ThemeProvider>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,yBAAyB;AAChC,OAAO,8BAA8B;AACrC,OAAO,gCAAgC;AACvC,OAAO,iCAAiC;AACxC,OAAO,qCAAqC;AAC5C,OAAO,0BAA0B;AACjC,OAAO,qBAAqB;AAC5B,OAAO,yBAAyB;AAChC,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC/D,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,WAAW,MAAM,iCAAiC;AACzD,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,OAAO,MAAM,wBAAwB;AAC5C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,IAAI,MAAM,qBAAqB;AACtC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,GAAG,MAAM,kBAAkB;AAClC,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,OAAOC,oBAAoB,MAAM,oCAAoC;AACrE,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,OAAOC,WAAW,MAAM,iCAAiC;;AAEzD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAC,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;EAC1C,IAAIA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,EAAE;IACvFC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEJ,KAAK,CAACC,OAAO,CAAC;IAClED,KAAK,CAACK,cAAc,CAAC,CAAC;IACtB,OAAO,KAAK;EACd;AACF,CAAC,CAAC;;AAEF;AACAP,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAGC,KAAK,IAAK;EACvD,IAAIA,KAAK,CAACM,MAAM,IAAIN,KAAK,CAACM,MAAM,CAACL,OAAO,IAAID,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,EAAE;IACrHC,OAAO,CAACC,IAAI,CAAC,iDAAiD,EAAEJ,KAAK,CAACM,MAAM,CAACL,OAAO,CAAC;IACrFD,KAAK,CAACK,cAAc,CAAC,CAAC;EACxB;AACF,CAAC,CAAC;AACF;;AAEA,SAASE,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC;EAAQ,CAAC,GAAGnC,WAAW,CAAEoC,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EACxD,oBACEd,OAAA,CAACH,aAAa;IAAAkB,QAAA,eACZf,OAAA,CAACJ,aAAa;MAAAmB,QAAA,GACXH,OAAO,iBAAIZ,OAAA,CAACxB,MAAM;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtBnB,OAAA,CAACR,iBAAiB;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBnB,OAAA,CAACrC,aAAa;QAAAoD,QAAA,eACdf,OAAA,CAACpC,MAAM;UAAAmD,QAAA,gBAELf,OAAA,CAACnC,KAAK;YAACuD,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAErB,OAAA,CAAClC,KAAK;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CnB,OAAA,CAACnC,KAAK;YAACuD,IAAI,EAAC,WAAW;YAACC,OAAO,eAAErB,OAAA,CAACjC,QAAQ;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDnB,OAAA,CAACnC,KAAK;YAACuD,IAAI,EAAC,GAAG;YAACC,OAAO,eAAErB,OAAA,CAACb,IAAI;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCnB,OAAA,CAACnC,KAAK;YAACuD,IAAI,EAAC,eAAe;YAACC,OAAO,eAAErB,OAAA,CAACF,WAAW;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDnB,OAAA,CAACnC,KAAK;YAACuD,IAAI,EAAC,OAAO;YAACC,OAAO,eACzBrB,OAAA,CAACtC,QAAQ;cAAC4D,QAAQ,eAAEtB,OAAA;gBAAAe,QAAA,EAAK;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAE;cAAAJ,QAAA,eAChDf,OAAA,CAACZ,IAAI;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,QAAQ;YACbC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACd,KAAK;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,UAAU;YACfC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAAChB,OAAO;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACX,IAAI;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,aAAa;YAClBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACV,KAAK;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,WAAW;YAChBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACT,GAAG;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAAC/B,IAAI;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACtB,SAAS;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,iBAAiB;YACtBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAAC9B,SAAS;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAAC7B,QAAQ;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAAC5B,UAAU;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACrB,WAAW;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACnB,aAAa;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACjB,oBAAoB;gBAAAgC,QAAA,eACnBf,OAAA,CAAClB,OAAO;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACf,OAAO;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACzB,KAAK;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAAC3B,KAAK;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAAC1B,WAAW;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,uBAAuB;YAC5BC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAAC1B,WAAW;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,wBAAwB;YAC7BC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACN,mBAAmB;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,qBAAqB;YAC1BC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACL,oBAAoB;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACpB,YAAY;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFnB,OAAA,CAACnC,KAAK;YACJuD,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLrB,OAAA,CAAChC,cAAc;cAAA+C,QAAA,eACbf,OAAA,CAACP,YAAY;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB;AAACR,EAAA,CAtNQD,GAAG;EAAA,QACUjC,WAAW;AAAA;AAAA8C,EAAA,GADxBb,GAAG;AAwNZ,eAAeA,GAAG;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}