import React, { useState, useEffect, useCallback } from "react";
import { useDispatch } from "react-redux";
import { 
  Modal, 
  Form, 
  Row, 
  Col, 
  Input, 
  Select, 
  InputNumber, 
  Button, 
  message,
  Alert,
  Divider
} from "antd";
import { FaRobot, FaMagic } from "react-icons/fa";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { addExam } from "../../../apicalls/exams";
import { generateExamName } from "../../../apicalls/aiQuestions";
import { primarySubjects, secondarySubjects, advanceSubjects } from "../../../data/Subjects";

const { Option } = Select;

function AutoGenerateExamModal({ 
  visible, 
  onCancel, 
  onSuccess, 
  prefilledData = {} 
}) {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [level, setLevel] = useState(prefilledData.level || "");
  // Remove unused selectedSubjects state - cleaned up
  const [availableSubjects, setAvailableSubjects] = useState([]);
  const [autoGeneratedName, setAutoGeneratedName] = useState("");
  const [isGeneratingName, setIsGeneratingName] = useState(false);
  const [passRate, setPassRate] = useState(0);

  const calculatePassRate = useCallback((values) => {
    const totalMarks = values?.totalMarks || 0;
    const passingMarks = values?.passingMarks || 0;
    if (totalMarks > 0 && passingMarks > 0) {
      return Math.round((passingMarks / totalMarks) * 100);
    }
    return 0;
  }, []);

  const handleLevelChange = useCallback((selectedLevel) => {
    setLevel(selectedLevel);
    form.setFieldsValue({ class: undefined, category: "" });

    // Set available subjects based on level
    let subjects = [];
    switch (selectedLevel) {
      case "primary":
        subjects = primarySubjects;
        break;
      case "secondary":
        subjects = secondarySubjects;
        break;
      case "advance":
        subjects = advanceSubjects;
        break;
      default:
        subjects = [];
    }
    setAvailableSubjects(subjects);
  }, [form]);

  const handleFormValuesChange = useCallback((changedValues, allValues) => {
    if (changedValues.totalMarks !== undefined || changedValues.passingMarks !== undefined) {
      setPassRate(calculatePassRate(allValues));
    }
  }, [calculatePassRate]);

  useEffect(() => {
    if (visible) {
      // Reset form when modal opens
      form.resetFields();
      setLevel(prefilledData.level || "");

      // Set initial values if provided
      if (prefilledData.level) {
        form.setFieldsValue({
          level: prefilledData.level,
          class: prefilledData.class,
          category: prefilledData.subjects?.[0] || "",
        });
        handleLevelChange(prefilledData.level);
      }

      // Initialize pass rate with default values
      setPassRate(calculatePassRate({ totalMarks: 100, passingMarks: 50 }));
    }
  }, [visible, prefilledData, form, handleLevelChange, calculatePassRate]);

  const handleAutoGenerateName = async () => {
    const currentValues = form.getFieldsValue();
    const { level: formLevel, class: formClass, category } = currentValues;
    
    if (!formLevel || !formClass || !category) {
      message.warning("Please select level, class, and category first");
      return;
    }

    try {
      setIsGeneratingName(true);
      const response = await generateExamName(formLevel, formClass, [category]);
      
      if (response.success) {
        setAutoGeneratedName(response.data.examName);
        form.setFieldsValue({ name: response.data.examName });
        message.success("Exam name generated successfully!");
      } else {
        message.error("Failed to generate exam name");
      }
    } catch (error) {
      message.error("Error generating exam name");
    } finally {
      setIsGeneratingName(false);
    }
  };

  const onFinish = async (values) => {
    try {
      dispatch(ShowLoading());
      
      // Prepare exam data following the same structure as manual creation
      const examData = {
        name: values.name,
        duration: values.duration,
        level: values.level,
        category: values.category,
        class: values.class,
        totalMarks: values.totalMarks,
        passingMarks: values.passingMarks,
        description: values.description || `Auto-generated exam for ${values.category} - ${values.level} Level Class ${values.class}`,
        isPublic: false, // Default to private
        questions: [], // Start with empty questions array
      };

      const response = await addExam(examData);

      if (response.success) {
        message.success("Exam created successfully!");
        // Ensure we have valid exam data before passing it back
        if (response.data && response.data._id) {
          onSuccess(response.data); // Pass the created exam data back
        } else {
          // If no data returned, create a minimal exam object for the UI
          const fallbackExam = {
            _id: Date.now().toString(), // Temporary ID
            name: examData.name,
            category: examData.category,
            level: examData.level,
            class: examData.class,
          };
          onSuccess(fallbackExam);
        }
        form.resetFields();
        setAutoGeneratedName("");
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error("Failed to create exam");
    } finally {
      dispatch(HideLoading());
    }
  };

  const getClassOptions = () => {
    switch (level) {
      case "primary":
        return ["1", "2", "3", "4", "5", "6", "7"];
      case "secondary":
        return ["Form-1", "Form-2", "Form-3", "Form-4"];
      case "advance":
        return ["Form-5", "Form-6"];
      default:
        return [];
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
          <FaRobot style={{ color: "#1890ff", fontSize: 20 }} />
          <span>Auto-Generate Exam</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
      destroyOnClose
      focusTriggerAfterClose={false}
      maskClosable={false}
    >
      <Alert
        message="Create New Exam"
        description="This will create a new exam following the same structure as manual exam creation. You can then use this exam for AI question generation."
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        onValuesChange={handleFormValuesChange}
        initialValues={{
          duration: 3600, // Default 1 hour
          totalMarks: 100,
          passingMarks: 50,
        }}
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <Form.Item
              name="name"
              label="Exam Name"
              rules={[{ required: true, message: "Please enter exam name" }]}
            >
              <Input 
                placeholder="Enter exam name or auto-generate"
                suffix={
                  <Button
                    type="link"
                    icon={<FaMagic />}
                    onClick={handleAutoGenerateName}
                    loading={isGeneratingName}
                    size="small"
                  >
                    Auto-Generate
                  </Button>
                }
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              name="duration"
              label="Exam Duration (Seconds)"
              rules={[{ required: true, message: "Please enter duration" }]}
            >
              <InputNumber
                min={300} // Minimum 5 minutes
                max={14400} // Maximum 4 hours
                style={{ width: "100%" }}
                placeholder="Duration in seconds"
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={8}>
            <Form.Item
              name="level"
              label="Level"
              rules={[{ required: true, message: "Please select level" }]}
            >
              <Select
                placeholder="Select Level"
                onChange={handleLevelChange}
              >
                <Option value="Primary">Primary</Option>
                <Option value="Secondary">Secondary</Option>
                <Option value="Advance">Advance</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} md={8}>
            <Form.Item
              name="class"
              label="Class"
              rules={[{ required: true, message: "Please select class" }]}
            >
              <Select placeholder="Select Class" disabled={!level}>
                {getClassOptions().map((cls) => (
                  <Option key={cls} value={cls}>
                    {cls}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} md={8}>
            <Form.Item
              name="category"
              label="Category (Subject)"
              rules={[{ required: true, message: "Please select category" }]}
            >
              <Select placeholder="Select Category" disabled={!level}>
                {availableSubjects.map((subject) => (
                  <Option key={subject} value={subject}>
                    {subject}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} md={8}>
            <Form.Item
              name="totalMarks"
              label="Total Marks"
              rules={[{ required: true, message: "Please enter total marks" }]}
            >
              <InputNumber
                min={1}
                max={1000}
                style={{ width: "100%" }}
                placeholder="Total marks"
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={8}>
            <Form.Item
              name="passingMarks"
              label="Passing Marks"
              rules={[{ required: true, message: "Please enter passing marks" }]}
            >
              <InputNumber
                min={1}
                max={1000}
                style={{ width: "100%" }}
                placeholder="Passing marks"
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={8}>
            <div style={{ paddingTop: 30 }}>
              <Alert
                message={`Pass Rate: ${passRate}%`}
                type="info"
                size="small"
              />
            </div>
          </Col>

          <Col xs={24}>
            <Form.Item
              name="description"
              label="Description (Optional)"
            >
              <Input.TextArea
                rows={3}
                placeholder="Enter exam description (optional)"
              />
            </Form.Item>
          </Col>
        </Row>

        {autoGeneratedName && (
          <Alert
            message="Auto-Generated Name"
            description={`Generated exam name: ${autoGeneratedName}`}
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        <Divider />

        <div style={{ display: "flex", justifyContent: "flex-end", gap: 12 }}>
          <Button onClick={onCancel}>
            Cancel
          </Button>
          <Button type="primary" htmlType="submit" icon={<FaRobot />}>
            Create Exam
          </Button>
        </div>
      </Form>
    </Modal>
  );
}

export default AutoGenerateExamModal;
