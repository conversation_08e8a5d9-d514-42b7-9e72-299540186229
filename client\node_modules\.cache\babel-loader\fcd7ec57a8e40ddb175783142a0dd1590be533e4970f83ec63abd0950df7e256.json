{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport Confetti from 'react-confetti';\nimport useWindowSize from 'react-use/lib/useWindowSize';\nimport { TbArrowLeft, TbCheck, TbX, TbTrophy, TbBrain, TbTarget, TbRefresh, TbEye, TbBulb } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport ContentRenderer from '../../../components/ContentRenderer';\nimport XPResultDisplay from '../../../components/modern/XPResultDisplay';\nimport './responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizResult = () => {\n  _s();\n  var _location$state, _result$correctAnswer2, _result$wrongAnswers;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [showReview, setShowReview] = useState(false);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const {\n    width,\n    height\n  } = useWindowSize();\n  const result = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.result;\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({\n          examId: id\n        });\n        dispatch(HideLoading());\n        if (response.success) {\n          var _response$data;\n          setExamData(response.data);\n          setQuestions(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.questions) || []);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n\n  // Play sound effect based on performance\n  useEffect(() => {\n    if (result) {\n      console.log(`Quiz ${result.verdict === \"Pass\" ? \"passed\" : \"failed\"}!`);\n\n      // Play performance-based sound\n      const playSound = () => {\n        try {\n          const score = result.score || 0;\n          let soundFile = '';\n          if (score === 100) {\n            soundFile = '/sounds/perfect.mp3';\n          } else if (score >= 80) {\n            soundFile = '/sounds/excellent.mp3';\n          } else if (result.verdict === \"Pass\") {\n            soundFile = '/sounds/pass.mp3';\n          } else {\n            soundFile = '/sounds/fail.mp3';\n          }\n\n          // Create audio element and play\n          const audio = new Audio(soundFile);\n          audio.volume = 0.5; // Set volume to 50%\n          audio.play().catch(error => {\n            console.log('Sound play failed:', error);\n            // Fallback to browser notification sounds\n            if (result.verdict === \"Pass\") {\n              // Success sound fallback\n              const context = new (window.AudioContext || window.webkitAudioContext)();\n              const oscillator = context.createOscillator();\n              const gainNode = context.createGain();\n              oscillator.connect(gainNode);\n              gainNode.connect(context.destination);\n              oscillator.frequency.setValueAtTime(523.25, context.currentTime); // C5\n              oscillator.frequency.setValueAtTime(659.25, context.currentTime + 0.1); // E5\n              oscillator.frequency.setValueAtTime(783.99, context.currentTime + 0.2); // G5\n\n              gainNode.gain.setValueAtTime(0.3, context.currentTime);\n              gainNode.gain.exponentialRampToValueAtTime(0.01, context.currentTime + 0.5);\n              oscillator.start(context.currentTime);\n              oscillator.stop(context.currentTime + 0.5);\n            }\n          });\n        } catch (error) {\n          console.log('Audio not supported:', error);\n        }\n      };\n\n      // Delay sound to sync with animation\n      setTimeout(playSound, 500);\n    }\n  }, [result]);\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({\n        question,\n        expectedAnswer,\n        userAnswer,\n        imageUrl\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [question]: response.explanation\n        }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n\n  // Handle missing result data\n  if (!result) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-700 mb-2\",\n          children: \"No Result Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 mb-4\",\n          children: \"Unable to load quiz results.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/quiz'),\n          className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Calculate performance level for animations and sounds\n  const getPerformanceLevel = () => {\n    const score = result.score || 0;\n    if (score === 100) return 'perfect';\n    if (score >= 80) return 'excellent';\n    if (score >= 60) return 'good';\n    if (result.verdict === \"Pass\") return 'pass';\n    return 'fail';\n  };\n  const performanceLevel = getPerformanceLevel();\n\n  // Performance-based styling and content\n  const getPerformanceConfig = () => {\n    switch (performanceLevel) {\n      case 'perfect':\n        return {\n          bgGradient: 'from-yellow-400 via-orange-500 to-red-500',\n          iconBg: 'from-yellow-400 to-orange-500',\n          icon: TbTrophy,\n          title: '🏆 PERFECT SCORE!',\n          subtitle: 'Outstanding! You\\'re a quiz master! 🌟',\n          confetti: true,\n          soundFile: '/sounds/perfect.mp3'\n        };\n      case 'excellent':\n        return {\n          bgGradient: 'from-green-400 via-emerald-500 to-teal-600',\n          iconBg: 'from-green-400 to-emerald-500',\n          icon: TbTrophy,\n          title: '🎉 EXCELLENT!',\n          subtitle: 'Amazing work! You\\'re doing great! ✨',\n          confetti: true,\n          soundFile: '/sounds/excellent.mp3'\n        };\n      case 'good':\n      case 'pass':\n        return {\n          bgGradient: 'from-blue-400 via-indigo-500 to-purple-600',\n          iconBg: 'from-blue-400 to-indigo-500',\n          icon: TbCheck,\n          title: '✅ Well Done!',\n          subtitle: 'Good job! Keep up the great work! 🚀',\n          confetti: result.verdict === \"Pass\",\n          soundFile: '/sounds/pass.mp3'\n        };\n      default:\n        return {\n          bgGradient: 'from-red-400 via-pink-500 to-rose-600',\n          iconBg: 'from-red-400 to-pink-500',\n          icon: TbX,\n          title: '💪 Keep Trying!',\n          subtitle: 'Don\\'t give up! Practice makes perfect! 🌱',\n          confetti: false,\n          soundFile: '/sounds/fail.mp3'\n        };\n    }\n  };\n  const config = getPerformanceConfig();\n\n  // Review Section Component\n  if (showReview) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gray-50 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 p-4 sm:p-6 lg:p-8 text-center flex-shrink-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-white/20 rounded-full mb-3 sm:mb-4\",\n          children: /*#__PURE__*/_jsxDEV(TbEye, {\n            className: \"w-6 h-6 sm:w-8 sm:h-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl sm:text-2xl lg:text-3xl font-black text-white mb-2 sm:mb-3\",\n          children: \"Review Your Answers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/90 text-sm sm:text-base lg:text-lg font-medium\",\n          children: \"Detailed breakdown of your quiz performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 sm:space-y-6\",\n            children: questions.map((question, index) => {\n              var _result$correctAnswer, _result$wrongAnswers$;\n              const userAnswer = ((_result$correctAnswer = result.correctAnswers.find(q => q._id === question._id)) === null || _result$correctAnswer === void 0 ? void 0 : _result$correctAnswer.userAnswer) || ((_result$wrongAnswers$ = result.wrongAnswers.find(q => q._id === question._id)) === null || _result$wrongAnswers$ === void 0 ? void 0 : _result$wrongAnswers$.userAnswer) || \"\";\n              const isCorrect = result.correctAnswers.some(q => q._id === question._id);\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `px-4 sm:px-6 py-3 sm:py-4 border-b-2 ${isCorrect ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-300' : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-base sm:text-lg font-bold text-gray-900 flex items-center flex-1 min-w-0\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `inline-flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 rounded-full mr-2 sm:mr-3 flex-shrink-0 ${isCorrect ? 'bg-green-100' : 'bg-red-100'}`,\n                        children: isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-3 h-3 sm:w-5 sm:h-5 text-green-600\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 266,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                          className: \"w-3 h-3 sm:w-5 sm:h-5 text-red-600\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 268,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 262,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"truncate\",\n                        children: [\"Question \", index + 1]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 271,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 261,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-semibold ${isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                      children: isCorrect ? 'Correct' : 'Wrong'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-4 sm:p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-semibold text-gray-900 mb-2 text-sm sm:text-base\",\n                      children: \"Question:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                      content: question.name,\n                      className: \"text-gray-700 text-sm sm:text-base\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 23\n                  }, this), question.image && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-4 text-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: question.image,\n                      alt: \"Question\",\n                      className: \"max-w-full max-h-48 sm:max-h-64 rounded-lg shadow-md mx-auto\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-semibold text-gray-900 mb-2 text-sm sm:text-base\",\n                        children: \"Your Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 302,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `p-3 rounded-lg text-sm sm:text-base ${isCorrect ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`,\n                        children: userAnswer || 'No answer provided'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 303,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-semibold text-gray-900 mb-2 text-sm sm:text-base\",\n                        children: \"Correct Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 311,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"p-3 bg-green-50 text-green-800 rounded-lg text-sm sm:text-base\",\n                        children: question.correctAnswer || question.correctOption || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 312,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 23\n                  }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => fetchExplanation(question.name, question.correctAnswer || question.correctOption, userAnswer, question.image),\n                      className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base\",\n                      children: [/*#__PURE__*/_jsxDEV(TbBulb, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 330,\n                        columnNumber: 29\n                      }, this), \"Get AI Explanation\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 27\n                    }, this), explanations[question.name] && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"font-semibold text-blue-900 mb-2 text-sm sm:text-base\",\n                        children: \"AI Explanation:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 336,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                        content: explanations[question.name],\n                        className: \"text-blue-800 text-sm sm:text-base\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 337,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white border-t border-gray-200 p-4 sm:p-6 flex-shrink-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowReview(false),\n            className: \"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm sm:text-base touch-manipulation\",\n            children: \"Back to Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/quiz'),\n            className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base touch-manipulation\",\n            children: \"More Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\",\n    children: [config.confetti && /*#__PURE__*/_jsxDEV(Confetti, {\n      width: width,\n      height: height\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 27\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-6 sm:py-8 lg:py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `bg-gradient-to-br ${config.bgGradient} rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 text-center relative overflow-hidden mb-6 sm:mb-8 lg:mb-12 shadow-2xl`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-10 -right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse delay-1000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 bg-gradient-to-br ${config.iconBg} rounded-full mb-4 sm:mb-6 shadow-2xl animate-bounce`,\n              children: /*#__PURE__*/_jsxDEV(config.icon, {\n                className: \"w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl sm:text-3xl lg:text-5xl font-black text-white mb-3 sm:mb-4 animate-pulse\",\n              children: config.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-base sm:text-lg lg:text-xl text-white/90 font-medium px-4\",\n              children: config.subtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8 lg:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-blue-600 mb-2\",\n              children: [result.score || 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-green-600 mb-2\",\n              children: ((_result$correctAnswer2 = result.correctAnswers) === null || _result$correctAnswer2 === void 0 ? void 0 : _result$correctAnswer2.length) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Correct\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-red-600 mb-2\",\n              children: ((_result$wrongAnswers = result.wrongAnswers) === null || _result$wrongAnswers === void 0 ? void 0 : _result$wrongAnswers.length) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Wrong\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-purple-600 mb-2\",\n              children: [Math.floor((result.timeSpent || 0) / 60), \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), result.xpData && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 sm:mb-8 lg:mb-12\",\n          children: /*#__PURE__*/_jsxDEV(XPResultDisplay, {\n            xpData: result.xpData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center mb-6 sm:mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\",\n            onClick: () => setShowReview(true),\n            children: [/*#__PURE__*/_jsxDEV(TbEye, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this), \"Review Answers\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\",\n            onClick: () => navigate(`/quiz/${id}/start`),\n            children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:rotate-180 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this), \"Retake Quiz\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\",\n            onClick: () => navigate('/quiz'),\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"More Quizzes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sm:hidden\",\n              children: \"More\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-4 left-4 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/quiz'),\n        className: \"flex items-center justify-center w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg transition-all duration-200 hover:scale-105 touch-manipulation\",\n        title: \"Back to Quiz Page\",\n        children: /*#__PURE__*/_jsxDEV(TbArrowLeft, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 486,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 372,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizResult, \"D4V/cGZGP63efKMIrBtSCOFOvoE=\", false, function () {\n  return [useParams, useNavigate, useLocation, useDispatch, useWindowSize];\n});\n_c = QuizResult;\nvar _c;\n$RefreshReg$(_c, \"QuizResult\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useLocation", "useDispatch", "message", "Confetti", "useWindowSize", "TbArrowLeft", "TbCheck", "TbX", "TbTrophy", "TbBrain", "TbTarget", "TbRefresh", "TbEye", "TbBulb", "getExamById", "chatWithChatGPTToExplainAns", "HideLoading", "ShowLoading", "Content<PERSON><PERSON><PERSON>", "XPResultDisplay", "jsxDEV", "_jsxDEV", "QuizResult", "_s", "_location$state", "_result$correctAnswer2", "_result$wrongAnswers", "examData", "setExamData", "questions", "setQuestions", "explanations", "setExplanations", "showReview", "setShowReview", "id", "navigate", "location", "dispatch", "width", "height", "result", "state", "fetchExamData", "response", "examId", "success", "_response$data", "data", "error", "console", "log", "verdict", "playSound", "score", "soundFile", "audio", "Audio", "volume", "play", "catch", "context", "window", "AudioContext", "webkitAudioContext", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "setValueAtTime", "currentTime", "gain", "exponentialRampToValueAtTime", "start", "stop", "setTimeout", "document", "body", "classList", "add", "remove", "fetchExplanation", "question", "expectedAnswer", "userAnswer", "imageUrl", "prev", "explanation", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "getPerformanceLevel", "performanceLevel", "getPerformanceConfig", "bgGradient", "iconBg", "icon", "title", "subtitle", "confetti", "config", "map", "index", "_result$correctAnswer", "_result$wrongAnswers$", "correctAnswers", "find", "q", "_id", "wrongAnswers", "isCorrect", "some", "content", "name", "image", "src", "alt", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "length", "Math", "floor", "timeSpent", "xpData", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport Confetti from 'react-confetti';\nimport useWindowSize from 'react-use/lib/useWindowSize';\nimport {\n  TbArrowLeft,\n  TbCheck,\n  TbX,\n  TbTrophy,\n  TbBrain,\n  TbTarget,\n  TbRefresh,\n  TbEye,\n  TbBulb\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport ContentRenderer from '../../../components/ContentRenderer';\nimport XPResultDisplay from '../../../components/modern/XPResultDisplay';\nimport './responsive.css';\n\nconst QuizResult = () => {\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [showReview, setShowReview] = useState(false);\n\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const { width, height } = useWindowSize();\n\n  const result = location.state?.result;\n\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({ examId: id });\n        dispatch(HideLoading());\n\n        if (response.success) {\n          setExamData(response.data);\n          setQuestions(response.data?.questions || []);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n\n  // Play sound effect based on performance\n  useEffect(() => {\n    if (result) {\n      console.log(`Quiz ${result.verdict === \"Pass\" ? \"passed\" : \"failed\"}!`);\n\n      // Play performance-based sound\n      const playSound = () => {\n        try {\n          const score = result.score || 0;\n          let soundFile = '';\n\n          if (score === 100) {\n            soundFile = '/sounds/perfect.mp3';\n          } else if (score >= 80) {\n            soundFile = '/sounds/excellent.mp3';\n          } else if (result.verdict === \"Pass\") {\n            soundFile = '/sounds/pass.mp3';\n          } else {\n            soundFile = '/sounds/fail.mp3';\n          }\n\n          // Create audio element and play\n          const audio = new Audio(soundFile);\n          audio.volume = 0.5; // Set volume to 50%\n          audio.play().catch(error => {\n            console.log('Sound play failed:', error);\n            // Fallback to browser notification sounds\n            if (result.verdict === \"Pass\") {\n              // Success sound fallback\n              const context = new (window.AudioContext || window.webkitAudioContext)();\n              const oscillator = context.createOscillator();\n              const gainNode = context.createGain();\n\n              oscillator.connect(gainNode);\n              gainNode.connect(context.destination);\n\n              oscillator.frequency.setValueAtTime(523.25, context.currentTime); // C5\n              oscillator.frequency.setValueAtTime(659.25, context.currentTime + 0.1); // E5\n              oscillator.frequency.setValueAtTime(783.99, context.currentTime + 0.2); // G5\n\n              gainNode.gain.setValueAtTime(0.3, context.currentTime);\n              gainNode.gain.exponentialRampToValueAtTime(0.01, context.currentTime + 0.5);\n\n              oscillator.start(context.currentTime);\n              oscillator.stop(context.currentTime + 0.5);\n            }\n          });\n        } catch (error) {\n          console.log('Audio not supported:', error);\n        }\n      };\n\n      // Delay sound to sync with animation\n      setTimeout(playSound, 500);\n    }\n  }, [result]);\n\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\n      dispatch(HideLoading());\n\n      if (response.success) {\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n\n  // Handle missing result data\n  if (!result) {\n    return (\n      <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <TbTarget className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h2 className=\"text-xl font-semibold text-gray-700 mb-2\">No Result Data</h2>\n          <p className=\"text-gray-500 mb-4\">Unable to load quiz results.</p>\n          <button\n            onClick={() => navigate('/quiz')}\n            className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Back to Quizzes\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Calculate performance level for animations and sounds\n  const getPerformanceLevel = () => {\n    const score = result.score || 0;\n    if (score === 100) return 'perfect';\n    if (score >= 80) return 'excellent';\n    if (score >= 60) return 'good';\n    if (result.verdict === \"Pass\") return 'pass';\n    return 'fail';\n  };\n\n  const performanceLevel = getPerformanceLevel();\n\n  // Performance-based styling and content\n  const getPerformanceConfig = () => {\n    switch (performanceLevel) {\n      case 'perfect':\n        return {\n          bgGradient: 'from-yellow-400 via-orange-500 to-red-500',\n          iconBg: 'from-yellow-400 to-orange-500',\n          icon: TbTrophy,\n          title: '🏆 PERFECT SCORE!',\n          subtitle: 'Outstanding! You\\'re a quiz master! 🌟',\n          confetti: true,\n          soundFile: '/sounds/perfect.mp3'\n        };\n      case 'excellent':\n        return {\n          bgGradient: 'from-green-400 via-emerald-500 to-teal-600',\n          iconBg: 'from-green-400 to-emerald-500',\n          icon: TbTrophy,\n          title: '🎉 EXCELLENT!',\n          subtitle: 'Amazing work! You\\'re doing great! ✨',\n          confetti: true,\n          soundFile: '/sounds/excellent.mp3'\n        };\n      case 'good':\n      case 'pass':\n        return {\n          bgGradient: 'from-blue-400 via-indigo-500 to-purple-600',\n          iconBg: 'from-blue-400 to-indigo-500',\n          icon: TbCheck,\n          title: '✅ Well Done!',\n          subtitle: 'Good job! Keep up the great work! 🚀',\n          confetti: result.verdict === \"Pass\",\n          soundFile: '/sounds/pass.mp3'\n        };\n      default:\n        return {\n          bgGradient: 'from-red-400 via-pink-500 to-rose-600',\n          iconBg: 'from-red-400 to-pink-500',\n          icon: TbX,\n          title: '💪 Keep Trying!',\n          subtitle: 'Don\\'t give up! Practice makes perfect! 🌱',\n          confetti: false,\n          soundFile: '/sounds/fail.mp3'\n        };\n    }\n  };\n\n  const config = getPerformanceConfig();\n\n  // Review Section Component\n  if (showReview) {\n    return (\n      <div className=\"h-screen bg-gray-50 flex flex-col overflow-hidden\">\n        {/* Review Header */}\n        <div className=\"bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 p-4 sm:p-6 lg:p-8 text-center flex-shrink-0\">\n          <div className=\"inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-white/20 rounded-full mb-3 sm:mb-4\">\n            <TbEye className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />\n          </div>\n          <h2 className=\"text-xl sm:text-2xl lg:text-3xl font-black text-white mb-2 sm:mb-3\">\n            Review Your Answers\n          </h2>\n          <p className=\"text-white/90 text-sm sm:text-base lg:text-lg font-medium\">\n            Detailed breakdown of your quiz performance\n          </p>\n        </div>\n\n        {/* Review Content */}\n        <div className=\"flex-1 overflow-y-auto\">\n          <div className=\"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8\">\n            <div className=\"space-y-4 sm:space-y-6\">\n              {questions.map((question, index) => {\n                const userAnswer = result.correctAnswers.find(q => q._id === question._id)?.userAnswer ||\n                                  result.wrongAnswers.find(q => q._id === question._id)?.userAnswer || \"\";\n                const isCorrect = result.correctAnswers.some(q => q._id === question._id);\n\n                return (\n                  <div key={index} className=\"bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 overflow-hidden\">\n                    {/* Question Header */}\n                    <div className={`px-4 sm:px-6 py-3 sm:py-4 border-b-2 ${\n                      isCorrect\n                        ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-300'\n                        : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300'\n                    }`}>\n                      <div className=\"flex items-center justify-between\">\n                        <h3 className=\"text-base sm:text-lg font-bold text-gray-900 flex items-center flex-1 min-w-0\">\n                          <span className={`inline-flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 rounded-full mr-2 sm:mr-3 flex-shrink-0 ${\n                            isCorrect ? 'bg-green-100' : 'bg-red-100'\n                          }`}>\n                            {isCorrect ? (\n                              <TbCheck className=\"w-3 h-3 sm:w-5 sm:h-5 text-green-600\" />\n                            ) : (\n                              <TbX className=\"w-3 h-3 sm:w-5 sm:h-5 text-red-600\" />\n                            )}\n                          </span>\n                          <span className=\"truncate\">Question {index + 1}</span>\n                        </h3>\n                        <span className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-semibold ${\n                          isCorrect\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                        }`}>\n                          {isCorrect ? 'Correct' : 'Wrong'}\n                        </span>\n                      </div>\n                    </div>\n\n                    {/* Question Content */}\n                    <div className=\"p-4 sm:p-6\">\n                      <div className=\"mb-4\">\n                        <h4 className=\"font-semibold text-gray-900 mb-2 text-sm sm:text-base\">Question:</h4>\n                        <ContentRenderer content={question.name} className=\"text-gray-700 text-sm sm:text-base\" />\n                      </div>\n\n                      {question.image && (\n                        <div className=\"mb-4 text-center\">\n                          <img\n                            src={question.image}\n                            alt=\"Question\"\n                            className=\"max-w-full max-h-48 sm:max-h-64 rounded-lg shadow-md mx-auto\"\n                          />\n                        </div>\n                      )}\n\n                      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n                        <div>\n                          <h5 className=\"font-semibold text-gray-900 mb-2 text-sm sm:text-base\">Your Answer:</h5>\n                          <div className={`p-3 rounded-lg text-sm sm:text-base ${\n                            isCorrect ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'\n                          }`}>\n                            {userAnswer || 'No answer provided'}\n                          </div>\n                        </div>\n\n                        <div>\n                          <h5 className=\"font-semibold text-gray-900 mb-2 text-sm sm:text-base\">Correct Answer:</h5>\n                          <div className=\"p-3 bg-green-50 text-green-800 rounded-lg text-sm sm:text-base\">\n                            {question.correctAnswer || question.correctOption || 'N/A'}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* AI Explanation Button for Wrong Answers */}\n                      {!isCorrect && (\n                        <div className=\"mt-4\">\n                          <button\n                            onClick={() => fetchExplanation(\n                              question.name,\n                              question.correctAnswer || question.correctOption,\n                              userAnswer,\n                              question.image\n                            )}\n                            className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base\"\n                          >\n                            <TbBulb className=\"w-4 h-4\" />\n                            Get AI Explanation\n                          </button>\n\n                          {explanations[question.name] && (\n                            <div className=\"mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n                              <h6 className=\"font-semibold text-blue-900 mb-2 text-sm sm:text-base\">AI Explanation:</h6>\n                              <ContentRenderer content={explanations[question.name]} className=\"text-blue-800 text-sm sm:text-base\" />\n                            </div>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n\n        {/* Review Footer */}\n        <div className=\"bg-white border-t border-gray-200 p-4 sm:p-6 flex-shrink-0\">\n          <div className=\"max-w-4xl mx-auto flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center\">\n            <button\n              onClick={() => setShowReview(false)}\n              className=\"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm sm:text-base touch-manipulation\"\n            >\n              Back to Results\n            </button>\n            <button\n              onClick={() => navigate('/quiz')}\n              className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base touch-manipulation\"\n            >\n              More Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\">\n      {config.confetti && <Confetti width={width} height={height} />}\n\n      {/* Main Content - Scrollable */}\n      <div className=\"flex-1 overflow-y-auto\">\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-6 sm:py-8 lg:py-12\">\n\n          {/* Hero Section with Performance Animation */}\n          <div className={`bg-gradient-to-br ${config.bgGradient} rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 text-center relative overflow-hidden mb-6 sm:mb-8 lg:mb-12 shadow-2xl`}>\n            {/* Animated Background Elements */}\n            <div className=\"absolute inset-0 overflow-hidden\">\n              <div className=\"absolute -top-10 -right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse\"></div>\n              <div className=\"absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse delay-1000\"></div>\n            </div>\n\n            <div className=\"relative z-10\">\n              {/* Animated Icon */}\n              <div className={`inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 bg-gradient-to-br ${config.iconBg} rounded-full mb-4 sm:mb-6 shadow-2xl animate-bounce`}>\n                <config.icon className=\"w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 text-white\" />\n              </div>\n\n              {/* Title with Animation */}\n              <h1 className=\"text-2xl sm:text-3xl lg:text-5xl font-black text-white mb-3 sm:mb-4 animate-pulse\">\n                {config.title}\n              </h1>\n\n              {/* Subtitle */}\n              <p className=\"text-base sm:text-lg lg:text-xl text-white/90 font-medium px-4\">\n                {config.subtitle}\n              </p>\n            </div>\n          </div>\n\n          {/* Stats Cards */}\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8 lg:mb-12\">\n            {/* Score Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-blue-600 mb-2\">\n                {result.score || 0}%\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Score\n              </div>\n            </div>\n\n            {/* Correct Answers Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-green-600 mb-2\">\n                {result.correctAnswers?.length || 0}\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Correct\n              </div>\n            </div>\n\n            {/* Wrong Answers Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-red-600 mb-2\">\n                {result.wrongAnswers?.length || 0}\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Wrong\n              </div>\n            </div>\n\n            {/* Time Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-purple-600 mb-2\">\n                {Math.floor((result.timeSpent || 0) / 60)}m\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Time\n              </div>\n            </div>\n          </div>\n\n          {/* XP Display */}\n          {result.xpData && (\n            <div className=\"mb-6 sm:mb-8 lg:mb-12\">\n              <XPResultDisplay xpData={result.xpData} />\n            </div>\n          )}\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center mb-6 sm:mb-8\">\n            <button\n              className=\"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\"\n              onClick={() => setShowReview(true)}\n            >\n              <TbEye className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300\" />\n              Review Answers\n            </button>\n\n            <button\n              className=\"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\"\n              onClick={() => navigate(`/quiz/${id}/start`)}\n            >\n              <TbRefresh className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:rotate-180 transition-transform duration-300\" />\n              Retake Quiz\n            </button>\n\n            <button\n              className=\"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\"\n              onClick={() => navigate('/quiz')}\n            >\n              <TbBrain className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300\" />\n              <span className=\"hidden sm:inline\">More Quizzes</span>\n              <span className=\"sm:hidden\">More</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Back Navigation */}\n      <div className=\"absolute bottom-4 left-4 z-50\">\n        <button\n          onClick={() => navigate('/quiz')}\n          className=\"flex items-center justify-center w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg transition-all duration-200 hover:scale-105 touch-manipulation\"\n          title=\"Back to Quiz Page\"\n        >\n          <TbArrowLeft className=\"w-6 h-6\" />\n        </button>\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACtE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SACEC,WAAW,EACXC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,2BAA2B,QAAQ,wBAAwB;AACpE,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,sBAAA,EAAAC,oBAAA;EACvB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAM;IAAEuC;EAAG,CAAC,GAAGrC,SAAS,CAAC,CAAC;EAC1B,MAAMsC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAMsC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAMsC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsC,KAAK;IAAEC;EAAO,CAAC,GAAGpC,aAAa,CAAC,CAAC;EAEzC,MAAMqC,MAAM,IAAAjB,eAAA,GAAGa,QAAQ,CAACK,KAAK,cAAAlB,eAAA,uBAAdA,eAAA,CAAgBiB,MAAM;EAErC5C,SAAS,CAAC,MAAM;IACd,MAAM8C,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFL,QAAQ,CAACrB,WAAW,CAAC,CAAC,CAAC;QACvB,MAAM2B,QAAQ,GAAG,MAAM9B,WAAW,CAAC;UAAE+B,MAAM,EAAEV;QAAG,CAAC,CAAC;QAClDG,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAI4B,QAAQ,CAACE,OAAO,EAAE;UAAA,IAAAC,cAAA;UACpBnB,WAAW,CAACgB,QAAQ,CAACI,IAAI,CAAC;UAC1BlB,YAAY,CAAC,EAAAiB,cAAA,GAAAH,QAAQ,CAACI,IAAI,cAAAD,cAAA,uBAAbA,cAAA,CAAelB,SAAS,KAAI,EAAE,CAAC;QAC9C,CAAC,MAAM;UACL3B,OAAO,CAAC+C,KAAK,CAACL,QAAQ,CAAC1C,OAAO,CAAC;UAC/BkC,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,CAAC,OAAOa,KAAK,EAAE;QACdX,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;QACvBd,OAAO,CAAC+C,KAAK,CAACA,KAAK,CAAC/C,OAAO,CAAC;QAC5BkC,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC;IAED,IAAID,EAAE,EAAE;MACNQ,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACR,EAAE,EAAEG,QAAQ,EAAEF,QAAQ,CAAC,CAAC;;EAE5B;EACAvC,SAAS,CAAC,MAAM;IACd,IAAI4C,MAAM,EAAE;MACVS,OAAO,CAACC,GAAG,CAAE,QAAOV,MAAM,CAACW,OAAO,KAAK,MAAM,GAAG,QAAQ,GAAG,QAAS,GAAE,CAAC;;MAEvE;MACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;QACtB,IAAI;UACF,MAAMC,KAAK,GAAGb,MAAM,CAACa,KAAK,IAAI,CAAC;UAC/B,IAAIC,SAAS,GAAG,EAAE;UAElB,IAAID,KAAK,KAAK,GAAG,EAAE;YACjBC,SAAS,GAAG,qBAAqB;UACnC,CAAC,MAAM,IAAID,KAAK,IAAI,EAAE,EAAE;YACtBC,SAAS,GAAG,uBAAuB;UACrC,CAAC,MAAM,IAAId,MAAM,CAACW,OAAO,KAAK,MAAM,EAAE;YACpCG,SAAS,GAAG,kBAAkB;UAChC,CAAC,MAAM;YACLA,SAAS,GAAG,kBAAkB;UAChC;;UAEA;UACA,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACF,SAAS,CAAC;UAClCC,KAAK,CAACE,MAAM,GAAG,GAAG,CAAC,CAAC;UACpBF,KAAK,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAACX,KAAK,IAAI;YAC1BC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,KAAK,CAAC;YACxC;YACA,IAAIR,MAAM,CAACW,OAAO,KAAK,MAAM,EAAE;cAC7B;cACA,MAAMS,OAAO,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;cACxE,MAAMC,UAAU,GAAGJ,OAAO,CAACK,gBAAgB,CAAC,CAAC;cAC7C,MAAMC,QAAQ,GAAGN,OAAO,CAACO,UAAU,CAAC,CAAC;cAErCH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;cAC5BA,QAAQ,CAACE,OAAO,CAACR,OAAO,CAACS,WAAW,CAAC;cAErCL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,MAAM,EAAEX,OAAO,CAACY,WAAW,CAAC,CAAC,CAAC;cAClER,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,MAAM,EAAEX,OAAO,CAACY,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;cACxER,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,MAAM,EAAEX,OAAO,CAACY,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;;cAExEN,QAAQ,CAACO,IAAI,CAACF,cAAc,CAAC,GAAG,EAAEX,OAAO,CAACY,WAAW,CAAC;cACtDN,QAAQ,CAACO,IAAI,CAACC,4BAA4B,CAAC,IAAI,EAAEd,OAAO,CAACY,WAAW,GAAG,GAAG,CAAC;cAE3ER,UAAU,CAACW,KAAK,CAACf,OAAO,CAACY,WAAW,CAAC;cACrCR,UAAU,CAACY,IAAI,CAAChB,OAAO,CAACY,WAAW,GAAG,GAAG,CAAC;YAC5C;UACF,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACdC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEF,KAAK,CAAC;QAC5C;MACF,CAAC;;MAED;MACA6B,UAAU,CAACzB,SAAS,EAAE,GAAG,CAAC;IAC5B;EACF,CAAC,EAAE,CAACZ,MAAM,CAAC,CAAC;EAEZ5C,SAAS,CAAC,MAAM;IACdkF,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,cAAc,EAAEC,UAAU,EAAEC,QAAQ,KAAK;IACjF,IAAI;MACFlD,QAAQ,CAACrB,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM2B,QAAQ,GAAG,MAAM7B,2BAA2B,CAAC;QAAEsE,QAAQ;QAAEC,cAAc;QAAEC,UAAU;QAAEC;MAAS,CAAC,CAAC;MACtGlD,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAI4B,QAAQ,CAACE,OAAO,EAAE;QACpBd,eAAe,CAAEyD,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAACJ,QAAQ,GAAGzC,QAAQ,CAAC8C;QAAY,CAAC,CAAC,CAAC;MAC5E,CAAC,MAAM;QACLxF,OAAO,CAAC+C,KAAK,CAACL,QAAQ,CAACK,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdX,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;MACvBd,OAAO,CAAC+C,KAAK,CAACA,KAAK,CAAC/C,OAAO,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,IAAI,CAACuC,MAAM,EAAE;IACX,oBACEpB,OAAA;MAAKsE,SAAS,EAAC,wFAAwF;MAAAC,QAAA,eACrGvE,OAAA;QAAKsE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvE,OAAA,CAACX,QAAQ;UAACiF,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7D3E,OAAA;UAAIsE,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5E3E,OAAA;UAAGsE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClE3E,OAAA;UACE4E,OAAO,EAAEA,CAAA,KAAM7D,QAAQ,CAAC,OAAO,CAAE;UACjCuD,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAM5C,KAAK,GAAGb,MAAM,CAACa,KAAK,IAAI,CAAC;IAC/B,IAAIA,KAAK,KAAK,GAAG,EAAE,OAAO,SAAS;IACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,WAAW;IACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,MAAM;IAC9B,IAAIb,MAAM,CAACW,OAAO,KAAK,MAAM,EAAE,OAAO,MAAM;IAC5C,OAAO,MAAM;EACf,CAAC;EAED,MAAM+C,gBAAgB,GAAGD,mBAAmB,CAAC,CAAC;;EAE9C;EACA,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC,QAAQD,gBAAgB;MACtB,KAAK,SAAS;QACZ,OAAO;UACLE,UAAU,EAAE,2CAA2C;UACvDC,MAAM,EAAE,+BAA+B;UACvCC,IAAI,EAAE/F,QAAQ;UACdgG,KAAK,EAAE,mBAAmB;UAC1BC,QAAQ,EAAE,wCAAwC;UAClDC,QAAQ,EAAE,IAAI;UACdnD,SAAS,EAAE;QACb,CAAC;MACH,KAAK,WAAW;QACd,OAAO;UACL8C,UAAU,EAAE,4CAA4C;UACxDC,MAAM,EAAE,+BAA+B;UACvCC,IAAI,EAAE/F,QAAQ;UACdgG,KAAK,EAAE,eAAe;UACtBC,QAAQ,EAAE,sCAAsC;UAChDC,QAAQ,EAAE,IAAI;UACdnD,SAAS,EAAE;QACb,CAAC;MACH,KAAK,MAAM;MACX,KAAK,MAAM;QACT,OAAO;UACL8C,UAAU,EAAE,4CAA4C;UACxDC,MAAM,EAAE,6BAA6B;UACrCC,IAAI,EAAEjG,OAAO;UACbkG,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,sCAAsC;UAChDC,QAAQ,EAAEjE,MAAM,CAACW,OAAO,KAAK,MAAM;UACnCG,SAAS,EAAE;QACb,CAAC;MACH;QACE,OAAO;UACL8C,UAAU,EAAE,uCAAuC;UACnDC,MAAM,EAAE,0BAA0B;UAClCC,IAAI,EAAEhG,GAAG;UACTiG,KAAK,EAAE,iBAAiB;UACxBC,QAAQ,EAAE,4CAA4C;UACtDC,QAAQ,EAAE,KAAK;UACfnD,SAAS,EAAE;QACb,CAAC;IACL;EACF,CAAC;EAED,MAAMoD,MAAM,GAAGP,oBAAoB,CAAC,CAAC;;EAErC;EACA,IAAInE,UAAU,EAAE;IACd,oBACEZ,OAAA;MAAKsE,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAEhEvE,OAAA;QAAKsE,SAAS,EAAC,yGAAyG;QAAAC,QAAA,gBACtHvE,OAAA;UAAKsE,SAAS,EAAC,yGAAyG;UAAAC,QAAA,eACtHvE,OAAA,CAACT,KAAK;YAAC+E,SAAS,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACN3E,OAAA;UAAIsE,SAAS,EAAC,oEAAoE;UAAAC,QAAA,EAAC;QAEnF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3E,OAAA;UAAGsE,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN3E,OAAA;QAAKsE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCvE,OAAA;UAAKsE,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EvE,OAAA;YAAKsE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EACpC/D,SAAS,CAAC+E,GAAG,CAAC,CAACvB,QAAQ,EAAEwB,KAAK,KAAK;cAAA,IAAAC,qBAAA,EAAAC,qBAAA;cAClC,MAAMxB,UAAU,GAAG,EAAAuB,qBAAA,GAAArE,MAAM,CAACuE,cAAc,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAK9B,QAAQ,CAAC8B,GAAG,CAAC,cAAAL,qBAAA,uBAAvDA,qBAAA,CAAyDvB,UAAU,OAAAwB,qBAAA,GACpEtE,MAAM,CAAC2E,YAAY,CAACH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAK9B,QAAQ,CAAC8B,GAAG,CAAC,cAAAJ,qBAAA,uBAArDA,qBAAA,CAAuDxB,UAAU,KAAI,EAAE;cACzF,MAAM8B,SAAS,GAAG5E,MAAM,CAACuE,cAAc,CAACM,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAK9B,QAAQ,CAAC8B,GAAG,CAAC;cAEzE,oBACE9F,OAAA;gBAAiBsE,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,gBAE7GvE,OAAA;kBAAKsE,SAAS,EAAG,wCACf0B,SAAS,GACL,+DAA+D,GAC/D,wDACL,EAAE;kBAAAzB,QAAA,eACDvE,OAAA;oBAAKsE,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDvE,OAAA;sBAAIsE,SAAS,EAAC,+EAA+E;sBAAAC,QAAA,gBAC3FvE,OAAA;wBAAMsE,SAAS,EAAG,yGAChB0B,SAAS,GAAG,cAAc,GAAG,YAC9B,EAAE;wBAAAzB,QAAA,EACAyB,SAAS,gBACRhG,OAAA,CAACf,OAAO;0BAACqF,SAAS,EAAC;wBAAsC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAE5D3E,OAAA,CAACd,GAAG;0BAACoF,SAAS,EAAC;wBAAoC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBACtD;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC,eACP3E,OAAA;wBAAMsE,SAAS,EAAC,UAAU;wBAAAC,QAAA,GAAC,WAAS,EAACiB,KAAK,GAAG,CAAC;sBAAA;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACL3E,OAAA;sBAAMsE,SAAS,EAAG,mEAChB0B,SAAS,GACL,6BAA6B,GAC7B,yBACL,EAAE;sBAAAzB,QAAA,EACAyB,SAAS,GAAG,SAAS,GAAG;oBAAO;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN3E,OAAA;kBAAKsE,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBvE,OAAA;oBAAKsE,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBvE,OAAA;sBAAIsE,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpF3E,OAAA,CAACH,eAAe;sBAACqG,OAAO,EAAElC,QAAQ,CAACmC,IAAK;sBAAC7B,SAAS,EAAC;oBAAoC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvF,CAAC,EAELX,QAAQ,CAACoC,KAAK,iBACbpG,OAAA;oBAAKsE,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,eAC/BvE,OAAA;sBACEqG,GAAG,EAAErC,QAAQ,CAACoC,KAAM;sBACpBE,GAAG,EAAC,UAAU;sBACdhC,SAAS,EAAC;oBAA8D;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN,eAED3E,OAAA;oBAAKsE,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDvE,OAAA;sBAAAuE,QAAA,gBACEvE,OAAA;wBAAIsE,SAAS,EAAC,uDAAuD;wBAAAC,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACvF3E,OAAA;wBAAKsE,SAAS,EAAG,uCACf0B,SAAS,GAAG,4BAA4B,GAAG,wBAC5C,EAAE;wBAAAzB,QAAA,EACAL,UAAU,IAAI;sBAAoB;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN3E,OAAA;sBAAAuE,QAAA,gBACEvE,OAAA;wBAAIsE,SAAS,EAAC,uDAAuD;wBAAAC,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1F3E,OAAA;wBAAKsE,SAAS,EAAC,gEAAgE;wBAAAC,QAAA,EAC5EP,QAAQ,CAACuC,aAAa,IAAIvC,QAAQ,CAACwC,aAAa,IAAI;sBAAK;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGL,CAACqB,SAAS,iBACThG,OAAA;oBAAKsE,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBvE,OAAA;sBACE4E,OAAO,EAAEA,CAAA,KAAMb,gBAAgB,CAC7BC,QAAQ,CAACmC,IAAI,EACbnC,QAAQ,CAACuC,aAAa,IAAIvC,QAAQ,CAACwC,aAAa,EAChDtC,UAAU,EACVF,QAAQ,CAACoC,KACX,CAAE;sBACF9B,SAAS,EAAC,8HAA8H;sBAAAC,QAAA,gBAExIvE,OAAA,CAACR,MAAM;wBAAC8E,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,sBAEhC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAERjE,YAAY,CAACsD,QAAQ,CAACmC,IAAI,CAAC,iBAC1BnG,OAAA;sBAAKsE,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,gBACpEvE,OAAA;wBAAIsE,SAAS,EAAC,uDAAuD;wBAAAC,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1F3E,OAAA,CAACH,eAAe;wBAACqG,OAAO,EAAExF,YAAY,CAACsD,QAAQ,CAACmC,IAAI,CAAE;wBAAC7B,SAAS,EAAC;sBAAoC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAzFEa,KAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0FV,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3E,OAAA;QAAKsE,SAAS,EAAC,4DAA4D;QAAAC,QAAA,eACzEvE,OAAA;UAAKsE,SAAS,EAAC,2EAA2E;UAAAC,QAAA,gBACxFvE,OAAA;YACE4E,OAAO,EAAEA,CAAA,KAAM/D,aAAa,CAAC,KAAK,CAAE;YACpCyD,SAAS,EAAC,yHAAyH;YAAAC,QAAA,EACpI;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3E,OAAA;YACE4E,OAAO,EAAEA,CAAA,KAAM7D,QAAQ,CAAC,OAAO,CAAE;YACjCuD,SAAS,EAAC,yHAAyH;YAAAC,QAAA,EACpI;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE3E,OAAA;IAAKsE,SAAS,EAAC,qFAAqF;IAAAC,QAAA,GACjGe,MAAM,CAACD,QAAQ,iBAAIrF,OAAA,CAAClB,QAAQ;MAACoC,KAAK,EAAEA,KAAM;MAACC,MAAM,EAAEA;IAAO;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG9D3E,OAAA;MAAKsE,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCvE,OAAA;QAAKsE,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAG3EvE,OAAA;UAAKsE,SAAS,EAAG,qBAAoBgB,MAAM,CAACN,UAAW,sHAAsH;UAAAT,QAAA,gBAE3KvE,OAAA;YAAKsE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CvE,OAAA;cAAKsE,SAAS,EAAC;YAAsF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5G3E,OAAA;cAAKsE,SAAS,EAAC;YAAmG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtH,CAAC,eAEN3E,OAAA;YAAKsE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAE5BvE,OAAA;cAAKsE,SAAS,EAAG,uGAAsGgB,MAAM,CAACL,MAAO,sDAAsD;cAAAV,QAAA,eACzLvE,OAAA,CAACsF,MAAM,CAACJ,IAAI;gBAACZ,SAAS,EAAC;cAAsD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eAGN3E,OAAA;cAAIsE,SAAS,EAAC,mFAAmF;cAAAC,QAAA,EAC9Fe,MAAM,CAACH;YAAK;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eAGL3E,OAAA;cAAGsE,SAAS,EAAC,gEAAgE;cAAAC,QAAA,EAC1Ee,MAAM,CAACF;YAAQ;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3E,OAAA;UAAKsE,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBAEnFvE,OAAA;YAAKsE,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJvE,OAAA;cAAKsE,SAAS,EAAC,gEAAgE;cAAAC,QAAA,GAC5EnD,MAAM,CAACa,KAAK,IAAI,CAAC,EAAC,GACrB;YAAA;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3E,OAAA;cAAKsE,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3E,OAAA;YAAKsE,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJvE,OAAA;cAAKsE,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAC7E,EAAAnE,sBAAA,GAAAgB,MAAM,CAACuE,cAAc,cAAAvF,sBAAA,uBAArBA,sBAAA,CAAuBqG,MAAM,KAAI;YAAC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACN3E,OAAA;cAAKsE,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3E,OAAA;YAAKsE,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJvE,OAAA;cAAKsE,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC3E,EAAAlE,oBAAA,GAAAe,MAAM,CAAC2E,YAAY,cAAA1F,oBAAA,uBAAnBA,oBAAA,CAAqBoG,MAAM,KAAI;YAAC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACN3E,OAAA;cAAKsE,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3E,OAAA;YAAKsE,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJvE,OAAA;cAAKsE,SAAS,EAAC,kEAAkE;cAAAC,QAAA,GAC9EmC,IAAI,CAACC,KAAK,CAAC,CAACvF,MAAM,CAACwF,SAAS,IAAI,CAAC,IAAI,EAAE,CAAC,EAAC,GAC5C;YAAA;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3E,OAAA;cAAKsE,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLvD,MAAM,CAACyF,MAAM,iBACZ7G,OAAA;UAAKsE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCvE,OAAA,CAACF,eAAe;YAAC+G,MAAM,EAAEzF,MAAM,CAACyF;UAAO;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN,eAGD3E,OAAA;UAAKsE,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFvE,OAAA;YACEsE,SAAS,EAAC,mVAAmV;YAC7VM,OAAO,EAAEA,CAAA,KAAM/D,aAAa,CAAC,IAAI,CAAE;YAAA0D,QAAA,gBAEnCvE,OAAA,CAACT,KAAK;cAAC+E,SAAS,EAAC;YAAoF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAE1G;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET3E,OAAA;YACEsE,SAAS,EAAC,uVAAuV;YACjWM,OAAO,EAAEA,CAAA,KAAM7D,QAAQ,CAAE,SAAQD,EAAG,QAAO,CAAE;YAAAyD,QAAA,gBAE7CvE,OAAA,CAACV,SAAS;cAACgF,SAAS,EAAC;YAAqF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE/G;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET3E,OAAA;YACEsE,SAAS,EAAC,uVAAuV;YACjWM,OAAO,EAAEA,CAAA,KAAM7D,QAAQ,CAAC,OAAO,CAAE;YAAAwD,QAAA,gBAEjCvE,OAAA,CAACZ,OAAO;cAACkF,SAAS,EAAC;YAAoF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1G3E,OAAA;cAAMsE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtD3E,OAAA;cAAMsE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3E,OAAA;MAAKsE,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CvE,OAAA;QACE4E,OAAO,EAAEA,CAAA,KAAM7D,QAAQ,CAAC,OAAO,CAAE;QACjCuD,SAAS,EAAC,2KAA2K;QACrLa,KAAK,EAAC,mBAAmB;QAAAZ,QAAA,eAEzBvE,OAAA,CAAChB,WAAW;UAACsF,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzE,EAAA,CAxdID,UAAU;EAAA,QAMCxB,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW,EACFG,aAAa;AAAA;AAAA+H,EAAA,GAVnC7G,UAAU;AAAA,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}