{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbTarget, TbBrain, TbSearch, TbFilter, TbRefresh, TbMedal, TbBolt, TbRocket, TbDiamond, TbHeart, TbEye, TbTrendingUp, TbAward, TbShield } from 'react-icons/tb';\nimport { getAllReportsForRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const user = userState.user || null;\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const headerRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Performance tiers with SPECTACULAR visual themes\n  const performanceTiers = {\n    legendary: {\n      min: 10000,\n      color: 'from-purple-500 via-pink-500 via-red-500 to-orange-500',\n      bgColor: 'bg-gradient-to-br from-purple-900/30 via-pink-900/30 to-red-900/30',\n      textColor: '#FFD700',\n      shadowColor: 'rgba(147, 51, 234, 0.8)',\n      glow: 'shadow-purple-500/70',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery',\n      borderColor: '#8B5CF6'\n    },\n    diamond: {\n      min: 7500,\n      color: 'from-cyan-300 via-blue-400 via-indigo-500 to-purple-600',\n      bgColor: 'bg-gradient-to-br from-cyan-900/30 via-blue-900/30 to-indigo-900/30',\n      textColor: '#00FFFF',\n      shadowColor: 'rgba(6, 182, 212, 0.8)',\n      glow: 'shadow-cyan-400/70',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance',\n      borderColor: '#06B6D4'\n    },\n    platinum: {\n      min: 5000,\n      color: 'from-slate-300 via-gray-300 via-zinc-400 to-stone-500',\n      bgColor: 'bg-gradient-to-br from-slate-800/30 via-gray-800/30 to-zinc-800/30',\n      textColor: '#E5E7EB',\n      shadowColor: 'rgba(148, 163, 184, 0.8)',\n      glow: 'shadow-slate-400/70',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding',\n      borderColor: '#94A3B8'\n    },\n    gold: {\n      min: 2500,\n      color: 'from-yellow-300 via-amber-400 via-orange-400 to-red-500',\n      bgColor: 'bg-gradient-to-br from-yellow-900/30 via-amber-900/30 to-orange-900/30',\n      textColor: '#FCD34D',\n      shadowColor: 'rgba(245, 158, 11, 0.8)',\n      glow: 'shadow-yellow-400/70',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent',\n      borderColor: '#F59E0B'\n    },\n    silver: {\n      min: 1000,\n      color: 'from-gray-300 via-slate-400 via-zinc-500 to-gray-600',\n      bgColor: 'bg-gradient-to-br from-gray-800/30 via-slate-800/30 to-zinc-800/30',\n      textColor: '#D1D5DB',\n      shadowColor: 'rgba(156, 163, 175, 0.8)',\n      glow: 'shadow-gray-400/70',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress',\n      borderColor: '#9CA3AF'\n    },\n    bronze: {\n      min: 0,\n      color: 'from-orange-300 via-amber-400 via-yellow-500 to-orange-600',\n      bgColor: 'bg-gradient-to-br from-orange-900/30 via-amber-900/30 to-yellow-900/30',\n      textColor: '#FB923C',\n      shadowColor: 'rgba(251, 146, 60, 0.8)',\n      glow: 'shadow-orange-400/70',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star',\n      borderColor: '#FB923C'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = xp => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return {\n        tier,\n        ...config\n      };\n    }\n    return {\n      tier: 'bronze',\n      ...performanceTiers.bronze\n    };\n  };\n\n  // Fetch ranking data with multiple fallbacks\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching amazing ranking data...');\n      let response;\n\n      // Try ranking reports first\n      try {\n        response = await getAllReportsForRanking();\n        console.log('✨ Ranking reports response:', response);\n      } catch (error) {\n        console.log('⚡ Trying all users...');\n        response = await getAllUsers();\n      }\n      if (!response || !response.success) {\n        console.log('🔄 Falling back to all users...');\n        response = await getAllUsers();\n      }\n      if (response && response.success && response.data) {\n        const transformedData = response.data.map((item, index) => {\n          // Handle both user data and report data structures\n          const userData = item.user || item;\n          const reportData = item.reports || [];\n\n          // Calculate stats from reports if available\n          const totalQuizzes = reportData.length || userData.totalQuizzesTaken || 0;\n          const totalScore = reportData.reduce((sum, report) => sum + (report.score || 0), 0);\n          const averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : 0;\n          const totalXP = userData.totalXP || userData.xp || totalScore || Math.floor(averageScore * totalQuizzes / 10) || 0;\n          return {\n            _id: userData._id || userData.userId || userData.id,\n            name: userData.name || userData.userName || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || userData.className || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || userData.avatar || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: userData.currentStreak || userData.streak || 0,\n            bestStreak: userData.bestStreak || userData.maxStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || userData.normalizedSubscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(totalXP)\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank\n        const userRank = user ? transformedData.findIndex(item => item._id === user._id) : -1;\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'champions');\n      } else {\n        // Fallback demo data to showcase the amazing design\n        console.log('🎭 Loading demo data to showcase the amazing design...');\n        const demoData = [{\n          _id: 'demo1',\n          name: 'Alex Champion',\n          email: '<EMAIL>',\n          class: '7',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 15000,\n          totalQuizzesTaken: 45,\n          averageScore: 92,\n          currentStreak: 12,\n          bestStreak: 18,\n          subscriptionStatus: 'premium',\n          rank: 1,\n          tier: getUserTier(15000)\n        }, {\n          _id: 'demo2',\n          name: 'Sarah Excellence',\n          email: '<EMAIL>',\n          class: '6',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 12500,\n          totalQuizzesTaken: 38,\n          averageScore: 88,\n          currentStreak: 8,\n          bestStreak: 15,\n          subscriptionStatus: 'premium',\n          rank: 2,\n          tier: getUserTier(12500)\n        }, {\n          _id: 'demo3',\n          name: 'Mike Achiever',\n          email: '<EMAIL>',\n          class: '7',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 9800,\n          totalQuizzesTaken: 32,\n          averageScore: 85,\n          currentStreak: 5,\n          bestStreak: 12,\n          subscriptionStatus: 'free',\n          rank: 3,\n          tier: getUserTier(9800)\n        }, {\n          _id: 'demo4',\n          name: 'Emma Rising',\n          email: '<EMAIL>',\n          class: '5',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 7200,\n          totalQuizzesTaken: 28,\n          averageScore: 82,\n          currentStreak: 3,\n          bestStreak: 9,\n          subscriptionStatus: 'free',\n          rank: 4,\n          tier: getUserTier(7200)\n        }, {\n          _id: 'demo5',\n          name: 'David Progress',\n          email: '<EMAIL>',\n          class: '6',\n          level: 'Secondary',\n          profilePicture: '',\n          totalXP: 5500,\n          totalQuizzesTaken: 22,\n          averageScore: 78,\n          currentStreak: 2,\n          bestStreak: 7,\n          subscriptionStatus: 'free',\n          rank: 5,\n          tier: getUserTier(5500)\n        }];\n        setRankingData(demoData);\n        setCurrentUserRank(null); // No current user in demo data\n        message.success('Welcome to the Hall of Champions! 🏆');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n    return () => clearInterval(animationTimer);\n  }, []);\n\n  // Filter and search functionality\n  const filteredData = rankingData.filter(rankingUser => {\n    const matchesSearch = rankingUser.name.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterType === 'all' || filterType === 'premium' && rankingUser.subscriptionStatus === 'premium' || filterType === 'free' && rankingUser.subscriptionStatus === 'free' || filterType === 'class' && user && rankingUser.class === user.class;\n    return matchesSearch && matchesFilter;\n  });\n\n  // Get top performers for special display\n  const topPerformers = filteredData.slice(0, 3);\n  const otherPerformers = filteredData.slice(3);\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading the Hall of Champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n      children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n        animate: {\n          y: [0, -100, 0],\n          x: [0, Math.random() * 100 - 50, 0],\n          opacity: [0.2, 0.8, 0.2]\n        },\n        transition: {\n          duration: 3 + Math.random() * 2,\n          repeat: Infinity,\n          delay: Math.random() * 2\n        },\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        ref: headerRef,\n        initial: {\n          opacity: 0,\n          y: -50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 1,\n          ease: \"easeOut\"\n        },\n        className: \"relative overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-purple-600 via-pink-500 via-red-500 via-orange-500 to-yellow-500 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10 px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-7xl mx-auto text-center\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  scale: [1, 1.02, 1],\n                  rotateY: [0, 5, 0]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                className: \"mb-8\",\n                children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-5xl sm:text-6xl lg:text-8xl font-black mb-4 tracking-tight\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    animate: {\n                      backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                    },\n                    transition: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"linear\"\n                    },\n                    className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                    style: {\n                      backgroundSize: '400% 400%',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      filter: 'drop-shadow(3px 3px 6px rgba(0,0,0,0.8))'\n                    },\n                    children: \"HALL OF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    animate: {\n                      textShadow: ['0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)', '0 0 50px rgba(255,215,0,1), 0 0 80px rgba(255,215,0,0.8)', '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)']\n                    },\n                    transition: {\n                      duration: 2.5,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    style: {\n                      color: '#FFD700',\n                      fontWeight: '900',\n                      textShadow: '4px 4px 8px rgba(0,0,0,0.9)'\n                    },\n                    children: \"CHAMPIONS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.5,\n                  duration: 0.8\n                },\n                className: \"text-xl sm:text-2xl lg:text-3xl font-semibold mb-8 max-w-4xl mx-auto leading-relaxed\",\n                style: {\n                  color: '#F3F4F6',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent'\n                },\n                children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  delay: 1,\n                  duration: 0.8\n                },\n                className: \"relative max-w-2xl mx-auto mb-8\",\n                style: {\n                  background: 'linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05))',\n                  backdropFilter: 'blur(20px)',\n                  borderRadius: '20px',\n                  padding: '24px',\n                  border: '2px solid rgba(255,255,255,0.2)',\n                  boxShadow: '0 8px 32px rgba(0,0,0,0.3)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-yellow-500/10 rounded-2xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 10\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"text-lg sm:text-xl font-semibold relative z-10\",\n                  style: {\n                    color: '#FBBF24',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    textAlign: 'center'\n                  },\n                  children: motivationalQuote\n                }, motivationalQuote, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 30\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 1.2,\n                  duration: 0.8\n                },\n                className: \"grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto\",\n                children: [{\n                  icon: TbTrophy,\n                  label: 'Total Champions',\n                  value: rankingData.length,\n                  color: 'text-yellow-400'\n                }, {\n                  icon: TbFlame,\n                  label: 'Active Streaks',\n                  value: rankingData.filter(u => u.currentStreak > 0).length,\n                  color: 'text-orange-400'\n                }, {\n                  icon: TbBrain,\n                  label: 'Quizzes Taken',\n                  value: rankingData.reduce((sum, u) => sum + u.totalQuizzesTaken, 0),\n                  color: 'text-blue-400'\n                }, {\n                  icon: TbBolt,\n                  label: 'Total XP',\n                  value: rankingData.reduce((sum, u) => sum + u.totalXP, 0).toLocaleString(),\n                  color: 'text-purple-400'\n                }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  whileHover: {\n                    scale: 1.05,\n                    y: -5\n                  },\n                  className: \"bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/20 text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(stat.icon, {\n                    className: `w-8 h-8 ${stat.color} mx-auto mb-2`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl sm:text-3xl font-bold text-white mb-1\",\n                    children: stat.value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-white/70\",\n                    children: stat.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 532,\n                    columnNumber: 23\n                  }, this)]\n                }, stat.label, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 1.5,\n          duration: 0.8\n        },\n        className: \"px-4 sm:px-6 lg:px-8 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col lg:flex-row gap-6 items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative flex-1 max-w-md\",\n                children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n                  className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search champions...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  className: \"w-full pl-12 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-3\",\n                children: [{\n                  key: 'all',\n                  label: 'All Champions',\n                  icon: TbTrophy\n                }, {\n                  key: 'premium',\n                  label: 'Premium',\n                  icon: TbCrown\n                }, {\n                  key: 'free',\n                  label: 'Free',\n                  icon: TbStar\n                }, {\n                  key: 'class',\n                  label: 'My Class',\n                  icon: TbTarget\n                }].map(filter => /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setFilterType(filter.key),\n                  className: `flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${filterType === filter.key ? 'bg-purple-600 text-white shadow-lg shadow-purple-500/25' : 'bg-white/10 text-white/80 hover:bg-white/20'}`,\n                  children: [/*#__PURE__*/_jsxDEV(filter.icon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"hidden sm:inline\",\n                    children: filter.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 23\n                  }, this)]\n                }, filter.key, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05,\n                  rotate: 180\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: fetchRankingData,\n                disabled: loading,\n                className: \"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50\",\n                children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                  className: `w-5 h-5 ${loading ? 'animate-spin' : ''}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Refresh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 9\n      }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"flex flex-col items-center justify-center py-20\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 11\n      }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3,\n          duration: 0.8\n        },\n        className: \"px-4 sm:px-6 lg:px-8 pb-20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              delay: 0.5,\n              duration: 0.8\n            },\n            className: \"mb-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl sm:text-4xl font-bold text-white text-center mb-8\",\n              children: \"\\uD83C\\uDFC6 Champions Podium \\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\",\n              children: topPerformers.map((champion, index) => {\n                const position = index + 1;\n                const isCurrentUser = user && champion._id === user._id;\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.7 + index * 0.2,\n                    duration: 0.8\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative ${position === 1 ? 'md:order-2 md:scale-110' : position === 2 ? 'md:order-1' : 'md:order-3'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-2xl ${champion.tier.glow} shadow-2xl`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-slate-900/90 backdrop-blur-lg rounded-2xl p-6 text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center text-white font-black text-xl shadow-lg`,\n                        children: position\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 665,\n                        columnNumber: 31\n                      }, this), position === 1 && /*#__PURE__*/_jsxDEV(motion.div, {\n                        animate: {\n                          rotate: [0, 10, -10, 0]\n                        },\n                        transition: {\n                          duration: 2,\n                          repeat: Infinity\n                        },\n                        className: \"absolute -top-8 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                          className: \"w-8 h-8 text-yellow-400\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 676,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 671,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-4 ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-20 h-20 rounded-full overflow-hidden mx-auto bg-gradient-to-br from-purple-500 to-pink-500 p-1\",\n                          children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: champion.profilePicture,\n                            alt: champion.name,\n                            className: \"w-full h-full object-cover rounded-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 684,\n                            columnNumber: 37\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-2xl\",\n                            children: champion.name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 690,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 682,\n                          columnNumber: 33\n                        }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-2 -right-2 bg-yellow-400 text-black rounded-full p-1\",\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-4 h-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 697,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 696,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 681,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-xl font-bold mb-2\",\n                        style: {\n                          color: '#ffffff',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          fontWeight: '800'\n                        },\n                        children: champion.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 703,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r ${champion.tier.color} rounded-full text-sm font-medium mb-3`,\n                        style: {\n                          color: '#ffffff',\n                          textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                          fontWeight: '700',\n                          border: '2px solid rgba(255,255,255,0.3)'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 714,\n                          columnNumber: 33\n                        }, this), champion.tier.title]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 708,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#e5e7eb',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '600'\n                            },\n                            children: \"XP:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 721,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#ffffff',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '800'\n                            },\n                            children: champion.totalXP.toLocaleString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 726,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 720,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#e5e7eb',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '600'\n                            },\n                            children: \"Quizzes:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 733,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#ffffff',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '800'\n                            },\n                            children: champion.totalQuizzesTaken\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 738,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 732,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#e5e7eb',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '600'\n                            },\n                            children: \"Streak:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 745,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#ffffff',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '800'\n                            },\n                            className: \"flex items-center gap-1\",\n                            children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                              className: \"w-4 h-4 text-orange-400\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 755,\n                              columnNumber: 37\n                            }, this), champion.currentStreak]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 750,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 744,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 719,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 662,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 661,\n                    columnNumber: 27\n                  }, this)\n                }, champion._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 17\n          }, this), otherPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 1,\n              duration: 0.8\n            },\n            className: \"mt-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl sm:text-3xl font-bold text-white text-center mb-8\",\n              children: \"\\u26A1 Rising Champions \\u26A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: otherPerformers.map((champion, index) => {\n                const actualRank = index + 4; // Since top 3 are shown separately\n                const isCurrentUser = user && champion._id === user._id;\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 1.2 + index * 0.1,\n                    duration: 0.6\n                  },\n                  whileHover: {\n                    scale: 1.02,\n                    x: 10\n                  },\n                  className: `relative ${isCurrentUser ? 'ring-2 ring-yellow-400' : ''}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-slate-900/90 backdrop-blur-lg rounded-xl p-4 flex items-center gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `flex-shrink-0 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg`,\n                        children: actualRank\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 799,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-14 h-14 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 to-pink-500 p-1\",\n                          children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: champion.profilePicture,\n                            alt: champion.name,\n                            className: \"w-full h-full object-cover rounded-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 807,\n                            columnNumber: 37\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-lg\",\n                            children: champion.name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 813,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 805,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 804,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-2 mb-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-lg font-bold truncate\",\n                            style: {\n                              color: '#ffffff',\n                              textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                              fontWeight: '800'\n                            },\n                            children: champion.name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 823,\n                            columnNumber: 35\n                          }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                              color: '#000000',\n                              fontWeight: '900',\n                              textShadow: 'none',\n                              border: '2px solid #ffffff',\n                              boxShadow: '0 4px 8px rgba(0,0,0,0.3)'\n                            },\n                            className: \"px-2 py-1 rounded-full text-xs\",\n                            children: \"YOU\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 829,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 822,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `inline-flex items-center gap-1 px-2 py-1 bg-gradient-to-r ${champion.tier.color} rounded-full text-xs font-medium`,\n                          style: {\n                            color: '#ffffff',\n                            textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                            fontWeight: '700',\n                            border: '1px solid rgba(255,255,255,0.3)'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                            className: \"w-3 h-3\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 847,\n                            columnNumber: 35\n                          }, this), champion.tier.title]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 841,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 821,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0 text-right\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-lg mb-1\",\n                          style: {\n                            color: '#ffffff',\n                            textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                            fontWeight: '800'\n                          },\n                          children: [champion.totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 854,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-4 text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"flex items-center gap-1\",\n                            style: {\n                              color: '#e5e7eb',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '600'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                              className: \"w-4 h-4\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 867,\n                              columnNumber: 37\n                            }, this), champion.totalQuizzesTaken]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 862,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"flex items-center gap-1\",\n                            style: {\n                              color: '#e5e7eb',\n                              textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                              fontWeight: '600'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                              className: \"w-4 h-4 text-orange-400\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 875,\n                              columnNumber: 37\n                            }, this), champion.currentStreak]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 870,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 861,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 853,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 796,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 795,\n                    columnNumber: 27\n                  }, this)\n                }, champion._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 787,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 17\n          }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              delay: 1.5,\n              duration: 0.8\n            },\n            className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-2\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"Your Current Position\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 898,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-6xl font-black mb-2\",\n                style: {\n                  color: '#fbbf24',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  fontWeight: '900'\n                },\n                children: [\"#\", currentUserRank]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 897,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 891,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 2,\n              duration: 0.8\n            },\n            className: \"mt-16 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  scale: [1, 1.05, 1]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity\n                },\n                children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                  className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 927,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-3xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"Ready to Rise Higher?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 933,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl mb-6 max-w-2xl mx-auto\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 938,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                onClick: () => window.location.href = '/user/quiz',\n                children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 15\n          }, this), filteredData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            className: \"text-center py-20\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold mb-4\",\n              style: {\n                color: '#ffffff',\n                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                fontWeight: '800'\n              },\n              children: \"No Champions Found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg\",\n              style: {\n                color: '#e5e7eb',\n                textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                fontWeight: '600'\n              },\n              children: \"Try adjusting your search or filter criteria to find more champions!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 970,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 959,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 356,\n    columnNumber: 5\n  }, this);\n};\n_s(AmazingRankingPage, \"w6ncjJ4K0HgVPDyL7WwM8iBVJKM=\", false, function () {\n  return [useSelector];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbTarget", "TbBrain", "TbSearch", "Tb<PERSON><PERSON>er", "TbRefresh", "TbMedal", "TbBolt", "TbRocket", "TbDiamond", "TbHeart", "TbEye", "TbTrendingUp", "TbAward", "TbShield", "getAllReportsForRanking", "getAllUsers", "jsxDEV", "_jsxDEV", "AmazingRankingPage", "_s", "userState", "state", "users", "user", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "searchTerm", "setSearchTerm", "filterType", "setFilterType", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "headerRef", "motivationalQuotes", "performanceTiers", "legendary", "min", "color", "bgColor", "textColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "diamond", "platinum", "gold", "silver", "bronze", "getUserTier", "xp", "tier", "config", "Object", "entries", "fetchRankingData", "console", "log", "response", "error", "success", "data", "transformedData", "map", "item", "index", "userData", "reportData", "reports", "totalQuizzes", "length", "totalQuizzesTaken", "totalScore", "reduce", "sum", "report", "score", "averageScore", "Math", "round", "totalXP", "floor", "_id", "userId", "id", "name", "userName", "email", "class", "className", "level", "profilePicture", "avatar", "currentStreak", "streak", "bestStreak", "maxStreak", "subscriptionStatus", "normalizedSubscriptionStatus", "rank", "sort", "a", "b", "for<PERSON>ach", "userRank", "findIndex", "demoData", "randomQuote", "random", "animationTimer", "setInterval", "prev", "clearInterval", "filteredData", "filter", "rankingUser", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "topPerformers", "slice", "otherPerformers", "children", "div", "initial", "opacity", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "_", "i", "y", "x", "delay", "style", "left", "top", "ref", "scale", "rotateY", "span", "backgroundPosition", "backgroundSize", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "fontWeight", "p", "background", "<PERSON><PERSON>ilter", "borderRadius", "padding", "border", "boxShadow", "textAlign", "label", "value", "u", "toLocaleString", "stat", "whileHover", "type", "placeholder", "onChange", "e", "target", "key", "button", "whileTap", "onClick", "disabled", "champion", "position", "isCurrentUser", "src", "alt", "char<PERSON>t", "toUpperCase", "actualRank", "window", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbTarget,\n  TbBrain,\n  TbSearch,\n  TbFilter,\n  TbRefresh,\n  TbMedal,\n  TbBolt,\n  TbRocket,\n  TbDiamond,\n  TbHeart,\n  TbEye,\n  TbTrendingUp,\n  TbAward,\n  TbShield\n} from 'react-icons/tb';\nimport { getAllReportsForRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const user = userState.user || null;\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const headerRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Performance tiers with SPECTACULAR visual themes\n  const performanceTiers = {\n    legendary: {\n      min: 10000,\n      color: 'from-purple-500 via-pink-500 via-red-500 to-orange-500',\n      bgColor: 'bg-gradient-to-br from-purple-900/30 via-pink-900/30 to-red-900/30',\n      textColor: '#FFD700',\n      shadowColor: 'rgba(147, 51, 234, 0.8)',\n      glow: 'shadow-purple-500/70',\n      icon: TbCrown,\n      title: 'LEGENDARY',\n      description: 'Absolute Mastery',\n      borderColor: '#8B5CF6'\n    },\n    diamond: {\n      min: 7500,\n      color: 'from-cyan-300 via-blue-400 via-indigo-500 to-purple-600',\n      bgColor: 'bg-gradient-to-br from-cyan-900/30 via-blue-900/30 to-indigo-900/30',\n      textColor: '#00FFFF',\n      shadowColor: 'rgba(6, 182, 212, 0.8)',\n      glow: 'shadow-cyan-400/70',\n      icon: TbDiamond,\n      title: 'DIAMOND',\n      description: 'Elite Performance',\n      borderColor: '#06B6D4'\n    },\n    platinum: {\n      min: 5000,\n      color: 'from-slate-300 via-gray-300 via-zinc-400 to-stone-500',\n      bgColor: 'bg-gradient-to-br from-slate-800/30 via-gray-800/30 to-zinc-800/30',\n      textColor: '#E5E7EB',\n      shadowColor: 'rgba(148, 163, 184, 0.8)',\n      glow: 'shadow-slate-400/70',\n      icon: TbShield,\n      title: 'PLATINUM',\n      description: 'Outstanding',\n      borderColor: '#94A3B8'\n    },\n    gold: {\n      min: 2500,\n      color: 'from-yellow-300 via-amber-400 via-orange-400 to-red-500',\n      bgColor: 'bg-gradient-to-br from-yellow-900/30 via-amber-900/30 to-orange-900/30',\n      textColor: '#FCD34D',\n      shadowColor: 'rgba(245, 158, 11, 0.8)',\n      glow: 'shadow-yellow-400/70',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Excellent',\n      borderColor: '#F59E0B'\n    },\n    silver: {\n      min: 1000,\n      color: 'from-gray-300 via-slate-400 via-zinc-500 to-gray-600',\n      bgColor: 'bg-gradient-to-br from-gray-800/30 via-slate-800/30 to-zinc-800/30',\n      textColor: '#D1D5DB',\n      shadowColor: 'rgba(156, 163, 175, 0.8)',\n      glow: 'shadow-gray-400/70',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Great Progress',\n      borderColor: '#9CA3AF'\n    },\n    bronze: {\n      min: 0,\n      color: 'from-orange-300 via-amber-400 via-yellow-500 to-orange-600',\n      bgColor: 'bg-gradient-to-br from-orange-900/30 via-amber-900/30 to-yellow-900/30',\n      textColor: '#FB923C',\n      shadowColor: 'rgba(251, 146, 60, 0.8)',\n      glow: 'shadow-orange-400/70',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Rising Star',\n      borderColor: '#FB923C'\n    }\n  };\n\n  // Get user's performance tier\n  const getUserTier = (xp) => {\n    for (const [tier, config] of Object.entries(performanceTiers)) {\n      if (xp >= config.min) return { tier, ...config };\n    }\n    return { tier: 'bronze', ...performanceTiers.bronze };\n  };\n\n  // Fetch ranking data with multiple fallbacks\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching amazing ranking data...');\n\n      let response;\n\n      // Try ranking reports first\n      try {\n        response = await getAllReportsForRanking();\n        console.log('✨ Ranking reports response:', response);\n      } catch (error) {\n        console.log('⚡ Trying all users...');\n        response = await getAllUsers();\n      }\n\n      if (!response || !response.success) {\n        console.log('🔄 Falling back to all users...');\n        response = await getAllUsers();\n      }\n\n      if (response && response.success && response.data) {\n        const transformedData = response.data.map((item, index) => {\n          // Handle both user data and report data structures\n          const userData = item.user || item;\n          const reportData = item.reports || [];\n\n          // Calculate stats from reports if available\n          const totalQuizzes = reportData.length || userData.totalQuizzesTaken || 0;\n          const totalScore = reportData.reduce((sum, report) => sum + (report.score || 0), 0);\n          const averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : 0;\n          const totalXP = userData.totalXP || userData.xp || totalScore || Math.floor(averageScore * totalQuizzes / 10) || 0;\n\n          return {\n            _id: userData._id || userData.userId || userData.id,\n            name: userData.name || userData.userName || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || userData.className || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || userData.avatar || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: userData.currentStreak || userData.streak || 0,\n            bestStreak: userData.bestStreak || userData.maxStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || userData.normalizedSubscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserTier(totalXP)\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank\n        const userRank = user ? transformedData.findIndex(item => item._id === user._id) : -1;\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'champions');\n      } else {\n        // Fallback demo data to showcase the amazing design\n        console.log('🎭 Loading demo data to showcase the amazing design...');\n        const demoData = [\n          {\n            _id: 'demo1',\n            name: 'Alex Champion',\n            email: '<EMAIL>',\n            class: '7',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 15000,\n            totalQuizzesTaken: 45,\n            averageScore: 92,\n            currentStreak: 12,\n            bestStreak: 18,\n            subscriptionStatus: 'premium',\n            rank: 1,\n            tier: getUserTier(15000)\n          },\n          {\n            _id: 'demo2',\n            name: 'Sarah Excellence',\n            email: '<EMAIL>',\n            class: '6',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 12500,\n            totalQuizzesTaken: 38,\n            averageScore: 88,\n            currentStreak: 8,\n            bestStreak: 15,\n            subscriptionStatus: 'premium',\n            rank: 2,\n            tier: getUserTier(12500)\n          },\n          {\n            _id: 'demo3',\n            name: 'Mike Achiever',\n            email: '<EMAIL>',\n            class: '7',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 9800,\n            totalQuizzesTaken: 32,\n            averageScore: 85,\n            currentStreak: 5,\n            bestStreak: 12,\n            subscriptionStatus: 'free',\n            rank: 3,\n            tier: getUserTier(9800)\n          },\n          {\n            _id: 'demo4',\n            name: 'Emma Rising',\n            email: '<EMAIL>',\n            class: '5',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 7200,\n            totalQuizzesTaken: 28,\n            averageScore: 82,\n            currentStreak: 3,\n            bestStreak: 9,\n            subscriptionStatus: 'free',\n            rank: 4,\n            tier: getUserTier(7200)\n          },\n          {\n            _id: 'demo5',\n            name: 'David Progress',\n            email: '<EMAIL>',\n            class: '6',\n            level: 'Secondary',\n            profilePicture: '',\n            totalXP: 5500,\n            totalQuizzesTaken: 22,\n            averageScore: 78,\n            currentStreak: 2,\n            bestStreak: 7,\n            subscriptionStatus: 'free',\n            rank: 5,\n            tier: getUserTier(5500)\n          }\n        ];\n\n        setRankingData(demoData);\n        setCurrentUserRank(null); // No current user in demo data\n        message.success('Welcome to the Hall of Champions! 🏆');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n    \n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    return () => clearInterval(animationTimer);\n  }, []);\n\n  // Filter and search functionality\n  const filteredData = rankingData.filter(rankingUser => {\n    const matchesSearch = rankingUser.name.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterType === 'all' ||\n                         (filterType === 'premium' && rankingUser.subscriptionStatus === 'premium') ||\n                         (filterType === 'free' && rankingUser.subscriptionStatus === 'free') ||\n                         (filterType === 'class' && user && rankingUser.class === user.class);\n    return matchesSearch && matchesFilter;\n  });\n\n  // Get top performers for special display\n  const topPerformers = filteredData.slice(0, 3);\n  const otherPerformers = filteredData.slice(3);\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center\"\n        >\n          <motion.div\n            animate={{ rotate: 360 }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n          />\n          <p className=\"text-white/80 text-lg font-medium\">Loading the Hall of Champions...</p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* SPECTACULAR HEADER */}\n        <motion.div\n          ref={headerRef}\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden\"\n        >\n          {/* Header Background with SPECTACULAR Gradient */}\n          <div className=\"bg-gradient-to-br from-purple-600 via-pink-500 via-red-500 via-orange-500 to-yellow-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n            \n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n                \n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-8\"\n                >\n                  <h1 className=\"text-5xl sm:text-6xl lg:text-8xl font-black mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(3px 3px 6px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)',\n                          '0 0 50px rgba(255,215,0,1), 0 0 80px rgba(255,215,0,0.8)',\n                          '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '4px 4px 8px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-xl sm:text-2xl lg:text-3xl font-semibold mb-8 max-w-4xl mx-auto leading-relaxed\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"relative max-w-2xl mx-auto mb-8\"\n                  style={{\n                    background: 'linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05))',\n                    backdropFilter: 'blur(20px)',\n                    borderRadius: '20px',\n                    padding: '24px',\n                    border: '2px solid rgba(255,255,255,0.2)',\n                    boxShadow: '0 8px 32px rgba(0,0,0,0.3)'\n                  }}\n                >\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-yellow-500/10 rounded-2xl\"></div>\n                  <motion.p\n                    key={motivationalQuote}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"text-lg sm:text-xl font-semibold relative z-10\"\n                    style={{\n                      color: '#FBBF24',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      textAlign: 'center'\n                    }}\n                  >\n                    {motivationalQuote}\n                  </motion.p>\n                </motion.div>\n\n                {/* Stats Overview */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.2, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    { icon: TbTrophy, label: 'Total Champions', value: rankingData.length, color: 'text-yellow-400' },\n                    { icon: TbFlame, label: 'Active Streaks', value: rankingData.filter(u => u.currentStreak > 0).length, color: 'text-orange-400' },\n                    { icon: TbBrain, label: 'Quizzes Taken', value: rankingData.reduce((sum, u) => sum + u.totalQuizzesTaken, 0), color: 'text-blue-400' },\n                    { icon: TbBolt, label: 'Total XP', value: rankingData.reduce((sum, u) => sum + u.totalXP, 0).toLocaleString(), color: 'text-purple-400' }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={stat.label}\n                      whileHover={{ scale: 1.05, y: -5 }}\n                      className=\"bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/20 text-center\"\n                    >\n                      <stat.icon className={`w-8 h-8 ${stat.color} mx-auto mb-2`} />\n                      <div className=\"text-2xl sm:text-3xl font-bold text-white mb-1\">{stat.value}</div>\n                      <div className=\"text-sm text-white/70\">{stat.label}</div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* INTERACTIVE CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1.5, duration: 0.8 }}\n          className=\"px-4 sm:px-6 lg:px-8 py-8\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10\">\n              <div className=\"flex flex-col lg:flex-row gap-6 items-center justify-between\">\n\n                {/* Search Bar */}\n                <div className=\"relative flex-1 max-w-md\">\n                  <TbSearch className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search champions...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"w-full pl-12 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300\"\n                  />\n                </div>\n\n                {/* Filter Controls */}\n                <div className=\"flex flex-wrap gap-3\">\n                  {[\n                    { key: 'all', label: 'All Champions', icon: TbTrophy },\n                    { key: 'premium', label: 'Premium', icon: TbCrown },\n                    { key: 'free', label: 'Free', icon: TbStar },\n                    { key: 'class', label: 'My Class', icon: TbTarget }\n                  ].map((filter) => (\n                    <motion.button\n                      key={filter.key}\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => setFilterType(filter.key)}\n                      className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${\n                        filterType === filter.key\n                          ? 'bg-purple-600 text-white shadow-lg shadow-purple-500/25'\n                          : 'bg-white/10 text-white/80 hover:bg-white/20'\n                      }`}\n                    >\n                      <filter.icon className=\"w-4 h-4\" />\n                      <span className=\"hidden sm:inline\">{filter.label}</span>\n                    </motion.button>\n                  ))}\n                </div>\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50\"\n                >\n                  <TbRefresh className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 lg:px-8 pb-20\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-3xl sm:text-4xl font-bold text-white text-center mb-8\">\n                    🏆 Champions Podium 🏆\n                  </h2>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\">\n                    {topPerformers.map((champion, index) => {\n                      const position = index + 1;\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          initial={{ opacity: 0, y: 50 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: 0.7 + index * 0.2, duration: 0.8 }}\n                          whileHover={{ scale: 1.05, y: -10 }}\n                          className={`relative ${\n                            position === 1 ? 'md:order-2 md:scale-110' :\n                            position === 2 ? 'md:order-1' : 'md:order-3'\n                          }`}\n                        >\n                          {/* Podium Card */}\n                          <div className={`relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-2xl ${champion.tier.glow} shadow-2xl`}>\n                            <div className=\"bg-slate-900/90 backdrop-blur-lg rounded-2xl p-6 text-center\">\n\n                              {/* Position Badge */}\n                              <div className={`absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center text-white font-black text-xl shadow-lg`}>\n                                {position}\n                              </div>\n\n                              {/* Crown for #1 */}\n                              {position === 1 && (\n                                <motion.div\n                                  animate={{ rotate: [0, 10, -10, 0] }}\n                                  transition={{ duration: 2, repeat: Infinity }}\n                                  className=\"absolute -top-8 left-1/2 transform -translate-x-1/2\"\n                                >\n                                  <TbCrown className=\"w-8 h-8 text-yellow-400\" />\n                                </motion.div>\n                              )}\n\n                              {/* Profile Picture */}\n                              <div className={`relative mx-auto mb-4 ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''}`}>\n                                <div className=\"w-20 h-20 rounded-full overflow-hidden mx-auto bg-gradient-to-br from-purple-500 to-pink-500 p-1\">\n                                  {champion.profilePicture ? (\n                                    <img\n                                      src={champion.profilePicture}\n                                      alt={champion.name}\n                                      className=\"w-full h-full object-cover rounded-full\"\n                                    />\n                                  ) : (\n                                    <div className=\"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-2xl\">\n                                      {champion.name.charAt(0).toUpperCase()}\n                                    </div>\n                                  )}\n                                </div>\n                                {isCurrentUser && (\n                                  <div className=\"absolute -bottom-2 -right-2 bg-yellow-400 text-black rounded-full p-1\">\n                                    <TbStar className=\"w-4 h-4\" />\n                                  </div>\n                                )}\n                              </div>\n\n                              {/* Champion Info */}\n                              <h3 className=\"text-xl font-bold mb-2\" style={{\n                                color: '#ffffff',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                fontWeight: '800'\n                              }}>{champion.name}</h3>\n                              <div className={`inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r ${champion.tier.color} rounded-full text-sm font-medium mb-3`} style={{\n                                color: '#ffffff',\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                fontWeight: '700',\n                                border: '2px solid rgba(255,255,255,0.3)'\n                              }}>\n                                <champion.tier.icon className=\"w-4 h-4\" />\n                                {champion.tier.title}\n                              </div>\n\n                              {/* Stats */}\n                              <div className=\"space-y-2\">\n                                <div className=\"flex justify-between text-sm\">\n                                  <span style={{\n                                    color: '#e5e7eb',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '600'\n                                  }}>XP:</span>\n                                  <span style={{\n                                    color: '#ffffff',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '800'\n                                  }}>{champion.totalXP.toLocaleString()}</span>\n                                </div>\n                                <div className=\"flex justify-between text-sm\">\n                                  <span style={{\n                                    color: '#e5e7eb',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '600'\n                                  }}>Quizzes:</span>\n                                  <span style={{\n                                    color: '#ffffff',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '800'\n                                  }}>{champion.totalQuizzesTaken}</span>\n                                </div>\n                                <div className=\"flex justify-between text-sm\">\n                                  <span style={{\n                                    color: '#e5e7eb',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '600'\n                                  }}>Streak:</span>\n                                  <span style={{\n                                    color: '#ffffff',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '800'\n                                  }} className=\"flex items-center gap-1\">\n                                    <TbFlame className=\"w-4 h-4 text-orange-400\" />\n                                    {champion.currentStreak}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* OTHER CHAMPIONS LIST */}\n              {otherPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"mt-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl font-bold text-white text-center mb-8\">\n                    ⚡ Rising Champions ⚡\n                  </h2>\n\n                  <div className=\"space-y-4\">\n                    {otherPerformers.map((champion, index) => {\n                      const actualRank = index + 4; // Since top 3 are shown separately\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          initial={{ opacity: 0, x: -50 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                          whileHover={{ scale: 1.02, x: 10 }}\n                          className={`relative ${isCurrentUser ? 'ring-2 ring-yellow-400' : ''}`}\n                        >\n                          <div className={`bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow}`}>\n                            <div className=\"bg-slate-900/90 backdrop-blur-lg rounded-xl p-4 flex items-center gap-4\">\n\n                              {/* Rank */}\n                              <div className={`flex-shrink-0 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg`}>\n                                {actualRank}\n                              </div>\n\n                              {/* Profile Picture */}\n                              <div className=\"flex-shrink-0\">\n                                <div className=\"w-14 h-14 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 to-pink-500 p-1\">\n                                  {champion.profilePicture ? (\n                                    <img\n                                      src={champion.profilePicture}\n                                      alt={champion.name}\n                                      className=\"w-full h-full object-cover rounded-full\"\n                                    />\n                                  ) : (\n                                    <div className=\"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-lg\">\n                                      {champion.name.charAt(0).toUpperCase()}\n                                    </div>\n                                  )}\n                                </div>\n                              </div>\n\n                              {/* Champion Info */}\n                              <div className=\"flex-1 min-w-0\">\n                                <div className=\"flex items-center gap-2 mb-1\">\n                                  <h3 className=\"text-lg font-bold truncate\" style={{\n                                    color: '#ffffff',\n                                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                    fontWeight: '800'\n                                  }}>{champion.name}</h3>\n                                  {isCurrentUser && (\n                                    <div style={{\n                                      background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                                      color: '#000000',\n                                      fontWeight: '900',\n                                      textShadow: 'none',\n                                      border: '2px solid #ffffff',\n                                      boxShadow: '0 4px 8px rgba(0,0,0,0.3)'\n                                    }} className=\"px-2 py-1 rounded-full text-xs\">\n                                      YOU\n                                    </div>\n                                  )}\n                                </div>\n                                <div className={`inline-flex items-center gap-1 px-2 py-1 bg-gradient-to-r ${champion.tier.color} rounded-full text-xs font-medium`} style={{\n                                  color: '#ffffff',\n                                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                  fontWeight: '700',\n                                  border: '1px solid rgba(255,255,255,0.3)'\n                                }}>\n                                  <champion.tier.icon className=\"w-3 h-3\" />\n                                  {champion.tier.title}\n                                </div>\n                              </div>\n\n                              {/* Stats */}\n                              <div className=\"flex-shrink-0 text-right\">\n                                <div className=\"text-lg mb-1\" style={{\n                                  color: '#ffffff',\n                                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                  fontWeight: '800'\n                                }}>\n                                  {champion.totalXP.toLocaleString()} XP\n                                </div>\n                                <div className=\"flex items-center gap-4 text-sm\">\n                                  <span className=\"flex items-center gap-1\" style={{\n                                    color: '#e5e7eb',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '600'\n                                  }}>\n                                    <TbBrain className=\"w-4 h-4\" />\n                                    {champion.totalQuizzesTaken}\n                                  </span>\n                                  <span className=\"flex items-center gap-1\" style={{\n                                    color: '#e5e7eb',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                    fontWeight: '600'\n                                  }}>\n                                    <TbFlame className=\"w-4 h-4 text-orange-400\" />\n                                    {champion.currentStreak}\n                                  </span>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold mb-2\" style={{\n                      color: '#ffffff',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>Your Current Position</h3>\n                    <div className=\"text-6xl font-black mb-2\" style={{\n                      color: '#fbbf24',\n                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                      fontWeight: '900'\n                    }}>#{currentUserRank}</div>\n                    <p className=\"text-lg\" style={{\n                      color: '#e5e7eb',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                      fontWeight: '600'\n                    }}>\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>Ready to Rise Higher?</h3>\n                  <p className=\"text-xl mb-6 max-w-2xl mx-auto\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {filteredData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbSearch className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Found</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Try adjusting your search or filter criteria to find more champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG1B,WAAW,CAAE2B,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,IAAI,GAAGH,SAAS,CAACG,IAAI,IAAI,IAAI;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACmD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAMqD,SAAS,GAAGnD,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMoD,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,gBAAgB,GAAG;IACvBC,SAAS,EAAE;MACTC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEvD,OAAO;MACbwD,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE;IACf,CAAC;IACDC,OAAO,EAAE;MACPV,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAE5C,SAAS;MACf6C,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,mBAAmB;MAChCC,WAAW,EAAE;IACf,CAAC;IACDE,QAAQ,EAAE;MACRX,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAEvC,QAAQ;MACdwC,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,aAAa;MAC1BC,WAAW,EAAE;IACf,CAAC;IACDG,IAAI,EAAE;MACJZ,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAExD,QAAQ;MACdyD,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE;IACf,CAAC;IACDI,MAAM,EAAE;MACNb,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAE/C,OAAO;MACbgD,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE;IACf,CAAC;IACDK,MAAM,EAAE;MACNd,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEtD,MAAM;MACZuD,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,aAAa;MAC1BC,WAAW,EAAE;IACf;EACF,CAAC;;EAED;EACA,MAAMM,WAAW,GAAIC,EAAE,IAAK;IAC1B,KAAK,MAAM,CAACC,IAAI,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACtB,gBAAgB,CAAC,EAAE;MAC7D,IAAIkB,EAAE,IAAIE,MAAM,CAAClB,GAAG,EAAE,OAAO;QAAEiB,IAAI;QAAE,GAAGC;MAAO,CAAC;IAClD;IACA,OAAO;MAAED,IAAI,EAAE,QAAQ;MAAE,GAAGnB,gBAAgB,CAACgB;IAAO,CAAC;EACvD,CAAC;;EAED;EACA,MAAMO,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFxC,UAAU,CAAC,IAAI,CAAC;MAChByC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAElD,IAAIC,QAAQ;;MAEZ;MACA,IAAI;QACFA,QAAQ,GAAG,MAAMxD,uBAAuB,CAAC,CAAC;QAC1CsD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEC,QAAQ,CAAC;MACtD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdH,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACpCC,QAAQ,GAAG,MAAMvD,WAAW,CAAC,CAAC;MAChC;MAEA,IAAI,CAACuD,QAAQ,IAAI,CAACA,QAAQ,CAACE,OAAO,EAAE;QAClCJ,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9CC,QAAQ,GAAG,MAAMvD,WAAW,CAAC,CAAC;MAChC;MAEA,IAAIuD,QAAQ,IAAIA,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACjD,MAAMC,eAAe,GAAGJ,QAAQ,CAACG,IAAI,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;UACzD;UACA,MAAMC,QAAQ,GAAGF,IAAI,CAACrD,IAAI,IAAIqD,IAAI;UAClC,MAAMG,UAAU,GAAGH,IAAI,CAACI,OAAO,IAAI,EAAE;;UAErC;UACA,MAAMC,YAAY,GAAGF,UAAU,CAACG,MAAM,IAAIJ,QAAQ,CAACK,iBAAiB,IAAI,CAAC;UACzE,MAAMC,UAAU,GAAGL,UAAU,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UACnF,MAAMC,YAAY,GAAGR,YAAY,GAAG,CAAC,GAAGS,IAAI,CAACC,KAAK,CAACP,UAAU,GAAGH,YAAY,CAAC,GAAG,CAAC;UACjF,MAAMW,OAAO,GAAGd,QAAQ,CAACc,OAAO,IAAId,QAAQ,CAAChB,EAAE,IAAIsB,UAAU,IAAIM,IAAI,CAACG,KAAK,CAACJ,YAAY,GAAGR,YAAY,GAAG,EAAE,CAAC,IAAI,CAAC;UAElH,OAAO;YACLa,GAAG,EAAEhB,QAAQ,CAACgB,GAAG,IAAIhB,QAAQ,CAACiB,MAAM,IAAIjB,QAAQ,CAACkB,EAAE;YACnDC,IAAI,EAAEnB,QAAQ,CAACmB,IAAI,IAAInB,QAAQ,CAACoB,QAAQ,IAAI,oBAAoB;YAChEC,KAAK,EAAErB,QAAQ,CAACqB,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEtB,QAAQ,CAACsB,KAAK,IAAItB,QAAQ,CAACuB,SAAS,IAAI,EAAE;YACjDC,KAAK,EAAExB,QAAQ,CAACwB,KAAK,IAAI,EAAE;YAC3BC,cAAc,EAAEzB,QAAQ,CAACyB,cAAc,IAAIzB,QAAQ,CAAC0B,MAAM,IAAI,EAAE;YAChEZ,OAAO,EAAEA,OAAO;YAChBT,iBAAiB,EAAEF,YAAY;YAC/BQ,YAAY,EAAEA,YAAY;YAC1BgB,aAAa,EAAE3B,QAAQ,CAAC2B,aAAa,IAAI3B,QAAQ,CAAC4B,MAAM,IAAI,CAAC;YAC7DC,UAAU,EAAE7B,QAAQ,CAAC6B,UAAU,IAAI7B,QAAQ,CAAC8B,SAAS,IAAI,CAAC;YAC1DC,kBAAkB,EAAE/B,QAAQ,CAAC+B,kBAAkB,IAAI/B,QAAQ,CAACgC,4BAA4B,IAAI,MAAM;YAClGC,IAAI,EAAElC,KAAK,GAAG,CAAC;YACfd,IAAI,EAAEF,WAAW,CAAC+B,OAAO;UAC3B,CAAC;QACH,CAAC,CAAC;;QAEF;QACAlB,eAAe,CAACsC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACtB,OAAO,GAAGqB,CAAC,CAACrB,OAAO,CAAC;;QAErD;QACAlB,eAAe,CAACyC,OAAO,CAAC,CAAC5F,IAAI,EAAEsD,KAAK,KAAK;UACvCtD,IAAI,CAACwF,IAAI,GAAGlC,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEFpD,cAAc,CAACiD,eAAe,CAAC;;QAE/B;QACA,MAAM0C,QAAQ,GAAG7F,IAAI,GAAGmD,eAAe,CAAC2C,SAAS,CAACzC,IAAI,IAAIA,IAAI,CAACkB,GAAG,KAAKvE,IAAI,CAACuE,GAAG,CAAC,GAAG,CAAC,CAAC;QACrFjE,kBAAkB,CAACuF,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;QAEvDhD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEK,eAAe,CAACQ,MAAM,EAAE,WAAW,CAAC;MACrF,CAAC,MAAM;QACL;QACAd,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrE,MAAMiD,QAAQ,GAAG,CACf;UACExB,GAAG,EAAE,OAAO;UACZG,IAAI,EAAE,eAAe;UACrBE,KAAK,EAAE,kBAAkB;UACzBC,KAAK,EAAE,GAAG;UACVE,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBX,OAAO,EAAE,KAAK;UACdT,iBAAiB,EAAE,EAAE;UACrBM,YAAY,EAAE,EAAE;UAChBgB,aAAa,EAAE,EAAE;UACjBE,UAAU,EAAE,EAAE;UACdE,kBAAkB,EAAE,SAAS;UAC7BE,IAAI,EAAE,CAAC;UACPhD,IAAI,EAAEF,WAAW,CAAC,KAAK;QACzB,CAAC,EACD;UACEiC,GAAG,EAAE,OAAO;UACZG,IAAI,EAAE,kBAAkB;UACxBE,KAAK,EAAE,mBAAmB;UAC1BC,KAAK,EAAE,GAAG;UACVE,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBX,OAAO,EAAE,KAAK;UACdT,iBAAiB,EAAE,EAAE;UACrBM,YAAY,EAAE,EAAE;UAChBgB,aAAa,EAAE,CAAC;UAChBE,UAAU,EAAE,EAAE;UACdE,kBAAkB,EAAE,SAAS;UAC7BE,IAAI,EAAE,CAAC;UACPhD,IAAI,EAAEF,WAAW,CAAC,KAAK;QACzB,CAAC,EACD;UACEiC,GAAG,EAAE,OAAO;UACZG,IAAI,EAAE,eAAe;UACrBE,KAAK,EAAE,kBAAkB;UACzBC,KAAK,EAAE,GAAG;UACVE,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBX,OAAO,EAAE,IAAI;UACbT,iBAAiB,EAAE,EAAE;UACrBM,YAAY,EAAE,EAAE;UAChBgB,aAAa,EAAE,CAAC;UAChBE,UAAU,EAAE,EAAE;UACdE,kBAAkB,EAAE,MAAM;UAC1BE,IAAI,EAAE,CAAC;UACPhD,IAAI,EAAEF,WAAW,CAAC,IAAI;QACxB,CAAC,EACD;UACEiC,GAAG,EAAE,OAAO;UACZG,IAAI,EAAE,aAAa;UACnBE,KAAK,EAAE,kBAAkB;UACzBC,KAAK,EAAE,GAAG;UACVE,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBX,OAAO,EAAE,IAAI;UACbT,iBAAiB,EAAE,EAAE;UACrBM,YAAY,EAAE,EAAE;UAChBgB,aAAa,EAAE,CAAC;UAChBE,UAAU,EAAE,CAAC;UACbE,kBAAkB,EAAE,MAAM;UAC1BE,IAAI,EAAE,CAAC;UACPhD,IAAI,EAAEF,WAAW,CAAC,IAAI;QACxB,CAAC,EACD;UACEiC,GAAG,EAAE,OAAO;UACZG,IAAI,EAAE,gBAAgB;UACtBE,KAAK,EAAE,mBAAmB;UAC1BC,KAAK,EAAE,GAAG;UACVE,KAAK,EAAE,WAAW;UAClBC,cAAc,EAAE,EAAE;UAClBX,OAAO,EAAE,IAAI;UACbT,iBAAiB,EAAE,EAAE;UACrBM,YAAY,EAAE,EAAE;UAChBgB,aAAa,EAAE,CAAC;UAChBE,UAAU,EAAE,CAAC;UACbE,kBAAkB,EAAE,MAAM;UAC1BE,IAAI,EAAE,CAAC;UACPhD,IAAI,EAAEF,WAAW,CAAC,IAAI;QACxB,CAAC,CACF;QAEDpC,cAAc,CAAC6F,QAAQ,CAAC;QACxBzF,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1BlC,OAAO,CAAC6E,OAAO,CAAC,sCAAsC,CAAC;MACzD;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD5E,OAAO,CAAC4E,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACArC,SAAS,CAAC,MAAM;IACd6E,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMoD,WAAW,GAAG5E,kBAAkB,CAAC+C,IAAI,CAACG,KAAK,CAACH,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG7E,kBAAkB,CAACuC,MAAM,CAAC,CAAC;IAC7FzC,oBAAoB,CAAC8E,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvCnF,iBAAiB,CAACoF,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,cAAc,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,YAAY,GAAGrG,WAAW,CAACsG,MAAM,CAACC,WAAW,IAAI;IACrD,MAAMC,aAAa,GAAGD,WAAW,CAAC9B,IAAI,CAACgC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpG,UAAU,CAACmG,WAAW,CAAC,CAAC,CAAC;IACvF,MAAME,aAAa,GAAGnG,UAAU,KAAK,KAAK,IACpBA,UAAU,KAAK,SAAS,IAAI+F,WAAW,CAAClB,kBAAkB,KAAK,SAAU,IACzE7E,UAAU,KAAK,MAAM,IAAI+F,WAAW,CAAClB,kBAAkB,KAAK,MAAO,IACnE7E,UAAU,KAAK,OAAO,IAAIT,IAAI,IAAIwG,WAAW,CAAC3B,KAAK,KAAK7E,IAAI,CAAC6E,KAAM;IACzF,OAAO4B,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAGP,YAAY,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC9C,MAAMC,eAAe,GAAGT,YAAY,CAACQ,KAAK,CAAC,CAAC,CAAC;;EAE7C;EACA,IAAI3G,OAAO,IAAIF,WAAW,CAAC0D,MAAM,KAAK,CAAC,EAAE;IACvC,oBACEjE,OAAA;MAAKoF,SAAS,EAAC,4GAA4G;MAAAkC,QAAA,eACzHtH,OAAA,CAACzB,MAAM,CAACgJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBrC,SAAS,EAAC,aAAa;QAAAkC,QAAA,gBAEvBtH,OAAA,CAACzB,MAAM,CAACgJ,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9D5C,SAAS,EAAC;QAAqF;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACFpI,OAAA;UAAGoF,SAAS,EAAC,mCAAmC;UAAAkC,QAAA,EAAC;QAAgC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACEpI,OAAA;IAAKoF,SAAS,EAAC,iHAAiH;IAAAkC,QAAA,gBAE9HtH,OAAA;MAAKoF,SAAS,EAAC,kCAAkC;MAAAkC,QAAA,gBAC/CtH,OAAA;QAAKoF,SAAS,EAAC;MAA2H;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjJpI,OAAA;QAAKoF,SAAS,EAAC;MAAkJ;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACxKpI,OAAA;QAAKoF,SAAS,EAAC;MAA2I;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9J,CAAC,eAGNpI,OAAA;MAAKoF,SAAS,EAAC,sDAAsD;MAAAkC,QAAA,EAClE,CAAC,GAAGe,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC3E,GAAG,CAAC,CAAC4E,CAAC,EAAEC,CAAC,kBACvBvI,OAAA,CAACzB,MAAM,CAACgJ,GAAG;QAETnC,SAAS,EAAC,mDAAmD;QAC7DsC,OAAO,EAAE;UACPc,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;UACfC,CAAC,EAAE,CAAC,CAAC,EAAEhE,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;UACnCkB,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;QACzB,CAAE;QACFG,UAAU,EAAE;UACVC,QAAQ,EAAE,CAAC,GAAGpD,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/BuB,MAAM,EAAEC,QAAQ;UAChBW,KAAK,EAAEjE,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG;QACzB,CAAE;QACFoC,KAAK,EAAE;UACLC,IAAI,EAAG,GAAEnE,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;UAC/BsC,GAAG,EAAG,GAAEpE,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG,GAAI;QAC9B;MAAE,GAfGgC,CAAC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENpI,OAAA;MAAKoF,SAAS,EAAC,eAAe;MAAAkC,QAAA,gBAE5BtH,OAAA,CAACzB,MAAM,CAACgJ,GAAG;QACTuB,GAAG,EAAErH,SAAU;QACf+F,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCd,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAE,CAAE;QAC9BZ,UAAU,EAAE;UAAEC,QAAQ,EAAE,CAAC;UAAEG,IAAI,EAAE;QAAU,CAAE;QAC7C5C,SAAS,EAAC,0BAA0B;QAAAkC,QAAA,eAGpCtH,OAAA;UAAKoF,SAAS,EAAC,kGAAkG;UAAAkC,QAAA,gBAC/GtH,OAAA;YAAKoF,SAAS,EAAC;UAA6E;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnGpI,OAAA;YAAKoF,SAAS,EAAC;UAA+E;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGrGpI,OAAA;YAAKoF,SAAS,EAAC,4DAA4D;YAAAkC,QAAA,eACzEtH,OAAA;cAAKoF,SAAS,EAAC,+BAA+B;cAAAkC,QAAA,gBAG5CtH,OAAA,CAACzB,MAAM,CAACgJ,GAAG;gBACTG,OAAO,EAAE;kBACPqB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;kBACnBC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnB,CAAE;gBACFpB,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXC,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR,CAAE;gBACF5C,SAAS,EAAC,MAAM;gBAAAkC,QAAA,eAEhBtH,OAAA;kBAAIoF,SAAS,EAAC,iEAAiE;kBAAAkC,QAAA,gBAC7EtH,OAAA,CAACzB,MAAM,CAAC0K,IAAI;oBACVvB,OAAO,EAAE;sBACPwB,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;oBACrD,CAAE;oBACFtB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR,CAAE;oBACF5C,SAAS,EAAC,+HAA+H;oBACzIuD,KAAK,EAAE;sBACLQ,cAAc,EAAE,WAAW;sBAC3BC,oBAAoB,EAAE,MAAM;sBAC5BC,mBAAmB,EAAE,aAAa;sBAClCxC,MAAM,EAAE;oBACV,CAAE;oBAAAS,QAAA,EACH;kBAED;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eACdpI,OAAA;oBAAAiI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNpI,OAAA,CAACzB,MAAM,CAAC0K,IAAI;oBACVvB,OAAO,EAAE;sBACP4B,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;oBAEhE,CAAE;oBACF1B,UAAU,EAAE;sBACVC,QAAQ,EAAE,GAAG;sBACbC,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR,CAAE;oBACFW,KAAK,EAAE;sBACL7G,KAAK,EAAE,SAAS;sBAChByH,UAAU,EAAE,KAAK;sBACjBD,UAAU,EAAE;oBACd,CAAE;oBAAAhC,QAAA,EACH;kBAED;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAGbpI,OAAA,CAACzB,MAAM,CAACiL,CAAC;gBACPhC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAG,CAAE;gBAC/Bd,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAE,CAAE;gBAC9BZ,UAAU,EAAE;kBAAEc,KAAK,EAAE,GAAG;kBAAEb,QAAQ,EAAE;gBAAI,CAAE;gBAC1CzC,SAAS,EAAC,sFAAsF;gBAChGuD,KAAK,EAAE;kBACL7G,KAAK,EAAE,SAAS;kBAChBwH,UAAU,EAAE,6BAA6B;kBACzCG,UAAU,EAAE,0CAA0C;kBACtDL,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE;gBACvB,CAAE;gBAAA/B,QAAA,EACH;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAGXpI,OAAA,CAACzB,MAAM,CAACgJ,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEsB,KAAK,EAAE;gBAAI,CAAE;gBACpCrB,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEsB,KAAK,EAAE;gBAAE,CAAE;gBAClCnB,UAAU,EAAE;kBAAEc,KAAK,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAI,CAAE;gBACxCzC,SAAS,EAAC,iCAAiC;gBAC3CuD,KAAK,EAAE;kBACLc,UAAU,EAAE,yEAAyE;kBACrFC,cAAc,EAAE,YAAY;kBAC5BC,YAAY,EAAE,MAAM;kBACpBC,OAAO,EAAE,MAAM;kBACfC,MAAM,EAAE,iCAAiC;kBACzCC,SAAS,EAAE;gBACb,CAAE;gBAAAxC,QAAA,gBAEFtH,OAAA;kBAAKoF,SAAS,EAAC;gBAAmG;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzHpI,OAAA,CAACzB,MAAM,CAACiL,CAAC;kBAEPhC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAG,CAAE;kBAC/Bd,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAE,CAAE;kBAC9BpD,SAAS,EAAC,gDAAgD;kBAC1DuD,KAAK,EAAE;oBACL7G,KAAK,EAAE,SAAS;oBAChBwH,UAAU,EAAE,6BAA6B;oBACzCS,SAAS,EAAE;kBACb,CAAE;kBAAAzC,QAAA,EAED/F;gBAAiB,GAVbA,iBAAiB;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGbpI,OAAA,CAACzB,MAAM,CAACgJ,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAG,CAAE;gBAC/Bd,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAE,CAAE;gBAC9BZ,UAAU,EAAE;kBAAEc,KAAK,EAAE,GAAG;kBAAEb,QAAQ,EAAE;gBAAI,CAAE;gBAC1CzC,SAAS,EAAC,kEAAkE;gBAAAkC,QAAA,EAE3E,CACC;kBAAEnF,IAAI,EAAExD,QAAQ;kBAAEqL,KAAK,EAAE,iBAAiB;kBAAEC,KAAK,EAAE1J,WAAW,CAAC0D,MAAM;kBAAEnC,KAAK,EAAE;gBAAkB,CAAC,EACjG;kBAAEK,IAAI,EAAErD,OAAO;kBAAEkL,KAAK,EAAE,gBAAgB;kBAAEC,KAAK,EAAE1J,WAAW,CAACsG,MAAM,CAACqD,CAAC,IAAIA,CAAC,CAAC1E,aAAa,GAAG,CAAC,CAAC,CAACvB,MAAM;kBAAEnC,KAAK,EAAE;gBAAkB,CAAC,EAChI;kBAAEK,IAAI,EAAEnD,OAAO;kBAAEgL,KAAK,EAAE,eAAe;kBAAEC,KAAK,EAAE1J,WAAW,CAAC6D,MAAM,CAAC,CAACC,GAAG,EAAE6F,CAAC,KAAK7F,GAAG,GAAG6F,CAAC,CAAChG,iBAAiB,EAAE,CAAC,CAAC;kBAAEpC,KAAK,EAAE;gBAAgB,CAAC,EACtI;kBAAEK,IAAI,EAAE9C,MAAM;kBAAE2K,KAAK,EAAE,UAAU;kBAAEC,KAAK,EAAE1J,WAAW,CAAC6D,MAAM,CAAC,CAACC,GAAG,EAAE6F,CAAC,KAAK7F,GAAG,GAAG6F,CAAC,CAACvF,OAAO,EAAE,CAAC,CAAC,CAACwF,cAAc,CAAC,CAAC;kBAAErI,KAAK,EAAE;gBAAkB,CAAC,CAC1I,CAAC4B,GAAG,CAAC,CAAC0G,IAAI,EAAExG,KAAK,kBAChB5D,OAAA,CAACzB,MAAM,CAACgJ,GAAG;kBAET8C,UAAU,EAAE;oBAAEtB,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAE,CAAE;kBACnCpD,SAAS,EAAC,gFAAgF;kBAAAkC,QAAA,gBAE1FtH,OAAA,CAACoK,IAAI,CAACjI,IAAI;oBAACiD,SAAS,EAAG,WAAUgF,IAAI,CAACtI,KAAM;kBAAe;oBAAAmG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9DpI,OAAA;oBAAKoF,SAAS,EAAC,gDAAgD;oBAAAkC,QAAA,EAAE8C,IAAI,CAACH;kBAAK;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClFpI,OAAA;oBAAKoF,SAAS,EAAC,uBAAuB;oBAAAkC,QAAA,EAAE8C,IAAI,CAACJ;kBAAK;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GANpDgC,IAAI,CAACJ,KAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOL,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbpI,OAAA,CAACzB,MAAM,CAACgJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAG,CAAE;QAC/Bd,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAE,CAAE;QAC9BZ,UAAU,EAAE;UAAEc,KAAK,EAAE,GAAG;UAAEb,QAAQ,EAAE;QAAI,CAAE;QAC1CzC,SAAS,EAAC,2BAA2B;QAAAkC,QAAA,eAErCtH,OAAA;UAAKoF,SAAS,EAAC,mBAAmB;UAAAkC,QAAA,eAChCtH,OAAA;YAAKoF,SAAS,EAAC,oEAAoE;YAAAkC,QAAA,eACjFtH,OAAA;cAAKoF,SAAS,EAAC,8DAA8D;cAAAkC,QAAA,gBAG3EtH,OAAA;gBAAKoF,SAAS,EAAC,0BAA0B;gBAAAkC,QAAA,gBACvCtH,OAAA,CAACf,QAAQ;kBAACmG,SAAS,EAAC;gBAA0E;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjGpI,OAAA;kBACEsK,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,qBAAqB;kBACjCN,KAAK,EAAEpJ,UAAW;kBAClB2J,QAAQ,EAAGC,CAAC,IAAK3J,aAAa,CAAC2J,CAAC,CAACC,MAAM,CAACT,KAAK,CAAE;kBAC/C7E,SAAS,EAAC;gBAAiN;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5N,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNpI,OAAA;gBAAKoF,SAAS,EAAC,sBAAsB;gBAAAkC,QAAA,EAClC,CACC;kBAAEqD,GAAG,EAAE,KAAK;kBAAEX,KAAK,EAAE,eAAe;kBAAE7H,IAAI,EAAExD;gBAAS,CAAC,EACtD;kBAAEgM,GAAG,EAAE,SAAS;kBAAEX,KAAK,EAAE,SAAS;kBAAE7H,IAAI,EAAEvD;gBAAQ,CAAC,EACnD;kBAAE+L,GAAG,EAAE,MAAM;kBAAEX,KAAK,EAAE,MAAM;kBAAE7H,IAAI,EAAEtD;gBAAO,CAAC,EAC5C;kBAAE8L,GAAG,EAAE,OAAO;kBAAEX,KAAK,EAAE,UAAU;kBAAE7H,IAAI,EAAEpD;gBAAS,CAAC,CACpD,CAAC2E,GAAG,CAAEmD,MAAM,iBACX7G,OAAA,CAACzB,MAAM,CAACqM,MAAM;kBAEZP,UAAU,EAAE;oBAAEtB,KAAK,EAAE;kBAAK,CAAE;kBAC5B8B,QAAQ,EAAE;oBAAE9B,KAAK,EAAE;kBAAK,CAAE;kBAC1B+B,OAAO,EAAEA,CAAA,KAAM9J,aAAa,CAAC6F,MAAM,CAAC8D,GAAG,CAAE;kBACzCvF,SAAS,EAAG,wFACVrE,UAAU,KAAK8F,MAAM,CAAC8D,GAAG,GACrB,yDAAyD,GACzD,6CACL,EAAE;kBAAArD,QAAA,gBAEHtH,OAAA,CAAC6G,MAAM,CAAC1E,IAAI;oBAACiD,SAAS,EAAC;kBAAS;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnCpI,OAAA;oBAAMoF,SAAS,EAAC,kBAAkB;oBAAAkC,QAAA,EAAET,MAAM,CAACmD;kBAAK;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAXnDvB,MAAM,CAAC8D,GAAG;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYF,CAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNpI,OAAA,CAACzB,MAAM,CAACqM,MAAM;gBACZP,UAAU,EAAE;kBAAEtB,KAAK,EAAE,IAAI;kBAAEpB,MAAM,EAAE;gBAAI,CAAE;gBACzCkD,QAAQ,EAAE;kBAAE9B,KAAK,EAAE;gBAAK,CAAE;gBAC1B+B,OAAO,EAAE5H,gBAAiB;gBAC1B6H,QAAQ,EAAEtK,OAAQ;gBAClB2E,SAAS,EAAC,4LAA4L;gBAAAkC,QAAA,gBAEtMtH,OAAA,CAACb,SAAS;kBAACiG,SAAS,EAAG,WAAU3E,OAAO,GAAG,cAAc,GAAG,EAAG;gBAAE;kBAAAwH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpEpI,OAAA;kBAAAsH,QAAA,EAAM;gBAAO;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGZ3H,OAAO,iBACNT,OAAA,CAACzB,MAAM,CAACgJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBrC,SAAS,EAAC,iDAAiD;QAAAkC,QAAA,gBAE3DtH,OAAA,CAACzB,MAAM,CAACgJ,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9D5C,SAAS,EAAC;QAA6E;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,eACFpI,OAAA;UAAGoF,SAAS,EAAC,mCAAmC;UAAAkC,QAAA,EAAC;QAAoB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CACb,EAGA,CAAC3H,OAAO,iBACPT,OAAA,CAACzB,MAAM,CAACgJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAG,CAAE;QAC/Bd,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEe,CAAC,EAAE;QAAE,CAAE;QAC9BZ,UAAU,EAAE;UAAEc,KAAK,EAAE,GAAG;UAAEb,QAAQ,EAAE;QAAI,CAAE;QAC1CzC,SAAS,EAAC,4BAA4B;QAAAkC,QAAA,eAEtCtH,OAAA;UAAKoF,SAAS,EAAC,mBAAmB;UAAAkC,QAAA,GAG/BH,aAAa,CAAClD,MAAM,GAAG,CAAC,iBACvBjE,OAAA,CAACzB,MAAM,CAACgJ,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAI,CAAE;YACpCrB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAE,CAAE;YAClCnB,UAAU,EAAE;cAAEc,KAAK,EAAE,GAAG;cAAEb,QAAQ,EAAE;YAAI,CAAE;YAC1CzC,SAAS,EAAC,OAAO;YAAAkC,QAAA,gBAEjBtH,OAAA;cAAIoF,SAAS,EAAC,4DAA4D;cAAAkC,QAAA,EAAC;YAE3E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELpI,OAAA;cAAKoF,SAAS,EAAC,yDAAyD;cAAAkC,QAAA,EACrEH,aAAa,CAACzD,GAAG,CAAC,CAACsH,QAAQ,EAAEpH,KAAK,KAAK;gBACtC,MAAMqH,QAAQ,GAAGrH,KAAK,GAAG,CAAC;gBAC1B,MAAMsH,aAAa,GAAG5K,IAAI,IAAI0K,QAAQ,CAACnG,GAAG,KAAKvE,IAAI,CAACuE,GAAG;gBAEvD,oBACE7E,OAAA,CAACzB,MAAM,CAACgJ,GAAG;kBAETC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAG,CAAE;kBAC/Bd,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAE,CAAE;kBAC9BZ,UAAU,EAAE;oBAAEc,KAAK,EAAE,GAAG,GAAG9E,KAAK,GAAG,GAAG;oBAAEiE,QAAQ,EAAE;kBAAI,CAAE;kBACxDwC,UAAU,EAAE;oBAAEtB,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCpD,SAAS,EAAG,YACV6F,QAAQ,KAAK,CAAC,GAAG,yBAAyB,GAC1CA,QAAQ,KAAK,CAAC,GAAG,YAAY,GAAG,YACjC,EAAE;kBAAA3D,QAAA,eAGHtH,OAAA;oBAAKoF,SAAS,EAAG,8BAA6B4F,QAAQ,CAAClI,IAAI,CAAChB,KAAM,oBAAmBkJ,QAAQ,CAAClI,IAAI,CAACZ,IAAK,aAAa;oBAAAoF,QAAA,eACnHtH,OAAA;sBAAKoF,SAAS,EAAC,8DAA8D;sBAAAkC,QAAA,gBAG3EtH,OAAA;wBAAKoF,SAAS,EAAG,mFAAkF4F,QAAQ,CAAClI,IAAI,CAAChB,KAAM,wFAAwF;wBAAAwF,QAAA,EAC5M2D;sBAAQ;wBAAAhD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,EAGL6C,QAAQ,KAAK,CAAC,iBACbjL,OAAA,CAACzB,MAAM,CAACgJ,GAAG;wBACTG,OAAO,EAAE;0BAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;wBAAE,CAAE;wBACrCC,UAAU,EAAE;0BAAEC,QAAQ,EAAE,CAAC;0BAAEC,MAAM,EAAEC;wBAAS,CAAE;wBAC9C3C,SAAS,EAAC,qDAAqD;wBAAAkC,QAAA,eAE/DtH,OAAA,CAACpB,OAAO;0BAACwG,SAAS,EAAC;wBAAyB;0BAAA6C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CACb,eAGDpI,OAAA;wBAAKoF,SAAS,EAAG,yBAAwB8F,aAAa,GAAG,wBAAwB,GAAG,EAAG,EAAE;wBAAA5D,QAAA,gBACvFtH,OAAA;0BAAKoF,SAAS,EAAC,kGAAkG;0BAAAkC,QAAA,EAC9G0D,QAAQ,CAAC1F,cAAc,gBACtBtF,OAAA;4BACEmL,GAAG,EAAEH,QAAQ,CAAC1F,cAAe;4BAC7B8F,GAAG,EAAEJ,QAAQ,CAAChG,IAAK;4BACnBI,SAAS,EAAC;0BAAyC;4BAAA6C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFpI,OAAA;4BAAKoF,SAAS,EAAC,yIAAyI;4BAAAkC,QAAA,EACrJ0D,QAAQ,CAAChG,IAAI,CAACqG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnC;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,EACL8C,aAAa,iBACZlL,OAAA;0BAAKoF,SAAS,EAAC,uEAAuE;0BAAAkC,QAAA,eACpFtH,OAAA,CAACnB,MAAM;4BAACuG,SAAS,EAAC;0BAAS;4BAAA6C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGNpI,OAAA;wBAAIoF,SAAS,EAAC,wBAAwB;wBAACuD,KAAK,EAAE;0BAC5C7G,KAAK,EAAE,SAAS;0BAChBwH,UAAU,EAAE,6BAA6B;0BACzCC,UAAU,EAAE;wBACd,CAAE;wBAAAjC,QAAA,EAAE0D,QAAQ,CAAChG;sBAAI;wBAAAiD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACvBpI,OAAA;wBAAKoF,SAAS,EAAG,6DAA4D4F,QAAQ,CAAClI,IAAI,CAAChB,KAAM,wCAAwC;wBAAC6G,KAAK,EAAE;0BAC/I7G,KAAK,EAAE,SAAS;0BAChBwH,UAAU,EAAE,6BAA6B;0BACzCC,UAAU,EAAE,KAAK;0BACjBM,MAAM,EAAE;wBACV,CAAE;wBAAAvC,QAAA,gBACAtH,OAAA,CAACgL,QAAQ,CAAClI,IAAI,CAACX,IAAI;0BAACiD,SAAS,EAAC;wBAAS;0BAAA6C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACzC4C,QAAQ,CAAClI,IAAI,CAACV,KAAK;sBAAA;wBAAA6F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC,eAGNpI,OAAA;wBAAKoF,SAAS,EAAC,WAAW;wBAAAkC,QAAA,gBACxBtH,OAAA;0BAAKoF,SAAS,EAAC,8BAA8B;0BAAAkC,QAAA,gBAC3CtH,OAAA;4BAAM2I,KAAK,EAAE;8BACX7G,KAAK,EAAE,SAAS;8BAChBwH,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,EAAC;0BAAG;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACbpI,OAAA;4BAAM2I,KAAK,EAAE;8BACX7G,KAAK,EAAE,SAAS;8BAChBwH,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,EAAE0D,QAAQ,CAACrG,OAAO,CAACwF,cAAc,CAAC;0BAAC;4BAAAlC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1C,CAAC,eACNpI,OAAA;0BAAKoF,SAAS,EAAC,8BAA8B;0BAAAkC,QAAA,gBAC3CtH,OAAA;4BAAM2I,KAAK,EAAE;8BACX7G,KAAK,EAAE,SAAS;8BAChBwH,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,EAAC;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAClBpI,OAAA;4BAAM2I,KAAK,EAAE;8BACX7G,KAAK,EAAE,SAAS;8BAChBwH,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,EAAE0D,QAAQ,CAAC9G;0BAAiB;4BAAA+D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnC,CAAC,eACNpI,OAAA;0BAAKoF,SAAS,EAAC,8BAA8B;0BAAAkC,QAAA,gBAC3CtH,OAAA;4BAAM2I,KAAK,EAAE;8BACX7G,KAAK,EAAE,SAAS;8BAChBwH,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,EAAC;0BAAO;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACjBpI,OAAA;4BAAM2I,KAAK,EAAE;8BACX7G,KAAK,EAAE,SAAS;8BAChBwH,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAACnE,SAAS,EAAC,yBAAyB;4BAAAkC,QAAA,gBACpCtH,OAAA,CAAClB,OAAO;8BAACsG,SAAS,EAAC;4BAAyB;8BAAA6C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC9C4C,QAAQ,CAACxF,aAAa;0BAAA;4BAAAyC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GA/GD4C,QAAQ,CAACnG,GAAG;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgHP,CAAC;cAEjB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,EAGAf,eAAe,CAACpD,MAAM,GAAG,CAAC,iBACzBjE,OAAA,CAACzB,MAAM,CAACgJ,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAG,CAAE;YAC/Bd,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAE,CAAE;YAC9BZ,UAAU,EAAE;cAAEc,KAAK,EAAE,CAAC;cAAEb,QAAQ,EAAE;YAAI,CAAE;YACxCzC,SAAS,EAAC,OAAO;YAAAkC,QAAA,gBAEjBtH,OAAA;cAAIoF,SAAS,EAAC,4DAA4D;cAAAkC,QAAA,EAAC;YAE3E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELpI,OAAA;cAAKoF,SAAS,EAAC,WAAW;cAAAkC,QAAA,EACvBD,eAAe,CAAC3D,GAAG,CAAC,CAACsH,QAAQ,EAAEpH,KAAK,KAAK;gBACxC,MAAM2H,UAAU,GAAG3H,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC9B,MAAMsH,aAAa,GAAG5K,IAAI,IAAI0K,QAAQ,CAACnG,GAAG,KAAKvE,IAAI,CAACuE,GAAG;gBAEvD,oBACE7E,OAAA,CAACzB,MAAM,CAACgJ,GAAG;kBAETC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCf,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAE,CAAE;kBAC9Bb,UAAU,EAAE;oBAAEc,KAAK,EAAE,GAAG,GAAG9E,KAAK,GAAG,GAAG;oBAAEiE,QAAQ,EAAE;kBAAI,CAAE;kBACxDwC,UAAU,EAAE;oBAAEtB,KAAK,EAAE,IAAI;oBAAEN,CAAC,EAAE;kBAAG,CAAE;kBACnCrD,SAAS,EAAG,YAAW8F,aAAa,GAAG,wBAAwB,GAAG,EAAG,EAAE;kBAAA5D,QAAA,eAEvEtH,OAAA;oBAAKoF,SAAS,EAAG,oBAAmB4F,QAAQ,CAAClI,IAAI,CAAChB,KAAM,mBAAkBkJ,QAAQ,CAAClI,IAAI,CAACZ,IAAK,EAAE;oBAAAoF,QAAA,eAC7FtH,OAAA;sBAAKoF,SAAS,EAAC,yEAAyE;sBAAAkC,QAAA,gBAGtFtH,OAAA;wBAAKoF,SAAS,EAAG,6CAA4C4F,QAAQ,CAAClI,IAAI,CAAChB,KAAM,uFAAuF;wBAAAwF,QAAA,EACrKiE;sBAAU;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR,CAAC,eAGNpI,OAAA;wBAAKoF,SAAS,EAAC,eAAe;wBAAAkC,QAAA,eAC5BtH,OAAA;0BAAKoF,SAAS,EAAC,0FAA0F;0BAAAkC,QAAA,EACtG0D,QAAQ,CAAC1F,cAAc,gBACtBtF,OAAA;4BACEmL,GAAG,EAAEH,QAAQ,CAAC1F,cAAe;4BAC7B8F,GAAG,EAAEJ,QAAQ,CAAChG,IAAK;4BACnBI,SAAS,EAAC;0BAAyC;4BAAA6C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFpI,OAAA;4BAAKoF,SAAS,EAAC,wIAAwI;4BAAAkC,QAAA,EACpJ0D,QAAQ,CAAChG,IAAI,CAACqG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnC;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNpI,OAAA;wBAAKoF,SAAS,EAAC,gBAAgB;wBAAAkC,QAAA,gBAC7BtH,OAAA;0BAAKoF,SAAS,EAAC,8BAA8B;0BAAAkC,QAAA,gBAC3CtH,OAAA;4BAAIoF,SAAS,EAAC,4BAA4B;4BAACuD,KAAK,EAAE;8BAChD7G,KAAK,EAAE,SAAS;8BAChBwH,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,EAAE0D,QAAQ,CAAChG;0BAAI;4BAAAiD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,EACtB8C,aAAa,iBACZlL,OAAA;4BAAK2I,KAAK,EAAE;8BACVc,UAAU,EAAE,0CAA0C;8BACtD3H,KAAK,EAAE,SAAS;8BAChByH,UAAU,EAAE,KAAK;8BACjBD,UAAU,EAAE,MAAM;8BAClBO,MAAM,EAAE,mBAAmB;8BAC3BC,SAAS,EAAE;4BACb,CAAE;4BAAC1E,SAAS,EAAC,gCAAgC;4BAAAkC,QAAA,EAAC;0BAE9C;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACNpI,OAAA;0BAAKoF,SAAS,EAAG,6DAA4D4F,QAAQ,CAAClI,IAAI,CAAChB,KAAM,mCAAmC;0BAAC6G,KAAK,EAAE;4BAC1I7G,KAAK,EAAE,SAAS;4BAChBwH,UAAU,EAAE,6BAA6B;4BACzCC,UAAU,EAAE,KAAK;4BACjBM,MAAM,EAAE;0BACV,CAAE;0BAAAvC,QAAA,gBACAtH,OAAA,CAACgL,QAAQ,CAAClI,IAAI,CAACX,IAAI;4BAACiD,SAAS,EAAC;0BAAS;4BAAA6C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACzC4C,QAAQ,CAAClI,IAAI,CAACV,KAAK;wBAAA;0BAAA6F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNpI,OAAA;wBAAKoF,SAAS,EAAC,0BAA0B;wBAAAkC,QAAA,gBACvCtH,OAAA;0BAAKoF,SAAS,EAAC,cAAc;0BAACuD,KAAK,EAAE;4BACnC7G,KAAK,EAAE,SAAS;4BAChBwH,UAAU,EAAE,6BAA6B;4BACzCC,UAAU,EAAE;0BACd,CAAE;0BAAAjC,QAAA,GACC0D,QAAQ,CAACrG,OAAO,CAACwF,cAAc,CAAC,CAAC,EAAC,KACrC;wBAAA;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACNpI,OAAA;0BAAKoF,SAAS,EAAC,iCAAiC;0BAAAkC,QAAA,gBAC9CtH,OAAA;4BAAMoF,SAAS,EAAC,yBAAyB;4BAACuD,KAAK,EAAE;8BAC/C7G,KAAK,EAAE,SAAS;8BAChBwH,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,gBACAtH,OAAA,CAAChB,OAAO;8BAACoG,SAAS,EAAC;4BAAS;8BAAA6C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC9B4C,QAAQ,CAAC9G,iBAAiB;0BAAA;4BAAA+D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB,CAAC,eACPpI,OAAA;4BAAMoF,SAAS,EAAC,yBAAyB;4BAACuD,KAAK,EAAE;8BAC/C7G,KAAK,EAAE,SAAS;8BAChBwH,UAAU,EAAE,6BAA6B;8BACzCC,UAAU,EAAE;4BACd,CAAE;4BAAAjC,QAAA,gBACAtH,OAAA,CAAClB,OAAO;8BAACsG,SAAS,EAAC;4BAAyB;8BAAA6C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC9C4C,QAAQ,CAACxF,aAAa;0BAAA;4BAAAyC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GA7FD4C,QAAQ,CAACnG,GAAG;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8FP,CAAC;cAEjB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,EAGAzH,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrCX,OAAA,CAACzB,MAAM,CAACgJ,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAI,CAAE;YACpCrB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAE,CAAE;YAClCnB,UAAU,EAAE;cAAEc,KAAK,EAAE,GAAG;cAAEb,QAAQ,EAAE;YAAI,CAAE;YAC1CzC,SAAS,EAAC,wIAAwI;YAAAkC,QAAA,eAElJtH,OAAA;cAAKoF,SAAS,EAAC,aAAa;cAAAkC,QAAA,gBAC1BtH,OAAA;gBAAIoF,SAAS,EAAC,yBAAyB;gBAACuD,KAAK,EAAE;kBAC7C7G,KAAK,EAAE,SAAS;kBAChBwH,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAAqB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BpI,OAAA;gBAAKoF,SAAS,EAAC,0BAA0B;gBAACuD,KAAK,EAAE;kBAC/C7G,KAAK,EAAE,SAAS;kBAChBwH,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,GAAC,GAAC,EAAC3G,eAAe;cAAA;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3BpI,OAAA;gBAAGoF,SAAS,EAAC,SAAS;gBAACuD,KAAK,EAAE;kBAC5B7G,KAAK,EAAE,SAAS;kBAChBwH,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAGDpI,OAAA,CAACzB,MAAM,CAACgJ,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAG,CAAE;YAC/Bd,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAE,CAAE;YAC9BZ,UAAU,EAAE;cAAEc,KAAK,EAAE,CAAC;cAAEb,QAAQ,EAAE;YAAI,CAAE;YACxCzC,SAAS,EAAC,mBAAmB;YAAAkC,QAAA,eAE7BtH,OAAA;cAAKoF,SAAS,EAAC,8HAA8H;cAAAkC,QAAA,gBAC3ItH,OAAA,CAACzB,MAAM,CAACgJ,GAAG;gBACTG,OAAO,EAAE;kBAAEqB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;gBAAE,CAAE;gBACjCnB,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEC,MAAM,EAAEC;gBAAS,CAAE;gBAAAT,QAAA,eAE9CtH,OAAA,CAACV,QAAQ;kBAAC8F,SAAS,EAAC;gBAAwC;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACbpI,OAAA;gBAAIoF,SAAS,EAAC,yBAAyB;gBAACuD,KAAK,EAAE;kBAC7C7G,KAAK,EAAE,SAAS;kBAChBwH,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAAqB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BpI,OAAA;gBAAGoF,SAAS,EAAC,gCAAgC;gBAACuD,KAAK,EAAE;kBACnD7G,KAAK,EAAE,SAAS;kBAChBwH,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAGH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJpI,OAAA,CAACzB,MAAM,CAACqM,MAAM;gBACZP,UAAU,EAAE;kBAAEtB,KAAK,EAAE;gBAAK,CAAE;gBAC5B8B,QAAQ,EAAE;kBAAE9B,KAAK,EAAE;gBAAK,CAAE;gBAC1B3D,SAAS,EAAC,sJAAsJ;gBAChK0F,OAAO,EAAEA,CAAA,KAAMU,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAa;gBAAApE,QAAA,EACpD;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EAGZxB,YAAY,CAAC3C,MAAM,KAAK,CAAC,IAAI,CAACxD,OAAO,iBACpCT,OAAA,CAACzB,MAAM,CAACgJ,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAI,CAAE;YACpCrB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEsB,KAAK,EAAE;YAAE,CAAE;YAClC3D,SAAS,EAAC,mBAAmB;YAAAkC,QAAA,gBAE7BtH,OAAA,CAACf,QAAQ;cAACmG,SAAS,EAAC;YAAsC;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7DpI,OAAA;cAAIoF,SAAS,EAAC,yBAAyB;cAACuD,KAAK,EAAE;gBAC7C7G,KAAK,EAAE,SAAS;gBAChBwH,UAAU,EAAE,6BAA6B;gBACzCC,UAAU,EAAE;cACd,CAAE;cAAAjC,QAAA,EAAC;YAAkB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BpI,OAAA;cAAGoF,SAAS,EAAC,SAAS;cAACuD,KAAK,EAAE;gBAC5B7G,KAAK,EAAE,SAAS;gBAChBwH,UAAU,EAAE,6BAA6B;gBACzCC,UAAU,EAAE;cACd,CAAE;cAAAjC,QAAA,EAAC;YAEH;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClI,EAAA,CA77BID,kBAAkB;EAAA,QACJxB,WAAW;AAAA;AAAAkN,EAAA,GADzB1L,kBAAkB;AA+7BxB,eAAeA,kBAAkB;AAAC,IAAA0L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}