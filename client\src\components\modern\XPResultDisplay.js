import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TbB<PERSON>, 
  TbTrophy, 
  Tb<PERSON>lame, 
  TbTarget, 
  TbClock, 
  TbTrendingUp,
  TbStar,
  TbMedal,
  TbChevronDown,
  TbChevronUp
} from 'react-icons/tb';

const XPResultDisplay = ({ xpData, className = '' }) => {
  const [showBreakdown, setShowBreakdown] = useState(false);
  const [animatedXP, setAnimatedXP] = useState(0);

  // Debug XP data
  console.log('🎨 XPResultDisplay - XP Data received:', xpData);

  useEffect(() => {
    if (xpData?.xpAwarded) {
      // Animate XP counter
      const duration = 2000;
      const steps = 60;
      const increment = xpData.xpAwarded / steps;
      let current = 0;

      const timer = setInterval(() => {
        current += increment;
        if (current >= xpData.xpAwarded) {
          setAnimatedXP(xpData.xpAwarded);
          clearInterval(timer);
        } else {
          setAnimatedXP(Math.floor(current));
        }
      }, duration / steps);

      return () => clearInterval(timer);
    }
  }, [xpData]);

  if (!xpData) {
    return null;
  }

  const {
    xpAwarded = 0,
    xpBreakdown = {},
    levelUp = false,
    newLevel = 1,
    newTotalXP = 0,
    currentStreak = 0,
    achievements = []
  } = xpData;

  return (
    <div className={`bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-6 border-2 border-purple-200 shadow-lg ${className}`}>
      {/* Level Up Notification */}
      <AnimatePresence>
        {levelUp && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: -20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: -20 }}
            className="mb-6 p-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl text-white text-center shadow-lg"
          >
            <div className="flex items-center justify-center mb-2">
              <TbTrophy className="w-8 h-8 mr-2" />
              <span className="text-xl font-bold">LEVEL UP!</span>
            </div>
            <p className="text-lg">You reached Level {newLevel}!</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main XP Display */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-3">
          <TbBolt className="w-8 h-8 text-purple-600 mr-2" />
          <h3 className="text-2xl font-bold text-gray-800">XP Earned</h3>
        </div>
        
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 200, damping: 10 }}
          className="text-6xl font-bold text-purple-600 mb-2"
        >
          +{animatedXP}
        </motion.div>
        
        <p className="text-gray-600">
          Total XP: {newTotalXP.toLocaleString()} | Level {newLevel}
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        {currentStreak > 0 && (
          <div className="bg-white rounded-lg p-3 text-center shadow-sm">
            <TbFlame className="w-6 h-6 text-orange-500 mx-auto mb-1" />
            <div className="text-lg font-bold text-gray-800">{currentStreak}</div>
            <div className="text-sm text-gray-600">Streak</div>
          </div>
        )}
        
        {achievements.length > 0 && (
          <div className="bg-white rounded-lg p-3 text-center shadow-sm">
            <TbMedal className="w-6 h-6 text-yellow-500 mx-auto mb-1" />
            <div className="text-lg font-bold text-gray-800">{achievements.length}</div>
            <div className="text-sm text-gray-600">New Badges</div>
          </div>
        )}
      </div>

      {/* XP Breakdown Toggle */}
      {Object.keys(xpBreakdown).length > 0 && (
        <div>
          <button
            onClick={() => setShowBreakdown(!showBreakdown)}
            className="w-full flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
          >
            <span className="font-medium text-gray-800">XP Breakdown</span>
            {showBreakdown ? (
              <TbChevronUp className="w-5 h-5 text-gray-600" />
            ) : (
              <TbChevronDown className="w-5 h-5 text-gray-600" />
            )}
          </button>

          <AnimatePresence>
            {showBreakdown && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-3 bg-white rounded-lg p-4 shadow-sm"
              >
                <div className="space-y-3">
                  {xpBreakdown.baseXP && (
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <TbTarget className="w-4 h-4 text-blue-500 mr-2" />
                        <span className="text-sm text-gray-700">Base XP</span>
                      </div>
                      <span className="font-medium text-gray-800">+{xpBreakdown.baseXP}</span>
                    </div>
                  )}
                  
                  {xpBreakdown.difficultyBonus > 0 && (
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <TbTrendingUp className="w-4 h-4 text-green-500 mr-2" />
                        <span className="text-sm text-gray-700">Difficulty Bonus</span>
                      </div>
                      <span className="font-medium text-green-600">+{xpBreakdown.difficultyBonus}</span>
                    </div>
                  )}
                  
                  {xpBreakdown.speedBonus > 0 && (
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <TbClock className="w-4 h-4 text-yellow-500 mr-2" />
                        <span className="text-sm text-gray-700">Speed Bonus</span>
                      </div>
                      <span className="font-medium text-yellow-600">+{xpBreakdown.speedBonus}</span>
                    </div>
                  )}
                  
                  {xpBreakdown.perfectScoreBonus > 0 && (
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <TbStar className="w-4 h-4 text-purple-500 mr-2" />
                        <span className="text-sm text-gray-700">Perfect Score</span>
                      </div>
                      <span className="font-medium text-purple-600">+{xpBreakdown.perfectScoreBonus}</span>
                    </div>
                  )}
                  
                  {xpBreakdown.streakBonus > 0 && (
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <TbFlame className="w-4 h-4 text-orange-500 mr-2" />
                        <span className="text-sm text-gray-700">Streak Bonus</span>
                      </div>
                      <span className="font-medium text-orange-600">+{xpBreakdown.streakBonus}</span>
                    </div>
                  )}
                  
                  {xpBreakdown.firstAttemptBonus > 0 && (
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <TbMedal className="w-4 h-4 text-indigo-500 mr-2" />
                        <span className="text-sm text-gray-700">First Attempt</span>
                      </div>
                      <span className="font-medium text-indigo-600">+{xpBreakdown.firstAttemptBonus}</span>
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}

      {/* Achievement Notifications */}
      {achievements.length > 0 && (
        <div className="mt-6">
          <h4 className="text-lg font-bold text-gray-800 mb-3 flex items-center">
            <TbMedal className="w-5 h-5 mr-2 text-yellow-500" />
            New Achievements
          </h4>
          <div className="space-y-2">
            {achievements.map((achievement, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200"
              >
                <TbTrophy className="w-6 h-6 text-yellow-500 mr-3" />
                <div>
                  <div className="font-medium text-gray-800">{achievement.name}</div>
                  <div className="text-sm text-gray-600">{achievement.description}</div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default XPResultDisplay;
